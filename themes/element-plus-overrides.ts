import { themes } from './index'

function hslToRgb(h: number, s: number, l: number): [number, number, number] {
  let r, g, b;

  if (s === 0) {
    r = g = b = l;
  } else {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, (h / 360) + 1 / 3);
    g = hue2rgb(p, q, h / 360);
    b = hue2rgb(p, q, (h / 360) - 1 / 3);
  }

  return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
}


export function generateElementPlusOverrides(): string {
  let css = '';

  themes.forEach(theme => {
    const { roots, light, dark } = theme;

    const primaryHsl = light['--primary'].split(' ');
    const h = parseInt(primaryHsl[0]);
    const s = parseInt(primaryHsl[1]) / 100;
    const l = parseInt(primaryHsl[2]) / 100;

    const primaryRgb = hslToRgb(h, s, l);

    css += `body${roots} {
      --el-color-primary: rgb(${primaryRgb.join(', ')});
      --el-color-primary-light-3: rgb(${hslToRgb(h, s, Math.min(l + 0.1, 0.9)).join(', ')});
      --el-color-primary-light-5: rgb(${hslToRgb(h, s, Math.min(l + 0.2, 0.9)).join(', ')});
      --el-color-primary-light-7: rgb(${hslToRgb(h, s, Math.min(l + 0.3, 0.9)).join(', ')});
      --el-color-primary-light-8: rgb(${hslToRgb(h, s, Math.min(l + 0.4, 0.9)).join(', ')});
      --el-color-primary-light-9: rgb(${hslToRgb(h, s, Math.min(l + 0.5, 0.95)).join(', ')});
      --el-color-primary-dark-2: rgb(${hslToRgb(h, s, Math.max(l - 0.1, 0.1)).join(', ')});
      --el-border-radius-base: var(--radius-size);
      --el-border-radius-small: calc(var(--radius-size) - 2px);
      --el-border-radius-round: calc(var(--radius-size) + 15px);
      --el-border-radius-circle: 50%;
    }\n`;

    const darkPrimaryHsl = dark['--primary'].split(' ');
    const darkH = parseInt(darkPrimaryHsl[0]);
    const darkS = parseInt(darkPrimaryHsl[1]) / 100;
    const darkL = parseInt(darkPrimaryHsl[2]) / 100;
    const darkPrimaryRgb = hslToRgb(darkH, darkS, darkL);

    css += `html.dark body${roots} {
      --el-color-primary: rgb(${darkPrimaryRgb.join(', ')});
      --el-color-primary-light-3: rgb(${hslToRgb(darkH, darkS, Math.min(darkL + 0.1, 0.9)).join(', ')});
      --el-color-primary-light-5: rgb(${hslToRgb(darkH, darkS, Math.min(darkL + 0.2, 0.9)).join(', ')});
      --el-color-primary-light-7: rgb(${hslToRgb(darkH, darkS, Math.min(darkL + 0.3, 0.9)).join(', ')});
      --el-color-primary-light-8: rgb(${hslToRgb(darkH, darkS, Math.min(darkL + 0.4, 0.9)).join(', ')});
      --el-color-primary-light-9: rgb(${hslToRgb(darkH, darkS, Math.min(darkL + 0.5, 0.95)).join(', ')});
      --el-color-primary-dark-2: rgb(${hslToRgb(darkH, darkS, Math.max(darkL - 0.1, 0.1)).join(', ')});
      --el-border-radius-base: var(--radius-size);
      --el-border-radius-small: calc(var(--radius-size) - 2px);
      --el-border-radius-round: calc(var(--radius-size) + 15px);
      --el-border-radius-circle: 50%;
    }\n`;
  });
  return css;
}
