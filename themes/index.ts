export type ThemeType = 'blue' | 'green' | 'red' | 'purple' | 'yellow';
export type ColorMode = 'light' | 'dark';

export const radiusPresets = {
  none: {
    '--radius': '0',
  },
  small: {
    '--radius': '0.25rem',
  },
  medium: {
    '--radius': '0.5rem',
  },
  large: {
    '--radius': '1rem',
  },
}

export const blueTheme = {
  roots: '.theme-blue',
  light: {
    '--background': '0 0% 100%',
    '--foreground': '220 14% 20%',
    '--card': '0 0% 100%',
    '--card-foreground': '220 14% 20%',
    '--popover': '0 0% 100%',
    '--popover-foreground': '220 14% 20%',
    '--primary': '210 100% 63%',
    '--primary-foreground': '0 0% 100%',
    '--secondary': '220 13% 91%',
    '--secondary-foreground': '220 14% 20%',
    '--muted': '220 13% 91%',
    '--muted-foreground': '220 9% 46%',
    '--accent': '220 13% 91%',
    '--accent-foreground': '220 14% 20%',
    '--destructive': '0 72% 51%',
    '--destructive-foreground': '0 0% 100%',
    '--border': '220 13% 91%',
    '--input': '220 13% 91%',
    '--ring': '203 89% 53%',
    '--radius': 'var(--radius-size)',
    '--chart-1': '203 89% 53%',
    '--chart-2': '142 73% 45%',
    '--chart-3': '32 87% 62%',
    '--chart-4': '0 72% 51%',
    '--chart-5': '220 9% 46%',
  },
  dark: {
    '--background': '220 14% 10%',
    '--foreground': '220 14% 90%',
    '--card': '220 14% 10%',
    '--card-foreground': '220 14% 90%',
    '--popover': '220 14% 10%',
    '--popover-foreground': '220 14% 90%',
    '--primary': '203 89% 53%',
    '--primary-foreground': '0 0% 100%',
    '--secondary': '220 14% 20%',
    '--secondary-foreground': '220 14% 90%',
    '--muted': '220 14% 20%',
    '--muted-foreground': '220 9% 60%',
    '--accent': '220 14% 20%',
    '--accent-foreground': '220 14% 90%',
    '--destructive': '0 72% 51%',
    '--destructive-foreground': '0 0% 100%',
    '--border': '220 14% 20%',
    '--input': '220 14% 20%',
    '--ring': '203 89% 53%',
    '--radius': 'var(--radius-size)',
    '--chart-1': '203 89% 53%',
    '--chart-2': '142 73% 45%',
    '--chart-3': '32 87% 62%',
    '--chart-4': '0 72% 51%',
    '--chart-5': '220 9% 60%',
  }
}

export const greenTheme = {
  roots: '.theme-green',
  light: {
    '--background': '0 0% 100%',
    '--foreground': '222.2 84% 4.9%',
    '--card': '0 0% 100%',
    '--card-foreground': '222.2 84% 4.9%',
    '--popover': '0 0% 100%',
    '--popover-foreground': '222.2 84% 4.9%',
    '--primary': '142.1 76.2% 36.3%',
    '--primary-foreground': '355.7 100% 97.3%',
    '--secondary': '240 4.8% 95.9%',
    '--secondary-foreground': '240 5.9% 10%',
    '--muted': '240 4.8% 95.9%',
    '--muted-foreground': '240 3.8% 46.1%',
    '--accent': '240 4.8% 95.9%',
    '--accent-foreground': '240 5.9% 10%',
    '--destructive': '0 84.2% 60.2%',
    '--destructive-foreground': '0 0% 98%',
    '--border': '240 5.9% 90%',
    '--input': '240 5.9% 90%',
    '--ring': '142.1 76.2% 36.3%',
    '--radius': 'var(--radius-size)',
    '--chart-1': '142 70% 45%',
    '--chart-2': '180 65% 40%',
    '--chart-3': '200 60% 35%',
    '--chart-4': '120 75% 50%',
    '--chart-5': '160 80% 45%',
  },
  dark: {
    '--background': '222.2 84% 4.9%',
    '--foreground': '210 40% 98%',
    '--card': '222.2 84% 4.9%',
    '--card-foreground': '210 40% 98%',
    '--popover': '222.2 84% 4.9%',
    '--popover-foreground': '210 40% 98%',
    '--primary': '142.1 70.2% 29.8%',
    '--primary-foreground': '355.7 100% 97.3%',
    '--secondary': '240 3.7% 15.9%',
    '--secondary-foreground': '0 0% 98%',
    '--muted': '240 3.7% 15.9%',
    '--muted-foreground': '240 5% 64.9%',
    '--accent': '240 3.7% 15.9%',
    '--accent-foreground': '0 0% 98%',
    '--destructive': '0 62.8% 30.6%',
    '--destructive-foreground': '0 0% 98%',
    '--border': '240 3.7% 15.9%',
    '--input': '240 3.7% 15.9%',
    '--ring': '142.4 71.8% 29.2%',
    '--radius': 'var(--radius-size)',
    '--chart-1': '142 65% 40%',
    '--chart-2': '180 60% 35%',
    '--chart-3': '200 55% 30%',
    '--chart-4': '120 70% 45%',
    '--chart-5': '160 75% 40%',
  }
}

export const redTheme = {
  roots: '.theme-red',
  light: {
    '--background': '0 0% 100%',
    '--foreground': '222.2 84% 4.9%',
    '--card': '0 0% 100%',
    '--card-foreground': '222.2 84% 4.9%',
    '--popover': '0 0% 100%',
    '--popover-foreground': '222.2 84% 4.9%',
    '--primary': '0 84.2% 60.2%',
    '--primary-foreground': '210 40% 98%',
    '--secondary': '210 40% 96.1%',
    '--secondary-foreground': '222.2 47.4% 11.2%',
    '--muted': '210 40% 96.1%',
    '--muted-foreground': '215.4 16.3% 46.9%',
    '--accent': '210 40% 96.1%',
    '--accent-foreground': '222.2 47.4% 11.2%',
    '--destructive': '0 84.2% 60.2%',
    '--destructive-foreground': '210 40% 98%',
    '--border': '214.3 31.8% 91.4%',
    '--input': '214.3 31.8% 91.4%',
    '--ring': '0 84.2% 60.2%',
    '--radius': 'var(--radius-size)',
    '--chart-1': '0 80% 65%',
    '--chart-2': '25 75% 60%',
    '--chart-3': '350 70% 55%',
    '--chart-4': '15 85% 70%',
    '--chart-5': '335 80% 65%',
  },
  dark: {
    '--background': '222.2 84% 4.9%',
    '--foreground': '210 40% 98%',
    '--card': '222.2 84% 4.9%',
    '--card-foreground': '210 40% 98%',
    '--popover': '222.2 84% 4.9%',
    '--popover-foreground': '210 40% 98%',
    '--primary': '0 72.2% 50.6%',
    '--primary-foreground': '210 40% 98%',
    '--secondary': '217.2 32.6% 17.5%',
    '--secondary-foreground': '210 40% 98%',
    '--muted': '217.2 32.6% 17.5%',
    '--muted-foreground': '215 20.2% 65.1%',
    '--accent': '217.2 32.6% 17.5%',
    '--accent-foreground': '210 40% 98%',
    '--destructive': '0 62.8% 30.6%',
    '--destructive-foreground': '210 40% 98%',
    '--border': '217.2 32.6% 17.5%',
    '--input': '217.2 32.6% 17.5%',
    '--ring': '0 72.2% 50.6%',
    '--radius': 'var(--radius-size)',
    '--chart-1': '0 75% 60%',
    '--chart-2': '25 70% 55%',
    '--chart-3': '350 65% 50%',
    '--chart-4': '15 80% 65%',
    '--chart-5': '335 75% 60%',
  }
}

export const purpleTheme = {
  roots: '.theme-purple',
  light: {
    '--background': '0 0% 100%',
    '--foreground': '222.2 84% 4.9%',
    '--card': '0 0% 100%',
    '--card-foreground': '222.2 84% 4.9%',
    '--popover': '0 0% 100%',
    '--popover-foreground': '222.2 84% 4.9%',
    '--primary': '270 75% 60%',
    '--primary-foreground': '0 0% 100%',
    '--secondary': '270 20% 96%',
    '--secondary-foreground': '270 25% 10%',
    '--muted': '270 20% 96%',
    '--muted-foreground': '270 10% 50%',
    '--accent': '270 20% 96%',
    '--accent-foreground': '270 25% 10%',
    '--destructive': '0 84.2% 60.2%',
    '--destructive-foreground': '0 0% 98%',
    '--border': '270 20% 90%',
    '--input': '270 20% 90%',
    '--ring': '270 75% 60%',
    '--radius': 'var(--radius-size)',
    '--chart-1': '270 75% 60%',
    '--chart-2': '290 70% 55%',
    '--chart-3': '250 65% 50%',
    '--chart-4': '310 60% 55%',
    '--chart-5': '230 70% 60%',
  },
  dark: {
    '--background': '222.2 84% 4.9%',
    '--foreground': '210 40% 98%',
    '--card': '222.2 84% 4.9%',
    '--card-foreground': '210 40% 98%',
    '--popover': '222.2 84% 4.9%',
    '--popover-foreground': '210 40% 98%',
    '--primary': '270 70% 55%',
    '--primary-foreground': '0 0% 100%',
    '--secondary': '270 25% 16%',
    '--secondary-foreground': '210 40% 98%',
    '--muted': '270 25% 16%',
    '--muted-foreground': '270 10% 65%',
    '--accent': '270 25% 16%',
    '--accent-foreground': '210 40% 98%',
    '--destructive': '0 62.8% 30.6%',
    '--destructive-foreground': '210 40% 98%',
    '--border': '270 25% 16%',
    '--input': '270 25% 16%',
    '--ring': '270 70% 55%',
    '--radius': 'var(--radius-size)',
    '--chart-1': '270 70% 55%',
    '--chart-2': '290 65% 50%',
    '--chart-3': '250 60% 45%',
    '--chart-4': '310 55% 50%',
    '--chart-5': '230 65% 55%',
  }
}

export const yellowTheme = {
  roots: '.theme-yellow',
  light: {
    '--background': '0 0% 100%',
    '--foreground': '222.2 84% 4.9%',
    '--card': '0 0% 100%',
    '--card-foreground': '222.2 84% 4.9%',
    '--popover': '0 0% 100%',
    '--popover-foreground': '222.2 84% 4.9%',
    '--primary': '45 93% 47%',
    '--primary-foreground': '0 0% 0%',
    '--secondary': '45 20% 96%',
    '--secondary-foreground': '45 25% 10%',
    '--muted': '45 20% 96%',
    '--muted-foreground': '45 10% 50%',
    '--accent': '45 20% 96%',
    '--accent-foreground': '45 25% 10%',
    '--destructive': '0 84.2% 60.2%',
    '--destructive-foreground': '0 0% 98%',
    '--border': '45 20% 90%',
    '--input': '45 20% 90%',
    '--ring': '45 93% 47%',
    '--radius': 'var(--radius-size)',
    '--chart-1': '45 93% 47%',
    '--chart-2': '25 88% 42%',
    '--chart-3': '65 85% 45%',
    '--chart-4': '15 80% 50%',
    '--chart-5': '85 75% 45%',
  },
  dark: {
    '--background': '222.2 84% 4.9%',
    '--foreground': '210 40% 98%',
    '--card': '222.2 84% 4.9%',
    '--card-foreground': '210 40% 98%',
    '--popover': '222.2 84% 4.9%',
    '--popover-foreground': '210 40% 98%',
    '--primary': '45 88% 42%',
    '--primary-foreground': '0 0% 0%',
    '--secondary': '45 25% 16%',
    '--secondary-foreground': '210 40% 98%',
    '--muted': '45 25% 16%',
    '--muted-foreground': '45 10% 65%',
    '--accent': '45 25% 16%',
    '--accent-foreground': '210 40% 98%',
    '--destructive': '0 62.8% 30.6%',
    '--destructive-foreground': '210 40% 98%',
    '--border': '45 25% 16%',
    '--input': '45 25% 16%',
    '--ring': '45 88% 42%',
    '--radius': 'var(--radius-size)',
    '--chart-1': '45 88% 42%',
    '--chart-2': '25 83% 37%',
    '--chart-3': '65 80% 40%',
    '--chart-4': '15 75% 45%',
    '--chart-5': '85 70% 40%',
  }
}

export const themes = [blueTheme, greenTheme, redTheme, purpleTheme, yellowTheme]
