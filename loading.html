<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宝鸡市黄河流域生态保护智能监管平台</title>
    <style>
        .body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #1a365d 0%, #065f46 100%);
            font-family: system-ui, -apple-system, sans-serif;
            color: white;
            overflow: hidden;
            z-index: 9999;
            width: 100%;
        }

        .body .loading-container {
            text-align: center;
            padding: 2rem;
        }

        .body .title {
            font-size: 1.75rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            background: linear-gradient(90deg, #60a5fa, #34d399);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .body .subtitle {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 2rem;
        }

        .body .loading-bar {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
            margin: 2rem auto;
        }

        .body .loading-progress {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 0%;
            background: linear-gradient(90deg, #60a5fa, #34d399);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .body .loading-text {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 1rem;
        }

        .body .loading-icons {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 2rem;
        }

        .body .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .body .icon {
            width: 3rem;
            height: 3rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }

            50% {
                transform: scale(1.1);
                opacity: 1;
            }

            100% {
                transform: scale(1);
                opacity: 0.8;
            }
        }

        .body .icon-label {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>

<body>
    <div class="body">
        <div class="loading-container">
            <h1 class="title">宝鸡市黄河流域生态保护智能监管平台</h1>
            <p class="subtitle">智慧监管 · 生态保护</p>

            <div class="loading-bar">
                <div class="loading-progress" id="progress"></div>
            </div>

            <div class="loading-text" id="status">系统初始化中...</div>

            <div class="loading-icons">
                <div class="icon-item">
                    <div class="icon">🌊</div>
                    <span class="icon-label">水质监测</span>
                </div>
                <div class="icon-item">
                    <div class="icon">🌳</div>
                    <span class="icon-label">生态监管</span>
                </div>
                <div class="icon-item">
                    <div class="icon">📊</div>
                    <span class="icon-label">数据分析</span>
                </div>
                <div class="icon-item">
                    <div class="icon">🛡️</div>
                    <span class="icon-label">智能预警</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        const progressBar = document.getElementById('progress');
        const statusText = document.getElementById('status');
        const loadingStates = [
            '系统初始化中...',
            '加载核心模块...',
            '连接监测设备...',
            '同步实时数据...',
            '准备就绪...'
        ];
        let progress = 0;

        function updateProgress() {
            if (progress < 100) {
                progress += Math.random() * 20;
                if (progress > 100) progress = 100;

                progressBar.style.width = `${progress}%`;
                const stateIndex = Math.min(
                    Math.floor((progress / 100) * loadingStates.length),
                    loadingStates.length - 1
                );
                statusText.textContent = loadingStates[stateIndex];

                if (progress < 100) {
                    setTimeout(updateProgress, 700 + Math.random() * 1000);
                }
            }
        }

        updateProgress();
    </script>
</body>

</html>