import type { Directive, DirectiveBinding } from 'vue'
import { useUserStore } from '@/stores/user'

export interface PermissionBinding {
  value: string | string[]
  arg?: 'some' | 'every'
}

function hasPermission(permissions: string | string[], mode: 'some' | 'every' = 'some'): boolean {
  const userStore = useUserStore()

  if (typeof permissions === 'string') {
    return userStore.hasPermission(permissions)
  }

  return mode === 'some'
    ? permissions.some(code => userStore.hasPermission(code))
    : permissions.every(code => userStore.hasPermission(code))
}

export const permission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding<string | string[]>) {
    checkAndRemoveElement(el, binding)
  },
  updated(el: HTMLElement, binding: DirectiveBinding<string | string[]>) {
    if (binding.value !== binding.oldValue) {
      checkAndRemoveElement(el, binding)
    }
  }
}

function checkAndRemoveElement(el: HTMLElement, binding: DirectiveBinding<string | string[]>) {
  const { value, arg = 'some' } = binding as DirectiveBinding<PermissionBinding['value']>

  if (!hasPermission(value, arg as 'some' | 'every')) {
    el.parentNode?.removeChild(el)
  }
}

export function usePermission() {
  return {
    has: hasPermission
  }
}
