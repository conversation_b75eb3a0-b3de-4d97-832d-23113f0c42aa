import type { Directive, DirectiveBinding } from 'vue'
import dayjs from '@/utils/dayjs'
import i18n from '@/i18n'

export type TimeValue = string | number | Date

export interface TimeOptions {
  value: TimeValue
  format?: string
}

export type TimeBinding = TimeValue | TimeOptions

function isTimeOptions(value: TimeBinding): value is TimeOptions {
  return typeof value === 'object' && 'value' in value
}

const DEFAULT_FORMAT = 'YYYY-MM-DD HH:mm:ss'
const UPDATE_INTERVAL = 60 * 1000

function formatTime(el: HTMLElement, binding: DirectiveBinding<TimeBinding>) {
  const { value, arg = 'format' } = binding

  const actualValue = isTimeOptions(value) ? value.value : value
  const format = isTimeOptions(value) ? value.format || DEFAULT_FORMAT : DEFAULT_FORMAT
  const t = i18n.global.t

  if (!actualValue) {
    el.innerText = '--'
    return
  }

  const time = dayjs(actualValue)

  if (!time.isValid()) {
    el.innerText = '--'
    return
  }

  if (arg === 'relative') {
    const now = dayjs()
    const diff = now.diff(time, 'day')

    if (diff === 0 && time.isSame(now, 'day')) {
      el.innerText = t('time.today')
      return
    }
    if (diff === 1 && time.isSame(now.subtract(1, 'day'), 'day')) {
      el.innerText = t('time.yesterday')
      return
    }
    if (diff === -1 && time.isSame(now.add(1, 'day'), 'day')) {
      el.innerText = t('time.tomorrow')
      return
    }

    el.innerText = time.fromNow()
  } else {
    el.innerText = time.format(format)
  }
}

export const time: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding<TimeBinding>) {
    formatTime(el, binding)

    if (binding.modifiers?.auto) {
      const intervalId = setInterval(() => {
        formatTime(el, binding)
      }, UPDATE_INTERVAL)

      el.dataset.timeInterval = intervalId.toString()
    }
  },

  updated(el: HTMLElement, binding: DirectiveBinding<TimeBinding>) {
    formatTime(el, binding)
  },

  unmounted(el: HTMLElement) {
    const intervalId = el.dataset.timeInterval
    if (intervalId) {
      clearInterval(parseInt(intervalId))
    }
  }
}

export function useTime() {
  return {
    format: (value: TimeValue | TimeOptions, customFormat?: string) => {
      const actualValue = isTimeOptions(value) ? value.value : value
      const format = customFormat || (isTimeOptions(value) ? value.format : undefined) || DEFAULT_FORMAT
      return dayjs(actualValue).format(format)
    },
    relative: (value: TimeValue | TimeOptions) => {
      const actualValue = isTimeOptions(value) ? value.value : value
      return dayjs(actualValue).fromNow()
    }
  }
}
