import type { App } from 'vue'
import { permission } from './permission'
import { time } from './time'

const directives = {
  permission,
  time,
} as const

export type DirectiveKey = keyof typeof directives

export function setupDirectives(app: App) {
  Object.entries(directives).forEach(([name, directive]) => {
    app.directive(name, directive)
  })
}

export { usePermission } from './permission'
export { useTime } from './time'

export type * from './permission'
export type * from './time'
