<template>
  <div class="map-container">
    <div
      id="map"
      class="map"
    />
  </div>
</template>
<script setup>
import 'ol/ol.css' // 引入 OpenLayers 的样式
import { onMounted ,ref} from 'vue'
import XYZ from 'ol/source/XYZ';
import { Map, View } from 'ol'
import TileLayer from 'ol/layer/Tile'
import VectorSource from 'ol/source/Vector'
import VectorLayer from 'ol/layer/Vector'
import GeoJSON from 'ol/format/GeoJSON'
import { Style, Stroke, Fill, Circle,Text  } from 'ol/style';
const layerTypeMap = {
  vector: ['vec', 'cva'], // [矢量底图, 矢量注记]
  image: ['img', 'cva'], // [影像底图, 影像注记]
  terrain: ['ter', 'cta'], // [地形晕渲, 地形注记]
}
const map=ref({})

onMounted(() => {
  // 创建地图实例
  initMap('image')
})
// 分辨率与缩放级别的关系（EPSG:4326 下的近似值）
const getResolutionForZoom = (zoom) => {
  // EPSG:4326 的分辨率计算，单位为度/像素
  return 180 / (256 * Math.pow(2, zoom)); // 简化公式，适用于天地图
}
const initMap = (layerType = 'image') => {
  const key = 'f4d0553a23372a2f48c74851c7e46f4d'

  const matrixSet = 'w' // c: 经纬度投影 w: 墨卡托投影
  map.value = new Map({
    target: 'map',
    layers: [
      // 底图
      new TileLayer({
        source: new XYZ({
          // url: `http://t{0-6}.tianditu.com/${layerTypeMap[layerType][0]}_${matrixSet}/wmts?tk=${key}`,
          url:`http://t{0-7}.tianditu.gov.cn/${layerTypeMap[layerType][0]}_${matrixSet}/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${key}`,
          layer: layerTypeMap[layerType][0],
          matrixSet: matrixSet,
          style: 'default',
          crossOrigin: 'anonymous',
          format: 'tiles',
          wrapX: true,
         
        }),
      }),
      // 注记
      new TileLayer({
        source: new XYZ({
          // url: `http:///t{0-7}.tianditu.gov.cn/${layerTypeMap[layerType][1]}_${matrixSet}/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}tk=${key}`,
          url:`http://t{0-7}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${key}`,
          layer: layerTypeMap[layerType][1],
          matrixSet: matrixSet,
          style: 'default',
          crossOrigin: 'anonymous',
          format: 'tiles',
          wrapX: true,
         
        }),
      }),
      new VectorLayer({
        source: new VectorSource({
          url: '/baoj.json', // 相对于 public 文件夹的路径
          format: new GeoJSON(),
          projection: 'EPSG:4326', // 确保与地图投影一致
        }),
        style: (feature,resolution) => {
          console.log("aa",feature,resolution, getResolutionForZoom(14))
          if (resolution < getResolutionForZoom(11)) {
            return null; // 超过 zoom 10 不显示
          }
          
          const geometryType = feature.getGeometry().getType();
          if (geometryType === 'Point') {
            return new Style({
              image: new Circle({
                radius: 6,
                fill: new Fill({ color: 'red' }),
                stroke: new Stroke({ color: 'white', width: 2 }),
              }),
            });
          } else if (geometryType === 'LineString') {
            return new Style({
              stroke: new Stroke({ color: 'blue', width: 3 }),
            });
          } else {
            return new Style({
              fill: new Fill({ color: 'rgba(0, 255, 255, 0.09)' }),
              stroke: new Stroke({ color: 'yellow', width: 2 }),
              text: new Text({
              text: feature.getProperties().name,
              font: '14px sans-serif',
              fill: new Fill({ color: 'black' }),
              stroke: new Stroke({ color: 'white', width: 2 }),
              overflow: true,
              placement: 'point', // 标注放置在多边形中心
            }),
            });
          }
        },
      }),
    ],
    view: new View({
      center: [107.2306393, 34.3645],
      projection: "EPSG:4326",
      zoom: 9,
      maxZoom: 17,
      minZoom: 1,
    }),
  })
}
</script>

<style lang="scss">
.map-container {
  width: 100%;
  height: 100%;
  .map {
    width: 100%;
    height: 100%;
  }
}
</style>
