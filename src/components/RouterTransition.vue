<template>
  <router-view v-slot="{ Component, route }">
    <transition :name="transitionName" mode="out-in">
      <div :key="route.fullPath" class="router__view">
        <component :is="Component" v-if="appStore.currentLayout === route.meta.layout" />
      </div>
    </transition>
  </router-view>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const transitionName = ref('fade')
const appStore = useAppStore()

watch(
  () => route.path,
  (newPath, oldPath) => {
    if (oldPath === '/' || newPath === '/') {
      transitionName.value = 'none'
      return
    }
    const newDepth = newPath.split('/').length
    const oldDepth = oldPath?.split('/').length || 0

    transitionName.value = newDepth > oldDepth
      ? 'slide-left'
      : newDepth < oldDepth
        ? 'slide-right'
        : 'fade'
  }
)
</script>

<style lang="scss">
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
