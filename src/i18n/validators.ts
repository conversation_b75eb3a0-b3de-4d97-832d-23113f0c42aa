import i18n from './index'

export function validateTranslationKey(key: string, locale: 'en' | 'zh'): boolean {
  const messages = (i18n.global.messages.value as Record<'en' | 'zh', Record<string, any>>)[locale]
  return key.split('.').reduce((obj: any, k) => obj && obj[k], messages) !== undefined
}

export function validateTranslationCompleteness(
  messages: Record<string, any>,
  reference: Record<string, any>,
  path: string[] = []
): string[] {
  const missingKeys: string[] = []

  Object.keys(reference).forEach(key => {
    const currentPath = [...path, key]
    const keyPath = currentPath.join('.')

    if (!(key in messages)) {
      missingKeys.push(keyPath)
    } else if (
      typeof reference[key] === 'object' &&
      reference[key] !== null &&
      !Array.isArray(reference[key])
    ) {
      missingKeys.push(
        ...validateTranslationCompleteness(
          messages[key] || {},
          reference[key],
          currentPath
        )
      )
    }
  })

  return missingKeys
}
