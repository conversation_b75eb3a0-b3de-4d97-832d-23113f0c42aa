import { createLogger } from '@/utils/logger'
import { createI18n, useI18n } from 'vue-i18n'
import type { MessageSchema } from './types'

type ModuleName = 'common' | 'business'

const logger = createLogger("i18n")

const i18n = createI18n({
  legacy: false,
  locale: 'zh',
  fallbackLocale: 'en',
  messages: {},
})

export async function loadLocaleModule(locale: 'en' | 'zh', module: ModuleName) {
  try {
    const messages = await import(`./locales/${locale}/${module}.json`)
    const currentMessages = (i18n.global.messages.value as any)[locale] || {}
    i18n.global.setLocaleMessage(locale, {
      ...currentMessages,
      ...messages.default,
    })
  } catch (error) {
    logger.error(`Failed to load ${locale}/${module}`, error)
  }
}

export async function loadLocaleMessages(locale: 'en' | 'zh') {
  const modules: ModuleName[] = ['common', 'business']
  await Promise.all(modules.map(module => loadLocaleModule(locale, module)))
  i18n.global.locale.value = locale
}

loadLocaleMessages('zh')

export const useTypedI18n = () => useI18n<{ message: MessageSchema }>();

export default i18n
