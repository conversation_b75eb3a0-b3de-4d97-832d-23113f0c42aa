{"table": {"operation": "Operation", "noData": "No Data", "pleaseInput": "Please Input", "pleaseSelect": "Please Select"}, "common": {"confirm": "Confirm", "cancel": "Cancel", "save": "Save", "submit": "Submit", "back": "Back", "refresh": "Refresh", "add": "Add", "edit": "Edit", "delete": "Delete", "remove": "Remove", "view": "View", "detail": "Detail", "download": "Download", "upload": "Upload", "import": "Import", "export": "Export", "search": "Search", "reset": "Reset", "select": "Please Select", "modifyPassword": "Modify Password", "resetPassword": "Reset Password", "logout": "Logout", "loading": "Loading", "processing": "Processing", "welcome": "Welcome", "tips": "Tips", "notice": "Notice", "warning": "Warning", "error": "Error", "query": "Query"}, "message": {"confirmDelete": "Are you sure you want to delete?", "confirmLogout": "Are you sure you want to logout?", "deleteSuccess": "Delete Successfully", "deleteFailed": "Delete Failed", "saveSuccess": "Save Successfully", "saveFailed": "Save Failed", "updateSuccess": "Update Successfully", "updateFailed": "Update Failed", "operationSuccess": "Operation Successfully", "operationFailed": "Operation Failed", "submitSuccess": "Submitted Successfully", "submitFailed": "Submission Failed", "networkError": "Network Error", "serverError": "Server Error", "parameterError": "Parameter Error", "unauthorized": "Unauthorized or Login Expired", "forbidden": "Access Forbidden", "logoutSuccess": "Logout Successfully", "logoutFailed": "Logout Failed", "resetPasswordSuccess": "Reset Password Successfully", "resetPasswordFailed": "Reset Password Failed", "confirmResetPassword": "Are you sure you want to reset this user's password?", "passwordResetTo": "Password will be reset to"}, "status": {"enable": "Enable", "disable": "Disable", "online": "Online", "offline": "Offline", "active": "Active", "inactive": "Inactive", "success": "Success", "failed": "Failed", "pending": "Pending", "processing": "Processing", "completed": "Completed", "cancelled": "Cancelled"}, "form": {"required": "Required", "optional": "Optional", "invalid": "Invalid", "placeholder": {"input": "Please input {field}", "select": "Please select {field}"}}, "time": {"year": "Year", "month": "Month", "day": "Day", "hour": "Hour", "minute": "Minute", "second": "Second", "yesterday": "Yesterday", "today": "Today", "tomorrow": "Tomorrow"}, "settings": {"title": "Settings", "language": {"title": "Language Settings", "select": "Select Language", "zh": "简体中文", "en": "English"}, "theme": {"title": "Theme Settings", "mode": "Theme Mode", "light": "Light", "dark": "Dark", "system": "System", "color": "Theme Color", "radius": "Border Radius", "radius_none": "None", "radius_small": "Small", "radius_medium": "Medium", "radius_large": "Large"}, "layout": {"title": "Layout Settings", "menu_mode": "Menu Mode", "vertical": "Vertical", "horizontal": "Horizontal", "fixed_sidebar": "Fixed Sidebar"}, "personalization": {"title": "Personalization", "dynamic_title": "Dynamic Title", "dynamic_title_tip": "When enabled, page title will change with current page", "progress": "Loading Progress", "progress_tip": "When enabled, show top progress bar during page transitions", "welcome": "Welcome Message", "welcome_placeholder": "Supports template syntax ${accountName}", "welcome_tip": "You can use ${accountName} as username variable"}}}