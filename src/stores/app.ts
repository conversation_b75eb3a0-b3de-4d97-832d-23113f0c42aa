import type { SystemApp } from '@/interfaces';
import { defineStore } from 'pinia';
import { ref } from 'vue';

export type LayoutKey = 'default' | 'blank';

export const useAppStore = defineStore('app', () => {
  const currentApp = ref<SystemApp | null>(null)
  const currentLayout = ref<LayoutKey>('blank')

  const setLayout = (layout: LayoutKey) => currentLayout.value = layout

  const setApp = (app: SystemApp | null) => currentApp.value = app

  return {
    currentApp,
    currentLayout,
    setApp,
    setLayout
  }
});
