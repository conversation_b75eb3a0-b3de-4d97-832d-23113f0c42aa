import { defineStore } from "pinia";
import { ref, watch, computed } from "vue";
import type { RouteMeta } from 'vue-router'
import { getStorage, setStorage } from '@/utils/storage'
import { defaultSettings } from '@/config/default-settings'
import type { AppSettings } from '@/types/app'

export const useSettingsStore = defineStore("settings", () => {
  const prefersColorScheme = window.matchMedia('(prefers-color-scheme: dark)')
  const storedSettings = getStorage<AppSettings.Config>('settings', defaultSettings)
  const settings = ref<AppSettings.Config>(storedSettings || defaultSettings)

  const currentColorScheme = ref<Exclude<AppSettings.Theme['colorScheme'], ''>>()
  const systemColorScheme = ref<'light' | 'dark'>(prefersColorScheme.matches ? 'dark' : 'light')

  const effectiveColorMode = computed(() => {
    if (settings.value.theme.followSystemTheme) {
      return systemColorScheme.value
    }

    const colorScheme = settings.value.theme.colorScheme
    if (colorScheme === '') {
      return systemColorScheme.value
    }

    return colorScheme
  })

  const radiusPresets: { [key in AppSettings.Theme['borderRadius']]: { '--radius': string } } = {
    'none': { '--radius': '0px' },
    'small': { '--radius': '4px' },
    'medium': { '--radius': '8px' },
    'large': { '--radius': '16px' }
  }

  const updateTheme = () => {
    let colorScheme = settings.value.theme.colorScheme
    if (colorScheme === '') {
      colorScheme = prefersColorScheme.matches ? 'dark' : 'light'
    }
    currentColorScheme.value = colorScheme

    document.body.classList.remove(
      'theme-blue',
      'theme-green',
      'theme-red',
      'theme-purple',
      'theme-yellow'
    )

    document.body.classList.add(`theme-${settings.value.theme.themeType}`)

    if (effectiveColorMode.value === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }

    const borderRadius = settings.value.theme.borderRadius || 'medium'
    const radius = radiusPresets[borderRadius] || radiusPresets['medium']
    document.documentElement.style.setProperty('--radius-size', radius['--radius'])
  }

  const title = ref<RouteMeta['title']>()
  const setTitle = (t: RouteMeta['title']) => title.value = t

  const setSidebarMode = (mode: AppSettings.SidebarMode) => {
    if (mode === 'horizontal') {
      settings.value.layout.sidebarCollapse = false
    }
    settings.value.layout.sidebarMode = mode
  }

  const setSidebarFixed = (b: boolean) => {
    settings.value.layout.sidebarFixed = b
  }

  const toggleSidebarCollapse = () => {
    if (settings.value.layout.sidebarMode === 'horizontal') {
      settings.value.layout.sidebarCollapse = false
      return
    }
    settings.value.layout.sidebarCollapse = !settings.value.layout.sidebarCollapse
  }

  const setThemeType = (themeType: AppSettings.ThemeType) => {
    settings.value.theme.themeType = themeType
    updateTheme()
  }

  const setColorMode = (colorMode: AppSettings.ColorMode) => {
    settings.value.theme.followSystemTheme = false
    settings.value.theme.colorScheme = colorMode
    updateTheme()
  }

  const toggleDarkMode = () => {
    settings.value.theme.followSystemTheme = false
    settings.value.theme.colorScheme = effectiveColorMode.value === 'light' ? 'dark' : 'light'
    updateTheme()
  }

  const toggleFollowSystemTheme = () => {
    settings.value.theme.followSystemTheme = !settings.value.theme.followSystemTheme
    updateTheme()
  }

  const themePresets = {
    'corporate': { theme: 'blue' as AppSettings.ThemeType, mode: 'light' as AppSettings.ColorMode },
    'nature': { theme: 'green' as AppSettings.ThemeType, mode: 'light' as AppSettings.ColorMode },
    'energy': { theme: 'red' as AppSettings.ThemeType, mode: 'light' as AppSettings.ColorMode },
    'elegant': { theme: 'purple' as AppSettings.ThemeType, mode: 'light' as AppSettings.ColorMode },
    'sunshine': { theme: 'yellow' as AppSettings.ThemeType, mode: 'light' as AppSettings.ColorMode },
    'night': { theme: 'blue' as AppSettings.ThemeType, mode: 'dark' as AppSettings.ColorMode },
    'forest': { theme: 'green' as AppSettings.ThemeType, mode: 'dark' as AppSettings.ColorMode },
    'passion': { theme: 'red' as AppSettings.ThemeType, mode: 'dark' as AppSettings.ColorMode },
    'twilight': { theme: 'purple' as AppSettings.ThemeType, mode: 'dark' as AppSettings.ColorMode },
    'desert': { theme: 'yellow' as AppSettings.ThemeType, mode: 'dark' as AppSettings.ColorMode }
  }

  const applyThemePreset = (presetName: keyof typeof themePresets) => {
    const preset = themePresets[presetName]
    if (preset) {
      settings.value.theme.followSystemTheme = false
      settings.value.theme.themeType = preset.theme
      settings.value.theme.colorScheme = preset.mode
      updateTheme()
    }
  }

  const setBorderRadius = (radius: AppSettings.Theme['borderRadius']) => {
    settings.value.theme.borderRadius = radius
    updateTheme()
  }

  prefersColorScheme.addEventListener('change', (e) => {
    systemColorScheme.value = e.matches ? 'dark' : 'light'
    if (settings.value.theme.followSystemTheme) {
      updateTheme()
    }
  })

  watch(
    () => [
      settings.value.theme.colorScheme,
      settings.value.theme.themeType,
      settings.value.theme.followSystemTheme,
      systemColorScheme.value
    ],
    updateTheme,
    { immediate: true }
  )

  watch(
    () => settings.value,
    (newSettings) => {
      setStorage('settings', newSettings)
    },
    { deep: true }
  )

  return {
    settings,
    currentColorScheme,
    systemColorScheme,
    effectiveColorMode,
    title,
    setTitle,
    setSidebarMode,
    setSidebarFixed,
    toggleSidebarCollapse,
    setThemeType,
    setColorMode,
    toggleDarkMode,
    toggleFollowSystemTheme,
    themePresets,
    applyThemePreset,
    updateTheme,
    setBorderRadius,
    radiusPresets,
  }
})
