import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import ComApi from '@/api/common'
interface Area {
  areaCode: string
  areaId: number
  areaName: string
  [key: string]: any
}

export const useCommonStroe = defineStore('commonStroe', () => {
  const areaData = ref<Array<Area>>([])
  const areaList = computed(() => areaData.value)
  const getAreaList = async () => {
    try {
      areaData.value = await ComApi.getAreaList().then((res) => res.data)
    } catch (error) {
      return []
    }
  }

  return { getAreaList, areaList}
})
