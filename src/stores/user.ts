import type { Application, Func, SystemApp } from '@/interfaces'
import type { RouteRecordRaw, RouterMatcher, RouteMeta } from 'vue-router'
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { createRouterMatcher, RouterView } from 'vue-router'
import uniqueId from 'lodash/uniqueId'

import AuthApi from '@/api/auth'
import { Bool, MenuType } from '@/utils/enums'
import { createLogger } from '@/utils/logger'
import { cacheService } from '@/utils/cache'
import { clearStorage } from '@/utils/storage'
import {
  getStorageAccount,
  getStorageToken,
  setStorageAccount,
  setStorageToken,
} from '@/utils/auth'
import { normalizePath, pathJoin } from '@/utils/utils'
import { loadView } from '@/utils/viewLoader'

interface LoginCredentials {
  accountNumber: string
  accountPassword: string
  verifyCode?: string
  uniqueIdentifier?: string
  loginType: string
}

export interface UserState {
  token: string | null
  account: any | null
  applications: SystemApp[] | null
  routes: RouteRecordRaw[] | null
  permissions: Func[] | null
  isAuthenticated: boolean
  routeMatcher: RouterMatcher | null
}

const logger = createLogger('UserStore')

function parseRoutes(
  prefix = '',
  nodes: Func[],
  permissions: Func[] = [],
  parent?: { path: string },
  level = 0,
): RouteRecordRaw[] {
  return nodes
    .filter((node) => {
      const isButton = node.functionType === MenuType.Btn
      if (isButton) {
        permissions.push(node)
        return false
      }
      return !!node.functionRoutePath
    })
    .map((node) => {
      const route = buildRouteFromNode(node, prefix, parent, level)

      if (node.children?.length) {
        const childRoutes = parseRoutes(prefix, node.children, permissions, route, level + 1)
        if (childRoutes.length) {
          route.children = childRoutes
        }
      }

      return route
    })
}

function buildRouteFromNode(node: Func, prefix: string, parent?: any, level = 0): RouteRecordRaw {
  const {
    functionId,
    functionType,
    functionShowStatus,
    functionIcon,
    functionName,
    functionLayout,
    functionRoutePath,
    functionApplication,
    functionComponentPath,
    functionRouteParameter,
    meta: extraInfo,
  } = node
  const isDirectory = functionType === MenuType.Dir
  const isVisible = functionShowStatus === Bool.TrueString
  const normalizedPath = normalizePath(prefix, functionRoutePath, level === 0)
  const fullPath = parent ? pathJoin(parent.meta.fullpath, normalizedPath) : normalizedPath

  const meta: RouteMeta = {
    index: fullPath,
    functionId,
    title: functionName,
    fullpath: fullPath,
    hidden: !isVisible,
    layout: functionLayout || 'default',
    application: functionApplication,
    extra: parseJSON(extraInfo, {}),
    params: parseJSON(functionRouteParameter, {}),
    ...(functionIcon && { icon: functionIcon }),
  }

  return {
    path: normalizedPath,
    name: `${functionName}_${uniqueId()}`,
    component: isDirectory ? RouterView : loadView(functionComponentPath),
    meta,
  }
}

function findFirstMenuPath(nodes: Func[]): string {
  let target = ''

  function traverse(nodes: Func[], parentPath = '') {
    for (const node of nodes) {
      if (target) break

      const isMenu = node.functionType === MenuType.Menu
      if (isMenu && node.functionRoutePath) {
        target = pathJoin(parentPath, node.functionRoutePath)
        break
      }

      if (node.children?.length) {
        traverse(node.children, node.functionRoutePath)
      }
    }
  }

  traverse(nodes)
  return target
}

function parseJSON(jsonString: string, defaultValue: any) {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    logger.error('JSON parse error:', error)
    return defaultValue
  }
}

export const useUserStore = defineStore('user', () => {
  const token = ref<string | null>(getStorageToken())
  const account = ref<any | null>(getStorageAccount())
  const applications = ref<SystemApp[] | null>(null)
  const routes = ref<RouteRecordRaw[] | null>(null)
  const permissions = ref<Func[] | null>(null)
  const isAuthenticated = ref(false)
  const routeMatcher = ref<RouterMatcher | null>(null)

  const hasPermissions = computed(() => !!permissions.value?.length)
  const userApplications = computed(() => applications.value || [])

  const clearUserState = () => {
    cacheService.clear()
    clearStorage()
    token.value = null
    account.value = null
    applications.value = null
    routes.value = null
    permissions.value = null
    isAuthenticated.value = false
    routeMatcher.value = null
  }

  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await AuthApi.login(credentials)
      clearUserState()

      const userData = response.data
      account.value = userData
      token.value = userData.token

      setStorageToken(userData.token)
      setStorageAccount(userData)

      return userData
    } catch (error) {
      logger.error('Login failed:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      await AuthApi.logout()
    } catch (error) {
      logger.error('Logout failed:', error)
    } finally {
      clearUserState()
    }
  }

  const fetchAuthorizationInfo = async () => {
    try {
      const [authInfo, permTree] = await Promise.all([
        AuthApi.getAuthorizationInfo(),
        // AuthApi.getPermissionTree(),
        Promise.resolve({ data: [] }),
      ])

      const parsedRoutes: RouteRecordRaw[] = []
      const parsedPermissions: Func[] = []
      const userApps = processApplicationData(
        authInfo.data,
        permTree.data,
        parsedRoutes,
        parsedPermissions,
      )

      applications.value = userApps
      routes.value = parsedRoutes
      permissions.value = parsedPermissions
      routeMatcher.value = createRouterMatcher(parsedRoutes, {})
      isAuthenticated.value = true

      return {
        applications: userApps,
        routes: parsedRoutes,
        permissions: parsedPermissions,
      }
    } catch (error) {
      logger.error('Failed to fetch authorization info:', error)
      throw error
    }
  }

  const processApplicationData = (
    authApps: Application[],
    permApps: Application[],
    routes: RouteRecordRaw[],
    permissions: Func[],
  ): SystemApp[] => {
    const processApps = (apps: Application[], isPersonal = false) =>
      apps
        .map((app) => {
          if (app.functionList) {
            const menu = findFirstMenuPath(app.functionList)
            logger.warn(menu, app)
            routes.push(...parseRoutes(app.applicationCode, app.functionList, permissions))

            return {
              id: app.applicationId,
              code: app.applicationCode,
              name: app.applicationName,
              englishName: app.applicationEnglishName,
              icon: app.applicationIcon,
              activeIcon: app.applicationIconActive,
              homePage: pathJoin('/', app.applicationCode, menu),
              personal: isPersonal,
            }
          }
          return null
        })
        .filter((app): app is SystemApp => app !== null)

    return [...processApps(authApps), ...processApps(permApps, true)]
  }

  const hasPermission = (permissionCode: string): boolean => {
    return permissions.value?.some((p) => p.functionPermissionCode === permissionCode) ?? false
  }

  return {
    token,
    account,
    applications,
    routes,
    permissions,
    isAuthenticated,
    routeMatcher,

    hasPermissions,
    userApplications,

    login,
    logout,
    hasPermission,
    fetchAuthorizationInfo,
    clearUserState,
  }
})
