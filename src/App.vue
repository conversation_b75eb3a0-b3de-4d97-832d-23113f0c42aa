<script setup lang="ts">
import { computed, watch, defineAsyncComponent } from 'vue';
import { useSettingsStore } from './stores/settings';
import { ElementUI } from './ui/providers';
import { useAppStore } from './stores/app';

const layoutMap = {
  default: defineAsyncComponent(() => import('@/layouts/default/index.vue')),
  blank: defineAsyncComponent(() => import('@/layouts/blank/index.vue')),
};

const settingsStore = useSettingsStore()
watch([
  () => settingsStore.settings.theme.dynamicTitle,
  () => settingsStore.title,
], ([dynamicTitle, title]) => {
  const appTitle = import.meta.env.VITE_APP_TITLE
  if (dynamicTitle && title) {
    document.title = typeof title === 'function' ? title() : title + `- ${appTitle}`
  } else {
    document.title = appTitle
  }
}, { immediate: true })

const appStore = useAppStore();
const currentLayout = computed(() => layoutMap[appStore.currentLayout]);
</script>

<template>
  <ElementUI.Provider>
    <component :is="currentLayout">
      <RouterView />
    </component>
  </ElementUI.Provider>
</template>
