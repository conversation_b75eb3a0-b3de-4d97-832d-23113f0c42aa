import { getStorage, setStorage, removeStorage } from '@/utils/storage'
import { DYNAMIC_ROUTES_KEY } from '@/utils/conts'

const ROUTES_BUILD_TIME_KEY = 'routes_build_time'

export function checkAndCleanupOnBuildTimeChange() {
  const savedBuildTime = getStorage<string>(ROUTES_BUILD_TIME_KEY)
  const currentBuildTime = __SYSTEM_INFO__.lastBuildTime

  if (savedBuildTime !== currentBuildTime) {
    removeStorage(DYNAMIC_ROUTES_KEY)
    setStorage(ROUTES_BUILD_TIME_KEY, currentBuildTime)
    return true
  }

  return false
}
