import dayjs from 'dayjs'

type LogLevel = 'log' | 'info' | 'warn' | 'error'

const LOG_LEVELS: Record<LogLevel, number> = {
  log: 0,
  info: 1,
  warn: 2,
  error: 3,
}

class Logger {
  private prefix: string
  private enabled: boolean
  private level: LogLevel

  private styles = {
    log: 'color: green; font-weight: bold;',
    info: 'color: blue; font-weight: bold;',
    warn: 'color: orange; font-weight: bold;',
    error: 'color: red; font-weight: bold;',
  }

  constructor(prefix = 'APP', options: { enabled?: boolean; level?: LogLevel } = {}) {
    this.prefix = prefix
    this.enabled = options.enabled ?? import.meta.env.VITE_LOG_ENABLED
    this.level = options.level ?? (import.meta.env.VITE_LOG_LEVEL as LogLevel)
  }

  private shouldLog(level: LogLevel): boolean {
    return this.enabled && LOG_LEVELS[level] >= LOG_LEVELS[this.level]
  }

  private formatMessage(level: LogLevel, ...args: any[]) {
    const timestamp = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
    const message = `[${timestamp}] [${this.prefix}] ${level.toUpperCase()}:`
    return [`%c${message}`, this.styles[level], ...args]
  }

  setLevel(level: LogLevel) {
    this.level = level
  }

  setEnabled(enabled: boolean) {
    this.enabled = enabled
  }

  log(...args: any[]) {
    if (this.shouldLog('log')) {
      console.log(...this.formatMessage('log', ...args))
    }
  }

  info(...args: any[]) {
    if (this.shouldLog('info')) {
      console.info(...this.formatMessage('info', ...args))
    }
  }

  warn(...args: any[]) {
    if (this.shouldLog('warn')) {
      console.warn(...this.formatMessage('warn', ...args))
    }
  }

  error(...args: any[]) {
    if (this.shouldLog('error')) {
      console.error(...this.formatMessage('error', ...args))
    }
  }
}

export const logger = new Logger('App')

export function createLogger(prefix: string, options?: { enabled?: boolean; level?: LogLevel }) {
  return new Logger(prefix, options)
}

export type { LogLevel }
