export interface CacheOptions {
  ttl?: number;
  strategy?: 'memory' | 'local' | 'session' | 'none';
  size?: number;
  cacheKey?: string;
}

export interface CacheItem<T> {
  value: T;
  timestamp: number;
  ttl: number;
}

class CacheService {
  private memoryCache: Map<string, CacheItem<any>> = new Map();
  private maxSize: number = 100;

  constructor(options?: { maxSize?: number }) {
    if (options?.maxSize) {
      this.maxSize = options.maxSize;
    }
  }

  set<T>(key: string, value: T, options: CacheOptions = {}): void {
    const { ttl = 3600, strategy = 'memory' } = options;
    const timestamp = Date.now();
    const cacheItem: CacheItem<T> = { value, timestamp, ttl };

    switch (strategy) {
      case 'local':
        try {
          localStorage.setItem(
            `cache_${key}`,
            JSON.stringify(cacheItem)
          );
        } catch (error) {
          console.error('Failed to store in localStorage:', error);
        }
        break;
      case 'session':
        try {
          sessionStorage.setItem(
            `cache_${key}`,
            JSON.stringify(cacheItem)
          );
        } catch (error) {
          console.error('Failed to store in sessionStorage:', error);
        }
        break;
      case 'memory':
        if (this.memoryCache.size >= this.maxSize) {
          const oldestKey = this.getOldestCacheKey();
          if (oldestKey) {
            this.memoryCache.delete(oldestKey);
          }
        }
        this.memoryCache.set(key, cacheItem);
        break;
      case 'none':
      default:
        break;
    }
  }

  get<T>(key: string, options: CacheOptions = {}): T | null {
    const { strategy = 'memory' } = options;
    let cacheItem: CacheItem<T> | null = null;

    switch (strategy) {
      case 'local':
        try {
          const item = localStorage.getItem(`cache_${key}`);
          if (item) {
            cacheItem = JSON.parse(item) as CacheItem<T>;
          }
        } catch (error) {
          console.error('Failed to retrieve from localStorage:', error);
        }
        break;
      case 'session':
        try {
          const item = sessionStorage.getItem(`cache_${key}`);
          if (item) {
            cacheItem = JSON.parse(item) as CacheItem<T>;
          }
        } catch (error) {
          console.error('Failed to retrieve from sessionStorage:', error);
        }
        break;
      case 'memory':
        if (this.memoryCache.has(key)) {
          cacheItem = this.memoryCache.get(key) as CacheItem<T>;
        }
        break;
      case 'none':
      default:
        return null;
    }

    if (cacheItem) {
      const now = Date.now();
      const expiresAt = cacheItem.timestamp + cacheItem.ttl * 1000;

      if (now < expiresAt) {
        return cacheItem.value;
      } else {
        this.remove(key, { strategy });
      }
    }

    return null;
  }

  remove(key: string, options: CacheOptions = {}): void {
    const { strategy = 'memory' } = options;

    switch (strategy) {
      case 'local':
        try {
          localStorage.removeItem(`cache_${key}`);
        } catch (error) {
          console.error('Failed to remove from localStorage:', error);
        }
        break;
      case 'session':
        try {
          sessionStorage.removeItem(`cache_${key}`);
        } catch (error) {
          console.error('Failed to remove from sessionStorage:', error);
        }
        break;
      case 'memory':
        this.memoryCache.delete(key);
        break;
      case 'none':
      default:
        break;
    }
  }

  clear(options: CacheOptions = {}): void {
    const { strategy = 'memory' } = options;

    switch (strategy) {
      case 'local':
        try {
          Object.keys(localStorage)
            .filter(key => key.startsWith('cache_'))
            .forEach(key => localStorage.removeItem(key));
        } catch (error) {
          console.error('Failed to clear localStorage:', error);
        }
        break;
      case 'session':
        try {
          Object.keys(sessionStorage)
            .filter(key => key.startsWith('cache_'))
            .forEach(key => sessionStorage.removeItem(key));
        } catch (error) {
          console.error('Failed to clear sessionStorage:', error);
        }
        break;
      case 'memory':
        this.memoryCache.clear();
        break;
      case 'none':
        break
      default:
        this.clear({ strategy: 'memory' });
        this.clear({ strategy: 'local' });
        this.clear({ strategy: 'session' });
        break;
    }
  }

  getStatus(key: string, options: CacheOptions = {}): { exists: boolean; expired: boolean; expiresIn?: number } {
    const { strategy = 'memory' } = options;
    let cacheItem: CacheItem<any> | null = null;

    switch (strategy) {
      case 'local':
        try {
          const item = localStorage.getItem(`cache_${key}`);
          if (item) {
            cacheItem = JSON.parse(item) as CacheItem<any>;
          }
        } catch (error) {
          console.error('Failed to retrieve from localStorage:', error);
        }
        break;
      case 'session':
        try {
          const item = sessionStorage.getItem(`cache_${key}`);
          if (item) {
            cacheItem = JSON.parse(item) as CacheItem<any>;
          }
        } catch (error) {
          console.error('Failed to retrieve from sessionStorage:', error);
        }
        break;
      case 'memory':
        if (this.memoryCache.has(key)) {
          cacheItem = this.memoryCache.get(key) as CacheItem<any>;
        }
        break;
      case 'none':
      default:
        return { exists: false, expired: true };
    }

    if (!cacheItem) {
      return { exists: false, expired: true };
    }

    const now = Date.now();
    const expiresAt = cacheItem.timestamp + cacheItem.ttl * 1000;
    const expired = now >= expiresAt;
    const expiresIn = expired ? 0 : Math.floor((expiresAt - now) / 1000);

    return {
      exists: true,
      expired,
      expiresIn
    };
  }

  private getOldestCacheKey(): string | null {
    if (this.memoryCache.size === 0) {
      return null;
    }

    let oldestKey: string | null = null;
    let oldestTimestamp = Infinity;

    for (const [key, item] of this.memoryCache.entries()) {
      if (item.timestamp < oldestTimestamp) {
        oldestTimestamp = item.timestamp;
        oldestKey = key;
      }
    }

    return oldestKey;
  }
}

export const cacheService = new CacheService({ maxSize: 200 });

export class AsyncDataCache {
  private promiseCache: Map<string, Promise<any>> = new Map();

  constructor(private cacheService: CacheService) { }

  async getOrFetch<T>(
    key: string,
    fetchFn: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const forceRefresh = options.strategy === 'none';
    if (!forceRefresh) {
      const cachedData = this.cacheService.get<T>(key, options);
      if (cachedData !== null) {
        return cachedData;
      }
      if (this.promiseCache.has(key)) {
        return this.promiseCache.get(key);
      }
    }

    const promise = fetchFn().then(data => {
      if (options.strategy !== 'none') {
        this.cacheService.set(key, data, options);
      }
      this.promiseCache.delete(key);
      return data;
    }).catch(error => {
      this.promiseCache.delete(key);
      throw error;
    });

    this.promiseCache.set(key, promise);

    return promise;
  }

  clearCache(key: string, options: CacheOptions = {}): void {
    this.cacheService.remove(key, options);
    this.promiseCache.delete(key);
  }

  clearAllCache(options: CacheOptions = {}): void {
    this.cacheService.clear(options);
    this.promiseCache.clear();
  }
}

export const asyncDataCache = new AsyncDataCache(cacheService);
