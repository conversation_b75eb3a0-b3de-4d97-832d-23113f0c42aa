
import type { ClassValue } from 'clsx'
import { clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

function pathJoin(...args: unknown[]) {
  return args.filter(Boolean).join('/').replace(/\/+/g, '/')
}

function getAssetUrl(path: string): string {
  if (!path) return ''

  if (/^(https?:)?\/\//.test(path)) {
    return path
  }

  const cleanPath = path.replace(/^\/+/, '')

  const baseUrl = import.meta.env.VITE_STATIC_ASSETS_URL || ''

  return `${baseUrl.replace(/\/+$/, '')}/${cleanPath}`
}

function getAssetUrls(paths: string[]): string[] {
  return paths.map(getAssetUrl)
}

function normalizePath(prefix: string, path: string, isRoot: boolean) {
  if (!isRoot && path.startsWith('/')) {
    path = path.slice(1)
  }

  return isRoot ? pathJoin(prefix ? '/' + prefix : '/', path) : path
}

function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

const generateCacheKey = (config: Record<string, any>): string => {
  const sortedParams = Object.keys(config)
    .sort()
    .map((key) => `${key}=${JSON.stringify(config[key])}`)
    .join('&');
  return sortedParams;
};

export {
  cn,
  pathJoin,
  normalizePath,
  generateCacheKey,
  getAssetUrl,
  getAssetUrls,
}
