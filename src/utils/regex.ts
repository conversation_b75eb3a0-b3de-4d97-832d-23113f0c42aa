export class RegexValidator {
  static readonly patterns = {
    lowercase: /^[a-z]+$/,

    mobile: /^1[3-9]\d{9}$/,

    email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,

    url: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,

    ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,

    idCard: /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/,

    chinese: /^[\u4e00-\u9fa5]+$/,

    number: /^[0-9]+$/,

    integer: /^-?[0-9]+$/,

    positiveInteger: /^[1-9]\d*$/,

    alphanumeric: /^[a-zA-Z0-9]+$/
  } as const

  static test(value: string, pattern: keyof typeof RegexValidator.patterns | RegExp): boolean {
    if (!value) return false

    const regex = pattern instanceof RegExp
      ? pattern
      : this.patterns[pattern]

    return regex.test(value)
  }

  /**
   * 验证字符串长度是否在指定范围内
   * @param value 要验证的字符串
   * @param min 最小长度
   * @param max 最大长度
   * @returns 是否符合长度要求
   */
  static length(value: string, min: number, max: number): boolean {
    if (!value) return false
    const len = value.length
    return len >= min && len <= max
  }

  /**
   * 验证字符串是否符合指定的模式并且长度在指定范围内
   * @param value 要验证的字符串
   * @param pattern 正则表达式模式名称或正则表达式对象
   * @param minLength 最小长度（可选）
   * @param maxLength 最大长度（可选）
   * @returns 是否符合验证条件
   */
  static testWithLength(
    value: string,
    pattern: keyof typeof RegexValidator.patterns | RegExp,
    minLength?: number,
    maxLength?: number
  ): boolean {
    // 首先验证格式
    const isValidFormat = this.test(value, pattern)
    if (!isValidFormat) return false

    // 如果没有指定长度限制，则只验证格式
    if (minLength === undefined && maxLength === undefined) {
      return true
    }

    const length = value.length

    // 如果只指定了最小长度
    if (minLength !== undefined && maxLength === undefined) {
      return length >= minLength
    }

    // 如果只指定了最大长度
    if (minLength === undefined && maxLength !== undefined) {
      return length <= maxLength
    }

    // 如果同时指定了最小和最大长度
    return length >= (minLength as number) && length <= (maxLength as number)
  }

  static getPattern(pattern: keyof typeof RegexValidator.patterns): RegExp {
    return this.patterns[pattern]
  }
}

/**
 * 验证字符串是否只包含小写字母
 * @param value 要验证的字符串
 * @returns 是否符合验证条件
 */
export const isLowercase = (value: string) => RegexValidator.test(value, 'lowercase')

/**
 * 验证字符串是否为有效的电子邮件地址
 * @param value 要验证的字符串
 * @returns 是否符合验证条件
 */
export const isEmail = (value: string) => RegexValidator.test(value, 'email')

/**
 * 验证字符串是否为有效的手机号码
 * @param value 要验证的字符串
 * @returns 是否符合验证条件
 */
export const isMobile = (value: string) => RegexValidator.test(value, 'mobile')

/**
 * 验证字符串是否为有效的URL
 * @param value 要验证的字符串
 * @returns 是否符合验证条件
 */
export const isUrl = (value: string) => RegexValidator.test(value, 'url')

/**
 * 验证字符串是否只包含中文字符
 * @param value 要验证的字符串
 * @returns 是否符合验证条件
 */
export const isChinese = (value: string) => RegexValidator.test(value, 'chinese')

/**
 * 验证字符串是否只包含数字
 * @param value 要验证的字符串
 * @returns 是否符合验证条件
 */
export const isNumber = (value: string) => RegexValidator.test(value, 'number')

/**
 * 验证字符串是否为整数
 * @param value 要验证的字符串
 * @returns 是否符合验证条件
 */
export const isInteger = (value: string) => RegexValidator.test(value, 'integer')

/**
 * 验证字符串是否为正整数
 * @param value 要验证的字符串
 * @returns 是否符合验证条件
 */
export const isPositiveInteger = (value: string) => RegexValidator.test(value, 'positiveInteger')

/**
 * 验证字符串是否只包含字母和数字
 * @param value 要验证的字符串
 * @returns 是否符合验证条件
 */
export const isAlphanumeric = (value: string) => RegexValidator.test(value, 'alphanumeric')

// 为其他常用模式添加带长度验证的便捷函数
/**
 * 验证字符串是否为有效的电子邮件地址，并且长度在指定范围内
 * @param value 要验证的字符串
 * @param minLength 最小长度（可选）
 * @param maxLength 最大长度（可选）
 * @returns 是否符合验证条件
 */
export const isEmailWithLength = (value: string, minLength?: number, maxLength?: number) =>
  RegexValidator.testWithLength(value, 'email', minLength, maxLength)

/**
 * 验证字符串是否为有效的手机号码，并且长度在指定范围内
 * @param value 要验证的字符串
 * @param minLength 最小长度（可选）
 * @param maxLength 最大长度（可选）
 * @returns 是否符合验证条件
 */
export const isMobileWithLength = (value: string, minLength?: number, maxLength?: number) =>
  RegexValidator.testWithLength(value, 'mobile', minLength, maxLength)

/**
 * 验证字符串是否为有效的URL，并且长度在指定范围内
 * @param value 要验证的字符串
 * @param minLength 最小长度（可选）
 * @param maxLength 最大长度（可选）
 * @returns 是否符合验证条件
 */
export const isUrlWithLength = (value: string, minLength?: number, maxLength?: number) =>
  RegexValidator.testWithLength(value, 'url', minLength, maxLength)

/**
 * 验证字符串是否只包含中文字符，并且长度在指定范围内
 * @param value 要验证的字符串
 * @param minLength 最小长度（可选）
 * @param maxLength 最大长度（可选）
 * @returns 是否符合验证条件
 *
 * @example
 * // 只验证是否为中文
 * isChineseWithLength('你好世界') // true
 *
 * // 验证是否为中文且长度至少为3
 * isChineseWithLength('你好世界', 3) // true
 * isChineseWithLength('你好', 3) // false
 *
 * // 验证是否为中文且长度不超过5
 * isChineseWithLength('你好世界', undefined, 5) // true
 * isChineseWithLength('你好世界你好世界', undefined, 5) // false
 *
 * // 验证是否为中文且长度在2到6之间
 * isChineseWithLength('你好世界', 2, 6) // true
 * isChineseWithLength('你', 2, 6) // false
 * isChineseWithLength('你好世界你好世界', 2, 6) // false
 */
export const isChineseWithLength = (value: string, minLength?: number, maxLength?: number) =>
  RegexValidator.testWithLength(value, 'chinese', minLength, maxLength)

/**
 * 验证字符串是否只包含数字，并且长度在指定范围内
 * @param value 要验证的字符串
 * @param minLength 最小长度（可选）
 * @param maxLength 最大长度（可选）
 * @returns 是否符合验证条件
 */
export const isNumberWithLength = (value: string, minLength?: number, maxLength?: number) =>
  RegexValidator.testWithLength(value, 'number', minLength, maxLength)

/**
 * 验证字符串是否为整数，并且长度在指定范围内
 * @param value 要验证的字符串
 * @param minLength 最小长度（可选）
 * @param maxLength 最大长度（可选）
 * @returns 是否符合验证条件
 */
export const isIntegerWithLength = (value: string, minLength?: number, maxLength?: number) =>
  RegexValidator.testWithLength(value, 'integer', minLength, maxLength)

/**
 * 验证字符串是否为正整数，并且长度在指定范围内
 * @param value 要验证的字符串
 * @param minLength 最小长度（可选）
 * @param maxLength 最大长度（可选）
 * @returns 是否符合验证条件
 */
export const isPositiveIntegerWithLength = (value: string, minLength?: number, maxLength?: number) =>
  RegexValidator.testWithLength(value, 'positiveInteger', minLength, maxLength)

/**
 * 验证字符串是否只包含字母和数字，并且长度在指定范围内
 * @param value 要验证的字符串
 * @param minLength 最小长度（可选）
 * @param maxLength 最大长度（可选）
 * @returns 是否符合验证条件
 *
 * @example
 * // 只验证是否为字母数字
 * isAlphanumericWithLength('abc123') // true
 *
 * // 验证是否为字母数字且长度至少为6
 * isAlphanumericWithLength('abc123', 6) // true
 * isAlphanumericWithLength('abc12', 6) // false
 *
 * // 验证是否为字母数字且长度不超过8
 * isAlphanumericWithLength('abc123', undefined, 8) // true
 * isAlphanumericWithLength('abc12345678', undefined, 8) // false
 *
 * // 验证是否为字母数字且长度在6到12之间
 * isAlphanumericWithLength('abc123', 6, 12) // true
 * isAlphanumericWithLength('abc12', 6, 12) // false
 * isAlphanumericWithLength('abc12345678901', 6, 12) // false
 */
export const isAlphanumericWithLength = (value: string, minLength?: number, maxLength?: number): boolean => {
  return RegexValidator.testWithLength(value, 'alphanumeric', minLength, maxLength)
}
