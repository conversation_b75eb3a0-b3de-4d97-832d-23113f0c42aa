import type { Account } from "@/interfaces";
import { getStorage, setStorage } from "./storage";
import { KEY_ACCOUNT, KEY_TOKEN } from "./conts";

export function getAuthHeaders() {
  return {
    [KEY_TOKEN]: getStorageToken() ?? ''
  }
}

export function getStorageToken(defaultVal = '') {
  return getStorage(KEY_TOKEN, defaultVal)
}

export function setStorageToken(val: string) {
  return setStorage(KEY_TOKEN, val)
}

export function setStorageAccount(val: Account) {
  return setStorage(KEY_ACCOUNT, val)
}

export function getStorageAccount() {
  return getStorage<Account>(KEY_ACCOUNT)
}
