import { h } from 'vue'
import { ElTag } from 'element-plus'
import type { TagProps } from 'element-plus'
import { Bool, Status, Platform, Visibility, MenuType } from '@/utils/enums'

export class BooleanUtils {
  static isTrue(value: any): boolean {
    if (typeof value === 'boolean') return value
    if (typeof value === 'number') return value === Bool.True
    if (typeof value === 'string') return value === '1' || value === 'true'
    return false
  }

  static normalize(value: any): Bool {
    return this.isTrue(value) ? Bool.True : Bool.False
  }
}

export class YesNoOptions {
  static getOptions(valueType: 'number' | 'boolean' | 'string' = 'number'): Option<number | boolean | string>[] {
    const valueMap = {
      number: { yes: Bool.True, no: Bool.False },
      boolean: { yes: true, no: false },
      string: { yes: Bool.TrueString, no: Bool.FalseString }
    }
    const values = valueMap[valueType]

    return [
      { label: '是', value: values.yes },
      { label: '否', value: values.no }
    ]
  }

  static getLabel(value: any): string {
    return BooleanUtils.isTrue(value) ? '是' : '否'
  }

  static getTagType(value: any): TagProps['type'] {
    return BooleanUtils.isTrue(value) ? 'success' : 'danger'
  }
}

export type PresetOptionKey = 'status' | 'platform' | 'menuType' | 'yesNo' | 'visibility' | 'layout'
export type DynamicOptionKey = string & {}
export type OptionKey = PresetOptionKey | DynamicOptionKey

export interface Option<T = string | number> {
  label: string
  value: T
  [key: string]: any
}

export interface TagConfig {
  type?: TagProps['type']
  effect?: 'plain' | 'light' | 'dark'
  size?: 'large' | 'default' | 'small'
}

export interface OptionsConfig<T = string | number> {
  options: Option<T>[]
  tagMap?: Record<string, TagProps['type']>
  defaultType?: TagProps['type']
  tagConfig?: TagConfig
}

export type LayoutKey = 'default' | 'blank'

export class OptionUtils {
  private static optionsMap = new Map<OptionKey, OptionsConfig>([
    ['status', {
      options: [
        { label: '启用', value: Status.Enabled },
        { label: '禁用', value: Status.Disabled },
      ],
      tagMap: {
        [Status.Enabled]: 'success',
        [Status.Disabled]: 'danger',
      },
      tagConfig: { effect: 'light', size: 'small' }
    }],
    ['platform', {
      options: [
        { label: '后台管理', value: Platform.Manage },
        { label: '移动端', value: Platform.Mobile },
      ],
      tagMap: {
        [Platform.Manage]: 'primary',
        [Platform.Mobile]: 'success',
      },
      tagConfig: { effect: 'light', size: 'small' }
    }],
    ['menuType', {
      options: [
        { label: '目录', value: MenuType.Dir },
        { label: '菜单', value: MenuType.Menu },
        { label: '按钮', value: MenuType.Btn },
        { label: '单页', value: MenuType.Page },
      ],
      tagMap: {
        [MenuType.Dir]: 'primary',
        [MenuType.Menu]: 'success',
        [MenuType.Btn]: 'warning',
        [MenuType.Page]: 'danger',
      },
      tagConfig: { effect: 'light', size: 'small' }
    }],
    ['visibility', {
      options: [
        { label: '显示', value: Visibility.Show },
        { label: '隐藏', value: Visibility.Hide },
      ],
      tagMap: {
        [Visibility.Show]: 'success',
        [Visibility.Hide]: 'info',
      },
      tagConfig: { effect: 'light', size: 'small' }
    }],
    ['layout', {
      options: [
        { label: '默认布局', value: 'default' },
        { label: '空白布局', value: 'blank' },
      ],
      tagMap: {
        default: 'primary',
        blank: 'info',
      },
      tagConfig: { effect: 'light', size: 'small' }
    }]
  ])

  static getOptions(key: Exclude<OptionKey, 'yesNo'>): Option[]
  static getOptions(key: 'yesNo', valueType: 'number'): Option<number>[]
  static getOptions(key: 'yesNo', valueType: 'boolean'): Option<boolean>[]
  static getOptions(key: 'yesNo', valueType: 'string'): Option<string>[]
  static getOptions(key: OptionKey, valueType?: 'number' | 'boolean' | 'string'): Option<any>[] {
    if (key === 'yesNo') {
      return YesNoOptions.getOptions(valueType || 'number')
    }
    return this.optionsMap.get(key)?.options || []
  }

  static getLabel(key: OptionKey, value: any): string {
    if (key === 'yesNo') {
      return YesNoOptions.getLabel(value)
    }
    const config = this.optionsMap.get(key)
    return config?.options.find(item => item.value === value)?.label || '未知'
  }

  static renderTag(
    value: any,
    key: OptionKey,
    customConfig?: Partial<TagConfig>
  ) {
    if (key === 'yesNo') {
      return h(ElTag, {
        type: YesNoOptions.getTagType(value),
        effect: 'light',
        size: 'small',
        ...customConfig,
      }, () => YesNoOptions.getLabel(value))
    }

    const config = this.optionsMap.get(key)
    if (!config) return null

    const label = this.getLabel(key, value)
    const type = config.tagMap?.[value] || config.defaultType || 'info'

    return h(ElTag, {
      type,
      ...config.tagConfig,
      ...customConfig,
    }, () => label)
  }
}

export const { status, platform, menuType, visibility, layout } = Object.fromEntries(
  Array.from(OptionUtils['optionsMap'].entries())
    .map(([key, { options }]) => [key, options])
)

export class NumberChineseConverter {
  private static mappings: Array<{ num: number; char: string }> = [
    { num: 1, char: '一' },
    { num: 2, char: '二' },
    { num: 3, char: '三' }
  ];

  static toChinese(num: number): string {
    const mapping = this.mappings.find(m => m.num === num)
    return mapping ? mapping.char : num.toString()
  }

  static toNumber(char: string): number | undefined {
    const mapping = this.mappings.find(m => m.char === char)
    return mapping?.num
  }
}
