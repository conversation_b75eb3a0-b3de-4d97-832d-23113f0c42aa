import { defineAsyncComponent } from 'vue'
import { createLogger } from './logger'

const logger = createLogger('ViewLoader')
const viewModules = import.meta.glob('../views/**/*.vue', { eager: false })

/**
 * 加载视图组件
 * @param viewPath 视图路径（相对于 views 目录）
 * @returns 异步组件
 */
export function loadView(viewPath: string) {
  if (viewPath.startsWith('/')) {
    viewPath = viewPath.slice(1)
  }
  const modulePath = `../views/${viewPath}.vue`

  if (!viewModules[modulePath]) {
    logger.error(`View "${viewPath}" not found in views directory`)
    return defineAsyncComponent(() =>
      import('../views/errors/ViewLoadError.vue').then(comp => ({
        ...comp.default,
        props: {
          viewPath,
          error: `视图 "${viewPath}" 不存在`
        }
      }))
    )
  }

  return defineAsyncComponent({
    loader: () => viewModules[modulePath]() as any,
    errorComponent: defineAsyncComponent(() =>
      import('../views/errors/ViewLoadError.vue')
    )
  })
}
