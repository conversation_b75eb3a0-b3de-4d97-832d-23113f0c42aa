import axios, { type AxiosInstance, type AxiosError, type AxiosRequestConfig } from 'axios'
import { getStorageToken } from './auth'
import { KEY_TOKEN } from './conts'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createLogger } from './logger'
import { clearStorage } from './storage'
import router from '@/router'
import { LoginRoute } from '@/router/routes'

const logger = createLogger('req')

export const abortControllers = new Map<string, AbortController>()

let isShowingError = false
let errorQueue: string[] = []
let debounceTimer: number | null = null
let isShowingTokenExpiredDialog = false

interface CustomAxiosConfig extends AxiosRequestConfig {
  silent?: boolean
  requestKey?: string
  allowConcurrent?: boolean
  isDownload?: boolean
  filename?: string
}

interface ApiResponse<T = any> {
  result: number
  message: string
  data: T
}

export const cancelAllRequests = () => {
  abortControllers.forEach((controller) => {
    controller.abort()
  })
  abortControllers.clear()
}

const showErrorMessage = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  debounceTimer = setTimeout(() => {
    if (!isShowingError && errorQueue.length > 0) {
      isShowingError = true
      const message =
        errorQueue.length > 1
          ? `发生 ${errorQueue.length} 个错误，最新错误: ${errorQueue[errorQueue.length - 1]}`
          : errorQueue[0]

      ElMessage({
        type: 'error',
        message,
        duration: 3000,
        onClose: () => {
          isShowingError = false
          errorQueue = []
          debounceTimer = null
        },
      })
    }
  }, 300)
}

const handleTokenExpired = async () => {
  if (isShowingTokenExpiredDialog) {
    return
  }

  isShowingTokenExpiredDialog = true

  try {
    await ElMessageBox.confirm('登录状态已失效，请重新登录', '提示', {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      showClose: false,
    })

    clearStorage()

    // 跳转到登录页面
    const currentPath = window.location.pathname + window.location.search
    const loginUrl = `/login?redirect=${encodeURIComponent(currentPath)}`
    window.location.href = loginUrl
    router.clearRoutes()
    router.replace({name:LoginRoute.})
  } catch (error) {
    clearStorage()
    window.location.href = '/login'
  } finally {
    isShowingTokenExpiredDialog = false
  }
}

const handleError = (error: Error | AxiosError, config?: CustomAxiosConfig) => {
  const isSilent = config?.silent || false

  if (axios.isAxiosError(error) && error.code === 'ERR_CANCELED') {
    return Promise.reject(error)
  }
  debugger
  if (!isSilent) {
    let errorMessage: string

    if (axios.isAxiosError(error)) {
      errorMessage = error.response?.data?.message || error.message || '未知错误'

      if (error.response?.status === 401) {
        return Promise.reject(error)
      }
    } else {
      errorMessage = error.message || '未知错误'
    }

    errorQueue.push(errorMessage)
    showErrorMessage()
  }

  return Promise.reject(error)
}

const instance: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: import.meta.env.VITE_API_TIMEOUT,
})

instance.interceptors.request.use(
  (config) => {
    const controller = new AbortController()
    const customConfig = config as CustomAxiosConfig
    let requestKey =
      customConfig.requestKey ||
      `${config.url}_${config.method}_${JSON.stringify(config.params)}_${JSON.stringify(config.data)}`

    if (customConfig.allowConcurrent) {
      requestKey = `${requestKey}_${Date.now()}_${Math.random().toString(36).slice(2)}`
    } else if (abortControllers.has(requestKey)) {
      // abortControllers.get(requestKey)?.abort()
    }

    abortControllers.set(requestKey, controller)
    config.signal = controller.signal

    const token = getStorageToken()
    if (token) {
      config.headers[KEY_TOKEN] = token
    }
    return config
  },
  (error) => handleError(error),
)

const downloadFile = (blob: Blob, filename: string) => {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

const getFilenameFromHeaders = (headers: any): string => {
  const disposition = headers['content-disposition']
  if (!disposition) return ''
  let filename = ''
  const matches = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
  if (matches && matches[1]) {
    filename = matches[1].replace(/['"]/g, '')
    try {
      filename = decodeURIComponent(filename)
    } catch (e) {
      logger.warn('Failed to decode filename', e)
    }
  }
  return filename
}

instance.interceptors.response.use(
  (response) => {
    const config = response.config as CustomAxiosConfig
    const requestKey =
      config.requestKey ||
      `${config.url}_${config.method}_${JSON.stringify(config.params)}_${JSON.stringify(config.data)}`

    abortControllers.delete(requestKey)

    if (config.isDownload && response.data instanceof Blob) {
      const filename = config.filename || getFilenameFromHeaders(response.headers) || 'download'
      downloadFile(response.data, filename)
      return response.data
    }

    if (response.data.result !== 0) {
      // 处理token失效的情况
      if (response.data.result === 600) {
        handleTokenExpired()
        const error = new Error('登录状态已失效')
        return Promise.reject(error)
      }

      const error = new Error(response.data.message || '结果码错误')
      return handleError(error, config)
    }
    return response.data
  },
  (error) => {
    if (error.config) {
      const config = error.config as CustomAxiosConfig
      const requestKey =
        config.requestKey ||
        `${config.url}_${config.method}_${JSON.stringify(config.params)}_${JSON.stringify(config.data)}`
      abortControllers.delete(requestKey)
    }
    return handleError(error, error.config as CustomAxiosConfig)
  },
)

function req<T = any>(config: CustomAxiosConfig) {
  return instance.request(config) as Promise<ApiResponse<T>>
}

req.download = (url: string, params?: any, options: Partial<CustomAxiosConfig> = {}) => {
  const isPost = options.method?.toUpperCase() === 'POST'
  return req({
    url,
    method: isPost ? 'POST' : 'GET',
    [isPost ? 'data' : 'params']: params,
    responseType: 'blob',
    isDownload: true,
    ...options,
  })
}

export default req
