type StorageValue = string | number | boolean | object | null

class StorageUtils {
  private prefix: string

  constructor(prefix = '') {
    this.prefix = prefix
  }

  private getKey(key: string): string {
    return this.prefix ? `${this.prefix}_${key}` : key
  }

  set<T extends StorageValue>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(this.getKey(key), serializedValue)
    } catch (error) {
      console.error(`Failed to set ${key} in LocalStorage:`, error)
    }
  }

  get<T extends StorageValue>(key: string, defaultValue: T | null = null): T | null {
    try {
      const value = localStorage.getItem(this.getKey(key))
      if (value === null) return defaultValue
      return JSON.parse(value) as T
    } catch (error) {
      console.error(`Failed to get ${key} from LocalStorage:`, error)
      return defaultValue
    }
  }

  remove(key: string): void {
    try {
      localStorage.removeItem(this.getKey(key))
    } catch (error) {
      console.error(`Failed to remove ${key} from LocalStorage:`, error)
    }
  }

  clear(): void {
    try {
      if (this.prefix) {
        const keys = Object.keys(localStorage).filter(k => k.startsWith(this.prefix))
        keys.forEach(k => localStorage.removeItem(k))
      } else {
        localStorage.clear()
      }
    } catch (error) {
      console.error('Failed to clear LocalStorage:', error)
    }
  }
}

export const storage = new StorageUtils(import.meta.env.VITE_APP_STORAGE_PREFIX)

export const setStorage = <T extends StorageValue>(key: string, value: T) => storage.set(key, value)
export const getStorage = <T extends StorageValue>(key: string, defaultValue: T | null = null) =>
  storage.get(key, defaultValue)
export const removeStorage = (key: string) => storage.remove(key)
export const clearStorage = () => storage.clear()
