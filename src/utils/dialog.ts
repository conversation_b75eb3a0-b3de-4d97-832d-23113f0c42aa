type BasicMode = 'create' | 'edit' | 'view'

export type DialogMode = BasicMode | (string & {})
export type ModelData = Record<string, any>
export type TitleGenerator = (data: ModelData | null) => string
export type ModeMappingValue = string | TitleGenerator

export const DialogUtils = {
  defaultModeMap: {
    create: '新增${_name}',
    edit: '编辑${_name}',
    view: '查看${_name}'
  } as Record<BasicMode, string>,

  parseTemplate(template: string, data: Record<string, any>): string {
    return template.replace(/\${(.*?)}/g, (match, key) => {
      const paths = key.trim().split('.')
      let value = data
      for (const path of paths) {
        value = value?.[path]
      }
      return String(value ?? match)
    })
  },

  getTitle({ name, mode = 'create', modeMap = {}, model = null }: {
    name: string
    mode: DialogMode
    modeMap?: Partial<Record<DialogMode, ModeMappingValue>>
    model?: ModelData | null
  }): string {
    const finalModeMap = { ...this.defaultModeMap, ...modeMap } as Record<DialogMode, ModeMappingValue>
    const modeText = finalModeMap[mode]

    if (typeof modeText === 'function') {
      return modeText(model)
    }

    if (typeof modeText === 'string') {
      const templateData = {
        ...model,
        _name: name
      }
      return this.parseTemplate(modeText, templateData)
    }

    return `${modeText || mode}${name}`
  }
}
