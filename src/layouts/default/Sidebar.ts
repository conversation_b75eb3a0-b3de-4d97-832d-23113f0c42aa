import { defineComponent, h, onMounted, onUnmounted, ref, watch } from 'vue'
import { ElIcon, ElMenu, ElMenuItem, ElSubMenu, type MenuInstance } from 'element-plus'
import type { RouteRecordRaw } from 'vue-router'
import { useRoute } from 'vue-router'
import PerfectScrollbar from 'perfect-scrollbar'
import 'perfect-scrollbar/css/perfect-scrollbar.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import type { AppSettings } from '@/types/app'

export default defineComponent({
  props: {
    routes: {
      type: Array as () => RouteRecordRaw[],
    },
    mode: {
      type: String as () => AppSettings.SidebarMode,
    },
    collapse: Boolean,
    fixed: Boolean,
  },

  setup(props) {
    const route = useRoute()
    const scrollContainer = ref<HTMLElement | null>(null)
    let psInstance: PerfectScrollbar | null = null

    const menuRef = ref<MenuInstance | null>(null)
    const activeIndex = ref('')

    const updateActiveMenu = () => {
      const { matched } = route
      const currentRoute = matched[matched.length - 1]
      activeIndex.value = String(
        currentRoute?.meta?.activeMenu || currentRoute?.meta?.index || route.path,
      )
    }

    // 监听路由和模式变化
    watch(
      [() => route.path, () => props.mode],
      () => {
        updateActiveMenu()
      },
      { immediate: true },
    )

    const options = {
      wheelSpeed: 0.5,
      suppressScrollX: true,
      minScrollbarLength: 20,
    }

    const renderMenuItems = (items: RouteRecordRaw[]) =>
      items
        .map((item, j) => {
          // 跳过隐藏的菜单项
          if (item.meta?.hidden) return null
          const IconComponent =
            ElementPlusIconsVue[item.meta?.icon as keyof typeof ElementPlusIconsVue]

          return item.children && item.children.length
            ? h(
                ElSubMenu,
                {
                  index: (item.meta?.index ?? '') + j,
                },
                {
                  title: () => [
                    IconComponent &&
                      h(
                        ElIcon,
                        {},
                        {
                          default: () => h(IconComponent),
                        },
                      ),
                    h('span', item.meta?.title),
                  ],
                  default: () => renderMenuItems(item.children ?? []),
                },
              )
            : h(
                ElMenuItem,
                {
                  index: item.meta?.index ?? '',
                  route: item.meta?.index ?? item.path,
                },
                {
                  default: () => [
                    IconComponent &&
                      h(
                        ElIcon,
                        {},
                        {
                          default: () => h(IconComponent),
                        },
                      ),
                    h('span', item.meta?.title),
                  ],
                },
              )
        })
        .filter(Boolean)

    const createPs = () => {
      if (scrollContainer.value && !psInstance && props.fixed && props.mode === 'vertical') {
        psInstance = new PerfectScrollbar(scrollContainer.value, options)
      }
    }

    const destroyPs = () => {
      if (psInstance) {
        psInstance.destroy()
        psInstance = null
      }
    }

    const updatePs = () => {
      if (psInstance) {
        psInstance.update()
      }
    }

    onMounted(createPs)
    onUnmounted(destroyPs)

    // 监听 fixed 状态变化
    watch(
      () => props.fixed,
      (v) => {
        if (v) {
          createPs()
        } else {
          destroyPs()
        }
      },
    )

    watch(
      () => props.collapse,
      () => {
        setTimeout(() => {
          if (psInstance) {
            updatePs()
          }
        }, 300)
      },
    )

    // 监听模式变化
    watch(
      () => props.mode,
      (newMode) => {
        if (newMode !== 'vertical') {
          destroyPs()
        } else if (props.fixed) {
          createPs()
        }
      },
    )

    return () =>
      h(
        'div',
        { ref: scrollContainer },
        h(
          ElMenu,
          {
            ref: menuRef,
            router: true,
            textColor: '#fff',
            activeTextColor: props.mode !== 'horizontal' ? undefined : 'hsl(var(--primary))',
            backgroundColor: props.mode !== 'horizontal' ? undefined : '#111f44',
            // backgroundColor: 'transparent',
            mode: props.mode,
            collapseTransition: false,
            collapse: props.collapse,
            defaultActive: activeIndex.value,
            popperClass: 'sidebar-popper',
          },
          { default: () => renderMenuItems(props.routes ?? []) },
        ),
      )
  },
})
