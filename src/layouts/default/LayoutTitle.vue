<template>
  <div v-if="layout.pageTitleVisible.value">
    <Transition
      name="fade"
      mode="out-in"
    >
      <div
        v-if="shouldRender"
        key="title"
        class="flex items-center gap-1 mb-2"
      >
        <template v-if="layout.customTitleRender?.value">
          <component :is="layout.customTitleRender.value" />
        </template>
        <template v-else>
          <el-button
            v-if="layout.showBackButton.value"
            type="text"
            class="p-0"
            @click="handleBack"
          >
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <div
            @click="handleClick"
            :class="[
              'layout-title youshe-bold inline-block',
              { 'cursor-pointer': layout.titleClickable.value, hasBack: layout.showBackButton.value },
            ]"
          >
            {{ layout.pageTitle }}
          </div>
        </template>
      </div>
      <div
        v-else
        key="placeholder"
        class="h-[29px]"
      ></div>
    </Transition>
  </div>
</template>
<script setup lang="ts">
import { useLayout } from '@/composables/useLayout'
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'

const layout = useLayout()
const route = useRoute()
const shouldRender = ref(false)
const isFirstLoad = ref(true)

const handleBack = () => {
  layout.backHandler.value?.()
}

const handleClick = (e: MouseEvent) => {
  layout.titleClickHandler.value?.(e)
}

watch(
  () => route.path,
  () => {
    shouldRender.value = false

    const delay = isFirstLoad.value ? 0 : 300
    isFirstLoad.value = false

    setTimeout(() => {
      shouldRender.value = true
    }, delay)
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.layout-title {
  position: relative;
  font-size: 16px;
  opacity: 0.72;

  color: hsl(var(--foreground));
  &:not(.hasBack) {
    padding-left: 8px;
  }
  &:not(.hasBack)::before {
    content: '';
    width: 4px;
    height: 12px;
    top: 4px;
    left: 0;
    position: absolute;
    background-color: hsl(var(--primary));
  }
}
</style>
