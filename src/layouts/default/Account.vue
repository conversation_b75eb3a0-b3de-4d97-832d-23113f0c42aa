<template>
  <div class="flex shrink-0 items-center px-5">
    <el-avatar
      :src="avatar"
      class="mr-2"
    />
    <el-dropdown>
      <span
        :class="
          cn([
            'el-dropdown-link flex items-center cursor-pointer text-[--el-text-color-regular]',
            {
              'text-[--el-color-white]': settingsStore.settings.layout.sidebarMode === 'horizontal',
            },
          ])
        "
      >
        {{ welcome }}
        <el-icon
          :class="
            cn([
              'el-icon--right text-[--el-text-color-regular]',
              {
                'text-[--el-color-white]':
                  settingsStore.settings.layout.sidebarMode === 'horizontal',
              },
            ])
          "
        >
          <arrow-down />
        </el-icon>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="showChangePasswordDialog = true"
            >{{ t('common.modifyPassword') }}
          </el-dropdown-item>
          <el-dropdown-item
            divided
            @click="handleLogout"
          >
            {{ t('common.logout') }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>

  <ChangePasswordDialog
    v-model="showChangePasswordDialog"
    @success="handlePasswordChangeSuccess"
  />
</template>
<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import avatar from '@/assets/imgs/layout/def-avatar.png'
import { useSettingsStore } from '@/stores/settings'
import { useUserStore } from '@/stores/user'
import c from 'lodash/template'
import { ArrowDown } from '@element-plus/icons-vue'
import { useTypedI18n } from '@/i18n'
import { cn } from '@/utils/utils'
import ChangePasswordDialog from './ChangePasswordDialog.vue'

const { t } = useTypedI18n()
const router = useRouter()
const settingsStore = useSettingsStore()
const userStore = useUserStore()

const showChangePasswordDialog = ref(false)

const welcome = computed(() => {
  const template = settingsStore.settings.user.welcome
  const accountName = userStore.account?.accountName
  try {
    return c(template)({ accountName: userStore.account?.accountName })
  } catch (e) {
    console.log(e)
    return accountName
  }
})

const handlePasswordChangeSuccess = () => {
  ElMessageBox.confirm('密码修改成功，是否重新登录？', '提示', {
    confirmButtonText: '重新登录',
    cancelButtonText: '稍后登录',
    type: 'success',
  })
    .then(() => {
      handleLogout(false)
    })
    .catch(() => {})
}

const handleLogout = async (needConfirm = true) => {
  try {
    if (needConfirm) {
      await ElMessageBox.confirm(t('message.confirmLogout'), t('common.tips'), {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      })
    }

    await userStore.logout()
    ElMessage.success(t('message.logoutSuccess'))
    router.replace('/login')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(t('message.logoutFailed'))
    }
  }
}
</script>
