<template>
  <el-icon
    class="sidebar-toggle-btn"
    :class="{ 'is-collapsed': collapsed }"
    @click="toggleSidebar"
  >
    <Fold />
  </el-icon>
</template>

<script setup lang="ts">
import { Fold } from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import { computed } from 'vue'

const settingsStore = useSettingsStore()
const collapsed = computed(() => settingsStore.settings.layout.sidebarCollapse)

const toggleSidebar = () => {
  settingsStore.toggleSidebarCollapse()
}
</script>

<style lang="scss" scoped>
.sidebar-toggle-btn {
  cursor: pointer;
  font-size: 20px;
  margin-left: 16px;
  transition: transform 0.3s;

  &:hover {
    color: var(--el-color-primary);
  }

  &.is-collapsed {
    transform: rotate(180deg);
  }
}
</style>
