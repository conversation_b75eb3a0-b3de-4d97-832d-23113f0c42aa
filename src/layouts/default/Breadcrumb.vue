<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item :to="{ path: '/' }">
      <el-icon>
        <HomeFilled />
      </el-icon>
      <span>首页</span>
    </el-breadcrumb-item>
    <template
      v-for="item in breadcrumbs"
      :key="item.path"
    >
      <el-breadcrumb-item :to="getItemPath(item)">
        <!-- <el-icon v-if="item.icon"><component :is="item.icon" /></el-icon> -->
        <span>{{ item.title }}</span>
      </el-breadcrumb-item>
    </template>
  </el-breadcrumb>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, type RouteLocationNormalizedLoaded } from 'vue-router'
import { HomeFilled } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user' // 导入 userStore

interface BreadcrumbItem {
  title: string
  path: string
  to?: { path: string }
  icon?: string
  isDirectory?: boolean
  matched?: RouteLocationNormalizedLoaded['matched'][0]
}

const route = useRoute()

const findFirstChildRoute = (matched: RouteLocationNormalizedLoaded['matched'][0]) => {
  if (!matched.children?.length) return null

  const findValidChild = (
    children: typeof matched.children,
  ): RouteLocationNormalizedLoaded['matched'][0] | null => {
    for (const child of children) {
      // 检查是否有组件定义（单组件或命名视图）
      if (
        (child.component || (child.components && Object.keys(child.components).length)) &&
        !child.children?.length
      ) {
        return child as RouteLocationNormalizedLoaded['matched'][0]
      }
      if (child.children?.length) {
        const found = findValidChild(child.children)
        if (found) return found
      }
    }
    return null
  }

  return findValidChild(matched.children)
}

const getItemPath = (item: BreadcrumbItem) => {
  if (!item.matched?.children?.length) {
    return item.to
  }

  const firstChild = findFirstChildRoute(item.matched)
  if (firstChild?.path) {
    return { path: firstChild.meta.fullpath as string }
  }

  return item.to
}

const getBreadcrumbs = (route: RouteLocationNormalizedLoaded): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = []
  const userStore = useUserStore()

  if (route.meta.isDynamicRoute) {
    const routes = userStore.routes || []
    const findRouteByPath = (routes: any[], path: string): any => {
      for (const r of routes) {
        if (r.meta.fullpath === path) {
          return r
        }
        if (r.children?.length) {
          const found = findRouteByPath(r.children, path)
          if (found) return found
        }
      }
      return null
    }

    const simulateMatched = () => {
      const pathSegments = route.path
        .replace(/^\/|\/$/g, '')
        .split('/')
        .filter(Boolean)
      let currentPath = ''
      const matched: any[] = []
      for (const segment of pathSegments) {
        currentPath = currentPath ? `${currentPath}/${segment}` : `/${segment}`

        const matchedRoute = findRouteByPath(routes, currentPath)

        if (matchedRoute) {
          matched.push(matchedRoute)
        }
      }

      return matched
    }

    const simulatedMatched = simulateMatched()
    if (simulatedMatched.length > 0) {
      const breadcrumbItems: BreadcrumbItem[] = []

      for (const matchedRoute of simulatedMatched) {
        if (matchedRoute?.meta?.title) {
          breadcrumbItems.push({
            title: matchedRoute.meta.title,
            path: matchedRoute.meta.fullpath,
            to:
              matchedRoute.meta.fullpath !== route.path
                ? { path: matchedRoute.meta.fullpath }
                : undefined,
            icon: matchedRoute.meta.icon,
            isDirectory: !!matchedRoute.children?.length,
            matched: matchedRoute,
          })
        }
      }

      const currentRouteInBreadcrumbs = breadcrumbItems.some((item) => item.path === route.path)

      if (!currentRouteInBreadcrumbs && route.meta.title) {
        breadcrumbItems.push({
          title: route.meta.title as string,
          path: route.path,
          to: undefined,
          icon: route.meta.icon as string,
          isDirectory: false,
        })
      }

      return breadcrumbItems
    }
  }

  // 如果不是由 ZDynamicRoute 创建的路由，或者没有找到匹配的路由，使用原来的逻辑
  let currentPath = ''
  const paths = route.path
    .replace(/^\/|\/$/g, '')
    .split('/')
    .filter(Boolean)

  for (const segment of paths) {
    currentPath = currentPath ? `${currentPath}/${segment}` : `/${segment}`

    const matchedRoute = route.matched.find((match) => match.path === currentPath)
    if (matchedRoute?.meta?.title) {
      items.push({
        title: matchedRoute.meta.title as string,
        path: currentPath,
        to: currentPath !== route.path ? { path: currentPath } : undefined,
        icon: matchedRoute.meta.icon as string,
        isDirectory: !!matchedRoute.children?.length,
        matched: matchedRoute,
      })
    }
  }
  return items
}

const breadcrumbs = computed(() => getBreadcrumbs(route))
</script>

<style scoped>
.el-breadcrumb {
  line-height: 40px;
}

.el-icon {
  margin-right: 4px;
  vertical-align: middle;
}

:deep(.el-breadcrumb__inner) {
  display: inline-flex;
  align-items: center;
}
</style>
