<template>
  <div
    :style="{
      '--size-menu': `${isCollapsed ? 80 : 256}px`,
    }"
    :class="[
      'z-layout',
      'z-layout-default',
      `z-layout--${settingsStore.settings.layout.sidebarMode}`,
      'group',
      {
        transforming,
        collapse: isCollapsed,
      },
    ]"
  >
    <div
      v-if="!isHorizontal"
      class="z-layout__sidebar-wrap"
    >
      <div
        :class="['z-layout__sidebar', 'w-[--size-menu]', { 'z-layout__sidebar--fixed': isFixed }]"
      >
        <Logo />
        <Sidebar
          id="ps-container"
          :routes="appStore.currentApp?.routes ?? []"
          :collapse="isCollapsed && !isHorizontal"
          :mode="settingsStore.settings.layout.sidebarMode"
          :fixed="isFixed"
        />
      </div>
      <div
        v-if="isFixed"
        class="sidebar-placeholder w-[--size-menu] invisible"
      ></div>
    </div>

    <div class="z-layout__main">
      <div
        v-if="isHorizontal"
        class="z-layout__sidebar-wrap"
      >
        <div :class="['z-layout__sidebar', { 'z-layout__sidebar--fixed': isFixed }]">
          <Logo />
          <div class="w-0 flex-1">
            <Sidebar
              :routes="appStore.currentApp?.routes ?? []"
              :collapse="isCollapsed && !isHorizontal"
              :mode="settingsStore.settings.layout.sidebarMode"
              :fixed="isFixed"
            />
          </div>
          <Account v-if="isHorizontal" />
        </div>
        <div
          v-if="isFixed"
          class="sidebar-placeholder h-[--el-menu-horizontal-height] invisible"
        ></div>
      </div>
      <div
        v-if="!isHorizontal"
        class="z-layout__navbar"
        :class="{ 'z-layout__navbar--fixed': isNavbarFixed }"
      >
        <div class="z-layout__navbar-left px-4 flex items-center">
          <Breadcrumb />
          <SidebarToggle />
        </div>
        <div class="z-layout__navbar-right flex items-center">
          <div class="mt-1">
            <Clock />
          </div>
          <Account />
        </div>
      </div>
      <div
        v-if="!isHorizontal && isNavbarFixed"
        class="navbar-placeholder h-[--el-menu-horizontal-height] invisible"
      ></div>
      <div
        class="z-layout__content"
        :class="cn('p-4 overflow-x-hidden', layoutContext.contentClass.value)"
        :style="layoutContext.contentStyle.value"
      >
        <LayoutTitle />
        <RouterTransition />
      </div>
    </div>

    <Settings />
  </div>
</template>
<script setup lang="ts">
import { createLayout } from '@/composables/useLayout'
import { useSettingsStore } from '@/stores/settings'
import { useAppStore } from '@/stores/app'
import { computed, ref, watch, onMounted, onUnmounted } from 'vue'
import LayoutTitle from './LayoutTitle.vue'
import Sidebar from './Sidebar'
import Logo from './Logo.vue'
import Breadcrumb from './Breadcrumb.vue'
import SidebarToggle from './SidebarToggle.vue'
import Account from './Account.vue'
import Clock from './Clock.vue'
import RouterTransition from '@/components/RouterTransition.vue'
import Settings from './Settings.vue'
import { cn } from '@/utils/utils'

const layoutContext = createLayout()
const transforming = ref(false)
const settingsStore = useSettingsStore()
const appStore = useAppStore()

const isHorizontal = computed(() => settingsStore.settings.layout.sidebarMode === 'horizontal')
const isCollapsed = computed(() => settingsStore.settings.layout.sidebarCollapse)
const isFixed = computed(() => settingsStore.settings.layout.sidebarFixed)
const isNavbarFixed = computed(
  () => !isHorizontal.value && settingsStore.settings.layout.navbarFixed,
)

watch([isHorizontal, isFixed, isNavbarFixed], async () => {
  transforming.value = true
  setTimeout(() => {
    transforming.value = false
  }, 0)
})

const handleScroll = () => {
  const scrolled = window.scrollY > 20
  document.body.classList.toggle('scrolled', scrolled)
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
$menu-collapse:
  margin-left 0.3s ease,
  width 0.3s ease;

.dark {
  .z-layout {
    background: none;
  }
}

.z-layout {
  --el-menu-horizontal-height: 70px;

  background-color: #f0f2f5;
  position: relative;
  display: flex;

  &::before {
    display: block;
    content: '';
    position: fixed;
    z-index: -2;
    width: 100%;
    max-width: inherit;
    bottom: 0;
    top: 0;
    background-color: inherit;
  }

  &__sidebar {
    &::before {
      content: '';
      width: inherit;
      display: block;
      position: fixed;
      top: 0;
      bottom: 0;
      background-color: inherit;
      z-index: 1;
    }

    &--fixed {
      position: fixed;
    }

    #ps-container {
      position: relative;
      padding: 0 8px;
      z-index: 2;
    }

    :deep(.el-menu) {
      background-color: transparent;
    }
  }

  &__main {
    flex: 1;
    overflow-x: hidden;
  }

  &__navbar {
    height: var(--el-menu-horizontal-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    transition: all 0.3s ease;
    padding: 0 1.5rem;
    background: transparent;

    &-left {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    &-right {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    &--fixed {
      position: fixed;
      top: 0;
      left: var(--size-menu);
      right: 0;
      z-index: 401;

      // 初始状态
      background: transparent;
      backdrop-filter: blur(8px);
      border-bottom: 1px solid transparent;

      // 滚动状态
      .scrolled & {
        @apply bg-background/95;
        backdrop-filter: blur(12px);
        border-bottom: 1px solid hsl(var(--border));
        box-shadow:
          0 1px 0 0 hsl(var(--border)),
          0 1px 6px -1px rgb(0 0 0 / 0.1),
          0 2px 4px -2px rgb(0 0 0 / 0.1);

        :deep(.breadcrumb) {
          @apply text-foreground/80;
        }
      }

      // 暗色主题特殊处理
      .dark & {
        background: transparent;
        border-bottom: 1px solid hsl(var(--border) / 0.1);

        .scrolled & {
          background: hsl(var(--background) / 0.8);
          border-bottom: 1px solid hsl(var(--border) / 0.2);
          box-shadow:
            0 1px 0 0 hsl(var(--border) / 0.1),
            0 4px 16px rgb(0 0 0 / 0.3);
        }
      }

      // 内容区域
      .z-layout__navbar-left,
      .z-layout__navbar-right {
        position: relative;
        z-index: 2;
      }
    }

    // 导航栏组件样式优化 - 更新 :deep 语法
    :deep(.breadcrumb) {
      @apply text-foreground/60 transition-colors duration-200;
      font-size: 0.875rem;
    }

    :deep(.sidebar-toggle) {
      @apply text-foreground/60 hover:text-foreground transition-colors duration-200;
    }

    :deep(.account-dropdown) {
      @apply hover:bg-foreground/5 rounded-full transition-colors duration-200;
    }

    :deep(.clock) {
      @apply text-foreground/60;
      font-size: 0.875rem;
    }
  }

  &--horizontal {
    display: block;

    .z-layout__sidebar {
      width: auto;

      background-image: url(@/assets/imgs/layout/top-bg.png);
      background-size: cover;

      display: flex;
      height: calc(var(--el-menu-horizontal-height) - 1px);

      #ps-container {
        width: 0;
        flex: 1;
      }

      &::before {
        bottom: auto;
      }

      &--fixed {
        width: 100%;
        left: 0;
        right: 0;
        top: auto;
        z-index: 12;

        & + .z-layout__main {
          padding-top: 70px;
        }
      }

      :deep(.el-menu) {
        background-color: inherit;
        justify-content: center;
      }
    }

    .z-layout__main {
      width: 100%;
    }
  }

  &--vertical {
    --el-menu-hover-bg-color: rgba(200, 200, 200, 0.2);
    --el-menu-active-color: hsl(var(--primary-foreground));

    .sidebar-placeholder {
      transition: $menu-collapse;
    }

    .z-layout__sidebar {
      transition: $menu-collapse;

      &--fixed {
        bottom: 0;
        top: 0;

        #ps-container {
          height: calc(100vh - var(--el-menu-horizontal-height));
        }
      }

      &::before {
        min-height: 100vh;
        background-color: #000;
        opacity: 0.8;
      }

      &::after {
        content: '';
        position: fixed;
        bottom: 0;
        top: 0;
        width: inherit;
        background-image: url(@/assets/imgs/layout/sidebar-1.jpg);
        background-repeat: no-repeat;
        z-index: 0;
        background-size: cover;
        background-position: center center;
        min-height: 100vh;
      }

      :deep(.el-menu) {
        border-right: 0;

        .el-menu-item {
          &.is-active {
            background: hsl(var(--primary));
            box-shadow:
              0 12px 20px -10px hsla(var(--primary) / 0.28),
              0 4px 20px 0 rgba(0, 0, 0, 0.12),
              0 7px 8px -5px hsla(var(--primary) / 0.2);
          }
        }
      }
    }

    .navbar-placeholder {
      transition: margin-left 0.3s ease;
    }

    .z-layout__navbar--fixed {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &.collapse {
      .z-layout__navbar--fixed {
        left: 80px;
      }
    }
  }

  &.transforming {
    .z-layout__sidebar,
    .z-layout__main,
    .z-layout__navbar--fixed {
      transition: none;
    }
  }
}
</style>

<style lang="scss">
// 弹出菜单样式
.sidebar-popper.el-popper {
  border: none !important;

  .el-menu {
    background: #000000dd !important;
    border: 1px solid hsla(var(--border) / 0.1);
    backdrop-filter: blur(12px);
    padding: 4px;

    .el-menu-item {
      margin: 4px 0;
      height: 40px;
      line-height: 40px;
      color: #fff !important;

      &:hover {
        background-color: hsla(var(--primary) / 0.2) !important;
      }

      &.is-active {
        background: hsl(var(--primary)) !important;
        color: hsl(var(--primary-foreground)) !important;
      }
    }

    .el-sub-menu__title {
      color: #fff !important;

      &:hover {
        background-color: hsla(var(--primary) / 0.2) !important;
      }
    }
  }

  // 移除默认的箭头
  .el-popper__arrow {
    display: none;
  }
}
</style>
