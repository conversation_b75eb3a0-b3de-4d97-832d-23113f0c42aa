<template>
  <div class="clock-container">
    <div class="clock-wrapper">
      <div class="time">{{ currentTime }}</div>
      <div class="date">{{ currentDate }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import dayjs from '@/utils/dayjs'

const currentTime = ref(dayjs().format('HH:mm:ss'))
const currentDate = ref(dayjs().format('YYYY-MM-DD'))
let timer: number

const updateTime = () => {
  const now = dayjs()
  currentTime.value = now.format('HH:mm:ss')
  currentDate.value = now.format('YYYY-MM-DD')
}

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  clearInterval(timer)
})
</script>

<style lang="scss" scoped>
.clock-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 100%;

  .clock-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 16px;
    border-radius: var(--el-border-radius-base);
    background: hsla(var(--background) / 0.2);
    backdrop-filter: blur(8px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.2);
    }

    .time {
      font-family: 'Roboto Mono', monospace;
      font-size: 1.2em;
      font-weight: 600;
      color: hsl(var(--foreground));
      letter-spacing: 0.05em;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

      &::after {
        content: '';
        display: block;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg,
            transparent,
            hsla(var(--primary) / 0.5),
            transparent);
        margin: 4px 0;
      }
    }

    .date {
      font-size: 0.9em;
      color: hsla(var(--foreground) / 0.8);
      font-weight: 500;
    }
  }
}

:global(.dark) .clock-wrapper {
  background: hsla(var(--background) / 0.4);
}
</style>
