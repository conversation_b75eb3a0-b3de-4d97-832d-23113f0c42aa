<template>
  <div class="settings-button">
    <el-button
      circle
      class="settings-trigger"
      @click="drawerVisible = true"
    >
      <el-icon>
        <Setting />
      </el-icon>
    </el-button>

    <el-drawer
      body-class="pr-1"
      v-model="drawerVisible"
      :title="t('settings.title')"
      direction="rtl"
      size="430px"
      :destroy-on-close="false"
    >
      <el-form
        label-width="110px"
        class="settings-form"
      >
        <!-- 语言设置 -->
        <div class="settings-section">
          <div class="settings-section-title">{{ t('settings.language.title') }}</div>
          <el-form-item
            :label="t('settings.language.select')"
            label-width="130px"
          >
            <el-radio-group
              v-model="currentLang"
              @change="(val) => handleLanguageChange(val as 'en' | 'zh')"
            >
              <el-radio-button
                v-for="lang in languages"
                :key="lang.value"
                :label="lang.value"
              >
                {{ t(lang.label) }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>

        <!-- 主题设置 -->
        <div class="settings-section">
          <div class="settings-section-title">{{ t('settings.theme.title') }}</div>
          <el-form-item :label="t('settings.theme.mode')">
            <el-radio-group
              v-model="colorScheme"
              @change="
                (val: string | number | boolean | undefined) =>
                  updateColorScheme(val?.toString() ?? '')
              "
            >
              <el-radio-button label="light">{{ t('settings.theme.light') }}</el-radio-button>
              <el-radio-button label="dark">{{ t('settings.theme.dark') }}</el-radio-button>
              <el-radio-button label="">{{ t('settings.theme.system') }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="t('settings.theme.color')">
            <div class="theme-colors">
              <div
                v-for="theme in themes"
                :key="theme.value"
                class="theme-color-item"
                :class="{ active: settingsStore.settings.theme.themeType === theme.value }"
                @click="setTheme(theme.value as AppSettings.ThemeType)"
              >
                <div
                  class="color-block"
                  :style="{ backgroundColor: theme.color }"
                ></div>
              </div>
            </div>
          </el-form-item>
          <el-form-item :label="t('settings.theme.radius')">
            <el-radio-group
              v-model="settingsStore.settings.theme.borderRadius"
              @change="(val: string) => handleRadiusChange(val as AppSettings.Theme['borderRadius'])"
            >
              <el-radio-button label="none">{{ t('settings.theme.radius_none') }}</el-radio-button>
              <el-radio-button label="small">{{
                t('settings.theme.radius_small')
              }}</el-radio-button>
              <el-radio-button label="medium">{{
                t('settings.theme.radius_medium')
              }}</el-radio-button>
              <el-radio-button label="large">{{
                t('settings.theme.radius_large')
              }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>

        <!-- 布局设置 -->
        <div class="settings-section">
          <div class="settings-section-title">{{ t('settings.layout.title') }}</div>
          <el-form-item :label="t('settings.layout.menu_mode')">
            <el-radio-group v-model="settingsStore.settings.layout.sidebarMode">
              <el-radio-button label="vertical">{{
                t('settings.layout.vertical')
              }}</el-radio-button>
              <el-radio-button label="horizontal">{{
                t('settings.layout.horizontal')
              }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="t('settings.layout.fixed_sidebar')">
            <el-switch v-model="settingsStore.settings.layout.sidebarFixed" />
          </el-form-item>
          <el-form-item
            v-if="settingsStore.settings.layout.sidebarMode === 'vertical'"
            :label="t('settings.layout.fixed_navbar')"
          >
            <el-switch v-model="settingsStore.settings.layout.navbarFixed" />
          </el-form-item>
        </div>

        <!-- 个性化设置 -->
        <div class="settings-section">
          <div class="settings-section-title">{{ t('settings.personalization.title') }}</div>
          <el-form-item :label="t('settings.personalization.dynamic_title')">
            <el-tooltip :content="t('settings.personalization.dynamic_title_tip')">
              <el-switch v-model="settingsStore.settings.theme.dynamicTitle" />
            </el-tooltip>
          </el-form-item>
          <el-form-item :label="t('settings.personalization.progress')">
            <el-tooltip :content="t('settings.personalization.progress_tip')">
              <el-switch v-model="settingsStore.settings.theme.nProgress" />
            </el-tooltip>
          </el-form-item>
          <el-form-item :label="t('settings.personalization.welcome')">
            <el-input
              v-model="settingsStore.settings.user.welcome"
              :placeholder="t('settings.personalization.welcome_placeholder')"
            >
              <template #append>
                <el-tooltip :content="t('settings.personalization.welcome_tip')">
                  <el-icon>
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </template>
            </el-input>
          </el-form-item>
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Setting, InfoFilled } from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import type { AppSettings } from '@/types/app'
import { useTypedI18n } from '@/i18n'
import { useLang } from '@/composables/useLang'

const { t } = useTypedI18n()
const { switchLanguage, locale } = useLang()
const drawerVisible = ref(false)
const settingsStore = useSettingsStore()

const languages = [
  { value: 'zh', label: 'settings.language.zh' },
  { value: 'en', label: 'settings.language.en' },
]

const currentLang = computed({
  get: () => locale.value,
  set: () => {},
})

// 处理语言切换
const handleLanguageChange = async (lang: 'en' | 'zh') => {
  await switchLanguage(lang)
}

// 主题选项
const themes = [
  { value: 'blue', color: 'hsl(203, 89%, 53%)' },
  { value: 'green', color: 'hsl(142.1, 76.2%, 36.3%)' },
  { value: 'red', color: 'hsl(0, 84.2%, 60.2%)' },
  { value: 'purple', color: 'hsl(270, 75%, 60%)' },
  { value: 'yellow', color: 'hsl(45, 93%, 47%)' },
]

// 主题模式
const colorScheme = computed({
  get: () => settingsStore.settings.theme.colorScheme,
  set: (value) => {
    settingsStore.settings.theme.colorScheme = value
    settingsStore.settings.theme.followSystemTheme = value === ''
  },
})

// 更新主题模式
const updateColorScheme = (value: string) => {
  settingsStore.settings.theme.colorScheme = value as AppSettings.ColorMode | ''
  settingsStore.settings.theme.followSystemTheme = value === ''
  settingsStore.updateTheme()
}

// 设置主题色
const setTheme = (theme: AppSettings.ThemeType) => {
  settingsStore.setThemeType(theme)
}

// 处理圆角变化
const handleRadiusChange = (radius: AppSettings.Theme['borderRadius']) => {
  settingsStore.setBorderRadius(radius)
}
</script>

<style lang="scss" scoped>
.settings-button {
  position: fixed;
  right: 16px;
  bottom: 16px;
  z-index: 1000;

  .settings-trigger {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    background: var(--el-color-primary);
    border-color: var(--el-color-primary);
    color: white;
    width: 48px;
    height: 48px;
    font-size: 20px;

    &:hover {
      background: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-3);
    }
  }
}

.settings-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  .settings-section-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
}

.theme-colors {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .theme-color-item {
    cursor: pointer;

    &.active .color-block {
      border: 2px solid var(--el-border-color-darker);
      transform: scale(1.1);
    }

    .color-block {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      transition: all 0.3s;
      border: 2px solid transparent;

      &:hover {
        transform: scale(1.1);
      }
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}
</style>
