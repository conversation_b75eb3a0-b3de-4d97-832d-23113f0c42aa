<template>
  <RouterLink to="/"
    :class="{ [`after:h-[1px] after:content-[''] after:left-2 after:right-2 after:pos-absolute after:bottom-0 after:bg-white after:opacity-30`]: settingsStore.settings.layout.sidebarMode === 'vertical' }"
    class="flex shrink-0  px-5 items-center no-underline whitespace-nowrap overflow-hidden z-[1000] pos-relative h-[--el-menu-horizontal-height]">
    <img class="w-8 h-8 mr-4" src="~@/assets/logo.png" alt="">
    <div class="flex flex-col opacity-100 transition-opacity duration-300 group-[.collapse]:opacity-0">
      <h1 class="youshe-bold m-0 text-white text-[20px]">{{ systemTitle }}</h1>
      <span class="alibaba-semiBold text-white opacity-60 text-xs tracking-[0.6em] line-clamp-1 whitespace-normal">{{
        appTitle }}</span>
    </div>
  </RouterLink>
</template>
<script setup lang="ts">
import { useAppStore } from '@/stores/app';
import { useSettingsStore } from '@/stores/settings';
import { computed } from 'vue';
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const appTitle = import.meta.env.VITE_APP_TITLE
const systemTitle = computed(() => appStore.currentApp?.name ?? '')

</script>
