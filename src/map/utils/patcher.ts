// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

/**
 * 修复天地图 API 的已知问题
 */
export function patchTMapAPI(): void {
  if (!window.T) return

  // 修复 Label 移除时的问题
  T.Label.prototype.onRemove = function (t) {
    if (this.fu && this.fu.contains(this.Ow)) {
      this.fu.removeChild(this.Ow)
    }

    t.off(
      {
        Ge: this.VP,
        moveend: this.TE,
        viewreset: this.CP,
      },
      this,
    )
    this.cP()
    this.wQ('remove', {})
    this.jE = null
  }

  T.Label.prototype.getOffset = function () {
    return new T.Point(...this.options.Jo)
  }

  T.LocalSearch.prototype._s = function () {
    const i = this.map.getBounds()
    // t && (i = t)
    const e = i.getSouthWest(),
      s = i.getNorthEast(),
      n = e.getLng() + ',' + e.getLat() + ',' + s.getLng() + ',' + s.getLat()
    return n
  }

  T.PolylineTool.prototype.clear = function () {
    if (this.lO) {
      this.lO.bE(function (t) {
        t.Qq()
      })
    }
  }

  T.PolygonTool.prototype.clear = function () {
    if (this.jP) {
      this.jP.bE(function (t) {
        t.Qq()
      })
    }
  }
}
