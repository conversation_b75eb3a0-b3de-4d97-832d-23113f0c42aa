export class IdGenerator {
  private static counters = new Map<string, number>()

  public static generate(prefix: string, length: number = 8): string {
    const count = (this.counters.get(prefix) || 0) + 1
    this.counters.set(prefix, count)

    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substring(2, 2 + length)

    return `${prefix}_${timestamp}_${random}_${count}`
  }

  public static reset(prefix: string): void {
    this.counters.delete(prefix)
  }

  public static resetAll(): void {
    this.counters.clear()
  }
}
