export function pixelRadiusToMeters(pixelRadius: number, zoom: number, latitude: number) {
  const earthCircumference = 40075017
  const tileSize = 256 * 2 // 瓦片大小（像素）
  const resolution = earthCircumference / (tileSize * Math.pow(2, zoom))
  const latRad = (latitude * Math.PI) / 180
  const adjustedResolution = resolution * Math.cos(latRad)
  return pixelRadius * adjustedResolution
}

// 计算像素半径（米 -> 像素）
export function metersToPixels(meters: number, zoom: number, latitude: number) {
  const earthCircumference = 40075017
  const tileSize = 256 * 2 // 瓦片大小（像素）
  const resolution = earthCircumference / (tileSize * Math.pow(2, zoom)) // 米/像素
  const latRad = (latitude * Math.PI) / 180
  const adjustedResolution = resolution * Math.cos(latRad)
  return meters / adjustedResolution
}

export function toMercator(lng: number, lat: number) {
  const earthRadius = 6378137 // 地球半径（米）
  const x = ((lng * Math.PI) / 180) * earthRadius
  const a = (lat * Math.PI) / 180
  const y = (earthRadius / 2) * Math.log((1 + Math.sin(a)) / (1 - Math.sin(a)))
  return [x, y]
}

// Web墨卡托到WGS84的转换函数
export function toWGS84(x: number, y: number) {
  const earthRadius = 6378137
  const lng = (x / earthRadius) * (180 / Math.PI)
  const lat = (Math.atan(Math.exp(y / earthRadius)) * 2 - Math.PI / 2) * (180 / Math.PI)
  return [lng, lat]
}
