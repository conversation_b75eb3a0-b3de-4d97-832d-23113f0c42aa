import type { Component, VNode } from 'vue'

/**
 * 判断一个对象是否为 Vue 组件
 */
export function isVueComponent(value: unknown): value is Component {
  return typeof value === 'object' &&
         value !== null &&
         ('render' in value || 'setup' in value)
}

/**
 * 判断一个对象是否为 Vue VNode
 */
export function isVNode(value: unknown): value is VNode {
  return typeof value === 'object' &&
         value !== null &&
         '__v_isVNode' in value
}
