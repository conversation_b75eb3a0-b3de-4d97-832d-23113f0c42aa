import { createLogger } from '@/utils/logger'
import { TMAP_CONFIG } from '../constants'
const logger = createLogger('TMapLoader')

export class TMapLoader {
  private static instance: TMapLoader
  private loaded = false
  private loading = false
  private callbacks: Array<(error?: Error) => void> = []
  private retryCount = 0
  private maxRetries = 3
  private retryDelay = 1000

  // 定义需要等待加载的关键组件列表
  // 主js会加载其它js，每个js取一个类来表示
  private criticalComponents: string[] = ['Geocoder', 'Label']

  private constructor() {}

  static getInstance(): TMapLoader {
    if (!TMapLoader.instance) {
      TMapLoader.instance = new TMapLoader()
    }
    return TMapLoader.instance
  }

  /**
   * 设置需要等待加载的关键组件列表
   * @param components 组件名称数组
   */
  setCriticalComponents(components: string[]): void {
    this.criticalComponents = [...components]
  }

  /**
   * 添加需要等待加载的关键组件
   * @param componentName 组件名称
   */
  addCriticalComponent(componentName: string): void {
    if (!this.criticalComponents.includes(componentName)) {
      this.criticalComponents.push(componentName)
    }
  }

  /**
   * 获取当前的关键组件列表
   */
  getCriticalComponents(): string[] {
    return [...this.criticalComponents]
  }

  /**
   * 等待指定的API组件加载完成
   * @param componentName 组件名称，如 'Geocoder'
   * @param maxWaitTime 最大等待时间（毫秒）
   */
  waitForComponent(componentName: string, maxWaitTime = 10000): Promise<void> {
    return new Promise((resolve, reject) => {
      if (window.T && (window.T as any)[componentName]) {
        resolve()
        return
      }

      const startTime = Date.now()
      const checkInterval = setInterval(() => {
        if (window.T && (window.T as any)[componentName]) {
          clearInterval(checkInterval)
          resolve()
          return
        }

        if (Date.now() - startTime > maxWaitTime) {
          clearInterval(checkInterval)
          reject(new Error(`等待组件 ${componentName} 加载超时`))
        }
      }, 100)
    })
  }

  /**
   * 等待多个API组件加载完成
   * @param componentNames 组件名称数组
   * @param maxWaitTime 每个组件的最大等待时间（毫秒）
   */
  async waitForAllComponents(componentNames: string[], maxWaitTime = 10000): Promise<void> {
    const promises = componentNames.map((name) =>
      this.waitForComponent(name, maxWaitTime).catch((error) => {
        logger.warn(`组件 ${name} 加载失败: ${error.message}`)
        return Promise.reject(error)
      }),
    )

    await Promise.all(promises)
  }

  /**
   * 加载天地图脚本
   * @param key API密钥
   * @param options 加载选项
   * @returns Promise
   */
  load(
    key: string = TMAP_CONFIG.KEY,
    options?: {
      maxRetries?: number
      retryDelay?: number
    },
  ): Promise<void> {
    this.retryCount = 0

    if (options) {
      this.maxRetries = options.maxRetries ?? this.maxRetries
      this.retryDelay = options.retryDelay ?? this.retryDelay
    }

    return this.loadWithRetry(key)
  }

  /**
   * 带重试机制的加载方法
   * @param key API密钥
   * @returns Promise
   */
  private loadWithRetry(key: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.loaded) {
        resolve()
        return
      }

      this.callbacks.push((error?: Error) => {
        if (error) reject(error)
        else resolve()
      })

      if (this.loading) return

      this.loading = true

      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = `${TMAP_CONFIG.API_URL}?v=${TMAP_CONFIG.VERSION}&tk=${key}`

      script.onload = async () => {
        logger.log('天地图主脚本加载完成，等待关键组件...')

        try {
          await this.waitForAllComponents(this.criticalComponents)

          logger.log('天地图所有关键组件加载完成')
          this.loaded = true
          this.loading = false
          this.callbacks.forEach((callback) => callback())
          this.callbacks = []
        } catch (error) {
          logger.error('天地图关键组件加载失败，应用无法正常运行:', error)
          this.loading = false

          const componentError = new Error(`天地图关键组件加载失败: ${(error as Error).message}`)

          this.callbacks.forEach((callback) => callback(componentError))
          this.callbacks = []
        }
      }

      script.onerror = () => {
        this.loading = false

        if (this.retryCount < this.maxRetries) {
          this.retryCount++
          logger.warn(`天地图脚本加载失败，正在进行第 ${this.retryCount} 次重试...`)

          setTimeout(() => {
            if (script.parentNode) {
              script.parentNode.removeChild(script)
            }

            this.loadWithRetry(key).then(
              () => {
                this.callbacks.forEach((callback) => callback())
                this.callbacks = []
              },
              (error) => {
                this.callbacks.forEach((callback) => callback(error))
                this.callbacks = []
              },
            )
          }, this.retryDelay)
        } else {
          const error = new Error(`天地图脚本加载失败，已重试 ${this.retryCount} 次`)
          this.callbacks.forEach((callback) => callback(error))
          this.callbacks = []
        }
      }

      document.body.appendChild(script)
    })
  }

  /**
   * 重置加载状态
   */
  reset(): void {
    this.loaded = false
    this.loading = false
    this.callbacks = []
    this.retryCount = 0
  }
}
