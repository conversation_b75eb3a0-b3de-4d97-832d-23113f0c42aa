<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="45px" viewBox="0 0 31 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <path d="M16.1111111,41.1111111 C13.3496911,41.1111111 11.1111111,40.6136489 11.1111111,40 C11.1111111,39.3863511 13.3496911,38.8888889 16.1111111,38.8888889 C18.8725311,38.8888889 21.1111111,39.3863511 21.1111111,40 C21.1111111,40.6136489 18.8725311,41.1111111 16.1111111,41.1111111 C16.1111111,41.1111111 16.1111111,41.1111111 16.1111111,41.1111111 Z" id="path-1"></path>
        <filter x="-19.6%" y="-88.1%" width="139.2%" height="276.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.301285714" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="0.7532143" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.815686285   0 0 0 0 0.815686285  0 0 0 0.239215687 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M16,0 C7.7179,0 1,6.7 1,14.96 C1,18.24 2.06284,21.28 3.86764,23.76 C3.86764,23.76 7.91176,29.1733333 16,40 L27.87166,24.12 L27.85162,24.12 C29.81686,21.58 31,18.42 31,14.96 C31,14.96 31,14.96 31,14.96 C31,6.7 24.2821,0 16,0 C16,0 16,0 16,0 Z" id="path-3"></path>
        <filter x="-27.6%" y="-20.7%" width="155.2%" height="141.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="6.77892876" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.266666681   0 0 0 0 0.0549019612  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="3.0128572" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="7.53214312" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.4627451   0 0 0 0 0.4627451  0 0 0 0.501960814 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="流域一张图图标" transform="translate(-30.000000, -90.000000)">
            <g id="流域红标" transform="translate(30.000000, 90.000000)">
                <path d="M16.1111111,44.4444444 C8.4405,44.4444444 2.22222222,42.4545956 2.22222222,40 C2.22222222,37.5454044 8.4405,35.5555556 16.1111111,35.5555556 C23.7817222,35.5555556 30,37.5454044 30,40 C30,42.4545956 23.7817222,44.4444444 16.1111111,44.4444444 C16.1111111,44.4444444 16.1111111,44.4444444 16.1111111,44.4444444 Z" id="椭圆形备份-8" stroke="#FF7575" stroke-width="0.602571428" fill-opacity="0.101960786" fill="#FF4C4C" stroke-dasharray="1,3"></path>
                <g id="椭圆形">
                    <use fill-opacity="0.58431375" fill="#FF4C4C" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use stroke="#FF7575" stroke-width="0.602571428" xlink:href="#path-1"></use>
                </g>
                <g id="路径">
                    <use fill="#981919" fill-rule="evenodd" xlink:href="#path-3"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <path stroke="#FF8484" stroke-width="0.7532143" d="M27.1199,24.4966071 L27.87166,24.4966071 L27.87166,24.12 L27.5700251,23.8945021 L27.1199,24.4966071 Z M16.0001048,39.3708832 L15.6983651,39.7745021 L16.30171,39.7746026 L16.0001048,39.3708832 Z" stroke-linejoin="square"></path>
                </g>
                <path d="M15.9319422,5.51581281 C15.575068,5.92843468 15.2182348,6.4786393 13.8357521,8.724799 C13.1665668,9.87080229 12.5869588,10.9251961 12.051627,11.9795899 C11.0258135,14.0425728 10,16.8389793 10,18.6267545 C10.0445632,20.1855904 10.6244171,21.5149812 11.7841251,22.6151797 C12.9881502,23.7154204 14.3708379,24.265625 16.0210685,24.265625 C17.6712991,24.265625 19.098509,23.7154204 20.258053,22.5694171 C21.4622831,21.4232031 22.042178,20.0938545 21.9976149,18.5352293 C21.9975739,13.8134227 15.8871741,5.47000808 15.9319422,5.51581281 Z" id="路径" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>
