<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="45px" viewBox="0 0 31 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <path d="M15,41 C12.23858,41 10,40.552284 10,40 C10,39.447716 12.23858,39 15,39 C17.76142,39 20,39.447716 20,40 C20,40.552284 17.76142,41 15,41 C15,41 15,41 15,41 Z" id="path-1"></path>
        <filter x="-19.6%" y="-97.9%" width="139.2%" height="295.8%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.301285714" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="0.7532143" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.964705884   0 0 0 0 0.815686285  0 0 0 0.239215687 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M15.0344828,0 C6.75238276,0 0.0344827586,6.7 0.0344827586,14.96 C0.0344827586,18.24 1.09732276,21.28 2.90212276,23.76 C2.90212276,23.76 6.94624276,29.1733333 15.0344828,40 L26.9061428,24.12 L26.8861028,24.12 C28.8513428,21.58 30.0344828,18.42 30.0344828,14.96 C30.0344828,14.96 30.0344828,14.96 30.0344828,14.96 C30.0344828,6.7 23.3165828,0 15.0344828,0 C15.0344828,0 15.0344828,0 15.0344828,0 Z" id="path-3"></path>
        <filter x="-27.6%" y="-20.7%" width="155.2%" height="141.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="6.77892876" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0235294122   0 0 0 0 0.411764711   0 0 0 0 0.890196085  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="3.0128572" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="7.53214312" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 0.290196091   0 0 0 0 0.576470613   0 0 0 0 0.937254906  0 0 0 1 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="流域一张图图标" transform="translate(-31.000000, -220.000000)">
            <g id="蓝" transform="translate(31.000000, 220.000000)">
                <path d="M15,44 C7.268024,44 1,41.985278 1,39.5 C1,37.014722 7.268024,35 15,35 C22.731976,35 29,37.014722 29,39.5 C29,41.985278 22.731976,44 15,44 C15,44 15,44 15,44 Z" id="椭圆形备份-8" stroke="#7DB7FF" stroke-width="0.602571428" fill-opacity="0.101960786" fill="#4296FF" stroke-dasharray="1,3"></path>
                <g id="椭圆形">
                    <use fill-opacity="0.580392182" fill="#4497FF" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use stroke="#88BDFF" stroke-width="0.602571428" xlink:href="#path-1"></use>
                </g>
                <g id="路径">
                    <use fill="#0B4389" fill-rule="evenodd" xlink:href="#path-3"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <path stroke="#3F91F7" stroke-width="0.7532143" d="M26.1543827,24.4966071 L26.9061428,24.4966071 L26.9061428,24.12 L26.6045078,23.8945021 L26.1543827,24.4966071 Z M15.0345875,39.3708832 L14.7328478,39.7745021 L15.3361928,39.7746026 L15.0345875,39.3708832 Z" stroke-linejoin="square"></path>
                </g>
                <path d="M15.4296736,6.6411722 C15.0609036,7.06317184 14.692176,7.6258811 13.2636105,9.92308989 C12.572119,11.0951387 11.9731908,12.173496 11.4200146,13.2518533 C10.3600073,15.3617222 9.3,18.2216834 9.3,20.0500899 C9.3460486,21.6443538 9.94523103,23.003958 11.1435959,24.1291611 C12.3877552,25.2544072 13.8165325,25.8171165 15.5217708,25.8171165 C17.2270091,25.8171165 18.7017926,25.2544072 19.8999881,24.0823584 C21.1443592,22.9100941 21.743584,21.550533 21.6975354,19.9564845 C21.697493,15.1273641 15.3834132,6.59432644 15.4296736,6.6411722 Z" id="路径" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>
