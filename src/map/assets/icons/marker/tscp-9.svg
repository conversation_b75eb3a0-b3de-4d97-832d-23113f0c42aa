<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="45px" viewBox="0 0 31 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <path d="M16,41 C13.23858,41 11,40.552284 11,40 C11,39.447716 13.23858,39 16,39 C18.76142,39 21,39.447716 21,40 C21,40.552284 18.76142,41 16,41 C16,41 16,41 16,41 Z" id="path-1"></path>
        <filter x="-19.6%" y="-97.9%" width="139.2%" height="295.8%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.301285714" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="0.7532143" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.964705884   0 0 0 0 0.815686285  0 0 0 0.239215687 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M16.0344828,0 C7.75238276,0 1.03448276,6.7 1.03448276,14.96 C1.03448276,18.24 2.09732276,21.28 3.90212276,23.76 C3.90212276,23.76 7.94624276,29.1733333 16.0344828,40 L27.9061428,24.12 L27.8861028,24.12 C29.8513428,21.58 31.0344828,18.42 31.0344828,14.96 C31.0344828,14.96 31.0344828,14.96 31.0344828,14.96 C31.0344828,6.7 24.3165828,0 16.0344828,0 C16.0344828,0 16.0344828,0 16.0344828,0 Z" id="path-3"></path>
        <filter x="-27.6%" y="-20.7%" width="155.2%" height="141.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="6.77892876" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0235294122   0 0 0 0 0.411764711   0 0 0 0 0.890196085  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="3.0128572" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="7.53214312" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 0.290196091   0 0 0 0 0.576470613   0 0 0 0 0.937254906  0 0 0 1 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="特色产品图标" transform="translate(-90.000000, -78.000000)">
            <g id="蓝复制-2" transform="translate(90.000000, 78.000000)">
                <path d="M16,44 C8.268024,44 2,41.985278 2,39.5 C2,37.014722 8.268024,35 16,35 C23.731976,35 30,37.014722 30,39.5 C30,41.985278 23.731976,44 16,44 C16,44 16,44 16,44 Z" id="椭圆形备份-8" stroke="#7DB7FF" stroke-width="0.602571428" fill-opacity="0.101960786" fill="#4296FF" stroke-dasharray="1,3"></path>
                <g id="椭圆形">
                    <use fill-opacity="0.580392182" fill="#4497FF" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use stroke="#88BDFF" stroke-width="0.602571428" xlink:href="#path-1"></use>
                </g>
                <g id="路径">
                    <use fill="#0B4389" fill-rule="evenodd" xlink:href="#path-3"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <path stroke="#3F91F7" stroke-width="0.7532143" d="M27.1543827,24.4966071 L27.9061428,24.4966071 L27.9061428,24.12 L27.6045078,23.8945021 L27.1543827,24.4966071 Z M16.0345875,39.3708832 L15.7328478,39.7745021 L16.3361928,39.7746026 L16.0345875,39.3708832 Z" stroke-linejoin="square"></path>
                </g>
                <g id="youyeyoujingdeputao" transform="translate(7.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                    <path d="M2.94182008,20.6451169 C4.06573677,20.6451169 5.04242262,20.007235 5.53271857,19.075291 C5.04708323,18.640878 4.68519813,18.0737042 4.50405585,17.4292975 C4.26339093,17.4823396 4.01447008,17.5123007 3.7582698,17.5123007 C2.47669141,17.5123007 1.35823429,16.8119667 0.757859234,15.7761133 C0.297124859,16.2934851 0.014603246,16.9725135 0.014603246,17.7182995 C0.0142925403,19.3321571 1.32760746,20.6451169 2.94182008,20.6451169 L2.94182008,20.6451169 Z" id="路径"></path>
                    <path d="M1.04761589,15.1457772 C1.08256786,15.2314769 1.12170485,15.3154097 1.16488565,15.3972724 C1.65447141,16.3313914 2.63226694,16.9710931 3.75795909,16.9710931 C3.97767347,16.9710931 4.19126246,16.9447718 4.39766083,16.8985652 C4.38212546,16.7654494 4.37204968,16.6304693 4.37204968,16.493403 C4.37204968,16.0734157 4.45070298,15.672559 4.58816871,15.2998879 C4.61992116,15.2135035 4.65507327,15.128407 4.69354284,15.0447973 C4.73140474,14.9629038 4.77077577,14.8820755 4.81476302,14.8037773 C5.02265235,14.432697 5.29654025,14.1026867 5.62295681,13.8299766 C4.15051542,13.7552737 2.91660841,12.7623867 2.49085079,11.4101002 C1.5105696,11.8838399 0.831186129,12.8846721 0.831186129,14.0443202 C0.831186129,14.334787 0.875217782,14.6150893 0.954226169,14.8803001 C0.981192453,14.9702025 1.01235653,15.058792 1.04761589,15.1457772 L1.04761589,15.1457772 Z M7.20756351,14.4036308 C7.09390298,14.1825398 7.00450536,13.9497964 6.94093234,13.7094666 L6.9380472,13.7105319 C6.84819652,13.7396394 6.75982084,13.7731173 6.67323591,13.8108459 C6.06132028,14.0781873 5.55837411,14.5483316 5.24917607,15.1364116 C5.20538418,15.2194096 5.16577131,15.3045462 5.13048593,15.3915022 C5.09288771,15.4837868 5.06013015,15.5779703 5.03234683,15.6736687 C4.95622357,15.9341745 4.91330159,16.2087508 4.91330159,16.4937581 C4.91330159,16.5727665 4.91907188,16.6499994 4.92519724,16.727188 C4.93278738,16.8217317 4.9450381,16.9144556 4.96128367,17.0060699 C4.97706929,17.095262 4.99717049,17.1836362 5.02151647,17.2708812 C5.15311669,17.7482438 5.40377381,18.1843737 5.74999167,18.538389 C5.88512926,18.6776055 6.03405633,18.8027376 6.1944805,18.911859 C6.6638703,19.2325976 7.23068899,19.4205755 7.84082915,19.4205755 C9.45468669,19.4205755 10.7676465,18.1076157 10.7676465,16.493403 C10.7676465,16.4122197 10.7618762,16.3325011 10.7553958,16.2527381 C10.6027942,16.2732892 10.4479732,16.2877149 10.2899564,16.2877149 C8.94774571,16.2877149 7.78414728,15.5206676 7.20756351,14.4036308 L7.20756351,14.4036308 Z" id="形状"></path>
                    <path d="M2.93138919,10.958332 C2.94993209,11.046727 2.97244415,11.1342426 2.99885704,11.2206132 C3.36398238,12.4210082 4.48066401,13.2973801 5.79903903,13.2973801 C6.06207486,13.2973801 6.31570071,13.2591187 6.55818546,13.1938258 C6.64550253,13.1702185 6.73170172,13.1426555 6.81651631,13.1112221 C6.82282868,13.1087669 6.82920804,13.1064875 6.835647,13.1043865 C6.82805685,13.0101978 6.82122131,12.9156985 6.82122131,12.8197344 C6.82122131,12.6985142 6.82770177,12.5787144 6.8399969,12.4603793 C6.92588524,11.6305248 7.30650155,10.8876683 7.87403042,10.3356303 C7.52297569,9.84311506 7.29749103,9.25645548 7.24444889,8.62039349 C6.80391048,8.82350722 6.31539,8.94006679 5.79908341,8.94006679 C5.03780639,8.94006679 4.33494234,8.69039137 3.76235339,8.27257897 C3.21431028,8.80477601 2.87226605,9.54763252 2.87226605,10.3702964 C2.87226605,10.4785556 2.87910161,10.5849951 2.89068655,10.6903248 C2.89991346,10.780266 2.91349696,10.8697068 2.93138919,10.958332 L2.93138919,10.958332 Z M11.1882997,15.6040259 C11.7261971,15.4293262 12.2021683,15.1029438 12.5589622,14.6641366 C11.7973301,14.02732 11.3112953,13.0711852 11.3112953,12.0032402 C11.3112953,11.9137566 11.3181309,11.8257377 11.3246557,11.7376744 C11.1207874,11.7751811 10.9119034,11.7975521 10.6976042,11.7975521 C10.1246602,11.7975521 9.584873,11.6553814 9.10829256,11.408591 C9.02528935,11.3656691 8.94521567,11.3183972 8.86620726,11.2693055 C8.78484418,11.2183796 8.70563418,11.1640921 8.62878258,11.1065837 C8.48372671,10.9983244 8.34626097,10.881099 8.21998069,10.7519336 C7.69031369,11.2819557 7.36234006,12.0132716 7.36234006,12.8200451 C7.36234006,12.8268806 7.36340534,12.8337606 7.36340534,12.8405961 C7.36376044,12.9148995 7.36846542,12.9877825 7.37459081,13.0606656 C7.37712085,13.089517 7.37854123,13.1184127 7.38182585,13.146909 C7.38866141,13.2082515 7.39913667,13.2684843 7.40961196,13.3287615 C7.41573732,13.3630281 7.42044232,13.3972946 7.42763296,13.4312505 C7.43917351,13.4861125 7.45470887,13.5395097 7.46948966,13.5932621 C7.80971403,14.8326287 8.94370651,15.7472619 10.2895125,15.7472619 C10.4154377,15.7472619 10.5384778,15.7364315 10.6604081,15.7209406 C10.7509571,15.7094 10.8404408,15.6946192 10.9281046,15.6747783 C11.0158038,15.6549086 11.1026146,15.6313029 11.1882997,15.6040259 L11.1882997,15.6040259 Z M3.96759768,7.75152302 C4.04268796,7.8118553 4.12071126,7.86844331 4.20138264,7.92108038 C4.66140683,8.22233321 5.20980502,8.3987705 5.79899464,8.39877048 C6.32542139,8.39877048 6.81824738,8.25735433 7.24542538,8.01309409 C7.25084056,7.95534694 7.25949599,7.89906456 7.26779631,7.84242708 C7.28151181,7.74575276 7.29775736,7.65049881 7.31937371,7.55666522 C7.33992478,7.46718155 7.3612304,7.37805296 7.38866141,7.29109935 C7.62209129,6.54855355 8.09978139,5.91426702 8.72576762,5.47949887 C8.72576762,5.47696883 8.72612272,5.47443879 8.7261227,5.47155365 C8.7261227,3.85769611 7.41280778,2.54473629 5.79895026,2.54473629 C4.18509274,2.54473629 2.8721329,3.85769611 2.8721329,5.47155365 C2.8721329,6.29381804 3.21417714,7.03707403 3.76222022,7.56927107 C3.82766828,7.63333248 3.89620926,7.69415574 3.96759768,7.75152302 L3.96759768,7.75152302 Z M7.88699135,7.5195579 C7.88015579,7.54410377 7.87434113,7.56900474 7.86786067,7.59350621 C7.84291072,7.689352 7.82292489,7.78642179 7.80798296,7.88432815 C7.80363306,7.91211425 7.79861736,7.93954524 7.79533274,7.96733135 C7.78545605,8.05072645 7.77907357,8.13449834 7.77620206,8.21842716 C7.7747373,8.25269373 7.77114198,8.2866052 7.77114196,8.32162633 C7.77114196,8.32415637 7.77078685,8.32704151 7.77078687,8.32957155 C7.77078687,8.94619218 7.96346972,9.51807095 8.29068877,9.99034589 C8.39325351,10.1391433 8.50932858,10.27816 8.637438,10.4056282 C8.66415883,10.4319496 8.6930102,10.4557853 8.72079629,10.4813964 C8.87637777,10.624534 9.04695229,10.7504633 9.22955714,10.856997 C9.66179522,11.1092025 10.1625665,11.2560338 10.6980037,11.2560338 C10.947324,11.2560338 11.187989,11.2214122 11.4192439,11.1625997 C11.4960773,10.8555767 11.6122818,10.5643996 11.7648834,10.2955935 C11.8096253,10.2169402 11.8554324,10.138642 11.9059445,10.063584 C11.9564566,9.98888113 12.0116737,9.91781794 12.0679561,9.84710986 C12.4654566,9.34831267 12.9924246,8.96838408 13.5912647,8.74884871 C13.6111056,8.61138296 13.6248211,8.47174228 13.6248211,8.32886137 C13.6248211,7.54627873 13.3141583,6.83586893 12.8119223,6.31019675 C12.8483638,6.21782794 12.8833405,6.12510403 12.9234218,6.03562036 C12.9616831,5.94973202 13.0046051,5.86748339 13.0461067,5.78412508 C14.1104564,3.64495044 16.1872233,2.36372716 18.3733147,1.45055871 C19.2190153,1.09732901 18.8481197,-0.299699294 17.9897689,0.0589455847 C15.4078809,1.13767657 13.0962194,2.59569226 11.8139308,5.04876994 C11.7724292,5.12813343 11.7345673,5.21113663 11.6952406,5.29231998 C11.655914,5.37421353 11.6137022,5.45322192 11.5765505,5.5372904 C11.2922367,5.44746572 10.9958143,5.40183695 10.6976486,5.40199962 C10.1716213,5.40199962 9.67875095,5.54341577 9.25157296,5.78767603 C9.14730851,5.84719865 9.04770466,5.91395631 8.95174052,5.98537458 C8.83052034,6.07592353 8.7154255,6.17370752 8.61005137,6.2812122 C8.2701911,6.62773965 8.0217317,7.05326259 7.88699135,7.5195579 Z M12.8634997,14.210948 C12.9325326,14.2707515 13.0042938,14.327331 13.0785534,14.3805053 C13.1515325,14.4327898 13.2268931,14.4816676 13.3043932,14.5269816 C13.738096,14.7817171 14.2413973,14.9300132 14.7797197,14.9300132 C16.3935772,14.9300132 17.7065371,13.6170534 17.7065371,12.0031958 C17.7065371,10.3889832 16.3932221,9.07602339 14.7797197,9.07602339 C14.5303994,9.07602339 14.2897345,9.11064504 14.0584795,9.16945748 C13.9558249,9.19562662 13.8546542,9.22730544 13.7554069,9.26435633 C13.637782,9.30838798 13.5226872,9.35814552 13.4126524,9.41660286 C13.0651749,9.60118142 12.7581222,9.8533914 12.5095598,10.1583941 C12.4526658,10.2280709 12.3989762,10.3003038 12.3486579,10.3748682 C12.2971489,10.4514882 12.2492333,10.5304636 12.2050668,10.6115383 C12.1520246,10.7093223 12.1062175,10.8110567 12.0643608,10.9153212 C12.0177991,11.0307711 11.9802923,11.1502158 11.9485558,11.2721462 C11.9221774,11.3745019 11.9013445,11.4782071 11.886148,11.582809 C11.8663071,11.7202748 11.8525916,11.8599154 11.8525916,12.0027964 C11.8525472,12.8842727 12.2458138,13.6737353 12.8634997,14.210948 L12.8634997,14.210948 Z" id="形状"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
