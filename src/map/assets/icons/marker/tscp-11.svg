<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="45px" viewBox="0 0 31 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <path d="M16,41 C13.23858,41 11,40.552284 11,40 C11,39.447716 13.23858,39 16,39 C18.76142,39 21,39.447716 21,40 C21,40.552284 18.76142,41 16,41 C16,41 16,41 16,41 Z" id="path-1"></path>
        <filter x="-19.6%" y="-97.9%" width="139.2%" height="295.8%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.301285714" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="0.7532143" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.964705884   0 0 0 0 0.815686285  0 0 0 0.239215687 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M16.0344828,0 C7.75238276,0 1.03448276,6.7 1.03448276,14.96 C1.03448276,18.24 2.09732276,21.28 3.90212276,23.76 C3.90212276,23.76 7.94624276,29.1733333 16.0344828,40 L27.9061428,24.12 L27.8861028,24.12 C29.8513428,21.58 31.0344828,18.42 31.0344828,14.96 C31.0344828,14.96 31.0344828,14.96 31.0344828,14.96 C31.0344828,6.7 24.3165828,0 16.0344828,0 C16.0344828,0 16.0344828,0 16.0344828,0 Z" id="path-3"></path>
        <filter x="-27.6%" y="-20.7%" width="155.2%" height="141.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="6.77892876" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0235294122   0 0 0 0 0.411764711   0 0 0 0 0.890196085  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="3.0128572" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="7.53214312" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 0.290196091   0 0 0 0 0.576470613   0 0 0 0 0.937254906  0 0 0 1 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="特色产品图标" transform="translate(-90.000000, -192.000000)">
            <g id="蓝复制-4" transform="translate(90.000000, 192.000000)">
                <path d="M16,44 C8.268024,44 2,41.985278 2,39.5 C2,37.014722 8.268024,35 16,35 C23.731976,35 30,37.014722 30,39.5 C30,41.985278 23.731976,44 16,44 C16,44 16,44 16,44 Z" id="椭圆形备份-8" stroke="#7DB7FF" stroke-width="0.602571428" fill-opacity="0.101960786" fill="#4296FF" stroke-dasharray="1,3"></path>
                <g id="椭圆形">
                    <use fill-opacity="0.580392182" fill="#4497FF" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use stroke="#88BDFF" stroke-width="0.602571428" xlink:href="#path-1"></use>
                </g>
                <g id="路径">
                    <use fill="#0B4389" fill-rule="evenodd" xlink:href="#path-3"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <path stroke="#3F91F7" stroke-width="0.7532143" d="M27.1543827,24.4966071 L27.9061428,24.4966071 L27.9061428,24.12 L27.6045078,23.8945021 L27.1543827,24.4966071 Z M16.0345875,39.3708832 L15.7328478,39.7745021 L16.3361928,39.7746026 L16.0345875,39.3708832 Z" stroke-linejoin="square"></path>
                </g>
                <g id="shucai-2" transform="translate(6.000000, 6.000000)" fill="#FFFFFF" fill-rule="nonzero">
                    <path d="M14.2645,3.29636655 L14.2695,3.29636655 C16.6325,3.29636655 18.5695,5.07117189 18.7505,7.3256139 C19.5175,8.00588939 20,8.98662407 20,10.0761629 C20,12.1299661 18.29,13.7944699 16.18,13.7944699 C15.2203977,13.7976022 14.294023,13.4438743 13.5815,12.8022559 C12.6624369,13.7214997 11.4135423,14.2361159 10.1125,14.231683 C8.75772022,14.2366491 7.46197174,13.6786321 6.53600001,12.6914554 C5.81140519,13.4016481 4.8354882,13.7979848 3.82,13.7944699 C1.71,13.7944699 0,12.1299661 0,10.0761629 C0,8.87432621 0.586000007,7.80525054 1.49500001,7.12597326 C1.77050001,4.96735876 3.6625,3.29636655 5.955,3.29636655 L5.96000001,3.29636655 C6.80399999,1.9213416 8.34800001,1 10.113,1 C11.878,1 13.422,1.92134158 14.265,3.29636655 L14.2645,3.29636655 Z M8.48999999,21 L11.941,21 C11.941,18.2384708 13.3845,15.8931923 15.118,14.6993412 C14.1865,14.6030146 13.584,14.3844081 12.9815,14.015572 C12.069,14.8780196 11.229,15.8971851 11.229,16.4072669 L11.229,15.204432 C11.229,15.204432 10.599,15.3651427 10.188,15.3681373 C9.7555,15.371631 9.0925,15.204432 9.0925,15.204432 L9.0925,16.4072669 C8.78249999,15.6785785 8.65400001,15.5323418 7.77749999,14.6030146 C7.22999999,14.4388101 6.5725,14.0559992 6.3535,13.7829906 C6.08000001,14.0559992 5.368,14.4388101 4.9845,14.5176682 C6.487,15.700539 8.4895,18.0113795 8.4895,21 L8.48999999,21 Z" id="形状"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
