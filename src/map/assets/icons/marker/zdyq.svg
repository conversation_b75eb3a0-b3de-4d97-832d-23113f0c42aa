<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="61px" viewBox="0 0 32 61" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F8DC97" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFF4C9" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-2" cx="16" cy="58" rx="3" ry="1"></ellipse>
        <filter x="-8.3%" y="-25.0%" width="116.7%" height="200.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.17254902   0 0 0 0 0.109803922   0 0 0 0 0.0509803922  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="28.265625%" y1="31.8632812%" x2="106.347656%" y2="84.3828125%" id="linearGradient-4">
            <stop stop-color="#D23B17" offset="0%"></stop>
            <stop stop-color="#FFD04F" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="5.79296875%" y1="28.4726562%" x2="92.7578125%" y2="80.1484375%" id="linearGradient-5">
            <stop stop-color="#FBEEE8" offset="0%"></stop>
            <stop stop-color="#E4CF81" stop-opacity="0.291111233" offset="66.411254%"></stop>
            <stop stop-color="#FFF23A" offset="100%"></stop>
        </linearGradient>
        <filter x="-52.8%" y="-52.8%" width="205.6%" height="205.6%" filterUnits="objectBoundingBox" id="filter-6">
            <feGaussianBlur stdDeviation="3" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="5.79296875%" y1="28.4726562%" x2="92.7578125%" y2="80.1484375%" id="linearGradient-7">
            <stop stop-color="#FBEEE8" offset="0%"></stop>
            <stop stop-color="#FCED8E" offset="37.6761573%"></stop>
            <stop stop-color="#E98533" offset="56.0097838%"></stop>
            <stop stop-color="#E97E1C" offset="68.0606597%"></stop>
            <stop stop-color="#FFE982" offset="100%"></stop>
        </linearGradient>
        <circle id="path-8" cx="16" cy="16" r="9"></circle>
        <filter x="-15.3%" y="-15.3%" width="130.6%" height="130.6%" filterUnits="objectBoundingBox" id="filter-9">
            <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.109233469   0 0 0 0 0   0 0 0 0 0  0 0 0 0.607599432 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="5.79296875%" y1="28.4726562%" x2="92.7578125%" y2="80.1484375%" id="linearGradient-10">
            <stop stop-color="#FBEEE8" offset="0%"></stop>
            <stop stop-color="#FCED8E" offset="37.6761573%"></stop>
            <stop stop-color="#FF4B01" offset="56.0097838%"></stop>
            <stop stop-color="#E97E1C" offset="68.0606597%"></stop>
            <stop stop-color="#FFE982" offset="100%"></stop>
        </linearGradient>
        <circle id="path-11" cx="9" cy="9" r="9"></circle>
        <filter x="-416.7%" y="-148.8%" width="933.3%" height="397.6%" filterUnits="objectBoundingBox" id="filter-13">
            <feGaussianBlur stdDeviation="2.5" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-84.1%" y="-61.4%" width="268.2%" height="222.8%" filterUnits="objectBoundingBox" id="filter-14">
            <feGaussianBlur stdDeviation="3" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-15">
            <stop stop-color="#F96F22" offset="0%"></stop>
            <stop stop-color="#FDE0D5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-16">
            <stop stop-color="#F7E035" offset="0%"></stop>
            <stop stop-color="#F7F481" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="5.79296875%" y1="28.4726562%" x2="92.7578125%" y2="80.1484375%" id="linearGradient-17">
            <stop stop-color="#FBEEE8" offset="0%"></stop>
            <stop stop-color="#FCED8E" offset="27.2746102%"></stop>
            <stop stop-color="#E98533" offset="45.2036409%"></stop>
            <stop stop-color="#E97E1C" offset="65.0676352%"></stop>
            <stop stop-color="#FFD100" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="11.9520399%" x2="87.9179688%" y2="82.1523438%" id="linearGradient-18">
            <stop stop-color="#F5B839" offset="0%"></stop>
            <stop stop-color="#E9882C" offset="27.8954027%"></stop>
            <stop stop-color="#EA5911" offset="60.3415272%"></stop>
            <stop stop-color="#E36725" offset="77.7657361%"></stop>
            <stop stop-color="#F38535" offset="100%"></stop>
        </linearGradient>
        <filter x="-448.5%" y="-184.5%" width="997.0%" height="469.0%" filterUnits="objectBoundingBox" id="filter-19">
            <feGaussianBlur stdDeviation="2.5" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-540.0%" y="-400.0%" width="1180.0%" height="900.0%" filterUnits="objectBoundingBox" id="filter-20">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-540.0%" y="-400.0%" width="1180.0%" height="900.0%" filterUnits="objectBoundingBox" id="filter-21">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-360.0%" y="-266.7%" width="820.0%" height="633.3%" filterUnits="objectBoundingBox" id="filter-22">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-540.0%" y="-400.0%" width="1180.0%" height="900.0%" filterUnits="objectBoundingBox" id="filter-23">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-122.1%" y="-67.0%" width="344.2%" height="234.0%" filterUnits="objectBoundingBox" id="filter-24">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-183.1%" y="-100.5%" width="466.3%" height="301.0%" filterUnits="objectBoundingBox" id="filter-25">
            <feGaussianBlur stdDeviation="3" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="特色产品图标" transform="translate(-87.000000, -392.000000)">
            <g id="定位标7" transform="translate(87.000000, 392.000000)">
                <ellipse id="椭圆形" stroke="url(#linearGradient-1)" fill-opacity="0.26447771" fill="#E6A41D" opacity="0.655552455" cx="16" cy="58" rx="9" ry="2"></ellipse>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill="#FDF0E7" fill-rule="evenodd" xlink:href="#path-2"></use>
                </g>
                <circle id="模糊" stroke="url(#linearGradient-5)" fill="url(#linearGradient-4)" opacity="0.662016369" filter="url(#filter-6)" cx="16" cy="16" r="9"></circle>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                    <use stroke="url(#linearGradient-7)" stroke-width="1" fill="url(#linearGradient-4)" fill-rule="evenodd" xlink:href="#path-8"></use>
                </g>
                <g id="椭圆形-+-椭圆形蒙版" transform="translate(7.000000, 7.000000)">
                    <mask id="mask-12" fill="white">
                        <use xlink:href="#path-11"></use>
                    </mask>
                    <use id="蒙版" stroke="url(#linearGradient-10)" fill="url(#linearGradient-4)" xlink:href="#path-11"></use>
                    <ellipse id="椭圆形" fill="#FFFFFF" opacity="0.879789807" filter="url(#filter-13)" mask="url(#mask-12)" transform="translate(14.979323, 14.063174) rotate(38.000000) translate(-14.979323, -14.063174) " cx="14.9793234" cy="14.0631736" rx="1" ry="2.52"></ellipse>
                    <path d="M7.29789687,17.4792721 C12.5573859,16.5782014 16.56,11.996531 16.56,6.48 C16.56,5.25923576 16.3639914,4.08425179 16.0017289,2.98480285 C17.2514902,4.53020592 18,6.49769105 18,8.64 C18,13.6105627 13.9705627,17.64 9,17.64 C8.41808129,17.64 7.84906173,17.5847722 7.29789687,17.4792721 Z" id="形状结合" fill="#FFE477" opacity="0.655645461" filter="url(#filter-14)" mask="url(#mask-12)"></path>
                </g>
                <rect id="矩形" stroke="url(#linearGradient-16)" fill="url(#linearGradient-15)" x="16.5" y="25.5" width="1" height="33"></rect>
                <circle id="光环" stroke="url(#linearGradient-17)" cx="16" cy="16" r="9"></circle>
                <circle id="椭圆形" fill="url(#linearGradient-18)" cx="13.5" cy="14.5" r="6.5"></circle>
                <g id="编组" transform="translate(7.000000, 7.000000)">
                    <ellipse id="椭圆形" fill="#FFF8DE" opacity="0.701892671" filter="url(#filter-19)" transform="translate(2.159752, 4.771365) rotate(38.000000) translate(-2.159752, -4.771365) " cx="2.1597519" cy="4.77136482" rx="1" ry="2.03237831"></ellipse>
                    <ellipse id="椭圆形" fill="#FEFDFD" filter="url(#filter-20)" cx="1.69753086" cy="4.83014993" rx="1" ry="1"></ellipse>
                    <ellipse id="椭圆形" fill="#FEFDFD" filter="url(#filter-21)" cx="1.69753086" cy="4.83014993" rx="1" ry="1"></ellipse>
                    <ellipse id="椭圆形" fill="#FEFDFD" filter="url(#filter-22)" cx="1.55864198" cy="4.64264993" rx="1" ry="1"></ellipse>
                    <ellipse id="椭圆形" fill="#FEFDFD" filter="url(#filter-23)" cx="1.69753086" cy="4.83014993" rx="1" ry="1"></ellipse>
                    <path d="M0.0308641975,8.95514993 C0.0308641975,8.20514993 -0.24691358,5.95514993 0.864197531,3.70514993 C1.65954932,2.09456257 3.21151009,0.676122731 4.91413256,-6.63722521e-10 C2.08526102,1.18534946 0.0308641975,4.74636438 0.0308641975,8.95514993 Z" id="形状结合" fill="#FFFFFF" filter="url(#filter-24)"></path>
                    <path d="M0.0308641975,8.95514993 C0.0308641975,8.20514993 -0.24691358,5.95514993 0.864197531,3.70514993 C1.65954932,2.09456257 3.21151009,0.676122731 4.91413256,-5.22085652e-10 C2.08526102,1.18534946 0.0308641975,4.74636438 0.0308641975,8.95514993 Z" id="形状结合" fill="#FFFFFF" filter="url(#filter-25)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
