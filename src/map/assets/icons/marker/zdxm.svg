<?xml version="1.0" encoding="UTF-8"?>
<svg width="57px" height="61px" viewBox="0 0 57 61" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <linearGradient x1="50%" y1="14.2857143%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#00428C" offset="0%"></stop>
            <stop stop-color="#17D7FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="14.2857143%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#00428C" offset="0%"></stop>
            <stop stop-color="#17D7FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="73.453776%" id="linearGradient-3">
            <stop stop-color="#3EB0FC" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="65.5113999%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#1CA1EB" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#00CAFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="90.3567136%" id="linearGradient-5">
            <stop stop-color="#0013CC" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#31E5FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#1CA1EB" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#2BC0F1" stop-opacity="0.405512456" offset="46.181884%"></stop>
            <stop stop-color="#37D8F6" offset="66.4448832%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#0013CC" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#31E5FF" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-8" points="0 0 0 27.4285714 16 39 32 27.4285714 32 0"></polygon>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="75.0171703%" id="linearGradient-10">
            <stop stop-color="#0A254A" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <filter x="-230.4%" y="-250.3%" width="560.8%" height="600.7%" filterUnits="objectBoundingBox" id="filter-11">
            <feGaussianBlur stdDeviation="10" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-230.4%" y="-250.3%" width="560.8%" height="600.7%" filterUnits="objectBoundingBox" id="filter-12">
            <feGaussianBlur stdDeviation="10" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-230.4%" y="-250.3%" width="560.8%" height="600.7%" filterUnits="objectBoundingBox" id="filter-13">
            <feGaussianBlur stdDeviation="10" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="12.3759502%" x2="50%" y2="95.6529772%" id="linearGradient-14">
            <stop stop-color="#E8F5FF" offset="0%"></stop>
            <stop stop-color="#95D4FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="-242.648384%" x2="50%" y2="100%" id="linearGradient-15">
            <stop stop-color="#1CA1EB" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <filter x="-57.2%" y="-160.2%" width="214.3%" height="425.5%" filterUnits="objectBoundingBox" id="filter-16">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-57.2%" y="-160.2%" width="214.3%" height="425.5%" filterUnits="objectBoundingBox" id="filter-17">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="产业链图标" transform="translate(-20.000000, -20.000000)">
            <g id="定位标6" transform="translate(20.000000, 18.000000)">
                <g id="编组-8" transform="translate(11.000000, 52.000000)">
                    <ellipse id="椭圆形" stroke-opacity="0.835773601" stroke="#4999FF" fill="url(#linearGradient-1)" opacity="0.299967448" cx="18.5" cy="5" rx="18.5" ry="5"></ellipse>
                    <ellipse id="椭圆形" stroke-opacity="0.810915647" stroke="#4999FF" fill="url(#linearGradient-2)" opacity="0.696707589" cx="18.5" cy="4.52380952" rx="12.1710526" ry="3.0952381"></ellipse>
                    <ellipse id="椭圆形" fill="url(#linearGradient-3)" cx="18.5" cy="4.28571429" rx="5.35526316" ry="1.42857143"></ellipse>
                </g>
                <g id="编组-6" transform="translate(14.000000, 0.000000)">
                    <path d="M-0.5,9.51375743 L-0.5,37.443294 L15,51.3287107 L30.5,37.443294 L30.5,10.3552978 L-0.5,9.51375743 Z" id="路径-34" stroke="url(#linearGradient-4)"></path>
                    <path d="M-0.5,10.5 L-0.5,37.1731213 L15,48.3829427 L30.5,37.1731213 L30.5,10.5 L-0.5,10.5 Z" id="蒙版" stroke="url(#linearGradient-6)" fill="url(#linearGradient-5)"></path>
                    <g id="直线-+-直线复制-2-+-直线复制-6-+-直线复制-+-直线复制-3-+-直线复制-7-+-直线复制-+-直线复制-4-+-直线复制-+-直线复制-5蒙版" transform="translate(-1.000000, 10.000000)">
                        <mask id="mask-9" fill="white">
                            <use xlink:href="#path-8"></use>
                        </mask>
                        <use id="蒙版" fill="url(#linearGradient-7)" xlink:href="#path-8"></use>
                        <path d="M3.5,0 L3.5,39" id="直线" stroke="url(#linearGradient-10)" stroke-width="0.5" opacity="0.497070312" stroke-linecap="square" stroke-dasharray="3,3" mask="url(#mask-9)"></path>
                        <path d="M15.5,0 L15.5,39" id="直线复制-2" stroke="url(#linearGradient-10)" stroke-width="0.5" opacity="0.497070312" stroke-linecap="square" stroke-dasharray="3,3" mask="url(#mask-9)"></path>
                        <path d="M26.5,0 L26.5,39" id="直线复制-6" stroke="url(#linearGradient-10)" stroke-width="0.5" opacity="0.497070312" stroke-linecap="square" stroke-dasharray="3,3" mask="url(#mask-9)"></path>
                        <path d="M6.5,0 L6.5,39" id="直线复制" stroke="url(#linearGradient-10)" stroke-width="0.5" opacity="0.497070312" stroke-linecap="square" stroke-dasharray="3,3" mask="url(#mask-9)"></path>
                        <path d="M17.5,0 L17.5,39" id="直线复制-3" stroke="url(#linearGradient-10)" stroke-width="0.5" opacity="0.497070312" stroke-linecap="square" stroke-dasharray="3,3" mask="url(#mask-9)"></path>
                        <path d="M28.5,0 L28.5,39" id="直线复制-7" stroke="url(#linearGradient-10)" stroke-width="0.5" opacity="0.497070312" stroke-linecap="square" stroke-dasharray="3,3" mask="url(#mask-9)"></path>
                        <path d="M9.5,0 L9.5,39" id="直线复制" stroke="url(#linearGradient-10)" stroke-width="0.5" opacity="0.497070312" stroke-linecap="square" stroke-dasharray="3,3" mask="url(#mask-9)"></path>
                        <path d="M20.5,0 L20.5,39" id="直线复制-4" stroke="url(#linearGradient-10)" stroke-width="0.5" opacity="0.497070312" stroke-linecap="square" stroke-dasharray="3,3" mask="url(#mask-9)"></path>
                        <path d="M11.5,0 L11.5,39" id="直线复制" stroke="url(#linearGradient-10)" stroke-width="0.5" opacity="0.497070312" stroke-linecap="square" stroke-dasharray="3,3" mask="url(#mask-9)"></path>
                        <path d="M23.5,0 L23.5,39" id="直线复制-5" stroke="url(#linearGradient-10)" stroke-width="0.5" opacity="0.497070312" stroke-linecap="square" stroke-dasharray="3,3" mask="url(#mask-9)"></path>
                    </g>
                </g>
                <path d="M23.9048936,25.7563523 L23.9048936,25.7563523 C26.499674,23.3600348 30.500326,23.3600348 33.0951064,25.7563523 L33.0951064,25.7563523 C35.4388083,27.9207952 35.5841265,31.5753686 33.4196836,33.9190705 C33.3158343,34.0315205 33.2075565,34.1397984 33.0951064,34.2436477 L33.0951064,34.2436477 C30.500326,36.6399652 26.499674,36.6399652 23.9048936,34.2436477 L23.9048936,34.2436477 C21.5611917,32.0792048 21.4158735,28.4246314 23.5803164,26.0809295 C23.6841657,25.9684795 23.7924435,25.8602016 23.9048936,25.7563523 Z" id="模糊" fill="#203D9A" filter="url(#filter-11)"></path>
                <path d="M23.9048936,25.7563523 L23.9048936,25.7563523 C26.499674,23.3600348 30.500326,23.3600348 33.0951064,25.7563523 L33.0951064,25.7563523 C35.4388083,27.9207952 35.5841265,31.5753686 33.4196836,33.9190705 C33.3158343,34.0315205 33.2075565,34.1397984 33.0951064,34.2436477 L33.0951064,34.2436477 C30.500326,36.6399652 26.499674,36.6399652 23.9048936,34.2436477 L23.9048936,34.2436477 C21.5611917,32.0792048 21.4158735,28.4246314 23.5803164,26.0809295 C23.6841657,25.9684795 23.7924435,25.8602016 23.9048936,25.7563523 Z" id="模糊" fill="#203D9A" filter="url(#filter-12)"></path>
                <path d="M23.9048936,25.7563523 L23.9048936,25.7563523 C26.499674,23.3600348 30.500326,23.3600348 33.0951064,25.7563523 L33.0951064,25.7563523 C35.4388083,27.9207952 35.5841265,31.5753686 33.4196836,33.9190705 C33.3158343,34.0315205 33.2075565,34.1397984 33.0951064,34.2436477 L33.0951064,34.2436477 C30.500326,36.6399652 26.499674,36.6399652 23.9048936,34.2436477 L23.9048936,34.2436477 C21.5611917,32.0792048 21.4158735,28.4246314 23.5803164,26.0809295 C23.6841657,25.9684795 23.7924435,25.8602016 23.9048936,25.7563523 Z" id="模糊" fill="#203D9A" filter="url(#filter-13)"></path>
                <g id="xiangmu" transform="translate(19.000000, 16.000000)" fill="url(#linearGradient-14)" fill-rule="nonzero">
                    <path d="M17.7530317,13.130916 L17.8656578,13.1958159 L19.5776793,14.1750253 L19.6376764,14.2118884 C19.87332,14.371781 20.0095029,14.6396222 19.9985068,14.9215613 C19.9875108,15.2035004 19.8308691,15.4602268 19.5834685,15.6017842 L10.417601,20.8223305 C10.1581014,20.9742003 9.83569719,20.9759871 9.57448433,20.8270033 L0.336515094,15.5654403 L0.169154858,15.392547 L0.153366156,15.3722982 L0.111262944,15.3073984 C-0.117702437,14.9142229 0.0193594639,14.4123934 0.417563753,14.1859284 L2.13800593,13.2061999 L2.250632,13.1423384 L2.36325806,13.2061999 L3.57793551,13.8982922 L3.91581372,14.0903958 L3.9105508,14.0929918 C5.0741781,14.7752193 8.92188466,17.0301008 9.54606467,17.3940593 L9.60921948,17.4309224 L9.63184994,17.4439024 C9.8402608,17.5383966 10.1370884,17.552415 10.3702349,17.422096 C10.3791818,17.416904 12.5596015,16.1391552 16.909915,13.5878112 L16.9288615,13.5997527 L17.6398793,13.1952967 L17.7530317,13.130916 L17.7530317,13.130916 Z M17.7456636,8.72135857 L17.858816,8.78625845 L19.5776793,9.76962142 L19.6376764,9.80596537 C19.8738195,9.96564693 20.0103853,10.2336998 19.9993822,10.5159256 C19.9883791,10.7981514 19.8313465,11.0550642 19.5834685,11.1963804 L10.417601,16.4169267 C10.1581014,16.5687964 9.83569719,16.5705833 9.57448433,16.4215995 L0.339146527,11.1610749 L0.17441775,10.9938927 L0.153366156,10.9679328 L0.111262944,10.9019945 C-0.117702437,10.5088191 0.0193594639,10.0069895 0.417563753,9.7805246 L2.1448477,8.79664244 L2.25747376,8.73278096 L2.37009985,8.79664244 L3.58477727,9.48873475 L3.92318177,9.68083839 L3.89423582,9.69745276 L6.30096021,11.0930598 L6.57463105,11.2514155 L6.84724928,11.409252 C7.74549873,11.9309338 8.64440282,12.4515177 9.5439595,12.9710027 C9.81236742,13.1059945 10.2586614,13.0789961 10.4844398,12.9710027 C10.588119,12.9211596 11.9701566,12.1112091 13.436927,11.2472619 L13.712703,11.0852718 C14.5873971,10.5707456 15.4652489,10.0520657 16.0978495,9.67928079 L16.0804819,9.67045442 L16.4173076,9.47835076 L17.6330376,8.78573926 L17.7456636,8.72135857 L17.7456636,8.72135857 Z M10.0270938,0.0284090909 C10.1734024,0.0284090909 10.3176059,0.0668298277 10.4444418,0.139517701 L19.5750479,5.36266 C19.8351984,5.50798977 19.9966952,5.77948161 19.9984339,6.07441318 C20.0001727,6.36934474 19.8418879,6.64267139 19.5834685,6.79097655 L10.417601,12.0115229 C10.1581014,12.1633926 9.83569719,12.1651795 9.57448433,12.0161957 L0.335462505,6.75359422 L0.128104234,6.63573603 L0.166523399,6.57862416 L0.15283985,6.56149059 L0.110736662,6.4965907 C-0.11814067,6.10327936 0.0191682508,5.60140769 0.417563753,5.37512078 L9.56711627,0.164439255 C9.70395169,0.0756562195 9.86289127,0.0284091152 10.0270938,0.0284090909 Z" id="形状"></path>
                </g>
                <polyline id="路径-34" stroke="url(#linearGradient-15)" filter="url(#filter-16)" points="23 46 28.6651715 50 34 46.2332449"></polyline>
                <polyline id="路径-34" stroke="url(#linearGradient-15)" filter="url(#filter-17)" points="23 46 28.6651715 50 34 46.2332449"></polyline>
            </g>
        </g>
    </g>
</svg>
