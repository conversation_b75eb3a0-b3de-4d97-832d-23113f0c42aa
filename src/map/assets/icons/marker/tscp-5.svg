<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="45px" viewBox="0 0 31 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <path d="M16.1111111,41.1111111 C13.3496911,41.1111111 11.1111111,40.6136489 11.1111111,40 C11.1111111,39.3863511 13.3496911,38.8888889 16.1111111,38.8888889 C18.8725311,38.8888889 21.1111111,39.3863511 21.1111111,40 C21.1111111,40.6136489 18.8725311,41.1111111 16.1111111,41.1111111 C16.1111111,41.1111111 16.1111111,41.1111111 16.1111111,41.1111111 Z" id="path-1"></path>
        <filter x="-19.6%" y="-88.1%" width="139.2%" height="276.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.301285714" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="0.7532143" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.964705884   0 0 0 0 0.815686285  0 0 0 0.239215687 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M16,0 C7.7179,0 1,6.7 1,14.96 C1,18.24 2.06284,21.28 3.86764,23.76 C3.86764,23.76 7.91176,29.1733333 16,40 L27.87166,24.12 L27.85162,24.12 C29.81686,21.58 31,18.42 31,14.96 C31,14.96 31,14.96 31,14.96 C31,6.7 24.2821,0 16,0 C16,0 16,0 16,0 Z" id="path-3"></path>
        <filter x="-27.6%" y="-20.7%" width="155.2%" height="141.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="6.77892876" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.827450991   0 0 0 0 0.0549019612  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="3.0128572" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="7.53214312" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.701960802   0 0 0 0 0.4627451  0 0 0 0.501960814 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="特色产品图标" transform="translate(-29.000000, -21.000000)">
            <g id="撒点图标复制" transform="translate(29.000000, 21.000000)">
                <path d="M16.1111111,44.4444444 C8.4405,44.4444444 2.22222222,42.4545956 2.22222222,40 C2.22222222,37.5454044 8.4405,35.5555556 16.1111111,35.5555556 C23.7817222,35.5555556 30,37.5454044 30,40 C30,42.4545956 23.7817222,44.4444444 16.1111111,44.4444444 C16.1111111,44.4444444 16.1111111,44.4444444 16.1111111,44.4444444 Z" id="椭圆形备份-8" stroke="#FFD375" stroke-width="0.602571428" fill-opacity="0.101960786" fill="#FFB14C" stroke-dasharray="1,3"></path>
                <g id="椭圆形">
                    <use fill-opacity="0.58431375" fill="#FFCE4C" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use stroke="#FFF475" stroke-width="0.602571428" xlink:href="#path-1"></use>
                </g>
                <g id="路径">
                    <use fill="#985719" fill-rule="evenodd" xlink:href="#path-3"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <path stroke="#FFD884" stroke-width="0.7532143" d="M27.1199,24.4966071 L27.87166,24.4966071 L27.87166,24.12 L27.5700251,23.8945021 L27.1199,24.4966071 Z M16.0001048,39.3708832 L15.6983651,39.7745021 L16.30171,39.7746026 L16.0001048,39.3708832 Z" stroke-linejoin="square"></path>
                </g>
                <g id="mianyang" transform="translate(6.000000, 7.000000)" fill="#FFFFFF" fill-rule="nonzero">
                    <path d="M17.1849026,0.0129629355 C16.5752947,0.0133937442 15.9872277,0.267145739 15.5331453,0.725105613 L15.2169246,1.03486395 L14.6663388,0.988766403 C14.6077475,0.984027409 14.5508795,1.01030733 14.5121059,1.06028225 C14.4737631,1.10982637 14.4578228,1.177034 14.4698857,1.24165675 L14.552172,1.6548115 C13.6009252,2.42554544 12.5148322,2.95846766 11.3658398,3.21825111 L5.26803763,3.57453784 C2.91749298,3.57841521 1.01155282,5.71872052 1.00250561,8.36437571 C1.00250561,9.76410438 1.14812219,11.159094 1.43590883,12.5204799 L1.23040849,15.4440129 L1.05334218,17.1642703 L1.05334218,17.7058089 L2.60385724,17.7058089 L2.60385724,17.6631579 C2.60385724,17.3284121 2.60385724,16.9936663 2.57240751,16.6658135 L2.49012122,15.4586606 L3.60378652,12.4778288 L4.30602036,12.5420208 L4.6226718,15.4517676 L4.8695307,17.1573772 L4.8695307,17.6989158 L6.42306148,17.6989158 L6.42306148,17.6562648 C6.42306148,17.321519 6.37265575,16.9893581 6.27442917,16.6735683 L5.88195369,15.4414279 L6.42952374,12.7350274 L7.8947368,12.8703043 L11.2271164,12.6208605 L11.6924001,14.2803726 L11.8569727,15.4448745 L11.8569727,17.6993466 L13.4074878,17.6993466 L13.4074878,17.6566956 C13.407057,17.3223806 13.3794846,16.9884965 13.3252015,16.6597821 L13.1132389,15.4418587 L13.2114654,14.3734294 L13.6513309,12.5928573 L14.1006744,12.5002314 L14.9011348,15.4595223 L15.2173555,17.165132 L15.2173555,17.7066706 L16.7708862,17.7066706 L16.7708862,17.6640195 C16.7717479,17.3288429 16.7170339,16.9971128 16.6093294,16.6847695 L16.1664482,15.4418587 L15.9764573,13.0951914 L16.3150805,12.0267621 C17.1220032,10.9583327 17.748413,9.56248138 18.2830585,7.64275504 L18.6975057,6.14695391 L19.0740409,5.75878662 L20.3936373,5.54854728 L20.4125933,5.54854728 L20.9821351,4.48011791 L20.9821351,3.89248174 L19.5961927,1.73451288 C19.1537423,0.684608677 18.2162816,0.0155478532 17.1849026,0.0129629355 Z" id="路径"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
