<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="45px" viewBox="0 0 31 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <path d="M16,41 C13.23858,41 11,40.552284 11,40 C11,39.447716 13.23858,39 16,39 C18.76142,39 21,39.447716 21,40 C21,40.552284 18.76142,41 16,41 C16,41 16,41 16,41 Z" id="path-1"></path>
        <filter x="-19.6%" y="-97.9%" width="139.2%" height="295.8%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.301285714" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="0.7532143" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.964705884   0 0 0 0 0.815686285  0 0 0 0.239215687 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M16.0344828,0 C7.75238276,0 1.03448276,6.7 1.03448276,14.96 C1.03448276,18.24 2.09732276,21.28 3.90212276,23.76 C3.90212276,23.76 7.94624276,29.1733333 16.0344828,40 L27.9061428,24.12 L27.8861028,24.12 C29.8513428,21.58 31.0344828,18.42 31.0344828,14.96 C31.0344828,14.96 31.0344828,14.96 31.0344828,14.96 C31.0344828,6.7 24.3165828,0 16.0344828,0 C16.0344828,0 16.0344828,0 16.0344828,0 Z" id="path-3"></path>
        <filter x="-27.6%" y="-20.7%" width="155.2%" height="141.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="6.77892876" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0235294122   0 0 0 0 0.411764711   0 0 0 0 0.890196085  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="3.0128572" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="7.53214312" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 0.290196091   0 0 0 0 0.576470613   0 0 0 0 0.937254906  0 0 0 1 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="特色产品图标" transform="translate(-90.000000, -21.000000)">
            <g id="蓝复制" transform="translate(90.000000, 21.000000)">
                <path d="M16,44 C8.268024,44 2,41.985278 2,39.5 C2,37.014722 8.268024,35 16,35 C23.731976,35 30,37.014722 30,39.5 C30,41.985278 23.731976,44 16,44 C16,44 16,44 16,44 Z" id="椭圆形备份-8" stroke="#7DB7FF" stroke-width="0.602571428" fill-opacity="0.101960786" fill="#4296FF" stroke-dasharray="1,3"></path>
                <g id="椭圆形">
                    <use fill-opacity="0.580392182" fill="#4497FF" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use stroke="#88BDFF" stroke-width="0.602571428" xlink:href="#path-1"></use>
                </g>
                <g id="路径">
                    <use fill="#0B4389" fill-rule="evenodd" xlink:href="#path-3"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <path stroke="#3F91F7" stroke-width="0.7532143" d="M27.1543827,24.4966071 L27.9061428,24.4966071 L27.9061428,24.12 L27.6045078,23.8945021 L27.1543827,24.4966071 Z M16.0345875,39.3708832 L15.7328478,39.7745021 L16.3361928,39.7746026 L16.0345875,39.3708832 Z" stroke-linejoin="square"></path>
                </g>
                <g id="huajiao" transform="translate(7.000000, 12.000000)" fill="#FFFFFF" fill-rule="nonzero">
                    <path d="M19.0253368,7.19705308 C18.8100232,5.5859131 18.91768,6.33765468 18.3756836,5.15528585 C16.9835696,2.68567557 15.2072321,2.70238093 15.2072321,2.70238093 L15.3148889,2.63184714 C15.9645421,1.45040638 19.6109528,-1.45168735 19.6109528,-1.45168732 L18.8592112,-1.98904333 C18.2893725,-1.90737265 16.4230117,0.145531515 15.3668611,1.32604418 L15.3668611,1.28613692 C15.3633937,0.653208644 14.933396,0.102283665 14.32026,-0.0547975347 C13.707124,-0.211878734 13.0651957,0.064426224 12.7578263,0.617720237 C12.4504569,1.17101425 12.5550211,1.86201546 13.0123323,2.29959593 L12.7636079,2.70145284 C12.5589493,2.40961381 12.4231381,2.07516264 12.3663914,1.72326073 C12.213723,1.4400732 12.0454834,1.16556081 11.8624461,0.900985384 C12.046916,0.335964279 11.8812035,-0.284695692 11.439646,-0.682571239 C10.9980885,-1.08044679 10.3635808,-1.18084356 9.82075835,-0.938724347 C9.27793585,-0.69660513 8.92870414,-0.157423675 8.92972587,0.436947367 L8.92972587,0.505625001 C8.39360254,0.327092175 7.80274572,0.490346423 7.43433409,0.918803041 C7.06592246,1.34725966 6.99305243,1.95590873 7.24990829,2.459225 C6.75105515,2.93009992 6.63418176,3.67892944 6.96582126,4.27942331 C7.29746077,4.87991718 7.9935084,5.17978837 8.65772135,5.00832757 C9.3219343,4.83686677 9.78586619,4.23755696 9.78541198,3.55157049 C9.7851525,3.44965205 9.77489453,3.3480049 9.75478547,3.24808963 C10.3264803,3.90516745 10.5362255,4.48985534 10.6076873,4.77106238 L10.590982,4.78591158 C9.51626992,5.75296678 7.47635883,6.17802561 7.2610452,6.28939473 C7.12368996,6.35807237 5.58401185,6.5149172 4.1538467,6.56224908 C4.10523773,6.06919475 4.13657707,5.57152587 4.24665429,5.08846437 C4.85288665,4.73946784 5.10599683,3.99480433 4.83801087,3.3486621 C4.5700249,2.70251987 3.86417581,2.35558246 3.18889307,2.53809129 C2.51361033,2.72060013 2.07862813,3.37587031 2.17261793,4.06903852 C2.26660774,4.76220673 2.86037751,5.27798379 3.55987805,5.27407956 L3.6424768,5.27407956 L3.48841619,6.57060176 C3.02786082,6.58253201 2.56707491,6.55863941 2.11022331,6.4991399 C1.95059423,6.47253506 1.78941836,6.43850561 1.62669569,6.39705156 L1.62669569,5.56178314 C2.09034108,5.08785324 2.18855738,4.36605383 1.86840109,3.78547089 C1.54824479,3.20488796 0.885399091,2.9027681 0.237170969,3.04196903 C-0.411057152,3.18116997 -0.891430144,3.72878537 -0.945002933,4.38962313 C-1.41509843,4.0995324 -2.0149267,4.12713162 -2.45639709,4.45916521 C-2.89786748,4.79119881 -3.09078231,5.35982833 -2.9424868,5.89194754 C-2.79419129,6.42406675 -2.33498398,6.81095733 -1.78541688,6.86680015 C-1.23584979,6.92264298 -0.708206043,6.6360287 -0.45590689,6.14461487 C-0.0144204871,6.39722268 0.447162871,6.61295935 0.924142157,6.78962771 L0.623445527,7.46526704 C0.0449776928,7.03881568 -0.762537479,7.12018192 -1.24430141,7.65346356 C-1.72606535,8.1867452 -1.7252547,8.99834889 -1.24242643,9.53066708 C-0.759598156,10.0629853 0.048077944,10.1427382 0.625692731,9.71513215 C1.20330752,9.28752608 1.36283637,8.49175491 0.994675925,7.87454856 C1.08179505,7.52572671 1.29125204,7.2197746 1.58493227,7.01236596 C1.61834301,7.02071864 1.64989758,7.02907133 1.67773989,7.03463978 C1.8633551,7.07083475 2.32739309,7.08289973 2.88980716,7.08197166 C3.11068924,7.32512757 3.44665277,7.70471068 3.59328879,7.86805204 C3.03171008,8.26990525 2.87349616,9.03599609 3.22993809,9.62743992 C3.58638002,10.2188838 4.33765485,10.4368584 4.95529982,10.1280359 C5.5729448,9.81921342 5.8493299,9.08740873 5.59003996,8.4473889 C5.33075002,7.80736908 4.62294897,7.47428574 3.96451919,7.68243682 C3.80489012,7.49682161 3.61370645,7.25459377 3.54966921,7.07269088 C3.86614314,7.07269088 4.18911359,7.06248204 4.49352252,7.05598551 C4.80442799,7.26758684 5.2953802,7.63232072 5.60721374,8.01468806 C5.61445657,8.67201853 6.09647661,9.22748483 6.74623106,9.32725945 C7.39598551,9.42703407 8.02245935,9.04178525 8.22659388,8.41691333 C8.43072841,7.79204142 8.15251209,7.11124644 7.56916645,6.80818922 C7.80025738,6.7627135 8.02763601,6.71538162 8.24016541,6.66804975 C8.57891317,6.73301506 9.02160543,6.93997603 9.32323012,7.53487274 C8.69598261,7.82460735 8.37615607,8.53055443 8.5719479,9.19316378 C8.76773973,9.85577313 9.41989559,10.2745129 10.1038817,10.1767963 C10.7878679,10.0790796 11.296683,9.49447938 11.2991039,8.80355266 C11.2986151,8.72605343 11.2914737,8.64873923 11.2777582,8.57246173 C11.9536244,8.46795756 12.4300186,7.85351632 12.3628608,7.17292393 C12.2957031,6.49233154 11.7084052,5.98285066 11.0251486,6.01245859 C10.3418921,6.04206653 9.8008682,6.6004416 9.79283657,7.28429221 C9.57288255,7.23324804 9.27311399,7.04763283 9.33622317,6.40726038 L9.41603771,6.38684271 L12.7515429,4.26804517 C12.2710145,4.75782642 12.2341792,5.53010469 12.6659102,6.06339786 C13.0976412,6.59669103 13.8606675,6.7214361 14.4397546,6.35339877 C15.0188416,5.98536143 15.2298658,5.24156084 14.9303329,4.62424952 C14.6308,4.0069382 13.9159478,3.71239309 13.2684813,3.93950626 C14.155722,3.37523603 14.925097,2.88521189 15.2174409,2.69866861 C15.2694132,2.71537398 14.7348414,3.00400563 14.7348414,3.21839119 C15.0568837,6.01097193 15.8086253,6.54832794 16.8824093,7.6221119 C18.1659384,8.80819305 19.7770784,8.91956217 19.9914639,8.91956217 C19.2397223,8.26990897 19.2397223,7.73440909 19.0253368,7.19705308 L19.0253368,7.19705308 Z M1.07541854,5.92094856 C1.16956714,6.04826973 1.25484222,6.18191903 1.33063945,6.32094931 C1.01045323,6.22814171 0.698619684,6.11955682 0.419268812,6.01282809 C0.462888394,6.01282809 0.512076405,6.01932461 0.552911759,6.01932461 C0.731563236,6.01805309 0.908536816,5.98473302 1.07541854,5.92094856 L1.07541854,5.92094856 Z M5.79468509,7.31306259 L5.15895302,7.04763283 C5.46336195,7.04113631 5.71579863,7.03649592 5.87357157,7.03649592 C5.91997538,7.03649592 5.96637916,7.03649592 6.02113565,7.03185555 C5.93290523,7.11505708 5.85613015,7.2096215 5.79282895,7.31306259 L5.79468509,7.31306259 Z M13.5561848,2.62071023 C13.6416849,2.64787972 13.729661,2.66653191 13.8188303,2.67639479 C13.6183659,2.79797275 13.3956277,2.92975954 13.1617525,3.06804288 L13.0123323,3.00864602 C13.0123323,3.00864602 12.9984112,2.99472488 12.9761373,2.96966683 C13.1553847,2.83234543 13.3492988,2.71531222 13.5543287,2.62071023 L13.5561848,2.62071023 Z M12.3820998,3.55323324 L12.2670872,3.62203539 C12.3029627,3.58506706 12.3420037,3.54604494 12.3820998,3.50702284 L12.3820998,3.55323324 Z M9.82995963,2.26154481 C9.87393172,2.12442431 9.89677652,1.98140958 9.89770917,1.83741408 C10.2405102,1.96982917 10.6203394,1.96982917 10.9631404,1.83741408 C11.4559488,2.00353969 12.2243958,2.36177702 12.3682475,3.00864602 L12.3682475,3.31119878 L12.1195232,3.70006264 C11.9042095,3.83463366 11.6926082,3.97013276 11.4921438,4.10563187 L11.506993,4.08242995 C10.5779889,4.08242995 9.9849483,2.66432981 9.82810347,2.25783251 L9.82995963,2.26154481 Z" id="形状"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
