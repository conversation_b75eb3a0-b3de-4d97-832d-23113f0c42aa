<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="45px" viewBox="0 0 31 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <path d="M15.0714286,40.5647841 C12.3168101,40.5647841 10.0837438,40.0739326 10.0837438,39.4684385 C10.0837438,38.8629445 12.3168101,38.372093 15.0714286,38.372093 C17.826047,38.372093 20.0591133,38.8629445 20.0591133,39.4684385 C20.0591133,40.0739326 17.826047,40.5647841 15.0714286,40.5647841 C15.0714286,40.5647841 15.0714286,40.5647841 15.0714286,40.5647841 Z" id="path-1"></path>
        <filter x="-19.6%" y="-89.3%" width="139.3%" height="278.6%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.301285714" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="0.7532143" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.964705884   0 0 0 0 0.815686285  0 0 0 0.239215687 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M14.9630542,3.55271368e-15 C6.70135345,3.55271368e-15 7.10542736e-15,6.61096346 7.10542736e-15,14.761196 C7.10542736e-15,17.997608 1.06022217,20.9972093 2.86057685,23.4442525 C2.86057685,23.4442525 6.89473596,28.7856478 14.9630542,39.4684385 L26.8054736,23.7994684 L26.785483,23.7994684 C28.7458825,21.2932226 29.9261084,18.1752159 29.9261084,14.761196 C29.9261084,14.761196 29.9261084,14.761196 29.9261084,14.761196 C29.9261084,6.61096346 23.2247549,3.55271368e-15 14.9630542,3.55271368e-15 C14.9630542,3.55271368e-15 14.9630542,3.55271368e-15 14.9630542,3.55271368e-15 Z" id="path-3"></path>
        <filter x="-27.7%" y="-21.0%" width="155.4%" height="142.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="6.77892876" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0627451017   0 0 0 0 1   0 0 0 0 0.458823532  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="3.0128572" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="7.53214312" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 0.509803951   0 0 0 0 1   0 0 0 0 0.713725507  0 0 0 0.501960814 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="特色产品图标" transform="translate(-213.000000, -78.000000)">
            <g id="核桃复制" transform="translate(213.000000, 78.000000)">
                <g id="编组" transform="translate(1.000000, 0.000000)">
                    <path d="M15.0714286,43.8538206 C7.41971059,43.8538206 1.21674877,41.8904149 1.21674877,39.4684385 C1.21674877,37.0464622 7.41971059,35.0830565 15.0714286,35.0830565 C22.7231466,35.0830565 28.9261084,37.0464622 28.9261084,39.4684385 C28.9261084,41.8904149 22.7231466,43.8538206 15.0714286,43.8538206 C15.0714286,43.8538206 15.0714286,43.8538206 15.0714286,43.8538206 Z" id="椭圆形备份-8" stroke="#73FFAE" stroke-width="0.602571428" fill-opacity="0.101960786" fill="#2AFF8C" stroke-dasharray="1,3"></path>
                    <g id="椭圆形">
                        <use fill-opacity="0.580392182" fill="#44FF93" fill-rule="evenodd" xlink:href="#path-1"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                        <use stroke="#7AFFB2" stroke-width="0.602571428" xlink:href="#path-1"></use>
                    </g>
                    <g id="路径">
                        <use fill="#0A853E" fill-rule="evenodd" xlink:href="#path-3"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                        <path stroke="#96FFC2" stroke-width="0.7532143" d="M26.0487684,24.1760756 L26.8054736,24.1760756 L26.8054736,23.7994684 L26.5050247,23.5723927 L26.0487684,24.1760756 Z M14.9631586,38.843694 L14.6626052,39.2413628 L15.263579,39.2414632 L14.9631586,38.843694 Z" stroke-linejoin="square"></path>
                    </g>
                </g>
                <g id="hua" transform="translate(6.000000, 7.000000)" fill="#FFFFFF" fill-rule="nonzero">
                    <path d="M8,7.47327527 C8.38884038,8.5951897 9.87261951,12.6581438 11.5045497,14.3058181 C13.1291352,15.9461672 14.7330344,16.4468672 15.5904508,15.5810631 C15.8423256,15.326699 15.9763264,15.0069749 16,14.6410317 C15.0517748,14.4086886 14.0428383,13.7685148 13.0397639,12.7557528 C11.4255553,11.1259039 10.0248584,7.5704988 9.4591397,6 L8,7.47327527 Z" id="形状"></path>
                    <path d="M6.61350134,5.46964459 L7.81752969,6.61243596 L12,2.64282 L10.7959243,1.500096 C10.2373245,0.969860021 9.49465454,0.677934893 8.70460633,0.677934893 C8.04571219,0.677934893 7.42018636,0.881678289 6.90650357,1.25673146 C6.60065099,1.01662375 6.14163543,0.69606087 5.59351938,0.439893596 C4.19112703,-0.215562258 2.90109834,-0.137263324 1.86308328,0.666300101 C0.85202322,1.44924438 -0.123775304,2.37676988 0.0128454745,4.32707782 C0.138982451,6.12766097 1.20302952,8.70247356 3.46294036,12.6757282 C5.16919741,15.6756145 5.79124441,18.1657492 6.0126586,19.7264493 C6.25227153,21.4158507 6.08720483,22.4101976 6.08571392,22.4187552 C6.03743635,22.6900836 6.23007331,22.9471269 6.51588125,22.9929248 C6.54496458,22.9976287 6.5744142,22.9999955 6.60391681,23 C6.85538615,22.9999551 7.07758128,22.8278816 7.1208891,22.5846519 C7.15174889,22.4112532 7.8366987,18.268014 4.3867695,12.2024532 C-0.432420415,3.72952635 0.938875494,2.6678415 2.52671053,1.43839573 C3.2276819,0.895604074 4.0962758,0.857105981 5.10823516,1.32375498 C5.554566,1.52956478 5.93785204,1.79318911 6.19892955,1.99495595 C5.48366426,3.07847284 5.62127899,4.52787883 6.61350134,5.46962213 L6.61350134,5.46964459 Z" id="路径"></path>
                    <path d="M19.6433355,11 C20.0105427,10.9768878 20.3312789,10.8428862 20.5863022,10.5904036 C21.0638326,10.1177595 21.0423897,9.48786691 20.9402125,9.0426925 C20.7607954,8.26004352 20.1971529,7.38238508 19.3103689,6.50463677 C17.6603985,4.87143961 13.5966584,3.38864042 12.4741839,3 L11,4.45919281 C12.5714145,5.02493719 16.1287385,6.42552959 17.7595351,8.03979223 C18.7590249,9.02914859 19.4084618,10.0511181 19.6433355,11 Z" id="形状"></path>
                    <path d="M17.2693406,9.3597997 C15.8404909,7.93005922 12.6387035,6.61205708 11,6 C11.6120696,7.63995688 12.9299689,10.8447602 14.3575907,12.2732721 C15.4703393,13.3867375 16.5841998,14 17.4939059,14 C17.9286725,14 18.2914105,13.8575684 18.5719955,13.5768317 C19.4564487,12.6918735 18.9451589,11.0366163 17.2693406,9.3597997 Z" id="路径"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
