<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="45px" viewBox="0 0 31 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <path d="M16.1111111,41.1111111 C13.3496911,41.1111111 11.1111111,40.6136489 11.1111111,40 C11.1111111,39.3863511 13.3496911,38.8888889 16.1111111,38.8888889 C18.8725311,38.8888889 21.1111111,39.3863511 21.1111111,40 C21.1111111,40.6136489 18.8725311,41.1111111 16.1111111,41.1111111 C16.1111111,41.1111111 16.1111111,41.1111111 16.1111111,41.1111111 Z" id="path-1"></path>
        <filter x="-19.6%" y="-88.1%" width="139.2%" height="276.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.301285714" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="0.7532143" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.815686285   0 0 0 0 0.815686285  0 0 0 0.239215687 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M16,0 C7.7179,0 1,6.7 1,14.96 C1,18.24 2.06284,21.28 3.86764,23.76 C3.86764,23.76 7.91176,29.1733333 16,40 L27.87166,24.12 L27.85162,24.12 C29.81686,21.58 31,18.42 31,14.96 C31,14.96 31,14.96 31,14.96 C31,6.7 24.2821,0 16,0 C16,0 16,0 16,0 Z" id="path-3"></path>
        <filter x="-27.6%" y="-20.7%" width="155.2%" height="141.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="6.77892876" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.266666681   0 0 0 0 0.0549019612  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="3.0128572" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="7.53214312" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.4627451   0 0 0 0 0.4627451  0 0 0 0.501960814 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="特色产品图标" transform="translate(-152.000000, -135.000000)">
            <g id="樱桃复制-2" transform="translate(152.000000, 135.000000)">
                <path d="M16.1111111,44.4444444 C8.4405,44.4444444 2.22222222,42.4545956 2.22222222,40 C2.22222222,37.5454044 8.4405,35.5555556 16.1111111,35.5555556 C23.7817222,35.5555556 30,37.5454044 30,40 C30,42.4545956 23.7817222,44.4444444 16.1111111,44.4444444 C16.1111111,44.4444444 16.1111111,44.4444444 16.1111111,44.4444444 Z" id="椭圆形备份-8" stroke="#FF7575" stroke-width="0.602571428" fill-opacity="0.101960786" fill="#FF4C4C" stroke-dasharray="1,3"></path>
                <g id="椭圆形">
                    <use fill-opacity="0.58431375" fill="#FF4C4C" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use stroke="#FF7575" stroke-width="0.602571428" xlink:href="#path-1"></use>
                </g>
                <g id="路径">
                    <use fill="#981919" fill-rule="evenodd" xlink:href="#path-3"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <path stroke="#FF8484" stroke-width="0.7532143" d="M27.1199,24.4966071 L27.87166,24.4966071 L27.87166,24.12 L27.5700251,23.8945021 L27.1199,24.4966071 Z M16.0001048,39.3708832 L15.6983651,39.7745021 L16.30171,39.7746026 L16.0001048,39.3708832 Z" stroke-linejoin="square"></path>
                </g>
                <g id="pingguoye" transform="translate(7.000000, 3.000000)" fill="#FFFFFF" fill-rule="nonzero">
                    <path d="M17.9581549,12.710974 C17.7219282,15.0260565 16.4800274,16.9312634 15.7379454,18.0695383 C15.5728483,18.3227678 15.4302873,18.5413882 15.3564413,18.6800255 C15.2201179,18.9356695 15.1468755,19.1578112 15.0694077,19.3929313 C14.9246333,19.8325867 14.7604416,20.3307957 14.2211846,20.953054 C13.5513389,21.7260226 12.4076312,22.2062228 11.236357,22.2062228 C10.8622978,22.2062228 10.5004121,22.1570257 10.1606601,22.0599393 C9.66385954,21.9179819 9.28406568,21.7196843 9.00005031,21.5215878 C8.71603493,21.7195837 8.33603986,21.9178813 7.83944052,22.0599393 C7.49978911,22.1570257 7.13780282,22.2062228 6.76374359,22.2062228 C5.59257001,22.2062228 4.4488623,21.7261232 3.77891601,20.953054 C3.23965904,20.3307957 3.07556796,19.8324861 2.93069297,19.3928307 C2.85312448,19.1577106 2.78008332,18.9355689 2.64365935,18.6800255 C2.56991396,18.5415894 2.42735292,18.322969 2.26225577,18.0698402 C1.52017378,16.9315653 0.278071807,15.0263583 0.0418450554,12.710974 C-0.28251399,9.53217486 1.41574305,7.20441581 4.70108568,6.32450136 C5.21207191,6.18767497 5.71661924,6.11825569 6.20084381,6.11825569 C7.0362897,6.11825569 7.72857089,6.32379711 8.27849227,6.59312375 C8.28895548,6.169968 8.23754496,5.30967203 7.7890361,4.2048995 C7.31296073,3.0322168 7.00308917,2.68783684 6.93004804,2.6177133 C6.61709009,2.45115286 6.46218324,2.08927106 6.55769913,1.74785962 C6.61163376,1.55512293 6.73994035,1.39171443 6.91438077,1.29359759 C7.08882119,1.19548075 7.29509933,1.17069666 7.48781681,1.22469987 C8.03451875,1.3777241 8.59037597,2.16689047 9.18738173,3.63727117 C9.19090301,3.64582282 9.19392122,3.65417326 9.19734189,3.6627249 L9.27903531,3.58495521 C9.34251873,2.93352072 9.70862994,1.33003609 11.7365782,0.397705034 C12.3069928,0.134566849 12.9278851,-0.00114712303 13.5560675,3.01445783e-06 C15.2360139,3.01445783e-06 16.5141334,0.947928231 16.5678579,0.988271913 C16.7533532,1.12790624 16.8642032,1.34518988 16.8683729,1.57732966 C16.8715924,1.75912766 16.8340657,3.39178851 14.5305531,4.71206264 C13.8889782,5.07978358 13.1764748,5.26620953 12.4128628,5.26620953 L12.412561,5.26620953 C11.5134305,5.26620953 10.7208435,5.00322116 10.2113663,4.78097887 L9.6738197,5.29287057 C9.76829027,5.81281087 9.79112821,6.24461888 9.78750633,6.56113053 C10.3265621,6.30749867 10.9968102,6.11815507 11.7990556,6.11815507 C12.2832802,6.11815507 12.7879281,6.18747374 13.2988137,6.32440073 C16.5844582,7.20441579 18.2826146,9.53227546 17.9581549,12.710974 Z" id="路径"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
