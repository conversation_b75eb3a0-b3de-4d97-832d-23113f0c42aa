<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="45px" viewBox="0 0 31 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <defs>
        <path d="M16.1111111,41.1111111 C13.3496911,41.1111111 11.1111111,40.6136489 11.1111111,40 C11.1111111,39.3863511 13.3496911,38.8888889 16.1111111,38.8888889 C18.8725311,38.8888889 21.1111111,39.3863511 21.1111111,40 C21.1111111,40.6136489 18.8725311,41.1111111 16.1111111,41.1111111 C16.1111111,41.1111111 16.1111111,41.1111111 16.1111111,41.1111111 Z" id="path-1"></path>
        <filter x="-19.6%" y="-88.1%" width="139.2%" height="276.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.301285714" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="0.7532143" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.964705884   0 0 0 0 0.815686285  0 0 0 0.239215687 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M16,0 C7.7179,0 1,6.7 1,14.96 C1,18.24 2.06284,21.28 3.86764,23.76 C3.86764,23.76 7.91176,29.1733333 16,40 L27.87166,24.12 L27.85162,24.12 C29.81686,21.58 31,18.42 31,14.96 C31,14.96 31,14.96 31,14.96 C31,6.7 24.2821,0 16,0 C16,0 16,0 16,0 Z" id="path-3"></path>
        <filter x="-27.6%" y="-20.7%" width="155.2%" height="141.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="6.77892876" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.827450991   0 0 0 0 0.0549019612  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="3.0128572" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="7.53214312" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.701960802   0 0 0 0 0.4627451  0 0 0 0.501960814 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="特色产品图标" transform="translate(-29.000000, -135.000000)">
            <g id="撒点图标复制-3" transform="translate(29.000000, 135.000000)">
                <path d="M16.1111111,44.4444444 C8.4405,44.4444444 2.22222222,42.4545956 2.22222222,40 C2.22222222,37.5454044 8.4405,35.5555556 16.1111111,35.5555556 C23.7817222,35.5555556 30,37.5454044 30,40 C30,42.4545956 23.7817222,44.4444444 16.1111111,44.4444444 C16.1111111,44.4444444 16.1111111,44.4444444 16.1111111,44.4444444 Z" id="椭圆形备份-8" stroke="#FFD375" stroke-width="0.602571428" fill-opacity="0.101960786" fill="#FFB14C" stroke-dasharray="1,3"></path>
                <g id="椭圆形">
                    <use fill-opacity="0.58431375" fill="#FFCE4C" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use stroke="#FFF475" stroke-width="0.602571428" xlink:href="#path-1"></use>
                </g>
                <g id="路径">
                    <use fill="#985719" fill-rule="evenodd" xlink:href="#path-3"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <path stroke="#FFD884" stroke-width="0.7532143" d="M27.1199,24.4966071 L27.87166,24.4966071 L27.87166,24.12 L27.5700251,23.8945021 L27.1199,24.4966071 Z M16.0001048,39.3708832 L15.6983651,39.7745021 L16.30171,39.7746026 L16.0001048,39.3708832 Z" stroke-linejoin="square"></path>
                </g>
                <g id="chengshuqi" transform="translate(6.000000, 6.000000)" fill="#FFFFFF" fill-rule="nonzero">
                    <path d="M12.3172912,6.69724605 C8.07950339,6.69724605 4.63530474,9.3637246 4.63530474,12.6333183 C4.63530474,15.9188036 8.07950339,18.5694131 12.3172912,18.5694131 C16.555079,18.5694131 19.9993228,15.9188036 19.9993228,12.6333183 C19.9993228,9.3637246 16.5551242,6.69724605 12.3173138,6.69724605 L12.3172912,6.69724605 Z M17.5391422,13.0460045 L16.824921,13.0460045 C16.8090519,13.1253499 16.7772912,13.2205869 16.745553,13.2999549 L17.2375847,13.4110384 C17.4597968,13.4586682 17.586772,13.6808578 17.5391648,13.90307 C17.491535,14.093544 17.3328217,14.2046501 17.1423476,14.2046501 C17.1106095,14.2046501 17.0788713,14.2046501 17.0471332,14.188781 L16.3011512,14.0141761 C16.2217833,14.1094131 16.1265463,14.2046501 16.0154402,14.2839955 L16.2217833,14.4109707 C16.4122573,14.5220767 16.4757336,14.7760271 16.3646275,14.9665011 C16.2852822,15.0934763 16.158307,15.1728217 16.0154402,15.1728217 C15.9519639,15.1728217 15.8725959,15.1569526 15.8091196,15.1251919 L15.2377201,14.7918962 C15.0155305,14.9030023 14.7774492,14.9982167 14.5393679,15.0776072 L14.6187133,15.1886907 C14.7456885,15.3632957 14.6980813,15.617246 14.5234989,15.7442212 C14.46,15.791851 14.364763,15.8235666 14.2854176,15.8235666 C14.1584424,15.8235666 14.0314673,15.7600903 13.9520993,15.6489842 L13.698149,15.2997968 C13.3807223,15.3632957 13.0632731,15.3950339 12.7140858,15.4109029 L12.7140858,15.6331151 C12.7140858,15.8553273 12.5395034,16.0299097 12.3172912,16.0299097 C12.095079,16.0299097 11.9204966,15.8553273 11.9204966,15.6331151 L11.9204966,15.4109029 C11.5554402,15.3950339 11.2221445,15.3474266 10.8888262,15.2839278 L10.6190068,15.6489842 C10.5396388,15.7600903 10.4126862,15.8076975 10.2856885,15.8076975 C10.2063431,15.8076975 10.1269752,15.7759594 10.0476072,15.7283521 C9.87302483,15.601377 9.82541761,15.3474266 9.95239278,15.1569526 L10.0317381,15.0458465 C9.79365688,14.9665011 9.58733634,14.8712641 9.38099323,14.7760271 L8.80961625,15.1093454 C8.74611738,15.1410835 8.68264108,15.1569752 8.60327314,15.1569752 C8.46042889,15.1569752 8.33345372,15.0776072 8.25408578,14.9506321 C8.14297968,14.760158 8.20645598,14.5062077 8.39693002,14.3951016 L8.60327314,14.2681264 C8.49216704,14.1728894 8.38106095,14.0776749 8.28582393,13.9665463 L7.53986456,14.1411512 C7.50810384,14.1411512 7.47636569,14.1570203 7.44460497,14.1570203 C7.2541535,14.1570203 7.09544018,14.0300451 7.04783296,13.8395937 C7.00020316,13.6173815 7.12717833,13.4110384 7.34941309,13.3475621 L7.84142212,13.236456 C7.80968397,13.157088 7.79379233,13.0936117 7.77792325,13.0142438 L7.06370203,13.0142438 C6.84148984,13.0142438 6.66690745,12.8396614 6.66690745,12.6174492 C6.66690745,12.395237 6.84148984,12.2206546 7.06370203,12.2206546 L7.77792325,12.2206546 C7.7938149,12.1412867 7.82555305,12.0460722 7.8572912,11.9667043 L7.36525959,11.8555982 C7.1430474,11.8079684 7.01607223,11.5857788 7.06367946,11.3635666 C7.11130926,11.141377 7.33352144,11.0144018 7.55573363,11.0619865 L8.30171558,11.2365914 C8.38106095,11.141377 8.49216704,11.04614 8.58740406,10.966772 L8.38108352,10.8397968 C8.19060948,10.7286907 8.12713318,10.4747404 8.2382167,10.2842664 C8.3493228,10.0938149 8.60327314,10.030316 8.79374718,10.1414447 L9.36512415,10.4747404 C9.58733634,10.3636343 9.82539503,10.2684199 10.0634989,10.1890519 L10.0317381,10.0938149 C9.90476298,9.91923251 9.95239278,9.66528217 10.1269752,9.53828442 C10.3015576,9.41133183 10.5555079,9.45893905 10.6825056,9.63354402 L10.936456,9.9827088 C11.2538826,9.91923251 11.5713093,9.88749436 11.9204966,9.87160271 L11.9204966,9.64941309 C11.9204966,9.4272009 12.0951016,9.25261851 12.3172912,9.25261851 C12.5394808,9.25261851 12.7140858,9.4272009 12.7140858,9.64941309 L12.7140858,9.87160271 C13.0791422,9.88749436 13.4124605,9.93510158 13.7457562,9.99857788 L14.0155982,9.63354402 C14.1425734,9.45893905 14.3965237,9.41133183 14.5869752,9.55417607 C14.7615801,9.68115124 14.8091874,9.93510158 14.6663431,10.1255756 L14.5869752,10.2366591 C14.8250564,10.3160271 15.0313995,10.4112641 15.2377201,10.5064786 L15.8091196,10.1731828 C15.9995711,10.0620767 16.2535214,10.125553 16.3646501,10.3160271 C16.4757336,10.5065011 16.4122573,10.7604515 16.2217833,10.8715576 L16.0154628,10.9985102 C16.1265463,11.0937698 16.2376524,11.1889842 16.3328894,11.3000903 L17.0788713,11.1254853 C17.3010609,11.0778781 17.5074041,11.2048533 17.5709029,11.4270655 C17.6185102,11.6492551 17.491535,11.8555982 17.2693228,11.9190971 L16.7772912,12.0302032 C16.8090519,12.1095485 16.824921,12.1730474 16.8407901,12.2523928 L17.5550113,12.2523928 C17.7772235,12.2523928 17.9518284,12.4269977 17.9518284,12.6491874 C17.9359594,12.8555305 17.7613544,13.0459819 17.5391422,13.0459819 L17.5391422,13.0460045 Z" id="形状"></path>
                    <path d="M15.793228,13.3634312 C15.8091196,13.3475621 15.8249887,13.3158014 15.8408578,13.2999323 C15.983702,13.0936117 16.06307,12.8713995 16.06307,12.6333409 C16.06307,12.4111061 15.983702,12.2047856 15.8567494,11.9984424 C15.8408578,11.9825734 15.8249887,11.9508126 15.8091196,11.9349661 C15.6345147,11.6968849 15.3805643,11.4905418 15.0631377,11.3000903 C15.0472686,11.3000903 15.0472686,11.2842212 15.0313995,11.2842212 C14.3488939,10.8874266 13.3807223,10.6493454 12.3172912,10.6493454 C11.25386,10.6493454 10.2856885,10.9032957 9.60320542,11.2842212 C9.58733634,11.2842212 9.58733634,11.3000903 9.57146727,11.3000903 C9.26988713,11.4746727 9.01593679,11.6810158 8.8413544,11.9032054 C8.82548533,11.9349661 8.80961625,11.9508352 8.7937246,11.9825734 C8.65088036,12.1888939 8.57153499,12.4111287 8.57153499,12.6333183 C8.57153499,12.8555305 8.65088036,13.0777427 8.77785553,13.2681941 C8.79374718,13.2840632 8.80961625,13.2999549 8.80961625,13.3158239 C8.98419865,13.5539052 9.22225734,13.7602257 9.53970655,13.9348307 C9.55559819,13.9348307 9.55559819,13.9506998 9.57146727,13.9506998 C10.2539503,14.3474944 11.2380135,14.5855756 12.3014221,14.5855756 C13.3648533,14.5855756 14.3330248,14.3316253 15.0313995,13.9506998 C15.0472686,13.9506998 15.0472686,13.9348307 15.0631377,13.9348307 C15.3805643,13.7760948 15.6186682,13.5856208 15.7932506,13.3634312 L15.793228,13.3634312 Z" id="路径"></path>
                    <path d="M16.5868397,1.99916479 C13.4283296,-1.15936795 7.58747178,-0.460993228 3.55598194,3.57045147 C-0.475462754,7.61781038 -1.18968397,13.4427991 1.98467269,16.6012867 C3.55600451,18.1726185 5.80981941,18.7916253 8.15887133,18.5059142 C5.58760722,17.3472686 3.84171558,15.1410835 3.84171558,12.6333183 C3.84171558,8.91930023 7.65097065,5.88776524 12.3331603,5.88776524 C14.7774492,5.88776524 16.9677652,6.71311512 18.523228,8.03047404 C18.7454176,5.72905192 18.1264108,3.53873589 16.5868397,1.99914221 L16.5868397,1.99916479 Z M16.5709707,3.55460497 C16.5074718,3.71331828 16.3646501,3.82442438 16.1900451,3.82442438 C16.1424153,3.82442438 16.0948081,3.82442438 16.06307,3.8085553 C14.7297968,3.34828442 14.5711061,2.18961625 14.5711061,2.14198646 C14.5393679,1.91977427 14.6980813,1.71343115 14.9202935,1.69756208 C15.1424831,1.66582393 15.3329571,1.82453725 15.3646953,2.04674944 C15.3646953,2.07848758 15.4916704,2.76097065 16.3170203,3.04668172 C16.5392099,3.12602709 16.650316,3.34826185 16.5709707,3.55458239 L16.5709707,3.55460497 Z" id="形状"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
