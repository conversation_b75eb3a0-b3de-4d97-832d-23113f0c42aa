<template>
  <component
    v-if="isVueComponent(content)"
    :is="content"
    v-bind="contentProps"
    v-on="contentEvents"
  />
  <component
    v-else-if="isVNode(content)"
    :is="() => content"
  />
  <div v-else>{{ content }}</div>
</template>

<script setup lang="ts">
import type { Component, PropType, VNode } from 'vue'
import { isVueComponent, isVNode } from '../utils/vue'

defineProps({
  content: {
    type: [String, Object] as PropType<string | Component | VNode>,
    required: true,
  },
  contentProps: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },
  contentEvents: {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
    type: Object as PropType<Record<string, Function>>,
    default: () => ({}),
  },
})
</script>
