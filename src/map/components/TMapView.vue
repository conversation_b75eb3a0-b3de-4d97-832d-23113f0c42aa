<template>
  <div class="t-map-view">
    <div
      class="t-map-search-container"
      v-if="$slots.search || searchConfig.show"
    >
      <slot name="search">
        <TMapSearch
          :map="mapInstance"
          :search-type="searchConfig.type"
          :placeholder="searchConfig.placeholder"
          :timeout="searchConfig.timeout"
          @search-start="handleSearchStart"
          @search-success="handleSearchSuccess"
          @search-error="handleSearchError"
          @select="handleSearchSelect"
        >
          <template #results="slotProps">
            <slot
              name="search-results"
              v-bind="slotProps"
            />
          </template>
        </TMapSearch>
      </slot>
    </div>
    <div
      ref="mapContainer"
      class="t-map-container"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, type Ref, computed, markRaw, nextTick } from 'vue'
import TMapSearch from './TMapSearch.vue'
import { TMapLoader } from '../utils/loader'
import { TMap } from '../core/TMap'
import { DEFAULT_MAP_CONFIG, TMAP_CONFIG, TSearchType } from '../constants'
import { createLogger } from '@/utils/logger'

const logger = createLogger('TMapView')

export interface TMapOptions extends Omit<T.MapOptions, 'center'> {
  center?: {
    lng: number
    lat: number
  }
  searchTimeout?: number
  idleTimeout?: number
  search?: TMapSearchOptions
}

export interface TMapSearchOptions {
  show?: boolean
  type?: TSearchType
  timeout?: number
  placeholder?: string
  pageSize?: number
  showPagination?: boolean
}

const props = withDefaults(
  defineProps<{
    options?: TMapOptions
    apiKey?: string
    idleTimeout?: number
    lazyDelay?: number
    scaleScreenSelector?: string
    disableScaleCompensation?: boolean
  }>(),
  {
    options: () => ({
      center: DEFAULT_MAP_CONFIG.CENTER,
      zoom: DEFAULT_MAP_CONFIG.ZOOM,
      search: {
        show: true,
        type: TSearchType.NORMAL,
        timeout: TMAP_CONFIG.SEARCH_TIMEOUT,
        placeholder: TMAP_CONFIG.SEARCH_PLACEHOLDER,
      },
    }),
    apiKey: TMAP_CONFIG.KEY,
    idleTimeout: 0,
    lazyDelay: 0,
    scaleScreenSelector: '',
    disableScaleCompensation: false,
  },
)

const searchConfig = computed(() => ({
  show: true,
  type: TSearchType.NORMAL,
  timeout: TMAP_CONFIG.SEARCH_TIMEOUT,
  placeholder: '搜索地点',
  ...props.options?.search,
}))

const emit = defineEmits<{
  (e: 'ready', map: TMap): void
  (e: 'error', error: Error): void
  (e: 'search-start', keyword: string): void
  (e: 'search-success', payload: { keyword: string; results: any[] }): void
  (e: 'search-error', payload: { keyword: string; error: Error }): void
  (e: 'select-result', result: any): void
  (e: 'scale-change', scale: { scaleX: number; scaleY: number }): void // 添加缩放变化事件
}>()

const mapContainer = ref<HTMLElement | null>(null)
const mapInstance = ref<TMap | null>(null) as Ref<TMap>

const handleSearchStart = (keyword: string) => {
  emit('search-start', keyword)
}

const handleSearchSuccess = (payload: { keyword: string; results: any[] }) => {
  emit('search-success', payload)
}

const handleSearchError = (payload: { keyword: string; error: Error }) => {
  emit('search-error', payload)
}

const handleSearchSelect = (result: any) => {
  if (!mapInstance.value) return
  const [lng, lat] = result.lonlat.split(',').map(Number)
  mapInstance.value.setCenter({ lng, lat }, 16)

  emit('select-result', result)
}

const initialized = ref(false)
let initTimer: number | null = null

const initMap = async () => {
  if (!mapContainer.value || initialized.value) return

  if (props.lazyDelay > 0) {
    if (initTimer !== null) {
      window.clearTimeout(initTimer)
    }

    initTimer = window.setTimeout(async () => {
      await performInit()
    }, props.lazyDelay)

    return
  }

  await performInit()
}

const performInit = async () => {
  try {
    await TMapLoader.getInstance().load(props.apiKey)
    mapInstance.value = markRaw(
      new TMap(mapContainer.value!, {
        ...props.options,
        idleTimeout: props.idleTimeout,
      }),
    )

    applyScaleCompensation()

    initialized.value = true
    emit('ready', mapInstance.value)
  } catch (error) {
    emit('error', error as Error)
  }
}

watch(
  () => props.options,
  (newOptions) => {
    if (mapInstance.value && newOptions) {
      if (newOptions.center) {
        mapInstance.value.setCenter({ lng: newOptions.center.lng, lat: newOptions.center.lat })
      }
      if (newOptions.zoom) {
        mapInstance.value.setZoom(newOptions.zoom)
      }

      applyScaleCompensation()
    }
  },
  { deep: true },
)

const currentScale = ref({ scaleX: 1, scaleY: 1 })

const handleScaleChange = (e: CustomEvent) => {
  if (!e.detail) return

  const { scaleX, scaleY } = e.detail

  if (
    Math.abs(scaleX - currentScale.value.scaleX) < 0.001 &&
    Math.abs(scaleY - currentScale.value.scaleY) < 0.001
  ) {
    return
  }

  currentScale.value = { scaleX, scaleY }
  emit('scale-change', { scaleX, scaleY })

  if (mapInstance.value) {
    nextTick(() => {
      applyScaleCompensation()
      mapInstance.value?.updateSize()
    })
  }
}

const applyScaleCompensation = () => {
  if (!mapInstance.value || props.disableScaleCompensation) return

  const { scaleX, scaleY } = currentScale.value

  if (Math.abs(scaleX - 1) < 0.01 && Math.abs(scaleY - 1) < 0.01) {
    if (mapContainer.value && mapContainer.value.style.transform) {
      mapContainer.value.style.transform = ''
      mapContainer.value.style.transformOrigin = ''
      logger.info('[TMapView] Cleared scale compensation')
    }
    return
  }

  const container = mapContainer.value
  if (!container) return

  const compensationX = 1 / scaleX
  const compensationY = 1 / scaleY

  const currentTransform = container.style.transform
  const newTransform = `scale(${compensationX}, ${compensationY})`

  if (currentTransform !== newTransform) {
    // TODO: 获取vscreen
    container.style.transformOrigin = 'left top'
    container.style.transform = newTransform

    logger.info(
      `[TMapView] Applied scale compensation: ${compensationX.toFixed(3)}, ${compensationY.toFixed(3)}`,
    )
  }
}

const setupScaleListener = () => {
  if (!props.scaleScreenSelector || props.disableScaleCompensation) return

  const scaleElement = document.querySelector(props.scaleScreenSelector)
  if (!scaleElement) return

  scaleElement.addEventListener('scalechange', handleScaleChange as EventListener)
}

const cleanupScaleListener = () => {
  if (!props.scaleScreenSelector) return

  const scaleElement = document.querySelector(props.scaleScreenSelector)
  if (!scaleElement) return

  scaleElement.removeEventListener('scalechange', handleScaleChange as EventListener)
}
onMounted(() => {
  initMap()

  setupScaleListener()
})

onUnmounted(() => {
  if (initTimer !== null) {
    window.clearTimeout(initTimer)
    initTimer = null
  }

  if (mapInstance.value) {
    mapInstance.value.destroy()
  }

  cleanupScaleListener()
})

const initialize = () => {
  if (!initialized.value) {
    initMap()
  }
}

defineExpose({
  getMap: () => mapInstance.value,
  search: () => mapInstance.value?.search,
  resetView: () => mapInstance.value?.resetView(),
  zoomIn: () => mapInstance.value?.zoomIn(),
  zoomOut: () => mapInstance.value?.zoomOut(),
  getZoom: () => mapInstance.value?.getZoom(),
  initialize,
  updateSize: () => mapInstance.value?.updateSize(),
})
</script>

<style scoped lang="scss">
.t-map-view {
  position: relative;
  width: 100%;
  height: 100%;
}

.t-map-container {
  width: 100%;
  height: 100%;
}

.t-map-search-container {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 300px;
  z-index: 401;
}
</style>
