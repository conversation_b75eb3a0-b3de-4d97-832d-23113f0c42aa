<template>
  <div class="t-map-search">
    <slot name="search-input">
      <el-input
        v-model="keyword"
        :placeholder="placeholder"
        v-loading="searching"
        clearable
        @keyup.enter="handleSearch"
        @input="handleInput"
        @focus="handleFocus"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #append>
          <el-button @click="handleSearch(1)">搜索</el-button>
        </template>
      </el-input>
    </slot>

    <div
      v-if="showResults"
      class="t-map-search__results"
    >
      <div class="t-map-search__results-header">
        <el-icon
          class="t-map-search__close-btn"
          @click="closeResults"
        >
          <Close />
        </el-icon>
      </div>

      <slot
        name="results"
        :results="results"
        :searching="searching"
        :error="searchError"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        :show-pagination="showPagination"
        :on-select="handleSelect"
        :on-retry="() => handleSearch(1)"
        :on-page-change="handlePageChange"
        :on-size-change="handleSizeChange"
        :on-close="closeResults"
      >
        <el-scrollbar max-height="300px">
          <el-empty
            v-if="searchError"
            :description="searchError"
            :image-size="60"
          >
            <el-button
              type="primary"
              @click="handleSearch(1)"
              >重试</el-button
            >
          </el-empty>

          <el-empty
            v-else-if="showEmptyResult"
            description="未找到相关结果"
            :image-size="60"
          />

          <template v-else>
            <div
              v-for="(item, index) in results"
              :key="index"
              class="t-map-search__result-item"
              @click="handleSelect(item)"
            >
              <div class="t-map-search__result-name">{{ item.name }}</div>
              <div class="t-map-search__result-address">{{ item.address }}</div>
            </div>
          </template>
        </el-scrollbar>

        <div
          v-if="showPagination"
          class="t-map-search__pagination"
        >
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 30]"
            size="small"
            layout="prev, pager, next,total"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Search, Close } from '@element-plus/icons-vue'
import type { TMap } from '../core/TMap'
import { TMAP_CONFIG, TSearchType } from '../constants'

const props = withDefaults(
  defineProps<{
    map: TMap | null
    searchType?: TSearchType
    placeholder?: string
    timeout?: number
    pageSize?: number
    showPagination?: boolean
  }>(),
  {
    searchType: TSearchType.NORMAL,
    placeholder: TMAP_CONFIG.SEARCH_PLACEHOLDER,
    timeout: TMAP_CONFIG.SEARCH_TIMEOUT,
    pageSize: TMAP_CONFIG.SEARCH_PAGE_SIZE,
    showPagination: true,
  },
)

const emit = defineEmits<{
  (e: 'search-start', keyword: string): void
  (
    e: 'search-success',
    payload: {
      keyword: string
      results: any[]
      total: number
      resultType: number
    },
  ): void
  (e: 'search-error', payload: { keyword: string; error: Error }): void
  (e: 'select', result: any): void
}>()

const keyword = ref('')
const searching = ref(false)
const results = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(props.pageSize)
const hasSearched = ref(false)

const showPagination = computed(() => props.showPagination && total.value > pageSize.value)

const searchError = ref<string>('')
const isResultsVisible = ref(true)
const showResults = computed(
  () =>
    isResultsVisible.value &&
    (searching.value ||
      results.value.length > 0 ||
      searchError.value ||
      (hasSearched.value && !searching.value)),
)

const showEmptyResult = computed(
  () => hasSearched.value && !results.value.length && !searching.value,
)

const handleSearch = async (page = 1) => {
  if (page === 1) {
    currentPage.value = 1
  }

  const searchText = keyword.value.trim()
  if (!searchText || !props.map) return

  isResultsVisible.value = true
  searching.value = true
  searchError.value = ''
  hasSearched.value = true
  emit('search-start', searchText)

  try {
    const result = await props.map.search(searchText, props.searchType, {
      timeout: props.timeout,
      pageSize: pageSize.value,
      pageNum: page,
    })

    let searchResults: any[] = []
    const resultType = Number(result.getResultType())

    switch (resultType) {
      case 1: // 普通搜索
        searchResults = result.getPois()
        break
      case 2: // 视野内搜索
        searchResults = result.getStatistics() as any
        break
      case 3: // 行政区划边界
        searchResults = [result.getArea()]
        break
      case 4: // 建议词搜索
        searchResults = result.getSuggests()
        break
      case 5: // 公交信息
        searchResults = result.getLineData()
        break
      default:
        console.warn(`未处理的搜索结果类型: ${resultType}`)
        searchResults = []
    }

    results.value = searchResults

    // api似乎不能超出100条
    total.value = Number(result.getCount() ?? 0)
    total.value = Math.min(100, total.value)

    emit('search-success', {
      keyword: searchText,
      results: searchResults,
      total: total.value,
      resultType,
    })
  } catch (error) {
    console.error('搜索失败:', error)
    results.value = []
    total.value = 0
    searchError.value = '搜索失败，请重试'
    emit('search-error', {
      keyword: searchText,
      error: error instanceof Error ? error : new Error('搜索失败'),
    })
  } finally {
    searching.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  handleSearch(page)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  handleSearch(1)
}

const handleSelect = (result: any) => {
  emit('select', result)
  closeResults()
  keyword.value = ''
}

const clearResults = () => {
  results.value = []
  total.value = 0
  currentPage.value = 1
  searchError.value = ''
  hasSearched.value = false
  keyword.value = ''
  isResultsVisible.value = false
}

const closeResults = () => {
  isResultsVisible.value = false
}

const handleClickOutside = (event: MouseEvent) => {
  const searchEl = document.querySelector('.t-map-search')
  if (searchEl && !searchEl.contains(event.target as Node)) {
    closeResults()
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeResults()
  }
}

onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside)
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('mousedown', handleClickOutside)
  document.removeEventListener('keydown', handleKeydown)
})

defineExpose({
  search: handleSearch,
  clearResults,
  closeResults,
})

const handleInput = () => {
  if (keyword.value.trim()) {
    isResultsVisible.value = true
  }
}

const handleFocus = () => {
  if (keyword.value.trim() || hasSearched.value) {
    isResultsVisible.value = true
  }
}
</script>

<style lang="scss" scoped>
.t-map-search {
  width: 100%;

  &__results {
    margin-top: 8px;
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: 120px;
  }

  &__result-item {
    padding: 12px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  &__result-name {
    font-size: 14px;
    color: #303133;
    margin-bottom: 4px;
  }

  &__result-address {
    font-size: 12px;
    color: #909399;
  }

  &__pagination {
    padding: 12px;
    border-top: 1px solid #ebeef5;
    display: flex;
    justify-content: center;
  }

  &__results-header {
    display: flex;
    justify-content: flex-end;
    padding: 8px;
  }

  &__close-btn {
    cursor: pointer;
    padding: 4px;
    color: #909399;
    transition: color 0.3s;

    &:hover {
      color: #303133;
    }
  }

  // 深度选择器处理
  :deep(.el-empty) {
    padding: 24px 0;
  }
}
</style>
