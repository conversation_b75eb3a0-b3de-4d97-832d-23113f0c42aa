export type TMapEventType = T.MapEventType
export type TMapEventHandler = (event: any) => void
export type TViewChangeEvent = T.ViewChangeEvent

export interface TMapEvent {
  type: string
  lnglat: T.LngLat
  pixel: { x: number; y: number }
  target: any
}

// Label 事件类型定义
export type TLabelEventType = T.LabelEventType
export type TLabelEventParams = T.LabelEventParams
export type TLabelEventHandler = T.LabelEventHandler

// Marker 事件类型定义
export type TMarkerEventType = T.MarkerEventType
export type TMarkerEventHandler = (evt: any) => void

