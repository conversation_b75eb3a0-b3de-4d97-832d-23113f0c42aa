import type { TNativeMap } from '../../types'
import type { TMap } from '../TMap'
import { IdGenerator } from '../../utils/IdGenerator'
import type { TOverlay, TOverlayOptions } from './TOverlay'

export abstract class TBaseOverlay<T = any> implements TOverlay {
  protected readonly id: string
  protected map: TMap | null = null
  protected hidden: boolean = false
  protected isAddedToMap: boolean = false
  protected userData: T | null = null

  constructor(options: TOverlayOptions, prefix: string) {
    this.id = options.id ?? IdGenerator.generate(prefix)
    this.userData = options.userData ?? null
  }

  public getId(): string {
    return this.id
  }

  public getUserData(): T | null {
    return this.userData
  }

  public setUserData(data: T | null): void {
    this.userData = data
  }

  protected assertMapExists(): TNativeMap {
    if (!this.map) {
      throw new Error('Overlay has not been added to a map')
    }
    return this.map.getNativeMap()
  }

  protected assertAddedToMap(operation: string): void {
    if (!this.isAddedToMap) {
      throw new Error(`Cannot ${operation}: overlay is not added to map`)
    }
  }

  public addTo(map: TMap): void {
    this.map = map
    this.isAddedToMap = true
    this.onAdd(map.getNativeMap())
  }

  public remove(): void {
    if (this.isAddedToMap && this.map) {
      this.onRemove()
      this.map = null
      this.isAddedToMap = false
    }
  }

  public show(): void {
    this.assertAddedToMap('show')
    this.onShow()
    this.hidden = false
  }

  public hide(): void {
    this.assertAddedToMap('hide')
    this.onHide()
    this.hidden = true
  }

  public isHidden(): boolean {
    return this.hidden
  }

  public hasAddedToMap(): boolean {
    return this.isAddedToMap
  }

  public getMap(): TMap | null {
    return this.map
  }

  protected abstract onAdd(map: TNativeMap): void
  protected abstract onRemove(): void
  protected abstract onShow(): void
  protected abstract onHide(): void
}
