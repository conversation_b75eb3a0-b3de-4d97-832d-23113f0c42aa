import {
  DEFAULT_MAP_CONFIG,
  LAYER_CONFIG,
  TControlPosition,
  TMAP_CONFIG,
  TSearchType,
} from '../constants'
import type {
  TLayerType,
  TMapEventHandler,
  TMapEventType,
  TNativeLocalSearchResult,
  TNativeMap,
  TNativeTileLayer,
  TNativeOverviewMap,
  TNativeOverviewMapOptions,
} from '../types'

import {
  TComponentOverlay,
  THtmlOverlay,
  TInfoWindow,
  TLabel,
  TMarker,
  TPolygon,
  TPolyline,
  type TComponentOverlayOptions,
  type THtmlOverlayOptions,
  type TInfoWindowOptions,
  type TLabelOptions,
  type TMarkerOptions,
  type TPolygonOptions,
  type TPolylineOptions,
} from './overlay'
import { TSearch, TGeocoder } from './service'
import { TMarkTool, type TMarkToolOptions } from './control'
import { TPointMarkerTool, type TPointMarkerToolOptions } from './control/TPointMarkerTool'
import { TPolygonTool, type TPolygonToolOptions } from './control/TPolygonTool'
import { TPolylineTool, type TPolylineToolOptions } from './control/TPolylineTool'
import type { TMapSearchOptions } from '../components/TMapView.vue'
import { validateTMapAPI } from '../utils/validator'
import { TOverlayLayer, type TOverlayLayerOptions } from './layer/TOverlayLayer'
import type { TOverlay } from './base/TOverlay'
import { patchTMapAPI } from '../utils/patcher'

export interface TNativeMapOptions {
  projection?: string
  minZoom?: number
  maxZoom?: number
  maxBounds?: T.LngLatBounds
  center?: T.LngLat
  zoom?: number
}

// 扩展的地图选项接口
export interface TMapOptions {
  projection?: string
  minZoom?: number
  maxZoom?: number
  maxBounds?: T.LngLatBounds
  center?: {
    lng: number
    lat: number
  }
  zoom?: number
  searchTimeout?: number
  idleTimeout?: number
  search?: TMapSearchOptions
}

export class TMap {
  private readonly map: TNativeMap
  private readonly searchService: TSearch
  private readonly geocoderService: TGeocoder
  private readonly initialOptions: TNativeMapOptions

  private readonly layers: Map<string, TNativeTileLayer> = new Map()
  private readonly controls: Set<T.Control> = new Set()
  private readonly eventHandlers: Map<string, Set<TMapEventHandler>> = new Map()
  private eventListenerMap = new Map<TMapEventType, (event: any) => void>()

  private idleTimer: number | null = null
  private readonly defaultIdleTimeout = 5 * 60 * 1000
  private readonly defaultIdleEvents = [
    'moveend',
    'zoomend',
    'click',
    'dblclick',
    'mousedown',
    'mouseup',
  ] as const
  private _cleanupIdleTimer: (() => void) | null = null

  private readonly overlayLayers: Map<string, TOverlayLayer> = new Map()
  private readonly defaultOverlayLayer: TOverlayLayer

  constructor(container: string | HTMLElement, options: TMapOptions = {}) {
    validateTMapAPI()

    this.initialOptions = this.createMapOptions(options)
    const mergedOptions = this.initialOptions
    this.map = this.initializeMap(container, mergedOptions)

    this.defaultOverlayLayer = new TOverlayLayer()
    this.defaultOverlayLayer.addTo(this)

    this.searchService = new TSearch(this.map, {
      timeout: options.searchTimeout,
    })
    this.geocoderService = new TGeocoder(this.map)

    this.initializeDefaultLayer()
    this.initializeCenter(mergedOptions)

    if (options.idleTimeout && options.idleTimeout > 0) {
      this.enableIdleTimer(options.idleTimeout)
    }

    patchTMapAPI()
  }

  // =============== 地图核心方法 ===============
  public getNativeMap(): TNativeMap {
    return this.map
  }

  public getInstance(): TNativeMap {
    return this.map
  }

  public getSearchService(): TSearch {
    return this.searchService
  }

  // =============== 地图视图控制 ===============
  public setCenter(center: { lng: number; lat: number }, zoom?: number): void {
    const point = new window.T.LngLat(center.lng, center.lat)
    if (zoom !== undefined) {
      this.map.centerAndZoom(point, zoom)
    } else {
      this.map.setCenter(point)
    }
  }

  public getCenter(): { lng: number; lat: number } {
    const center = this.map.getCenter()
    return { lng: center.lng, lat: center.lat }
  }

  public setZoom(zoom: number): void {
    this.map.setZoom(zoom)
  }

  public getZoom(): number {
    return this.map.getZoom()
  }

  public panTo(center: { lng: number; lat: number }, zoom?: number): void {
    const point = new window.T.LngLat(center.lng, center.lat)
    if (zoom !== undefined) {
      this.map.centerAndZoom(point, zoom)
    } else {
      this.map.panTo(point)
    }
  }

  /**
   * 获取地图当前视野范围
   */
  public getBounds(): T.LngLatBounds {
    return this.map.getBounds()
  }

  /**
   * 放大一级视图
   */
  public zoomIn(): void {
    this.map.zoomIn()
  }

  /**
   * 缩小一级视图
   */
  public zoomOut(): void {
    this.map.zoomOut()
  }

  /**
   * 恢复地图视图到初始状态
   */
  public resetView(options?: { center?: { lng: number; lat: number }; zoom?: number }): void {
    const center = options?.center ?? this.initialOptions.center ?? DEFAULT_MAP_CONFIG.CENTER
    const zoom = options?.zoom ?? this.initialOptions.zoom ?? DEFAULT_MAP_CONFIG.ZOOM
    this.setCenter(center, zoom)
  }

  // =============== 图层管理 ===============
  public switchBaseLayer(type: TLayerType = 'vec'): void {
    this.clearLayers()
    const { baseLayer, annotationLayer } = this.createLayers(type)
    this.addLayersToMap(baseLayer, annotationLayer)

    // 更新所有鹰眼地图的图层
    this.controls.forEach((control) => {
      if (control instanceof T.Control.OverviewMap) {
        const miniMap = control.getMiniMap()
        if (miniMap) {
          miniMap.clearOverLays()
          const { baseLayer: miniBaseLayer, annotationLayer: miniAnnotationLayer } =
            this.createLayers(type)
          miniMap.addLayer(miniBaseLayer)
          miniMap.addLayer(miniAnnotationLayer)
        }
      }
    })
  }

  private clearLayers(): void {
    this.layers.forEach((layer) => this.map.removeLayer(layer))
    this.layers.clear()
  }

  private createLayers(type: TLayerType): {
    baseLayer: TNativeTileLayer
    annotationLayer: TNativeTileLayer
  } {
    const layerConfig = LAYER_CONFIG[type]
    const layerOptions = {
      minZoom: DEFAULT_MAP_CONFIG.MIN_ZOOM,
      maxZoom: DEFAULT_MAP_CONFIG.MAX_ZOOM,
    }

    const baseLayer = new window.T.TileLayer(
      this.createLayerUrl(layerConfig.base.type, layerConfig.base.layer),
      layerOptions,
    )

    const annotationLayer = new window.T.TileLayer(
      this.createLayerUrl(layerConfig.annotation.type, layerConfig.annotation.layer),
      layerOptions,
    )

    return { baseLayer, annotationLayer }
  }

  private createLayerUrl(type: string, layer: string): string {
    return `http://t0.tianditu.gov.cn/${type}/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=${layer}&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TMAP_CONFIG.KEY}`
  }

  private addLayersToMap(baseLayer: TNativeTileLayer, annotationLayer: TNativeTileLayer): void {
    this.map.addLayer(baseLayer)
    this.map.addLayer(annotationLayer)
    this.layers.set('base', baseLayer)
    this.layers.set('annotation', annotationLayer)
  }

  /**
   * 获取当前图层类型
   * @private
   * @returns {TLayerType} 当前图层类型
   */
  private getCurrentLayerType(): TLayerType {
    // 从 LAYER_CONFIG 中获取所有可能的图层类型
    const layerTypes = Object.keys(LAYER_CONFIG) as TLayerType[]
    for (const type of layerTypes) {
      const baseLayer = this.layers.get('base')
      if (baseLayer) {
        const url = baseLayer.getTileUrl(0, 0, 0)
        if (url.includes(LAYER_CONFIG[type].base.type)) {
          return type
        }
      }
    }
    return 'vec'
  }

  /**
   * 创建标注工具
   */
  public createMarkTool(options?: TMarkToolOptions): TMarkTool {
    return new TMarkTool(this, options)
  }

  public createPointMarkerTool(options?: TPointMarkerToolOptions): TPointMarkerTool {
    return new TPointMarkerTool(this, options)
  }

  public createPolylineTool(options?: TPolylineToolOptions): TPolylineTool {
    return new TPolylineTool(this, options)
  }

  public createPolygonTool(options?: TPolygonToolOptions): TPolygonTool {
    return new TPolygonTool(this, options)
  }

  // =============== 事件管理 ===============
  public on(eventName: TMapEventType, handler: TMapEventHandler): void {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, new Set())
      const listener = (event: any) => {
        const handlers = this.eventHandlers.get(eventName)
        handlers?.forEach((h) => h(event))
      }
      this.eventListenerMap.set(eventName, listener)
      this.map.addEventListener(eventName, listener)
    }
    this.eventHandlers.get(eventName)?.add(handler)
  }

  public off(eventName: TMapEventType, handler: TMapEventHandler): void {
    const handlers = this.eventHandlers.get(eventName)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        const listener = this.eventListenerMap.get(eventName)
        if (listener) {
          this.map.removeEventListener(eventName, listener)
          this.eventListenerMap.delete(eventName)
          this.eventHandlers.delete(eventName)
        }
      }
    }
  }

  // =============== 控件管理 ===============
  /**
   * 向地图添加控件
   * @param control 要添加的控件
   */
  public addControl(control: T.Control): void {
    if (!this.controls.has(control)) {
      this.map.addControl(control)
      this.controls.add(control)
    }
  }

  /**
   * 从地图移除控件
   * @param control 要移除的控件
   */
  public removeControl(control: T.Control): void {
    if (this.controls.has(control)) {
      this.map.removeControl(control)
      this.controls.delete(control)
    }
  }

  /**
   * 清除地图上所有控件
   */
  public clearControls(): void {
    this.controls.forEach((control) => {
      this.map.removeControl(control)
    })
    this.controls.clear()
  }

  /**
   * 添加鹰眼地图控件
   * @param options 鹰眼地图配置选项
   * @returns 鹰眼地图控件实例
   */
  public addOverviewMap(options?: TNativeOverviewMapOptions): TNativeOverviewMap {
    const overviewMap = new T.Control.OverviewMap({
      position: TControlPosition.BOTTOM_RIGHT,
      ...options,
    })
    this.addControl(overviewMap)

    // 获取鹰眼地图实例并设置其图层
    const miniMap = overviewMap.getMiniMap()
    if (miniMap) {
      // 清除原有图层
      miniMap.clearOverLays()
      // 获取当前主图的图层类型
      const currentType = this.getCurrentLayerType()
      // 创建与主图相同类型的图层
      const { baseLayer, annotationLayer } = this.createLayers(currentType)
      // 添加到鹰眼地图
      miniMap.addLayer(baseLayer)
      miniMap.addLayer(annotationLayer)
    }

    return overviewMap
  }

  // =============== 搜索服务 ===============
  /**
   * 搜索地点
   * @param keyword 搜索关键词
   * @param type 搜索类型
   * @param options 搜索选项，包括超时时间、分页大小和页码
   */
  public search(
    keyword: string,
    type: TSearchType = TSearchType.NORMAL,
    options?: {
      timeout?: number
      pageSize?: number
      pageNum?: number
    },
  ): Promise<TNativeLocalSearchResult> {
    return this.searchService.search(keyword, type, options)
  }

  /**
   * 在指定范围内搜索
   * @param keyword 搜索关键词
   * @param bounds 搜索范围
   * @param options 搜索选项
   */
  public searchInBounds(
    keyword: string,
    bounds: T.LngLatBounds,
    options?: {
      timeout?: number
      pageSize?: number
      pageNum?: number
    },
  ): Promise<TNativeLocalSearchResult> {
    return this.searchService.searchInBounds(keyword, bounds, options)
  }

  /**
   * 在指定中心点周围搜索
   * @param keyword 搜索关键词
   * @param center 中心点坐标
   * @param radius 搜索半径（米）
   * @param options 搜索选项
   */
  public searchNearby(
    keyword: string,
    center: { lng: number; lat: number },
    radius: number,
    options?: {
      timeout?: number
      pageSize?: number
      pageNum?: number
    },
  ): Promise<TNativeLocalSearchResult> {
    return this.searchService.searchNearby(keyword, center, radius, options)
  }

  public async geocode(address: string) {
    return this.geocoderService.getPoint(address)
  }

  public async reverseGeocode(point: T.LngLat) {
    return this.geocoderService.getLocation(point)
  }

  // =============== 空闲计时器管理 ===============
  /**
   * 启用空闲重置定时器
   * @param timeout 超时时间（毫秒）
   * @param events 要监听的事件数组（可选）
   */
  public enableIdleTimer(
    timeout: number = this.defaultIdleTimeout,
    events?: TMapEventType[],
  ): void {
    this.disableIdleTimer() // 先清理现有定时器

    const resetTimer = () => {
      if (this.idleTimer) {
        window.clearTimeout(this.idleTimer)
      }

      this.idleTimer = window.setTimeout(() => {
        this.resetView()
      }, timeout)
    }

    // 监听地图事件
    const eventsToListen = events || this.defaultIdleEvents
    eventsToListen.forEach((event) => {
      this.on(event, resetTimer)
    })

    // 监听全局事件
    const handleGlobalEvent = () => {
      if (this.isMouseOverMap()) {
        resetTimer()
      }
    }

    window.addEventListener('mousemove', handleGlobalEvent)
    window.addEventListener('keydown', handleGlobalEvent)
    window.addEventListener('wheel', handleGlobalEvent)
    window.addEventListener('touchstart', handleGlobalEvent)

    // 保存清理函数
    this._cleanupIdleTimer = () => {
      if (this.idleTimer) {
        window.clearTimeout(this.idleTimer)
        this.idleTimer = null
      }

      eventsToListen.forEach((event) => {
        this.off(event, resetTimer)
      })

      window.removeEventListener('mousemove', handleGlobalEvent)
      window.removeEventListener('keydown', handleGlobalEvent)
      window.removeEventListener('wheel', handleGlobalEvent)
      window.removeEventListener('touchstart', handleGlobalEvent)
    }

    // 初始启动定时器
    resetTimer()
  }

  /**
   * 禁用空闲重置定时器
   */
  public disableIdleTimer(): void {
    if (this._cleanupIdleTimer) {
      this._cleanupIdleTimer()
      this._cleanupIdleTimer = null
    }
  }

  private isMouseOverMap(): boolean {
    const mapElement = this.map.getContainer()
    if (!mapElement) return false

    const rect = mapElement.getBoundingClientRect()
    const mouseX = (window.event as MouseEvent)?.clientX ?? 0
    const mouseY = (window.event as MouseEvent)?.clientY ?? 0

    return (
      mouseX >= rect.left && mouseX <= rect.right && mouseY >= rect.top && mouseY <= rect.bottom
    )
  }

  // =============== 初始化方法 ===============
  private createMapOptions(options: TMapOptions): TNativeMapOptions {
    // 创建基础配置
    const baseOptions = {
      ...DEFAULT_MAP_CONFIG,
      minZoom: Math.max(
        options.minZoom ?? DEFAULT_MAP_CONFIG.MIN_ZOOM,
        DEFAULT_MAP_CONFIG.MIN_ZOOM,
      ),
      maxZoom: Math.min(
        options.maxZoom ?? DEFAULT_MAP_CONFIG.MAX_ZOOM,
        DEFAULT_MAP_CONFIG.MAX_ZOOM,
      ),
    }

    // 处理 center，确保它是 T.LngLat 类型
    if (options.center) {
      return {
        ...baseOptions,
        ...options,
        center: new window.T.LngLat(options.center.lng, options.center.lat),
      } as TNativeMapOptions
    }

    return {
      ...baseOptions,
      ...options,
    } as TNativeMapOptions
  }

  private initializeMap(container: string | HTMLElement, options: TNativeMapOptions): TNativeMap {
    try {
      return new window.T.Map(container, options)
    } catch (error) {
      throw new Error(`地图初始化失败: ${(error as Error).message}`)
    }
  }

  private initializeDefaultLayer(): void {
    this.switchBaseLayer('vec')
  }

  private initializeCenter(options: TNativeMapOptions): void {
    if (options.center) {
      this.setCenter(options.center, options.zoom ?? DEFAULT_MAP_CONFIG.ZOOM)
    }
  }

  // =============== 清理方法 ===============
  public destroy(): void {
    this.clearInfoWindows()
    this.disableIdleTimer()
    this.clearLayers()
    this.clearControls()
    this.eventHandlers.clear()
    this.eventListenerMap.forEach((listener, eventName) => {
      this.map.removeEventListener(eventName, listener)
    })
    this.eventListenerMap.clear()
    if (typeof this.map.destroy === 'function') {
      this.map.destroy()
    }
    this.searchService.destroy()
  }

  // =============== 叠加物管理 ===============
  /**
   * 添加叠加物到默认图层
   */
  public addOverlay(overlay: TOverlay): string {
    return this.defaultOverlayLayer.add(overlay)
  }

  public getOverlay<T extends TOverlay>(id: string): T | undefined {
    return this.defaultOverlayLayer.get(id) as T | undefined
  }

  /**
   * 移除默认图层中的叠加物
   */
  public removeOverlay(id: string): void {
    this.defaultOverlayLayer.remove(id)
  }

  /**
   * 清除默认图层中的所有叠加物
   */
  public clearOverlays(): void {
    this.defaultOverlayLayer.clear()
  }

  // =============== 叠加层管理 ===============
  /**
   * 创建新的叠加层
   */
  public createOverlayLayer(options: TOverlayLayerOptions = {}): TOverlayLayer {
    const layer = new TOverlayLayer(options)
    if (options.id) {
      this.overlayLayers.set(options.id, layer)
    }
    layer.addTo(this)
    return layer
  }

  /**
   * 添加已存在的叠加层
   */
  public addOverlayLayer(layer: TOverlayLayer): void {
    this.overlayLayers.set(layer.getId(), layer)
    layer.addTo(this)
  }

  /**
   * 移除叠加层
   */
  public removeOverlayLayer(id: string): void {
    const layer = this.overlayLayers.get(id)
    if (layer) {
      layer.removeFromMap()
      this.overlayLayers.delete(id)
    }
  }

  /**
   * 获取叠加层
   */
  public getOverlayLayer(id: string): TOverlayLayer | undefined {
    return this.overlayLayers.get(id)
  }

  /**
   * 获取默认叠加层
   */
  public getDefaultOverlayLayer(): TOverlayLayer {
    return this.defaultOverlayLayer
  }

  /**
   * 清除所有叠加层（除默认层外）
   */
  public clearOverlayLayers(): void {
    this.overlayLayers.forEach((layer) => {
      layer.removeFromMap()
    })
    this.overlayLayers.clear()
  }

  // =============== 叠加层显示控制 ===============
  /**
   * 显示指定叠加层
   */
  public showOverlayLayer(id: string): void {
    const layer = this.overlayLayers.get(id)
    layer?.show()
  }

  /**
   * 隐藏指定叠加层
   */
  public hideOverlayLayer(id: string): void {
    const layer = this.overlayLayers.get(id)
    layer?.hide()
  }

  /**
   * 设置叠加层可见性
   */
  public setOverlayLayerVisible(id: string, visible: boolean): void {
    const layer = this.overlayLayers.get(id)
    if (layer) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      visible ? layer.show() : layer.hide()
    }
  }

  // =============== 信息窗口管理 ===============
  private infoWindows = new Map<string, TInfoWindow>()

  /**
   * 创建信息窗口
   * @param options 信息窗口配置选项
   * @returns 信息窗口ID
   */
  public createInfoWindow<T = any>(options: TInfoWindowOptions<T>): string {
    const infoWindow = new TInfoWindow<T>(options)
    infoWindow.addTo(this)
    const id = infoWindow.getId()
    this.infoWindows.set(id, infoWindow)
    return id
  }

  /**
   * 获取信息窗口实例
   * @param id 信息窗口ID
   */
  public getInfoWindow<T = any>(id: string): TInfoWindow<T> | undefined {
    return this.infoWindows.get(id) as TInfoWindow<T> | undefined
  }

  /**
   * 移除信息窗口
   * @param id 信息窗口ID
   * @returns 是否成功移除
   */
  public removeInfoWindow(id: string): boolean {
    const infoWindow = this.infoWindows.get(id)
    if (infoWindow) {
      infoWindow.destroy()
      this.infoWindows.delete(id)
      return true
    }
    return false
  }

  /**
   * 清除所有信息窗口
   */
  public clearInfoWindows(): void {
    this.infoWindows.forEach((infoWindow) => infoWindow.destroy())
    this.infoWindows.clear()
  }

  /**
   * 在指定位置打开信息窗口
   * @param id 信息窗口ID
   * @param position 位置坐标 [经度, 纬度]
   */
  public openInfoWindowAt(id: string, position: [number, number]): void {
    const infoWindow = this.infoWindows.get(id)
    if (infoWindow) {
      infoWindow.openAt(position)
    }
  }

  /**
   * 关闭指定的信息窗口
   * @param id 信息窗口ID
   */
  public closeInfoWindow(id: string): void {
    const infoWindow = this.infoWindows.get(id)
    if (infoWindow) {
      infoWindow.close()
    }
  }

  // =============== 覆盖物工厂方法 ===============
  /**
   * 创建标记点并添加到默认图层
   * @returns 标记点ID
   */
  public createMarker<T = any>(options: TMarkerOptions<T>): string {
    const marker = new TMarker<T>(options)
    return this.defaultOverlayLayer.add(marker)
  }

  /**
   * 创建折线并添加到默认图层
   * @returns 折线ID
   */
  public createPolyline<T = any>(options: TPolylineOptions<T>): string {
    const polyline = new TPolyline<T>(options)
    return this.defaultOverlayLayer.add(polyline)
  }

  /**
   * 创建多边形并添加到默认图层
   */
  public createPolygon<T = any>(options: TPolygonOptions<T>): string {
    const polygon = new TPolygon<T>(options)
    return this.defaultOverlayLayer.add(polygon)
  }

  /**
   * 创建文本标注并添加到默认图层
   */
  public createLabel<T = any>(options: TLabelOptions<T>): string {
    const label = new TLabel<T>(options)
    return this.defaultOverlayLayer.add(label)
  }

  /**
   * 创建HTML覆盖物并添加到默认图层
   */
  public createHtmlOverlay<T = any>(options: THtmlOverlayOptions<T>): string {
    const overlay = new THtmlOverlay<T>(options)
    return this.defaultOverlayLayer.add(overlay)
  }

  /**
   * 创建Vue组件覆盖物并添加到默认图层
   */
  public createComponentOverlay<T = any>(options: TComponentOverlayOptions<T>): string {
    const overlay = new TComponentOverlay<T>(options)
    return this.defaultOverlayLayer.add(overlay)
  }

  public updateSize(): void {
    this.map.checkResize()
  }
}
