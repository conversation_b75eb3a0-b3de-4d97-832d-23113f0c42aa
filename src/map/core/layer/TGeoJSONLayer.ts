import { createLogger } from '@/utils/logger'
import type { TMap } from '../TMap'
import {
  TPolygon,
  type TPolygonOptions,
  TLabel,
  type TLabelOptions,
  type TPolygonStyle,
} from '../overlay'

const logger = createLogger('TGeoJSONLayer')

export interface GeoJSONFeature {
  type: 'Feature'
  geometry: {
    type: 'MultiPolygon' | 'Polygon'
    coordinates: number[][][] | number[][][][]
  }
  properties: {
    name: string
    gb: string
    [key: string]: any
  }
}

export interface GeoJSONData {
  type: 'FeatureCollection'
  features: GeoJSONFeature[]
}

export type TGeoJSONStyle = TPolygonStyle

export interface TGeoJSONLabelOptions {
  show?: boolean
  offset?: [number, number]
  style?: Partial<CSSStyleDeclaration>
}

export interface TGeoJSONLayerOptions {
  style?: TGeoJSONStyle
  computeFeatureStyle?: (feature: GeoJSONFeature) => Partial<TGeoJSONStyle>
  label?: TGeoJSONLabelOptions
  onClick?: (polygon: TPolygon, feature: GeoJSONFeature) => void
  onMouseOver?: (polygon: TPolygon, feature: GeoJSONFeature) => void
  onMouseOut?: (polygon: TPolygon, feature: GeoJSONFeature) => void
  drilldown?: {
    enable?: boolean
    inactiveStyle?: TGeoJSONStyle
    zoomLevels?: {
      min: number
      max: number
    }
    maxDepth?: number
  }
}

export class TGeoJSONLayer {
  private map: TMap
  private polygons: TPolygon[] = []
  private labels: TLabel[] = []
  private options: TGeoJSONLayerOptions
  private featureMap: Map<TPolygon, GeoJSONFeature> = new Map()
  private currentFeature: GeoJSONFeature | null = null
  private originalBounds: T.LngLatBounds | null = null
  private drillStack: GeoJSONFeature[] = []
  private originalZoom: number | null = null
  private boundsList: T.LngLatBounds[] = []
  private originalData: GeoJSONData | null = null

  constructor(map: TMap, options: TGeoJSONLayerOptions = {}) {
    this.map = map
    this.options = options
  }

  public async load(data: GeoJSONData) {
    this.clear()
    this.originalData = data

    for (const feature of data.features) {
      const polygons = this.createPolygonsFromFeature(feature)
      for (const polygon of polygons) {
        polygon.addTo(this.map)
        this.polygons.push(polygon)
        this.featureMap.set(polygon, feature)
        this.bindPolygonEvents(polygon, feature)
      }
    }
  }

  /**
   * 根据属性过滤并加载GeoJSON数据
   * @param data GeoJSON数据
   * @param key 属性名
   * @param value 属性值
   * @returns 是否成功加载
   */
  public async loadByProperty(data: GeoJSONData, key: string, value: any): Promise<boolean> {
    if (!data || !data.features || data.features.length === 0) {
      logger.warn('GeoJSON数据为空或无效')
      return false
    }

    const filteredFeatures = data.features.filter(feature =>
      feature.properties && feature.properties[key] === value
    )

    if (filteredFeatures.length === 0) {
      logger.warn(`未找到 ${key}="${value}" 的区域`)
      return false
    }

    const filteredData: GeoJSONData = {
      type: 'FeatureCollection',
      features: filteredFeatures
    }

    this.originalData = data
    await this.load(filteredData)
    return true
  }

  /**
   * 根据名称加载GeoJSON数据
   * @param data GeoJSON数据
   * @param name 区域名称
   * @returns 是否成功加载
   */
  public async loadByName(data: GeoJSONData, name: string): Promise<boolean> {
    return this.loadByProperty(data, 'name', name)
  }

  /**
   * 根据GB编码加载GeoJSON数据
   * @param data GeoJSON数据
   * @param gb GB编码
   * @returns 是否成功加载
   */
  public async loadByGB(data: GeoJSONData, gb: string): Promise<boolean> {
    return this.loadByProperty(data, 'gb', gb)
  }

  private getFeatureStyle(feature: GeoJSONFeature): TGeoJSONStyle {
    const defaultStyle: TGeoJSONStyle = {
      color: '#6FF9ED',
      weight: 1.5,
      opacity: 0.9,
      fillColor: '#5CA0FA',
      fillOpacity: 0.9,
      lineStyle: 'solid',
    }

    const globalStyle = { ...defaultStyle, ...this.options.style }

    if (this.options.computeFeatureStyle) {
      const featureStyle = this.options.computeFeatureStyle(feature)
      return { ...globalStyle, ...featureStyle }
    }

    return globalStyle
  }

  private createPolygonsFromFeature(feature: GeoJSONFeature): TPolygon[] {
    const polygons: TPolygon[] = []

    if (feature.geometry.type === 'MultiPolygon') {
      const coordinates = feature.geometry.coordinates as number[][][][]
      for (const polygonCoords of coordinates) {
        const polygon = this.createPolygon(polygonCoords[0], feature)
        polygons.push(polygon)
      }
    } else if (feature.geometry.type === 'Polygon') {
      const coordinates = feature.geometry.coordinates as number[][][]
      const polygon = this.createPolygon(coordinates[0], feature)
      polygons.push(polygon)
    }

    return polygons
  }

  private createPolygon(coordinates: number[][], feature: GeoJSONFeature): TPolygon {
    const path = coordinates.map(([lng, lat]) => [lng, lat])
    const style = this.getFeatureStyle(feature)

    const polygonOptions: TPolygonOptions = {
      path: path as [number, number][],
      color: style.color,
      weight: style.weight,
      opacity: style.opacity,
      fillColor: style.fillColor,
      fillOpacity: style.fillOpacity,
      lineStyle: style.lineStyle,
      userData: feature.properties,
    }

    const polygon = new TPolygon(polygonOptions)

    if (this.options.label?.show && feature.properties.name) {
      this.createLabel(feature, path, this.options.label)
    }

    return polygon
  }

  private createLabel(
    feature: GeoJSONFeature,
    path: number[][],
    labelOptions: TGeoJSONLabelOptions,
  ) {
    const center = this.calculatePolygonCenter(path)

    const options: TLabelOptions = {
      text: feature.properties.name,
      position: center,
      offset: [0, 0],
      style: {
        background: 'none',
        lineHeight: '1',
        textAlign: 'center',
        whiteSpace: 'nowrap',
        position: 'absolute',
        left: '50%',
        ...labelOptions?.style,
      },
    }

    const label = new TLabel(options)
    label.addTo(this.map)
    this.labels.push(label)

    this.updateLabelPosition(label)
  }

  private calculatePolygonCenter(coordinates: number[][]): [number, number] {
    let area = 0
    let centerX = 0
    let centerY = 0
    const n = coordinates.length

    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n
      const [xi, yi] = coordinates[i]
      const [xj, yj] = coordinates[j]

      const factor = xi * yj - xj * yi
      area += factor
      centerX += (xi + xj) * factor
      centerY += (yi + yj) * factor
    }

    area = area / 2
    const f = area * 6

    const centroid: [number, number] = [centerX / f, centerY / f]

    if (!isFinite(centroid[0]) || !isFinite(centroid[1])) {
      let minX = Infinity
      let maxX = -Infinity
      let minY = Infinity
      let maxY = -Infinity

      coordinates.forEach(([x, y]) => {
        minX = Math.min(minX, x)
        maxX = Math.max(maxX, x)
        minY = Math.min(minY, y)
        maxY = Math.max(maxY, y)
      })

      return [(minX + maxX) / 2, (minY + maxY) / 2]
    }

    return centroid
  }

  private bindPolygonEvents(polygon: TPolygon, feature: GeoJSONFeature) {
    if (this.options.onClick) {
      polygon.on('click', () => this.options.onClick?.(polygon, feature))
    }

    if (this.options.onMouseOver) {
      polygon.on('mouseover', () => this.options.onMouseOver?.(polygon, feature))
    }

    if (this.options.onMouseOut) {
      polygon.on('mouseout', () => this.options.onMouseOut?.(polygon, feature))
    }
  }

  public getPolygonAtPixel(pixel: [number, number]): TPolygon | null {
    const nativeMap = this.map.getNativeMap()
    const lngLat = nativeMap.containerPointToLngLat(new T.Point(pixel[0], pixel[1]))

    for (const polygon of this.polygons) {
      if (this.isPointInPolygon(lngLat, polygon.getPath()[0])) {
        return polygon
      }
    }

    return null
  }

  private isPointInPolygon(point: T.LngLat, polygon: [number, number][]): boolean {
    let inside = false
    const x = point.lng
    const y = point.lat

    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i][0]
      const yi = polygon[i][1]
      const xj = polygon[j][0]
      const yj = polygon[j][1]
      // x=xj+t(xi−xj),y=yj+t(yi−yj),t∈[0,1]
      //射线方程为 𝑦=point.laty=point.lat
      const intersect = yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi

      if (intersect) inside = !inside
    }

    return inside
  }

  public getPolygonByProperty(key: string, value: any): TPolygon | null {
    return this.polygons.find((polygon) => polygon.getUserData()?.[key] === value) ?? null
  }

  public getFeatureByPolygon(polygon: TPolygon): GeoJSONFeature | null {
    return this.featureMap.get(polygon) ?? null
  }

  public setStyle(style: Partial<TGeoJSONLayerOptions['style']>) {
    if (!style) return

    this.options.style = { ...this.options.style, ...style }

    for (const polygon of this.polygons) {
      if ('color' in style && style.color) polygon.setColor(style.color)
      if ('weight' in style && style.weight) polygon.setWeight(style.weight!)
      if ('opacity' in style && style.opacity) polygon.setOpacity(style.opacity!)
      if ('fillColor' in style && style.fillColor) polygon.setFillColor(style.fillColor!)
      if ('fillOpacity' in style && style.fillOpacity) polygon.setFillOpacity(style.fillOpacity!)
      if ('lineStyle' in style && style.lineStyle) polygon.setLineStyle(style.lineStyle!)
    }
  }

  public getPolygons(): TPolygon[] {
    return [...this.polygons]
  }

  public clear() {
    for (const polygon of this.polygons) {
      polygon.remove()
    }
    for (const label of this.labels) {
      label.remove()
    }
    this.polygons = []
    this.labels = []
    this.featureMap.clear()
  }

  /**
   * 加载原始数据中的所有区域
   * @returns 是否成功加载
   */
  public async loadAllData(): Promise<boolean> {
    if (!this.originalData) {
      logger.warn('没有可用的原始数据')
      return false
    }

    await this.load(this.originalData)
    return true
  }

  public remove() {
    this.clear()
  }

  public updateFeatureStyle(feature: GeoJSONFeature) {
    const polygons = this.polygons.filter(
      (polygon) => polygon.getUserData()?.gb === feature.properties.gb,
    )

    const style = this.getFeatureStyle(feature)
    for (const polygon of polygons) {
      polygon.setColor(style.color!)
      polygon.setWeight(style.weight!)
      polygon.setOpacity(style.opacity!)
      polygon.setFillColor(style.fillColor!)
      polygon.setFillOpacity(style.fillOpacity!)
      polygon.setLineStyle(style.lineStyle!)
    }
  }

  private updateLabelPosition(label: TLabel) {
    const element = label.getElement()
    if (element) {
      const width = element.offsetWidth
      element.style.marginLeft = `-${width / 2}px`
    }
  }

  public getDrillDepth(): number {
    return this.drillStack.length
  }

  public getDrillPath(): GeoJSONFeature[] {
    return [...this.drillStack]
  }

  public async drillTo(feature: GeoJSONFeature) {
    if (!this.options.drilldown?.enable) return

    const maxDepth = this.options.drilldown.maxDepth || 0
    if (maxDepth > 0 && this.drillStack.length >= maxDepth) {
      logger.warn(`已达到最大下钻深度: ${maxDepth}`)
      return
    }

    if (this.drillStack.length === 0) {
      const nativeMap = this.map.getNativeMap()
      this.originalZoom = nativeMap.getZoom()
      this.boundsList.push(nativeMap.getBounds())
    }

    const bounds = this.calculateFeatureBounds(feature)

    const viewport = this.map
      .getNativeMap()
      .getViewport([
        new T.LngLat(bounds.getSouthWest().lng, bounds.getSouthWest().lat),
        new T.LngLat(bounds.getNorthEast().lng, bounds.getNorthEast().lat),
      ])

    if (viewport && this.options.drilldown.zoomLevels) {
      const { min, max } = this.options.drilldown.zoomLevels
      if (viewport.zoom < min || viewport.zoom > max) {
        logger.warn(`目标缩放级别 ${viewport.zoom} 超出限制范围 [${min}, ${max}]`)
        return
      }
    }

    this.drillStack.push(feature)
    this.boundsList.push(bounds)
    this.currentFeature = feature

    this.updatePolygonsStyle(feature)
    this.panToBounds(bounds)
  }

  public async drillUp() {
    if (this.drillStack.length === 0) return

    this.drillStack.pop()
    this.boundsList.pop()

    const previousFeature = this.drillStack[this.drillStack.length - 1]
    const previousBounds = this.boundsList[this.boundsList.length - 1]

    if (previousFeature) {
      this.currentFeature = previousFeature
      this.updatePolygonsStyle(previousFeature)
      this.panToBounds(previousBounds)
    } else {
      this.currentFeature = null
      this.resetPolygonsStyle()
      this.panToBounds(previousBounds)
    }
  }

  public async drillToLevel(level: number) {
    if (level < 0 || level >= this.drillStack.length) return

    while (this.drillStack.length > level + 1) {
      await this.drillUp()
    }
  }

  public async reset() {
    if (this.drillStack.length === 0) return

    this.drillStack = []
    this.boundsList = [this.boundsList[0]]
    this.currentFeature = null
    this.resetPolygonsStyle()

    if (this.originalZoom !== null) {
      const nativeMap = this.map.getNativeMap()
      nativeMap.setZoom(this.originalZoom)
    }

    this.panToBounds(this.boundsList[0])
  }

  private calculateFeatureBounds(feature: GeoJSONFeature): T.LngLatBounds {
    let minLng = Infinity
    let maxLng = -Infinity
    let minLat = Infinity
    let maxLat = -Infinity

    const updateBounds = (lng: number, lat: number) => {
      minLng = Math.min(minLng, lng)
      maxLng = Math.max(maxLng, lng)
      minLat = Math.min(minLat, lat)
      maxLat = Math.max(maxLat, lat)
    }

    if (feature.geometry.type === 'MultiPolygon') {
      const coordinates = feature.geometry.coordinates as number[][][][]
      coordinates.forEach((polygon) => {
        polygon[0].forEach(([lng, lat]) => {
          updateBounds(lng, lat)
        })
      })
    } else if (feature.geometry.type === 'Polygon') {
      const coordinates = feature.geometry.coordinates as number[][][]
      coordinates[0].forEach(([lng, lat]) => {
        updateBounds(lng, lat)
      })
    }

    return new T.LngLatBounds(new T.LngLat(minLng, minLat), new T.LngLat(maxLng, maxLat))
  }

  private updatePolygonsStyle(activeFeature: GeoJSONFeature) {
    const inactiveStyle = this.options.drilldown?.inactiveStyle || {
      opacity: 0.3,
      fillOpacity: 0.1,
    }

    this.polygons.forEach((polygon) => {
      const feature = this.featureMap.get(polygon)
      if (!feature) return

      const isInPath = this.drillStack.some((f) => f.properties.gb === feature.properties.gb)

      if (feature === activeFeature || isInPath) {
        const style = this.getFeatureStyle(feature)
        polygon.updateStyle(style)
      } else {
        polygon.updateStyle(inactiveStyle)
      }
    })
  }

  private resetPolygonsStyle() {
    this.polygons.forEach((polygon) => {
      const feature = this.featureMap.get(polygon)
      if (feature) {
        const style = this.getFeatureStyle(feature)
        polygon.setColor(style.color!)
        polygon.setWeight(style.weight!)
        polygon.setOpacity(style.opacity!)
        polygon.setFillColor(style.fillColor!)
        polygon.setFillOpacity(style.fillOpacity!)
        polygon.setLineStyle(style.lineStyle!)
      }
    })
  }

  private panToBounds(bounds: T.LngLatBounds) {
    const nativeMap = this.map.getNativeMap()

    const viewport = nativeMap.getViewport([
      new T.LngLat(bounds.getSouthWest().lng, bounds.getSouthWest().lat),
      new T.LngLat(bounds.getNorthEast().lng, bounds.getNorthEast().lat),
    ])

    if (viewport) {
      nativeMap.panTo(viewport.center, viewport.zoom)
    }
  }

  /**
   * 根据属性获取 GeoJSON Feature
   * @param key 属性名
   * @param value 属性值
   * @returns 匹配的 Feature 或 null
   */
  public getFeatureByProperty(key: string, value: any): GeoJSONFeature | null {
    // 先从当前加载的数据中查找
    for (const feature of this.featureMap.values()) {
      if (feature.properties[key] === value) {
        return feature
      }
    }

    // 如果没找到且有原始数据，从原始数据中查找
    if (this.originalData && this.originalData.features) {
      const feature = this.originalData.features.find(f =>
        f.properties && f.properties[key] === value
      )
      if (feature) {
        return feature
      }
    }

    return null
  }

  /**
   * 根据名称获取 Feature
   * @param name 名称
   * @returns 匹配的 Feature 或 null
   */
  public getFeatureByName(name: string): GeoJSONFeature | null {
    return this.getFeatureByProperty('name', name)
  }

  /**
   * 根据 GB 编码获取 Feature
   * @param gb GB 编码
   * @returns 匹配的 Feature 或 null
   */
  public getFeatureByGB(gb: string): GeoJSONFeature | null {
    return this.getFeatureByProperty('gb', gb)
  }

  /**
   * 根据名称下钻
   * @param name 名称
   * @returns 是否成功下钻
   */
  public async drillToByName(name: string): Promise<boolean> {
    const feature = this.getFeatureByName(name)
    if (!feature) {
      logger.warn(`未找到名称为 "${name}" 的区域`)
      return false
    }

    await this.drillTo(feature)
    return true
  }

  /**
   * 根据 GB 编码下钻
   * @param gb GB 编码
   * @returns 是否成功下钻
   */
  public async drillToByGB(gb: string): Promise<boolean> {
    const feature = this.getFeatureByGB(gb)
    if (!feature) {
      logger.warn(`未找到 GB 编码为 "${gb}" 的区域`)
      return false
    }

    await this.drillTo(feature)
    return true
  }

  /**
   * 根据属性下钻
   * @param key 属性名
   * @param value 属性值
   * @returns 是否成功下钻
   */
  public async drillToByProperty(key: string, value: any): Promise<boolean> {
    const feature = this.getFeatureByProperty(key, value)
    if (!feature) {
      logger.warn(`未找到 ${key}="${value}" 的区域`)
      return false
    }

    // 如果当前没有加载该区域，先加载
    if (!this.isFeatureLoaded(feature) && this.originalData) {
      await this.loadByProperty(this.originalData, key, value)
    }

    await this.drillTo(feature)
    return true
  }

  /**
   * 检查指定的Feature是否已加载到地图上
   * @param feature 要检查的Feature
   * @returns 是否已加载
   */
  private isFeatureLoaded(feature: GeoJSONFeature): boolean {
    return Array.from(this.featureMap.values()).some(f =>
      f.properties.gb === feature.properties.gb
    )
  }
}
