import type { TMap } from '../TMap'
import type { TNativeMap } from '../../types'
import { IdGenerator } from '../../utils/IdGenerator'
import type { TOverlay } from '../base/TOverlay'
import {
  type TMarkerOptions,
  TMarker,
  type TPolylineOptions,
  TPolyline,
  type TPolygonOptions,
  TPolygon,
  type TLabelOptions,
  TLabel,
  type THtmlOverlayOptions,
  THtmlOverlay,
  type TComponentOverlayOptions,
  TComponentOverlay,
  type TSvgOverlayOptions,
  TSvgOverlay,
  type TContentOverlayOptions,
  createContentOverlay,
} from '../overlay'

export interface TOverlayLayerOptions {
  id?: string
  title?: string
  opacity?: number
  visible?: boolean
  minZoom?: number
  maxZoom?: number
  zIndex?: number
}

export class TOverlayLayer {
  private readonly id: string
  private readonly overlays: Map<string, TOverlay> = new Map()
  private map: TMap | null = null
  private nativeMap: TNativeMap | null = null
  private visible: boolean

  constructor(options: TOverlayLayerOptions = {}) {
    this.id = options.id ?? IdGenerator.generate('layer')
    this.visible = options.visible ?? true
  }

  public getId(): string {
    return this.id
  }

  public add(overlay: TOverlay): string
  public add(id: string, overlay: TOverlay): string
  public add(overlayOrId: TOverlay | string, maybeOverlay?: TOverlay): string {
    let id: string
    let overlay: TOverlay

    if (typeof overlayOrId === 'string') {
      id = overlayOrId
      overlay = maybeOverlay!
    } else {
      id = overlayOrId.getId()
      overlay = overlayOrId
    }

    if (this.overlays.has(id)) {
      this.remove(id)
    }

    this.overlays.set(id, overlay)
    if (this.nativeMap && this.visible) {
      overlay.addTo(this.map!)
    }

    return id
  }

  public remove(id: string): void {
    const overlay = this.overlays.get(id)
    if (overlay) {
      overlay.remove()
      this.overlays.delete(id)
    }
  }

  // 移除整个图层
  public removeFromMap(): void {
    this.overlays.forEach((overlay) => overlay.remove())
    this.overlays.clear()
    this.map = null
    this.nativeMap = null
  }

  public show(): void {
    this.visible = true
    if (this.nativeMap) {
      this.overlays.forEach((overlay) => overlay.show())
    }
  }

  public hide(): void {
    this.visible = false
    this.overlays.forEach((overlay) => overlay.hide())
  }

  public isVisible(): boolean {
    return this.visible
  }

  public addTo(map: TMap): void {
    this.map = map
    this.nativeMap = map.getNativeMap()
    if (this.visible) {
      this.overlays.forEach((overlay) => overlay.addTo(this.map!))
    }
  }

  public getMap(): TMap | null {
    return this.map
  }

  /**
   * @deprecated Use `get<T>()` instead
   * 获取叠加物
   * @param id 叠加物ID
   * @returns 叠加物实例或undefined
   */
  public getOverlay<T extends TOverlay>(id: string): T | undefined {
    return this.overlays.get(id) as T | undefined
  }

  /**
   * 获取叠加物
   * @param id 叠加物ID
   * @returns 叠加物实例或undefined
   */
  public get<T extends TOverlay>(id: string): T | undefined {
    return this.overlays.get(id) as T | undefined
  }

  public getOverlays(): Map<string, TOverlay> {
    return new Map(this.overlays)
  }

  /**
   * 清除图层中的所有叠加物
   */
  public clear(): void {
    this.overlays.forEach((overlay) => overlay.remove())
    this.overlays.clear()
  }

  // =============== 覆盖物工厂方法 ===============
  /**
   * 创建并添加标记点
   * @returns 覆盖物ID
   */
  public createMarker<T = any>(options: TMarkerOptions<T>): string {
    const marker = new TMarker<T>(options)
    return this.add(marker)
  }

  /**
   * 创建并添加折线
   * @returns 覆盖物ID
   */
  public createPolyline<T = any>(options: TPolylineOptions<T>): string {
    const polyline = new TPolyline<T>(options)
    return this.add(polyline)
  }

  /**
   * 创建并添加多边形
   * @returns 覆盖物ID
   */
  public createPolygon<T = any>(options: TPolygonOptions<T>): string {
    const polygon = new TPolygon<T>(options)
    return this.add(polygon)
  }

  /**
   * 创建并添加文本标注
   * @returns 覆盖物ID
   */
  public createLabel<T = any>(options: TLabelOptions<T>): string {
    const label = new TLabel<T>(options)
    return this.add(label)
  }

  /**
   * 创建并添加HTML覆盖物
   * @returns 覆盖物ID
   */
  public createHtmlOverlay<T = any>(options: THtmlOverlayOptions<T>): string {
    const overlay = new THtmlOverlay<T>(options)
    return this.add(overlay)
  }

  /**
   * 创建并添加Vue组件覆盖物
   * @returns 覆盖物ID
   */
  public createComponentOverlay<T = any>(options: TComponentOverlayOptions<T>): string {
    const overlay = new TComponentOverlay<T>(options)
    return this.add(overlay)
  }

  /**
   * 创建并添加SVG覆盖物
   * @returns 覆盖物ID
   */
  public createSvgOverlay<T = any>(options: TSvgOverlayOptions<T>): string {
    const overlay = new TSvgOverlay<T>(options)
    return this.add(overlay)
  }

  /**
   * 创建并添加内容覆盖物
   * @returns 覆盖物ID
   */
  public createContentOverlay(options: TContentOverlayOptions): string {
    const overlay = createContentOverlay(options)
    return this.add(overlay)
  }
}
