import { createLogger } from '@/utils/logger'
import type { TMap } from '@/map'
import {
  TPolygon,
  type TPolygonOptions,
  TLabel,
  type TPolygonStyle,
  TPolyline,
  type TPolylineOptions,
  type TPolylineStyle,
  TMarker,
  type TMarkerOptions,
  TSvgOverlay,
  type TSvgOverlayOptions,
  type TAnchorPosition,
} from '../overlay'

const logger = createLogger('TFeatureLayer')

/**
 * 地理要素
 */
export interface TFeature {
  type: 'Feature'
  geometry: {
    type: 'MultiPolygon' | 'Polygon' | 'MultiLineString' | 'LineString' | 'Point' | 'MultiPoint'
    coordinates: number[] | number[][] | number[][][] | number[][][][]
  }
  properties: {
    [key: string]: any
  }
}

/**
 * 地理要素集合
 */
export interface TFeatureCollection {
  type: 'FeatureCollection'
  features: TFeature[]
  name?: string
  crs?: {
    type: string
    properties: {
      name: string
    }
  }
}

/**
 * 几何类型(来自已提供的数据)
 */
export type GeometryType =
  | 'Point'
  | 'MultiPoint'
  | 'LineString'
  | 'MultiLineString'
  | 'Polygon'
  | 'MultiPolygon'

/**
 * 线条要素样式
 */
export interface TFeatureLineStyle extends TPolylineStyle {
  showArrow?: boolean
  arrowSize?: number
  labelText?: string | ((feature: TFeature) => string | null)
  labelOffset?: [number, number]
  labelStyle?: Partial<CSSStyleDeclaration>
}

/**
 * 基础点样式属性
 */
interface TFeaturePointStyleBase {
  size?: number | [number, number]
  anchor?: TAnchorPosition
  labelText?: string | ((feature: TFeature) => string | null)
  labelOffset?: [number, number]
  labelStyle?: Partial<CSSStyleDeclaration>
}

export interface TMarkerPointStyle extends TFeaturePointStyleBase {
  type: 'marker'
  icon?: string
  rotation?: number
}

export interface TSvgPointStyle extends TFeaturePointStyleBase {
  type: 'svg'
  svgOptions: Partial<TSvgOverlayOptions>
}

export type TFeaturePointStyle = TMarkerPointStyle | TSvgPointStyle

/**
 * 多边形要素样式
 */
export interface TFeaturePolygonStyle extends TPolygonStyle {
  labelText?: string | ((feature: TFeature) => string | null)
  labelOffset?: [number, number]
  labelStyle?: Partial<CSSStyleDeclaration>
}

export interface TFeatureLayerOptions {
  polygonStyle?: TFeaturePolygonStyle
  lineStyle?: TFeatureLineStyle
  pointStyle?: TFeaturePointStyle
  computeFeatureStyle?: (
    feature: TFeature,
    geometryType: GeometryType,
  ) => Partial<TFeaturePolygonStyle | TFeatureLineStyle | TFeaturePointStyle>
  events?: {
    onClick?: (feature: TFeature, geometryType: GeometryType, overlay: any) => void
    onMouseOver?: (feature: TFeature, geometryType: GeometryType, overlay: any) => void
    onMouseOut?: (feature: TFeature, geometryType: GeometryType, overlay: any) => void
  }
  fieldMapping?: {
    nameField?: string
    codeField?: string
    typeField?: string
    centerField?: string
    custom?: Record<string, string>
  }
  autoDetectFields?: boolean
  loadAllGeometryTypes?: boolean
  geometryTypes?: GeometryType[]
}

export class TFeatureLayer {
  private map: TMap
  private options: TFeatureLayerOptions
  private originalData: TFeatureCollection | null = null

  private polygons: TPolygon[] = []
  private polylines: TPolyline[] = []
  private markers: (TMarker | TSvgOverlay)[] = []
  /** @deprecated 已废弃，使用各覆盖物自身的标签功能 */
  private labels: TLabel[] = []

  private featureMap = new Map<TPolygon | TPolyline | TMarker | TSvgOverlay, TFeature>()

  private fieldMapping: Required<NonNullable<TFeatureLayerOptions['fieldMapping']>>

  private static readonly COMMON_NAME_FIELDS = [
    'name',
    'NAME',
    'Name',
    'title',
    'TITLE',
    'Title',
    'label',
    'LABEL',
    'Label',
  ]
  private static readonly COMMON_CODE_FIELDS = [
    'gb',
    'GB',
    'code',
    'CODE',
    'Code',
    'id',
    'ID',
    'Id',
    'adcode',
    'ADCODE',
    'osm_id',
  ]
  private static readonly COMMON_TYPE_FIELDS = [
    'type',
    'TYPE',
    'Type',
    'fclass',
    'FCLASS',
    'class',
    'CLASS',
    'category',
    'CATEGORY',
  ]
  private static readonly COMMON_CENTER_FIELDS = [
    'center',
    'CENTER',
    'Center',
    'centroid',
    'CENTROID',
    'Centroid',
  ]

  constructor(map: TMap, options: TFeatureLayerOptions = {}) {
    this.map = map
    this.options = options

    this.fieldMapping = {
      nameField: 'name',
      codeField: 'gb',
      typeField: 'type',
      centerField: 'center',
      custom: {},
    }

    if (options.fieldMapping) {
      this.setFieldMapping(options.fieldMapping)
    }
  }

  /**
   * 设置字段映射
   * @param mapping 字段映射配置
   */
  public setFieldMapping(mapping: NonNullable<TFeatureLayerOptions['fieldMapping']>): void {
    this.fieldMapping = {
      ...this.fieldMapping,
      ...mapping,
      custom: {
        ...this.fieldMapping.custom,
        ...mapping.custom,
      },
    }
  }

  /**
   * 自动检测GeoJSON数据的字段
   * @param data 要素集合
   * @returns 检测到的字段映射
   */
  private autoDetectFields(
    data: TFeatureCollection,
  ): NonNullable<TFeatureLayerOptions['fieldMapping']> {
    if (!data.features || data.features.length === 0) {
      return {}
    }

    const mapping: NonNullable<TFeatureLayerOptions['fieldMapping']> = {}
    const sampleFeature = data.features[0]

    if (!sampleFeature.properties) {
      return mapping
    }

    const props = Object.keys(sampleFeature.properties)

    mapping.nameField = this.findMatchingField(props, TFeatureLayer.COMMON_NAME_FIELDS)

    mapping.codeField = this.findMatchingField(props, TFeatureLayer.COMMON_CODE_FIELDS)

    mapping.typeField = this.findMatchingField(props, TFeatureLayer.COMMON_TYPE_FIELDS)

    mapping.centerField = this.findMatchingField(props, TFeatureLayer.COMMON_CENTER_FIELDS)

    return mapping
  }

  /**
   * 在属性列表中查找匹配的字段
   * @param props 属性列表
   * @param commonFields 常见字段名称列表
   * @returns 匹配的字段名或undefined
   */
  private findMatchingField(props: string[], commonFields: string[]): string | undefined {
    for (const field of commonFields) {
      if (props.includes(field)) {
        return field
      }
    }
    return undefined
  }

  /**
   * 获取映射后的字段名
   * @param standardKey 标准化的字段名
   * @returns 实际的字段名
   */
  private getMappedField(standardKey: string): string | undefined {
    if (standardKey === 'name') return this.fieldMapping.nameField
    if (standardKey === 'code' || standardKey === 'gb') return this.fieldMapping.codeField
    if (standardKey === 'type') return this.fieldMapping.typeField
    if (standardKey === 'center') return this.fieldMapping.centerField

    return this.fieldMapping.custom?.[standardKey]
  }

  /**
   * 获取要素的属性值
   * @param feature 地理要素
   * @param key 标准化的字段名
   * @returns 属性值
   */
  public getFeatureProperty(feature: TFeature, key: string): any {
    const mappedKey = this.getMappedField(key) || key
    return feature.properties?.[mappedKey]
  }

  /**
   * 获取要素的名称
   * @param feature 地理要素
   * @returns 名称
   */
  public getFeatureName(feature: TFeature): string | undefined {
    return this.getFeatureProperty(feature, 'name')
  }

  /**
   * 获取要素的编码
   * @param feature 地理要素
   * @returns 编码
   */
  public getFeatureCode(feature: TFeature): string | undefined {
    return this.getFeatureProperty(feature, 'code')
  }

  /**
   * 获取要素的类型
   * @param feature 地理要素
   * @returns 类型
   */
  public getFeatureType(feature: TFeature): string | undefined {
    return this.getFeatureProperty(feature, 'type')
  }

  /**
   * 加载要素集合
   * @param data 要素集合
   * @param autoDetect 是否自动检测字段，默认为options中的设置
   */
  public async load(data: TFeatureCollection, autoDetect?: boolean): Promise<void> {
    this.clear()
    this.originalData = data

    const shouldAutoDetect = autoDetect ?? this.options.autoDetectFields ?? false
    if (shouldAutoDetect) {
      const detectedMapping = this.autoDetectFields(data)
      this.setFieldMapping(detectedMapping)
      logger.info('自动检测到的字段映射:', detectedMapping)
    }

    const geometryTypes =
      this.options.geometryTypes ||
      (this.options.loadAllGeometryTypes
        ? ([
            'Point',
            'MultiPoint',
            'LineString',
            'MultiLineString',
            'Polygon',
            'MultiPolygon',
          ] as GeometryType[])
        : (['Polygon', 'MultiPolygon'] as GeometryType[]))

    for (const feature of data.features) {
      const geometryType = feature.geometry.type as GeometryType

      if (!geometryTypes.includes(geometryType)) {
        continue
      }

      this.loadFeature(feature, geometryType)
    }
  }

  /**
   * 加载单个要素
   * @param feature 地理要素
   * @param geometryType 几何类型
   */
  private loadFeature(feature: TFeature, geometryType: GeometryType): void {
    switch (geometryType) {
      case 'Polygon':
      case 'MultiPolygon':
        this.loadPolygonFeature(feature)
        break
      case 'LineString':
      case 'MultiLineString':
        this.loadLineFeature(feature)
        break
      case 'Point':
      case 'MultiPoint':
        this.loadPointFeature(feature)
        break
    }
  }

  /**
   * 加载多边形要素
   * @param feature 地理要素
   */
  private loadPolygonFeature(feature: TFeature): void {
    try {
      const coordinates = this.getCoordinatesFromGeometry(feature.geometry)
      if (!coordinates || coordinates.length === 0) return

      for (const coords of coordinates) {
        if (!coords || coords.length < 3) continue

        const style = this.getFeatureStyle(feature, 'Polygon')

        const polygonStyle = style as TFeaturePolygonStyle

        const labelText = this.getLabelText(feature, polygonStyle.labelText)

        const options: TPolygonOptions = {
          path: coords,
          ...style,
          label: labelText || undefined,
          labelOffset: polygonStyle.labelOffset,
          labelStyle: polygonStyle.labelStyle,
        }

        const polygon = new TPolygon(options)
        polygon.addTo(this.map)
        this.polygons.push(polygon)
        this.featureMap.set(polygon, feature)

        this.bindEvents(polygon, feature, 'Polygon')
      }
    } catch (error) {
      logger.error('加载多边形要素失败:', error)
    }
  }

  /**
   * 加载线要素
   * @param feature 地理要素
   */
  private loadLineFeature(feature: TFeature): void {
    try {
      const coordinates = this.getCoordinatesFromGeometry(feature.geometry)
      if (!coordinates || coordinates.length === 0) return

      for (const coords of coordinates) {
        if (!coords || coords.length < 2) continue

        const style = this.getFeatureStyle(feature, 'LineString')

        const lineStyle = style as TFeatureLineStyle

        const labelText = this.getLabelText(feature, lineStyle.labelText)

        const options: TPolylineOptions = {
          path: coords,
          ...style,
          label: labelText || undefined,
          labelOffset: lineStyle.labelOffset,
          labelStyle: lineStyle.labelStyle,
        }

        const polyline = new TPolyline(options)
        polyline.addTo(this.map)
        this.polylines.push(polyline)
        this.featureMap.set(polyline, feature)

        this.bindEvents(polyline, feature, 'LineString')
      }
    } catch (error) {
      logger.error('加载线要素失败:', error)
    }
  }

  /**
   * 加载点要素
   * @param feature 地理要素
   */
  private loadPointFeature(feature: TFeature): void {
    try {
      const coordinates = this.getCoordinatesFromGeometry(feature.geometry, true)
      if (!coordinates || coordinates.length === 0) return

      for (const coord of coordinates) {
        const style = this.getFeatureStyle(feature, 'Point') as TFeaturePointStyle

        switch (style.type) {
          case 'marker':
            this.createMarkerPoint(feature, coord, style as TMarkerPointStyle)
            break
          case 'svg':
            this.createSvgPoint(feature, coord, style as TSvgPointStyle)
            break
          default:
            this.createMarkerPoint(feature, coord, {
              type: 'marker',
              labelText: 'name',
              size: 20,
            })
        }
      }
    } catch (error) {
      logger.error('加载点要素失败:', error)
    }
  }

  /**
   * 创建标记点
   * @param feature 地理要素
   * @param coord 坐标
   * @param style 标记点样式
   */
  private createMarkerPoint(
    feature: TFeature,
    coord: [number, number],
    style: TMarkerPointStyle,
  ): void {
    const labelText = this.getLabelText(feature, style.labelText)

    const options: TMarkerOptions = {
      position: coord,
      icon: style.icon,
      label: labelText || undefined,
      labelOffset: style.labelOffset,
      labelStyle: style.labelStyle,
    }

    if (style.anchor) {
      logger.warn('TMarker does not support anchor property yet')
    }

    if (style.rotation) {
      logger.warn('TMarker does not support rotation property yet')
    }

    const marker = new TMarker(options)
    marker.addTo(this.map)
    this.markers.push(marker)
    this.featureMap.set(marker, feature)

    this.bindEvents(marker, feature, 'Point')
  }

  /**
   * 创建SVG点
   * @param feature 地理要素
   * @param coord 坐标
   * @param style SVG点样式
   */
  private createSvgPoint(feature: TFeature, coord: [number, number], style: TSvgPointStyle): void {
    const labelText = this.getLabelText(feature, style.labelText)

    const options: TSvgOverlayOptions = {
      position: coord,
      size: style.size,
      anchor: style.anchor,
      ...style.svgOptions,
      label: labelText || undefined,
      labelOffset: style.labelOffset,
      labelStyle: style.labelStyle,
    }

    const svgOverlay = new TSvgOverlay(options)

    svgOverlay.addTo(this.map)
    this.markers.push(svgOverlay)
    this.featureMap.set(svgOverlay, feature)

    this.bindEvents(svgOverlay, feature, 'Point')
  }

  /**
   * 从几何对象中获取坐标数组
   * @param geometry 几何对象
   * @param flatten 是否扁平化坐标（用于点）
   * @returns 坐标数组
   */
  private getCoordinatesFromGeometry(geometry: any, flatten: boolean = false): any[] {
    if (!geometry || !geometry.coordinates) return []

    const { type, coordinates } = geometry

    switch (type) {
      case 'Point':
        return flatten ? [coordinates] : [[coordinates]]
      case 'MultiPoint':
        return flatten ? coordinates : coordinates.map((coord: any) => [coord])
      case 'LineString':
        return [coordinates]
      case 'MultiLineString':
        return coordinates
      case 'Polygon':
        return [coordinates[0]]
      case 'MultiPolygon':
        return coordinates.map((poly: any) => poly[0])
      default:
        logger.warn(`未知的几何类型: ${type}`)
        return []
    }
  }

  /**
   * 获取要素的样式
   * @param feature 地理要素
   * @param geometryType 几何类型
   * @returns 样式对象
   */
  private getFeatureStyle(feature: TFeature, geometryType: GeometryType): any {
    let defaultStyle: any = {}

    switch (geometryType) {
      case 'Polygon':
      case 'MultiPolygon':
        defaultStyle = {
          fillColor: '#6FF9ED',
          fillOpacity: 0.4,
          color: '#1ECAD3',
          weight: 2,
          opacity: 0.8,
        }
        break
      case 'LineString':
      case 'MultiLineString':
        defaultStyle = {
          color: '#1ECAD3',
          weight: 3,
          opacity: 0.8,
          showArrow: false,
          arrowSize: 10,
        }
        break
      case 'Point':
      case 'MultiPoint':
        defaultStyle = {
          type: 'marker',
          icon: undefined,
          size: [20, 20],
          anchor: [10, 10],
          rotation: 0,
        }
        break
    }

    let configStyle = {}
    switch (geometryType) {
      case 'Polygon':
      case 'MultiPolygon':
        configStyle = this.options.polygonStyle || {}
        break
      case 'LineString':
      case 'MultiLineString':
        configStyle = this.options.lineStyle || {}
        break
      case 'Point':
      case 'MultiPoint':
        configStyle = this.options.pointStyle || {}
        break
    }

    const computedStyle = this.options.computeFeatureStyle
      ? this.options.computeFeatureStyle(feature, geometryType) || {}
      : {}

    return {
      ...defaultStyle,
      ...configStyle,
      ...computedStyle,
    }
  }

  /**
   * 绑定事件
   * @param overlay 覆盖物
   * @param feature 地理要素
   * @param geometryType 几何类型
   */
  private bindEvents(overlay: any, feature: TFeature, geometryType: GeometryType): void {
    if (!this.options.events) return

    const { onClick, onMouseOver, onMouseOut } = this.options.events

    if (onClick) {
      overlay.on('click', () => {
        onClick(feature, geometryType, overlay)
      })
    }

    if (onMouseOver) {
      overlay.on('mouseover', () => {
        onMouseOver(feature, geometryType, overlay)
      })
    }

    if (onMouseOut) {
      overlay.on('mouseout', () => {
        onMouseOut(feature, geometryType, overlay)
      })
    }
  }

  /**
   * 获取标签文本
   * @param feature 地理要素
   * @param labelTextConfig 标签文本配置（属性名或函数）
   * @returns 标签文本或null
   */
  private getLabelText(
    feature: TFeature,
    labelTextConfig?: string | ((feature: TFeature) => string | null),
  ): string | null {
    if (!labelTextConfig) return null

    if (typeof labelTextConfig === 'function') {
      return labelTextConfig(feature)
    } else {
      return this.getFeatureProperty(feature, labelTextConfig) || null
    }
  }

  /**
   * 清除所有图层
   */
  public clear(): void {
    for (const polygon of this.polygons) {
      polygon.remove()
    }
    this.polygons = []

    for (const polyline of this.polylines) {
      polyline.remove()
    }
    this.polylines = []

    for (const marker of this.markers) {
      marker.remove()
    }
    this.markers = []

    if (this.labels.length > 0) {
      for (const label of this.labels) {
        label.remove()
      }
      this.labels = []
    }

    this.featureMap.clear()
  }

  /**
   * 根据属性过滤并加载要素集合
   * @param data 要素集合
   * @param key 属性名
   * @param value 属性值
   * @param autoDetect 是否自动检测字段
   * @returns 是否成功加载
   */
  public async loadByProperty(
    data: TFeatureCollection,
    key: string,
    value: any,
    autoDetect?: boolean,
  ): Promise<boolean> {
    if (!data || !data.features || data.features.length === 0) {
      logger.warn('要素集合为空或无效')
      return false
    }

    const actualKey = this.getMappedField(key) || key

    const filteredFeatures = data.features.filter(
      (feature) => feature.properties && feature.properties[actualKey] === value,
    )

    if (filteredFeatures.length === 0) {
      logger.warn(`未找到 ${actualKey}="${value}" 的要素`)
      return false
    }

    const filteredData: TFeatureCollection = {
      type: 'FeatureCollection',
      features: filteredFeatures,
      name: data.name,
      crs: data.crs,
    }

    this.originalData = data
    await this.load(filteredData, autoDetect)
    return true
  }

  /**
   * 根据名称加载要素集合
   * @param data 要素集合
   * @param name 名称
   * @param autoDetect 是否自动检测字段
   * @returns 是否成功加载
   */
  public async loadByName(
    data: TFeatureCollection,
    name: string,
    autoDetect?: boolean,
  ): Promise<boolean> {
    const nameField = this.fieldMapping.nameField || 'name'
    return this.loadByProperty(data, nameField, name, autoDetect)
  }

  /**
   * 根据编码加载要素集合
   * @param data 要素集合
   * @param code 编码
   * @param autoDetect 是否自动检测字段
   * @returns 是否成功加载
   */
  public async loadByCode(
    data: TFeatureCollection,
    code: string,
    autoDetect?: boolean,
  ): Promise<boolean> {
    const codeField = this.fieldMapping.codeField || 'gb'
    return this.loadByProperty(data, codeField, code, autoDetect)
  }

  /**
   * 根据类型加载要素集合
   * @param data 要素集合
   * @param type 类型值
   * @param autoDetect 是否自动检测字段
   * @returns 是否成功加载
   */
  public async loadByType(
    data: TFeatureCollection,
    type: string,
    autoDetect?: boolean,
  ): Promise<boolean> {
    const typeField = this.fieldMapping.typeField || 'type'
    return this.loadByProperty(data, typeField, type, autoDetect)
  }

  /**
   * 根据属性获取地理要素
   * @param key 属性名
   * @param value 属性值
   * @returns 匹配的要素或 null
   */
  public getFeatureByProperty(key: string, value: any): TFeature | null {
    const actualKey = this.getMappedField(key) || key

    for (const feature of this.featureMap.values()) {
      if (feature.properties && feature.properties[actualKey] === value) {
        return feature
      }
    }

    if (this.originalData && this.originalData.features) {
      const feature = this.originalData.features.find(
        (f) => f.properties && f.properties[actualKey] === value,
      )
      if (feature) {
        return feature
      }
    }

    return null
  }

  /**
   * 根据名称获取要素
   * @param name 名称
   * @returns 匹配的要素或 null
   */
  public getFeatureByName(name: string): TFeature | null {
    const nameField = this.fieldMapping.nameField || 'name'
    return this.getFeatureByProperty(nameField, name)
  }

  /**
   * 根据编码获取要素
   * @param code 编码
   * @returns 匹配的要素或 null
   */
  public getFeatureByCode(code: string): TFeature | null {
    const codeField = this.fieldMapping.codeField || 'gb'
    return this.getFeatureByProperty(codeField, code)
  }

  /**
   * 根据类型获取要素
   * @param type 类型值
   * @returns 匹配的要素或 null
   */
  public getFeatureByType(type: string): TFeature | null {
    const typeField = this.fieldMapping.typeField || 'type'
    return this.getFeatureByProperty(typeField, type)
  }
}
