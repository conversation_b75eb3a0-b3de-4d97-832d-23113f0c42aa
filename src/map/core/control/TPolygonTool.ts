import type { TNativePolygon } from '../../types'
import type { TMap } from '../TMap'
import { validateTMapAPI } from '../../utils/validator'

export interface TPolygonToolOptions {
  /** 多边形边线颜色，默认"#0000FF" */
  color?: string
  /** 多边形边线的宽度，以像素为单位，默认3 */
  weight?: number
  /** 多边形边线的透明度（范围0-1 之间），默认0.5 */
  opacity?: number
  /** 多边形填充颜色，默认"#0000FF" */
  fillColor?: string
  /** 多边形填充的透明度（范围0-1 之间），默认0.2 */
  fillOpacity?: number
  /** 边框样式，solid或dashed */
  lineStyle?: 'solid' | 'dashed'
  /** 是否显示面积信息，默认true */
  showArea?: boolean
  /** 绘制完成回调 */
  onComplete?: (polygons: TNativePolygon[]) => void
  /** 面积变化回调 */
  onAreaChange?: (area: number) => void
  /** 多边形移除回调 */
  onRemove?: (polygon: TNativePolygon) => void
}

export class TPolygonTool {
  private polygonTool: T.PolygonTool
  private map: TMap
  private options: TPolygonToolOptions
  private isActive = false

  constructor(map: TMap, options: TPolygonToolOptions = {}) {
    validateTMapAPI()

    this.map = map
    this.options = {
      color: '#0000FF',
      weight: 3,
      opacity: 0.5,
      fillColor: '#0000FF',
      fillOpacity: 0.2,
      showArea: true,
      ...options,
    }

    this.polygonTool = new T.PolygonTool(map.getNativeMap(), {
      color: this.options.color,
      weight: this.options.weight,
      opacity: this.options.opacity,
      fillColor: this.options.fillColor,
      fillOpacity: this.options.fillOpacity,
      lineStyle: this.options.lineStyle,
      showLabel: this.options.showArea,
    })

    this.setupEventListeners()
  }

  /**
   * 开启绘制工具
   */
  public start(): void {
    this.polygonTool.open()
    this.isActive = true
  }

  /**
   * 关闭绘制工具
   */
  public stop(): void {
    this.polygonTool.close()
    this.isActive = false
  }

  /**
   * 清除绘制的所有多边形
   */
  public clear(): void {
    this.polygonTool.clear()
  }

  /**
   * 完成当前多边形的绘制
   */
  public endDraw(): void {
    this.polygonTool.endDraw()
  }

  /**
   * 获取绘制工具是否处于激活状态
   */
  public isActivated(): boolean {
    return this.isActive
  }

  /**
   * 计算面积
   */
  public getArea(points: T.LngLat[]): number {
    return this.polygonTool.getArea(points)
  }

  /**
   * 设置提示文本
   */
  public setTips(text: string): void {
    this.polygonTool.setTips(text)
  }

  /**
   * 获取所有绘制的多边形
   */
  public getPolygons(): TNativePolygon[] {
    return this.polygonTool.getPolygons()
  }

  private setupEventListeners(): void {
    this.polygonTool.addEventListener('draw', (e: T.PolygonToolEvent) => {
      e.currentPolygon.addEventListener('remove', () => {
        if (this.options.onRemove) {
          this.options.onRemove(e.currentPolygon)
        }
      })

      if (this.options.onComplete) {
        this.options.onComplete(e.allPolygons)
      }
    })

    this.polygonTool.addEventListener('addpoint', (e: T.PolygonToolEvent) => {
      if (this.options.onAreaChange) {
        this.options.onAreaChange(e.currentArea)
      }
    })
  }
}
