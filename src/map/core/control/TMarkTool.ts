import type { TNativeIcon, TNativeMarker, TNativeMarkTool } from '@/map/types'
import type { TMap } from '../TMap'
import { validateTMapAPI } from '../../utils/validator'

/** 标注工具模式 */
export enum TMarkToolMode {
  /** 默认模式 */
  DEFAULT = 0,
  /** 跟随鼠标 */
  FOLLOW = 1 << 0, // 1
  /** 保持激活 */
  KEEP_ACTIVE = 1 << 1, // 2
}

/** 标注工具配置选项 */
export interface TMarkToolOptions {
  /** 标注图标URL */
  iconUrl?: string
  /** 自定义图标对象 */
  icon?: TNativeIcon
  /** 工具模式，支持位运算组合 */
  mode?: TMarkToolMode
  /** 标注回调 */
  onMark?: (position: [number, number]) => void
  /** 完成标注回调 */
  onComplete?: (markers: TNativeMarker[]) => void
}

export class TMarkTool {
  private markTool: TNativeMarkTool
  private options: TMarkToolOptions
  private isActive = false
  private map: TMap

  constructor(map: TMap, options: TMarkToolOptions = {}) {
    validateTMapAPI()

    this.map = map
    this.options = {
      mode: TMarkToolMode.DEFAULT,
      ...options,
    }

    // 创建标注工具实例
    this.markTool = new T.MarkTool(map.getNativeMap(), {
      follow: this.hasMode(TMarkToolMode.FOLLOW),
      icon: options.icon,
    })

    // 设置标注图标
    if (options.iconUrl) {
      this.setIcon(options.iconUrl)
    }

    // 监听标注事件
    this.setupEventListeners()
  }

  /** 检查是否启用了指定模式 */
  private hasMode(mode: TMarkToolMode): boolean {
    return (this.options.mode! & mode) === mode
  }

  /**
   * 开启标注工具
   * @returns {boolean} 是否成功开启
   */
  public start(): boolean {
    if (this.isActive) return false
    this.markTool.open()
    // 由于 open() 实际上不返回任何值，直接返回 true 表示成功开启 api文档有误
    this.isActive = true
    return true
  }

  /**
   * 停止标注工具
   */
  public stop(): void {
    if (!this.isActive) return
    this.markTool.close()
    this.isActive = false
  }

  /**
   * 切换标注工具的激活状态
   * @returns {boolean} 切换后的激活状态
   */
  public toggle(): boolean {
    if (this.isActive) {
      this.stop()
      return false
    } else {
      return this.start()
    }
  }

  /**
   * 清除所有标注
   */
  public clear(): void {
    this.markTool.clear()
  }

  /**
   * 获取所有标注点
   * @returns {TNativeMarker[]} 标注点数组
   */
  public getMarkers(): TNativeMarker[] {
    return this.markTool.getMarkers()
  }

  /**
   * 获取当前标注点坐标
   * @returns {[number, number] | null} 经纬度坐标，如果没有标注则返回 null
   */
  public getCurrentPosition(): [number, number] | null {
    const lngLat = this.markTool.getMarkControlPoint()
    return lngLat ? [lngLat.lng, lngLat.lat] : null
  }

  /**
   * 设置标注图标
   * @param {string} url 图标URL
   */
  public setIcon(url: string): void {
    this.markTool.setPointImage(url)
  }

  /**
   * 获取标注工具是否处于激活状态
   * @returns {boolean} 是否激活
   */
  public isActivated(): boolean {
    return this.isActive
  }

  private setupEventListeners(): void {
    // 监听标注事件 (mouseup)
    this.markTool.addEventListener('mouseup', (e: T.MarkToolEvent) => {
      if (!this.isActive) return

      // 先处理内部状态
      if (!this.hasMode(TMarkToolMode.KEEP_ACTIVE)) {
        this.stop()
      }

      // 再触发外部回调
      if (this.options.onMark) {
        this.options.onMark([e.currentLnglat.lng, e.currentLnglat.lat])
      }

      if (this.options.onComplete) {
        this.options.onComplete(e.allMarkers)
      }

      // 如果是保持激活模式，需要重新开启
      if (this.hasMode(TMarkToolMode.KEEP_ACTIVE)) {
        setTimeout(() => {
          if (this.isActive) {
            this.markTool.open()
          }
        }, 0)
      }
    })
  }
}
