import type { TNativePolyline } from '../../types'
import type { TMap } from '../TMap'
import { validateTMapAPI } from '../../utils/validator'

export interface TPolylineToolOptions {
  /** 折线颜色，默认"#0000FF" */
  color?: string
  /** 折线的宽度，以像素为单位，默认3 */
  weight?: number
  /** 折线的透明度（范围0-1 之间），默认0.5 */
  opacity?: number
  /** 折线样式，solid或dashed */
  lineStyle?: 'solid' | 'dashed'
  /** 是否显示距离信息，默认true */
  showDistance?: boolean
  /** 绘制完成回调 */
  onComplete?: (polylines: TNativePolyline[]) => void
  /** 距离变化回调 */
  onDistanceChange?: (distance: number) => void
  /** 折线移除回调 */
  onRemove?: (polyline: TNativePolyline) => void
}

export class TPolylineTool {
  private polylineTool: T.PolylineTool
  private map: TMap
  private options: TPolylineToolOptions
  private isActive = false

  constructor(map: TMap, options: TPolylineToolOptions = {}) {
    validateTMapAPI()

    this.map = map
    this.options = {
      color: '#0000FF',
      weight: 3,
      opacity: 0.5,
      lineStyle: 'solid',
      showDistance: true,
      ...options,
    }

    this.polylineTool = new T.PolylineTool(map.getNativeMap(), {
      color: this.options.color,
      weight: this.options.weight,
      opacity: this.options.opacity,
      lineStyle: this.options.lineStyle,
      showLabel: this.options.showDistance,
    })

    this.setupEventListeners()
  }

  /**
   * 开启绘制工具
   */
  public start(): void {
    this.polylineTool.open()
    this.isActive = true
  }

  /**
   * 关闭绘制工具
   */
  public stop(): void {
    this.polylineTool.close()
    this.isActive = false
  }

  /**
   * 清除绘制的所有折线
   */
  public clear(): void {
    this.polylineTool.clear()
  }

  /**
   * 完成当前折线的绘制
   */
  public endDraw(): void {
    this.polylineTool.endDraw()
  }

  /**
   * 获取绘制工具是否处于激活状态
   */
  public isActivated(): boolean {
    return this.isActive
  }

  /**
   * 计算距离
   */
  public getDistance(points: T.LngLat[]): number {
    return this.polylineTool.getDistance(points)
  }

  /**
   * 设置提示文本
   */
  public setTips(text: string): void {
    this.polylineTool.setTips(text)
  }

  /**
   * 获取所有绘制的折线
   */
  public getPolylines(): TNativePolyline[] {
    return this.polylineTool.getPolylines()
  }

  private setupEventListeners(): void {
    this.polylineTool.addEventListener('draw', (e: T.PolylineToolEvent) => {
      e.currentPolyline.addEventListener('remove', () => {
        if (this.options.onRemove) {
          this.options.onRemove(e.currentPolyline)
        }
      })

      if (this.options.onComplete) {
        this.options.onComplete(e.allPolylines)
      }
    })

    this.polylineTool.addEventListener('addpoint', (e: T.PolylineToolEvent) => {
      if (this.options.onDistanceChange) {
        this.options.onDistanceChange(e.currentDistance)
      }
    })
  }
}
