import { TMap } from '../TMap'
import { TMarker } from '../overlay/TMarker'
import { TAnchorPosition, TSvgOverlay, type TSvgOverlayOptions } from '../overlay/TSvgOverlay'
import { TGeocoder } from '../service/TGeocoder'
import { validateTMapAPI } from '../../utils/validator'

/** 地址格式化函数类型 */
export type TAddressFormatter = (address: string, detail: T.AddressComponent) => string

/** 标注工具模式 */
export enum TPointMarkerMode {
  /** 默认模式 */
  DEFAULT = 0,
  /** 跟随鼠标 */
  FOLLOW = 1 << 0, // 1
  /** 保持激活 */
  KEEP_ACTIVE = 1 << 1, // 2
  /** 单点模式 */
  SINGLE = 1 << 2, // 4
}

/** 标注工具配置选项 */
export interface TPointMarkerToolOptions {
  /** 标注图标URL */
  iconUrl?: string
  /** SVG 图标名称 (与 iconUrl 互斥) */
  iconName?: string
  /** SVG 标签内容 (与 iconUrl, iconName 互斥) */
  svgMarkup?: string
  /** 图标宽度 */
  width?: number
  /** 图标高度 */
  height?: number
  /** 工具模式，支持位运算组合 */
  mode?: TPointMarkerMode
  /** 图标样式 */
  iconStyle?: Partial<CSSStyleDeclaration>
  // 标签相关配置
  /** 默认标签文本 */
  label?: string
  /** 标签偏移量 */
  labelOffset?: [number, number]
  /** 标签样式 */
  labelStyle?: Partial<CSSStyleDeclaration>
  /** 标签是否可删除 */
  removeable?: boolean
  /** 事件处理器 */
  events?: Partial<{
    click: (evt: any) => void
    dblclick: (evt: any) => void
    mousedown: (evt: any) => void
    mouseup: (evt: any) => void
    mouseover: (evt: any) => void
    mouseout: (evt: any) => void
    dragstart: (evt: any) => void
    drag: (evt: any) => void
    dragend: (evt: any) => void
  }>

  // 地理编码相关配置
  /** 是否启用地理编码 */
  enableGeocoding?: boolean
  /** 是否自动将地址设置为标记标签 */
  autoSetAddress?: boolean
  /** 地址格式化函数 */
  formatAddress?: TAddressFormatter
  /** 拖拽后是否自动更新地址 */
  updateAddressOnDrag?: boolean
  /** 地理编码防抖时间（毫秒）。设置为 0 则禁用防抖 */
  geocodeDebounceTime?: number

  // 事件回调
  /** 标注回调 */
  onMark?: (position: [number, number], address?: string) => void
  /** 完成标注回调 */
  onComplete?: (markers: (TMarker | TSvgOverlay)[]) => void
  /** 地理编码完成回调 */
  onGeocodeComplete?: (
    marker: TMarker | TSvgOverlay,
    address: string,
    detail: T.AddressComponent,
  ) => void
  /** 地理编码错误回调 */
  onGeocodeError?: (error: Error, marker: TMarker | TSvgOverlay) => void
  /** 标注删除回调 */
  onRemove?: (marker: TMarker | TSvgOverlay) => void
}

export class TPointMarkerTool {
  private map: TMap
  private options: TPointMarkerToolOptions
  private geocoder: TGeocoder
  private isActive = false
  private markers: (TMarker | TSvgOverlay)[] = []
  private followMarker: TMarker | TSvgOverlay | null = null
  private mouseMoveHandler: ((e: any) => void) | null = null
  private clickHandler: ((e: any) => void) | null = null
  private geocodeQueue: Set<TMarker | TSvgOverlay> = new Set()
  private geocodeDebounceTimer: number | null = null

  constructor(map: TMap, options: TPointMarkerToolOptions = {}) {
    validateTMapAPI()

    this.map = map
    this.options = {
      mode: TPointMarkerMode.DEFAULT,
      width: 32,
      height: 32,
      enableGeocoding: false,
      autoSetAddress: false,
      updateAddressOnDrag: false,
      geocodeDebounceTime: 300,
      ...options,
    }

    this.geocoder = new TGeocoder(map.getNativeMap())
  }

  /** 检查是否启用了指定模式 */
  private hasMode(mode: TPointMarkerMode): boolean {
    return (this.options.mode! & mode) === mode
  }

  /**
   * 创建标记点
   * @private
   */
  private async createMarker(position: [number, number]): Promise<TMarker | TSvgOverlay> {
    const marker = this.createBaseMarker(position)

    if (this.options.label) {
      this.setMarkerLabel(marker, this.options.label)
    }

    if (this.options.enableGeocoding) {
      await this.geocodeMarker(marker)
    }

    // 设置拖拽事件
    if (this.options.updateAddressOnDrag) {
      marker.on('dragend', async () => {
        const pos = marker.getPosition()
        await this.geocodeMarker(marker, [pos[0], pos[1]])
      })
    }

    return marker
  }

  /**
   * 创建基础标记点
   * @private
   */
  private createBaseMarker(position: [number, number]): TMarker | TSvgOverlay {
    const { iconUrl, iconName, svgMarkup, width, height, iconStyle } = this.options
    const markerWidth = width || 32
    const markerHeight = height || 32

    if (iconName || svgMarkup) {
      const svgOptions: TSvgOverlayOptions = {
        position,
        iconName,
        svgMarkup,
        size: [markerWidth, markerHeight],
        zIndexOffset: 600,
        anchor: TAnchorPosition.BOTTOM_CENTER,
        iconStyle: {
          cursor: 'pointer',
          ...iconStyle,
        },
        events: this.options.events,
      }
      const marker = new TSvgOverlay(svgOptions)
      marker.addTo(this.map)
      return marker
    }

    const marker = new TMarker({
      position,
      icon: iconUrl,
      width: markerWidth,
      height: markerHeight,
      draggable: this.options.updateAddressOnDrag,
      events: this.options.events,
    })
    marker.addTo(this.map)
    return marker
  }

  /**
   * 对标记进行地理编码
   * @private
   */
  private async geocodeMarker(
    marker: TMarker | TSvgOverlay,
    position?: [number, number],
  ): Promise<void> {
    this.geocodeQueue.add(marker)

    if (this.options.geocodeDebounceTime === 0) {
      await this.processGeocodeQueue(marker, position)
      return
    }

    // 清除现有定时器
    if (this.geocodeDebounceTimer) {
      window.clearTimeout(this.geocodeDebounceTimer)
    }

    // 设置新的定时器
    this.geocodeDebounceTimer = window.setTimeout(
      () => this.processGeocodeQueue(marker, position),
      this.options.geocodeDebounceTime,
    )
  }

  private async processGeocodeQueue(
    marker: TMarker | TSvgOverlay,
    position?: [number, number],
  ): Promise<void> {
    for (const queuedMarker of this.geocodeQueue) {
      try {
        const pos = position || queuedMarker.getPosition()
        const result = await this.geocoder.getLocation(new T.LngLat(pos[0], pos[1]))
        const address = result.getAddress()
        const detail = result.getAddressComponent()

        if (this.options.autoSetAddress) {
          const text = this.options.formatAddress?.(address, detail) ?? address
          this.setMarkerLabel(queuedMarker, text)
        }

        if (this.options.onGeocodeComplete) {
          this.options.onGeocodeComplete(queuedMarker, address, detail)
        }
      } catch (error) {
        if (this.options.onGeocodeError) {
          this.options.onGeocodeError(error as Error, queuedMarker)
        }
      }
    }

    this.geocodeQueue.clear()
  }

  /**
   * 设置标记标签
   * @private
   */
  private setMarkerLabel(marker: TMarker | TSvgOverlay, label: string): void {
    const { labelOffset = [0, 0], labelStyle, removeable } = this.options
    // const { width = 32, height = 32 } = this.options

    if (marker instanceof TSvgOverlay) {
      // TODO: adjustedOffset
      // const adjustedOffset: [number, number] = [labelOffset[0] - width / 2, labelOffset[1] - height]
      marker.setLabel(label, {
        style: labelStyle,
        removeable,
        onRemove: () => {
          const index = this.markers.indexOf(marker)
          if (index > -1) {
            this.markers.splice(index, 1)
          }
          marker.remove()
          this.options.onRemove?.(marker)
        },
      })
    } else {
      marker.setLabel(label, {
        offset: labelOffset,
        style: labelStyle,
        removeable,
        onRemove: () => {
          const index = this.markers.indexOf(marker)
          if (index > -1) {
            this.markers.splice(index, 1)
          }
          marker.remove()
          this.options.onRemove?.(marker)
        },
      })
    }
  }

  /**
   * 设置事件监听
   * @private
   */
  private setupEventListeners(): void {
    const nativeMap = this.map.getNativeMap()

    if (this.hasMode(TPointMarkerMode.FOLLOW)) {
      this.mouseMoveHandler = (e: any) => {
        if (this.followMarker && e.lnglat) {
          this.followMarker.setPosition([e.lnglat.lng, e.lnglat.lat])
        }
      }
      nativeMap.addEventListener('mousemove', this.mouseMoveHandler)
    }

    this.clickHandler = async (e: any) => {
      if (!this.isActive) return
      const lnglat = e.lnglat

      if (this.hasMode(TPointMarkerMode.SINGLE)) {
        this.clear()
      }

      const marker = await this.createMarker([lnglat.lng, lnglat.lat])
      this.markers.push(marker)

      if (!this.hasMode(TPointMarkerMode.KEEP_ACTIVE)) {
        this.stop()
      }

      if (this.options.onMark) {
        this.options.onMark([lnglat.lng, lnglat.lat])
      }

      if (this.options.onComplete) {
        this.options.onComplete(this.getMarkers())
      }
    }
    nativeMap.addEventListener('click', this.clickHandler)
  }

  /**
   * 移除事件监听
   * @private
   */
  private removeEventListeners(): void {
    const nativeMap = this.map.getNativeMap()

    if (this.mouseMoveHandler) {
      nativeMap.removeEventListener('mousemove', this.mouseMoveHandler)
      this.mouseMoveHandler = null
    }
    if (this.clickHandler) {
      nativeMap.removeEventListener('click', this.clickHandler)
      this.clickHandler = null
    }
  }

  /**
   * 创建跟随标记
   * @private
   */
  private createFollowMarker(): void {
    if (this.followMarker) return

    this.followMarker = this.createBaseMarker([0, 0])

    // 设置半透明效果
    if (this.followMarker instanceof TSvgOverlay) {
      const style = { ...this.options.iconStyle, opacity: '0.6' }
      this.followMarker.setIconStyle(style)
    } else {
      this.followMarker.setOpacity(0.6)
    }
  }

  // =============== Public Methods ===============

  /**
   * 开启标注工具
   */
  public start(): boolean {
    if (this.isActive) return false

    this.isActive = true
    this.setupEventListeners()

    if (this.hasMode(TPointMarkerMode.FOLLOW)) {
      this.createFollowMarker()
    }

    return true
  }

  /**
   * 停止标注工具
   */
  public stop(): void {
    if (!this.isActive) return

    this.isActive = false
    this.removeEventListeners()

    if (this.followMarker) {
      this.followMarker.remove()
      this.followMarker = null
    }
  }

  /**
   * 切换标注工具的激活状态
   */
  public toggle(): boolean {
    return this.isActive ? (this.stop(), false) : this.start()
  }

  /**
   * 清除所有标注
   */
  public clear(): void {
    this.markers.forEach((marker) => marker.remove())
    this.markers = []
  }

  /**
   * 获取所有标注点
   */
  public getMarkers(): (TMarker | TSvgOverlay)[] {
    return [...this.markers]
  }

  /**
   * 在指定位置添加标记点
   */
  public async addMarkerAt(
    position: [number, number],
    options?: Partial<{
      iconUrl: string
      iconName: string
      svgMarkup: string
      width: number
      height: number
      iconStyle: Partial<CSSStyleDeclaration>
      label: string
    }>,
  ): Promise<TMarker | TSvgOverlay> {
    const markerOptions = { ...this.options, ...options }
    const originalOptions = { ...this.options }
    Object.assign(this.options, markerOptions)

    const marker = await this.createMarker(position)
    this.markers.push(marker)

    this.options = originalOptions

    if (this.hasMode(TPointMarkerMode.SINGLE) && this.markers.length > 1) {
      const oldMarker = this.markers[0]
      oldMarker.remove()
      this.markers = [marker]
    }

    if (this.options.onMark) {
      this.options.onMark(position, options?.label)
    }

    if (this.options.onComplete) {
      this.options.onComplete(this.getMarkers())
    }

    return marker
  }

  /**
   * 搜索地址并添加标记
   */
  public async searchAndMark(address: string): Promise<TMarker | TSvgOverlay> {
    try {
      const result = await this.geocoder.getPoint(address)
      const point = result.getLocationPoint()
      const marker = await this.addMarkerAt([point.lng, point.lat])

      if (this.options.autoSetAddress) {
        const text =
          this.options.formatAddress?.(result.getAddress(), result.getAddressComponent()) ??
          result.getAddress()
        this.setMarkerLabel(marker, text)
      }

      return marker
    } catch (error) {
      throw new Error(`地址搜索失败: ${(error as Error).message}`)
    }
  }

  /**
   * 更新所有标记的地址信息
   */
  public async updateAllAddresses(): Promise<void> {
    const promises = this.markers.map((marker) => this.geocodeMarker(marker))
    await Promise.all(promises)
  }

  /**
   * 设置标注图标
   */
  public setIcon(url: string): void {
    this.options.iconUrl = url
    this.options.iconName = undefined
    this.options.svgMarkup = undefined
    if (this.followMarker) {
      this.followMarker.remove()
      this.followMarker = null
      if (this.hasMode(TPointMarkerMode.FOLLOW) && this.isActive) {
        this.createFollowMarker()
      }
    }
  }

  /**
   * 设置 SVG 图标
   */
  public setSvgIcon(iconNameOrMarkup: string, isMarkup = false): void {
    if (isMarkup) {
      this.options.svgMarkup = iconNameOrMarkup
      this.options.iconName = undefined
    } else {
      this.options.iconName = iconNameOrMarkup
      this.options.svgMarkup = undefined
    }
    this.options.iconUrl = undefined

    // 更新跟随标记
    if (this.followMarker) {
      this.followMarker.remove()
      this.followMarker = null
      if (this.hasMode(TPointMarkerMode.FOLLOW) && this.isActive) {
        this.createFollowMarker()
      }
    }
  }

  public setIconStyle(style: Partial<CSSStyleDeclaration>): void {
    this.options.iconStyle = style

    if (this.followMarker instanceof TSvgOverlay) {
      this.followMarker?.setIconStyle({
        ...style,
        opacity: '0.6',
      })
    }

    this.markers.forEach((marker) => {
      if (marker instanceof TSvgOverlay) {
        marker.setIconStyle(style)
      }
    })
  }

  /**
   * 获取标注工具是否处于激活状态
   */
  public isActivated(): boolean {
    return this.isActive
  }
}
