import { TBaseContentOverlay, type TBaseContentOverlayOptions } from './TBaseContentOverlay'

export interface THtmlOverlayOptions<T = any> extends TBaseContentOverlayOptions<T> {
  html: string
}

export class THtmlOverlay<T = any> extends TBase<PERSON>ontentOverlay<T> {
  protected html: string

  constructor(options: THtmlOverlayOptions<T>) {
    const { html, ...baseOptions } = options
    super(baseOptions)
    this.html = html
  }

  protected renderContent(): void {
    if (this.containerElement) {
      this.containerElement.innerHTML = this.html
      Object.assign(this.containerElement.style, this.options.style)
    }
  }

  public setContent(html: string): void {
    this.html = html
    this.renderContent()
  }

  public getContent(): string {
    return this.html
  }
}
