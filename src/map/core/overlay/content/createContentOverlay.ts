import type { Component, VNode } from 'vue'
import { isVueComponent } from '../../../utils/vue'
import { THtmlOverlay, type THtmlOverlayOptions } from './THtmlOverlay'
import { TComponentOverlay, type TComponentOverlayOptions } from './TComponentOverlay'
import { TVNodeOverlay, type TVNodeOverlayOptions } from './TVNodeOverlay'

export type TContentOverlayOptions =
  | (Omit<THtmlOverlayOptions, 'content'> & { content: string })
  | (Omit<TComponentOverlayOptions, 'content'> & { content: Component })
  | (Omit<TVNodeOverlayOptions, 'content'> & { content: VNode })

export function createContentOverlay(options: TContentOverlayOptions) {
  if (typeof options.content === 'string') {
    return new THtmlOverlay(options as THtmlOverlayOptions)
  }

  if (isVueComponent(options.content)) {
    return new TComponentOverlay(options as TComponentOverlayOptions)
  }

  return new TVNodeOverlay(options as TVNodeOverlayOptions)
}

// 导出所有类型
export type {
  TBaseContentOverlayOptions,
  TBaseContentOverlayEventType,
  TBaseContentOverlayEventHandler
} from './TBaseContentOverlay'

export type { THtmlOverlayOptions } from './THtmlOverlay'
export type { TComponentOverlayOptions } from './TComponentOverlay'
export type { TVNodeOverlayOptions } from './TVNodeOverlay'
