import { createApp, h } from 'vue'
import type { App, Component, VNode } from 'vue'
import type { TNativeMap, TNativeOverlay } from '../../../types'
import TOverlayContent from '../../../components/TOverlayContent.vue'

/**
 * @deprecated
 * - 字符串内容使用 THtmlOverlay
 * - Vue组件使用 TComponentOverlay
 * - VNode使用 TVNodeOverlay
 * 或使用 createContentOverlay 工厂函数自动创建对应类型的覆盖物
 */
export type TContentOverlayEventType =
  | 'click'
  | 'dblclick'
  | 'mousedown'
  | 'mouseup'
  | 'mouseout'
  | 'mouseover'

/**
 * @deprecated 请使用新的专门类型覆盖物类的事件处理器类型
 */
export type TContentOverlayEventHandler = (event: Event) => void

/**
 * @deprecated 请使用新的专门类型覆盖物类的选项接口
 */
export interface TContentOverlayOptions {
  position: [number, number]
  content: string | Component | VNode
  offset?: [number, number]
  props?: Record<string, any>
  events?: Partial<Record<TContentOverlayEventType, TContentOverlayEventHandler>>
  style?: Partial<CSSStyleDeclaration>
}

export class TContentOverlay {
  protected map: TNativeMap | null = null
  protected nativeOverlay: TNativeOverlay | null = null
  private containerElement: HTMLElement | null = null
  private vueApp: App | null = null
  private position: [number, number]
  private content: string | Component | VNode
  private offset: [number, number]
  private props: Record<string, any>
  private events: Partial<Record<TContentOverlayEventType, TContentOverlayEventHandler>>
  private style: Partial<CSSStyleDeclaration>
  private eventHandlers: Map<TContentOverlayEventType, Set<TContentOverlayEventHandler>> = new Map()

  constructor(options: TContentOverlayOptions) {
    this.position = options.position
    this.content = options.content
    this.offset = options.offset ?? [0, 0]
    this.props = options.props ?? {}
    this.events = options.events ?? {}
    this.style = options.style ?? {}
    this.nativeOverlay = this.createNativeOverlay()
  }

  public addTo(map: TNativeMap): void {
    if (!this.nativeOverlay) return
    this.map = map
    map.addOverLay(this.nativeOverlay)
  }

  public remove(): void {
    if (!this.map || !this.nativeOverlay) return
    this.map.removeOverLay(this.nativeOverlay)
    this.map = null
  }

  public show(): void {
    this.nativeOverlay?.show()
  }

  public hide(): void {
    this.nativeOverlay?.hide()
  }

  public isHidden(): boolean {
    return this.nativeOverlay?.isHidden() ?? true
  }

  public getPosition(): [number, number] {
    if (!this.nativeOverlay) return [0, 0]

    const position = (this.nativeOverlay as any).position
    if (!position) return [0, 0]

    if ('lng' in position && 'lat' in position) {
      return [position.lng, position.lat]
    }

    if (Array.isArray(position)) {
      return [position[0], position[1]] as [number, number]
    }

    return [0, 0]
  }

  private createNativeOverlay(): TNativeOverlay {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this

    interface CustomOverlay extends T.Overlay {
      position: T.LngLat
      offset: T.Point
      map: TNativeMap
    }

    const CustomOverlay = T.Overlay.extend({
      initialize: function (options: any) {
        this.position = options.position
        this.offset = options.offset
      },

      onAdd: function (map: TNativeMap) {
        this.map = map
        self.createContainer()
        self.bindContainerEvents()
        self.appendToMap(map)
        self.renderContent()
        this.update()
        return self.containerElement!
      },

      onRemove: function () {
        if (self.vueApp) {
          self.vueApp.unmount()
          self.vueApp = null
        }
        if (self.containerElement?.parentNode) {
          self.containerElement.parentNode.removeChild(self.containerElement)
        }
        self.containerElement = null
        this.map = null
      },

      update: function () {
        if (!self.containerElement || !this.map) return
        const pos = this.map.lngLatToLayerPoint(this.position)
        const offsetX = this.offset.x
        const offsetY = this.offset.y

        self.containerElement.style.left = pos.x + offsetX + 'px'
        self.containerElement.style.top = pos.y + offsetY + 'px'
      },
    }) as unknown as new (options: any) => CustomOverlay

    return new CustomOverlay({
      position: new T.LngLat(...this.position),
      offset: new T.Point(...this.offset),
    })
  }

  private createContainer(): void {
    this.containerElement = document.createElement('div')
    this.containerElement.style.position = 'absolute'
    this.containerElement.style.whiteSpace = 'nowrap'
  }

  private bindContainerEvents(): void {
    if (!this.containerElement) return

    const events: TContentOverlayEventType[] = [
      'click',
      'dblclick',
      'mousedown',
      'mouseup',
      'mouseout',
      'mouseover',
    ]
    events.forEach((eventName) => {
      if (this.events[eventName]) {
        this.on(eventName, this.events[eventName])
      }
    })

    this.eventHandlers.forEach((handlers, eventName) => {
      handlers.forEach((handler) => {
        this.containerElement!.addEventListener(eventName, (e: Event) => {
          e.stopPropagation()
          handler(e)
        })
      })
    })
  }

  private appendToMap(map: TNativeMap): void {
    if (!this.containerElement) return
    map.getPanes().overlayPane.appendChild(this.containerElement)
  }

  private renderContent(): void {
    if (!this.containerElement) return

    if (this.vueApp) {
      this.vueApp.unmount()
      this.vueApp = null
    }
    this.containerElement.innerHTML = ''

    if (typeof this.content === 'string') {
      this.containerElement.innerHTML = this.content
      Object.assign(this.containerElement.style, this.style)
    } else {
      this.vueApp = createApp({
        render: () =>
          h(TOverlayContent, {
            content: this.content,
            contentProps: this.props,
            contentEvents: this.events,
          }),
      })
      this.vueApp.mount(this.containerElement)
    }
  }

  /**
   * 更新覆盖物位置
   */
  public setPosition(position: [number, number]): void {
    this.position = position

    if (this.nativeOverlay) {
      ;(this.nativeOverlay as any).position = new T.LngLat(...position)
      this.nativeOverlay.update()
    }
  }

  /**
   * 更新覆盖物内容
   */
  public setContent(content: string | Component | VNode): void {
    this.content = content
    this.renderContent()
  }

  /**
   * 更新内容属性
   */
  public setProps(props: Record<string, any>): void {
    this.props = props
    this.renderContent()
  }

  /**
   * 更新事件处理器
   */
  public setEvents(
    events: Partial<Record<TContentOverlayEventType, TContentOverlayEventHandler>>,
  ): void {
    if (this.containerElement) {
      this.eventHandlers.forEach((handlers, eventName) => {
        handlers.forEach((handler) => {
          this.containerElement!.removeEventListener(eventName, handler)
        })
      })
    }

    this.events = events
    this.eventHandlers.clear()

    if (this.containerElement) {
      this.bindContainerEvents()
    }
  }

  /**
   * 在覆盖物容器中查找指定选择器的元素
   * @param selector CSS选择器
   * @returns 匹配的元素或null
   */
  protected querySelector<T extends Element>(selector: string): T | null {
    return this.containerElement?.querySelector<T>(selector) ?? null
  }

  /**
   * 在覆盖物容器中查找所有指定选择器的元素
   * @param selector CSS选择器
   * @returns 匹配的元素列表
   */
  protected querySelectorAll<T extends Element>(selector: string): T[] {
    return Array.from(this.containerElement?.querySelectorAll<T>(selector) ?? [])
  }

  /**
   * 设置覆盖物偏移量
   */
  public setOffset(offset: [number, number]): void {
    if (this.nativeOverlay) {
      ;(this.nativeOverlay as any).offset = new T.Point(offset[0], offset[1])
      this.nativeOverlay.update()
    }
  }

  /**
   * 绑定事件监听
   * @param eventName 事件名称
   * @param handler 事件处理函数
   */
  public on(eventName: TContentOverlayEventType, handler: TContentOverlayEventHandler): void {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, new Set())
    }
    this.eventHandlers.get(eventName)!.add(handler)

    if (this.containerElement) {
      this.containerElement.addEventListener(eventName, (e: Event) => {
        e.stopPropagation()
        handler(e)
      })
    }
  }

  /**
   * 解绑事件监听
   * @param eventName 事件名称
   * @param handler 事件处理函数
   */
  public off(eventName: TContentOverlayEventType, handler: TContentOverlayEventHandler): void {
    const handlers = this.eventHandlers.get(eventName)
    if (handlers) {
      handlers.delete(handler)
      if (this.containerElement) {
        this.containerElement.removeEventListener(eventName, handler)
      }
    }
  }

  /**
   * 点击事件监听
   * @param handler 事件处理函数
   */
  public onClick(handler: TContentOverlayEventHandler): void {
    this.on('click', handler)
  }

  /**
   * 双击事件监听
   * @param handler 事件处理函数
   */
  public onDblClick(handler: TContentOverlayEventHandler): void {
    this.on('dblclick', handler)
  }
}
