import { createApp, h, type Component } from 'vue'
import { TBaseContentOverlay, type TBaseContentOverlayOptions } from './TBaseContentOverlay'
import TOverlayContent from '../../../components/TOverlayContent.vue'

export interface TComponentOverlayOptions<T = any> extends TBaseContentOverlayOptions<T> {
  component: Component
  props?: Record<string, any>
}

export class TComponentOverlay<T = any> extends TBaseContentOverlay<T> {
  private component: Component
  private props: Record<string, any>

  constructor(options: TComponentOverlayOptions<T>) {
    const { component, props, ...baseOptions } = options
    super(baseOptions)
    this.component = component
    this.props = props ?? {}
  }

  protected renderContent(): void {
    if (!this.containerElement) return

    if (this.vueApp) {
      this.vueApp.unmount()
      this.vueApp = null
    }

    this.containerElement.innerHTML = ''
    this.vueApp = createApp({
      render: () =>
        h(TOverlayContent, {
          content: this.component,
          contentProps: this.props,
          contentEvents: this.options.events,
        }),
    })
    this.vueApp.mount(this.containerElement)
  }

  public setContent(component: Component, props?: Record<string, any>): void {
    this.component = component
    if (props) {
      this.props = props
    }
    this.renderContent()
  }

  public setProps(props: Record<string, any>): void {
    this.props = props
    this.renderContent()
  }
}
