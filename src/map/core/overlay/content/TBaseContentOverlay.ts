import { TBaseOverlay } from '../../base/TBaseOverlay'
import type { TNativeMap, TNativeOverlay } from '@/map/types'

export type TBaseContentOverlayEventType =
  | 'click'
  | 'dblclick'
  | 'mousedown'
  | 'mouseup'
  | 'mouseout'
  | 'mouseover'

export type TBaseContentOverlayEventHandler = (event: Event) => void

export interface TBaseContentOverlayOptions<T = any> {
  id?: string
  position: [number, number]
  offset?: [number, number]
  style?: Partial<CSSStyleDeclaration>
  events?: Record<string, (event: Event) => void>
  userData?: T
}

export abstract class TBaseContentOverlay<T = any> extends TBaseOverlay<T> {
  private eventHandlers: Map<string, Set<(e: Event) => void>> = new Map()
  protected containerElement: HTMLElement | null = null
  protected options: TBaseContentOverlayOptions<T>
  protected nativeOverlay: TNativeOverlay | null = null
  protected vueApp: any = null

  constructor(options: TBaseContentOverlayOptions<T>) {
    super({
      id: options.id,
      userData: options.userData
    }, 'content-overlay')

    this.options = {
      ...options,
      offset: options.offset ?? [0, 0],
      style: options.style ?? {},
      events: options.events ?? {},
    }
    this.nativeOverlay = this.createNativeOverlay()
  }

  protected onAdd(map: TNativeMap): void {
    if (!this.nativeOverlay) return
    map.addOverLay(this.nativeOverlay)
  }

  protected onRemove(): void {
    if (this.vueApp) {
      this.vueApp.unmount()
      this.vueApp = null
    }
    if (this.containerElement?.parentNode) {
      this.containerElement.parentNode.removeChild(this.containerElement)
    }
    this.containerElement = null
    const map = this.assertMapExists()
    if (this.nativeOverlay) {
      map.removeOverLay(this.nativeOverlay)
    }
  }

  protected onShow(): void {
    this.nativeOverlay?.show()
  }

  protected onHide(): void {
    this.nativeOverlay?.hide()
  }

  public setOffset(offset: [number, number]): void {
    this.options.offset = offset
    if (this.nativeOverlay) {
      ;(this.nativeOverlay as any).offset = new T.Point(...offset)
      this.nativeOverlay.update()
    }
  }

  public setPosition(position: [number, number]): void {
    this.options.position = position
    if (this.nativeOverlay) {
      ;(this.nativeOverlay as any).position = new T.LngLat(...position)
      this.nativeOverlay.update()
    }
  }

  protected bindContainerEvents(): void {
    if (!this.containerElement) return

    const events: TBaseContentOverlayEventType[] = [
      'click',
      'dblclick',
      'mousedown',
      'mouseup',
      'mouseout',
      'mouseover',
    ]

    events.forEach((eventName) => {
      if (this.options.events && this.options.events[eventName]) {
        this.containerElement!.addEventListener(eventName, (e: Event) => {
          e.stopPropagation()
          this.options.events![eventName]!(e)
        })
      }
    })
  }

  protected createContainer(): void {
    this.containerElement = document.createElement('div')
    this.containerElement.style.position = 'absolute'
    this.containerElement.style.whiteSpace = 'nowrap'
    Object.assign(this.containerElement.style, this.options.style)
  }

  protected createNativeOverlay(): TNativeOverlay {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this

    const CustomOverlay = T.Overlay.extend({
      initialize: function (options: any) {
        this.position = options.position
        this.offset = options.offset
      },

      onAdd: function (map: TNativeMap) {
        this.map = map
        self.createContainer()
        self.bindContainerEvents()
        self.appendToMap(map)
        self.renderContent()
        this.update()
        return self.containerElement!
      },

      onRemove: function () {
        if (self.vueApp) {
          self.vueApp.unmount()
          self.vueApp = null
        }
        if (self.containerElement?.parentNode) {
          self.containerElement.parentNode.removeChild(self.containerElement)
        }
        self.containerElement = null
        this.map = null
      },

      update: function () {
        if (!self.containerElement || !this.map) return
        const pos = this.map.lngLatToLayerPoint(this.position)
        const offsetX = this.offset.x
        const offsetY = this.offset.y

        self.containerElement.style.left = pos.x + offsetX + 'px'
        self.containerElement.style.top = pos.y + offsetY + 'px'
      },
    }) as unknown as new (options: any) => TNativeOverlay

    return new CustomOverlay({
      position: new T.LngLat(...this.options.position),
      offset: new T.Point(...this.options.offset!),
    })
  }

  protected abstract renderContent(): void

  protected appendToMap(map: TNativeMap): void {
    if (!this.containerElement) return
    map.getPanes().overlayPane.appendChild(this.containerElement)
  }

  protected querySelector<T extends Element>(selector: string): T | null {
    return this.containerElement?.querySelector<T>(selector) ?? null
  }

  protected querySelectorAll<T extends Element>(selector: string): T[] {
    return Array.from(this.containerElement?.querySelectorAll<T>(selector) ?? [])
  }

  public on(eventName: string, handler: (e: Event) => void): void {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, new Set())
    }
    this.eventHandlers.get(eventName)!.add(handler)

    if (eventName === 'dragend') {
      if (this.containerElement) {
        this.containerElement.draggable = true
        this.containerElement.addEventListener('dragend', handler)
      }
    } else {
      this.containerElement?.addEventListener(eventName, handler)
    }
  }

  public off(eventName: string, handler: (e: Event) => void): void {
    const handlers = this.eventHandlers.get(eventName)
    if (handlers) {
      handlers.delete(handler)
      this.containerElement?.removeEventListener(eventName, handler)
    }
  }

  protected emit(eventName: string, event: Event): void {
    const handlers = this.eventHandlers.get(eventName)
    if (handlers) {
      handlers.forEach((handler) => handler(event))
    }
  }
}
