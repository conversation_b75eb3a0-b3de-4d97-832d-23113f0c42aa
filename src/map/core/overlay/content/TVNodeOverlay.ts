import { createApp, type VNode } from 'vue'
import { TBaseContentOverlay, type TBaseContentOverlayOptions } from './TBaseContentOverlay'

export interface TVNodeOverlayOptions<T = any> extends TBaseContentOverlayOptions<T> {
  vnode: VNode
}

export class TVN<PERSON><PERSON>verlay<T = any> extends TBase<PERSON>ontentOverlay<T> {
  private vnode: VNode

  constructor(options: TVNodeOverlayOptions<T>) {
    const { vnode, ...baseOptions } = options
    super(baseOptions)
    this.vnode = vnode
  }

  protected renderContent(): void {
    if (!this.containerElement) return

    if (this.vueApp) {
      this.vueApp.unmount()
      this.vueApp = null
    }

    this.containerElement.innerHTML = ''
    this.vueApp = createApp({
      render: () => this.vnode,
    })
    this.vueApp.mount(this.containerElement)
  }

  public setContent(vnode: VNode): void {
    this.vnode = vnode
    this.renderContent()
  }

  public getContent(): VNode {
    return this.vnode
  }
}
