import type { TNativeMap, TNativePolyline, TMapEvent } from '../../types'
import { TLabel, type TLabelOptions } from './TLabel'
import { TBaseOverlay } from '../base/TBaseOverlay'
import { validateTMapAPI } from '../../utils/validator'

// 扩展折线配置选项
export interface TPolylineOptions<T = any> extends T.PolylineOptions {
  id?: string
  userData?: T
  path: [number, number][]
  label?: string
  labelPosition?: number | [number, number]
  labelOffset?: [number, number]
  labelStyle?: Partial<CSSStyleDeclaration>
  events?: Partial<{
    click: (evt: any) => void
    dblclick: (evt: any) => void
    mousedown: (evt: any) => void
    mouseup: (evt: any) => void
    mouseover: (evt: any) => void
    mouseout: (evt: any) => void
    remove: (evt: any) => void
  }>
}

export interface TPolylineEditEvent {
  type: 'editstart' | 'editing' | 'editend'
  target: T.Polyline
  lnglat: T.LngLat
  points: [number, number][]
}

export interface TPolylineStyle {
  color?: string
  weight?: number
  opacity?: number
  lineStyle?: 'solid' | 'dashed'
}

export class TPolyline<T = any> extends TBaseOverlay<T> {
  private polyline: TNativePolyline
  private label: TLabel | null = null
  private options: TPolylineOptions<T>
  private isEditing = false
  private isMouseDown = false
  private editEventHandlers = new Map<string, ((e: TPolylineEditEvent) => void)[]>()
  private lastPath: [number, number][] = []
  private lastLngLat: T.LngLat | null = null
  private isRealEditing = false
  private initialPath: [number, number][] = []

  constructor(options: TPolylineOptions<T>) {
    super({ id: options.id, userData: options.userData }, 'polyline')

    validateTMapAPI()

    this.options = options

    const lngLats = options.path.map(([lng, lat]) => new T.LngLat(lng, lat))

    this.polyline = new T.Polyline(lngLats, {
      color: options.color ?? '#0000FF',
      weight: options.weight ?? 3,
      opacity: options.opacity ?? 0.5,
      lineStyle: options.lineStyle ?? 'solid',
    })

    if (options.events) {
      Object.entries(options.events).forEach(([eventName, handler]) => {
        if (handler) {
          this.on(eventName as T.PolygonEventType, handler)
        }
      })
    }
    if (options.label) {
      this.createLabel()
    }
  }

  protected onAdd(map: TNativeMap): void {
    map.addOverLay(this.polyline)
    if (this.label && this.map) {
      this.label.addTo(this.map)
    }
  }

  protected onRemove(): void {
    const map = this.assertMapExists()
    map.removeOverLay(this.polyline)
    if (this.label) {
      this.label.remove()
    }
    this.disableEdit()
  }

  protected onShow(): void {
    this.polyline.show()
    this.label?.show()
  }

  protected onHide(): void {
    this.polyline.hide()
    this.label?.hide()
  }

  private createLabel(): void {
    if (!this.options.label) return

    const position = this.calculateLabelPosition()

    this.label = new TLabel({
      position,
      text: this.options.label,
      offset: this.options.labelOffset ?? [0, 0],
      style: this.options.labelStyle,
    })
  }

  private calculateLabelPosition(): [number, number] {
    const { labelPosition } = this.options

    if (Array.isArray(labelPosition)) {
      return labelPosition
    }

    // 获取折线所有点
    const points = this.getPath()

    if (points.length === 0) return [0, 0]
    if (points.length === 1) return points[0]

    // 如果提供了百分比位置
    if (typeof labelPosition === 'number') {
      return this.getPointAtPercentage(labelPosition)
    }

    // 默认使用中点位置
    return this.getPointAtPercentage(0.5)
  }

  private getPointAtPercentage(percentage: number): [number, number] {
    const points = this.getPath()
    if (points.length < 2) return points[0] || [0, 0]

    // 计算折线总长度
    let totalLength = 0
    const segments: { length: number; start: [number, number]; end: [number, number] }[] = []

    for (let i = 0; i < points.length - 1; i++) {
      const start = points[i]
      const end = points[i + 1]
      const length = this.calculateDistance(start, end)
      totalLength += length
      segments.push({ length, start, end })
    }

    // 计算目标位置
    const targetLength = totalLength * percentage
    let accumulatedLength = 0

    for (const segment of segments) {
      if (accumulatedLength + segment.length >= targetLength) {
        const segmentPercentage = (targetLength - accumulatedLength) / segment.length
        return this.interpolatePoint(segment.start, segment.end, segmentPercentage)
      }
      accumulatedLength += segment.length
    }

    return points[points.length - 1]
  }

  private calculateDistance(point1: [number, number], point2: [number, number]): number {
    const [x1, y1] = point1
    const [x2, y2] = point2
    return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2))
  }

  private interpolatePoint(
    start: [number, number],
    end: [number, number],
    percentage: number,
  ): [number, number] {
    return [
      start[0] + (end[0] - start[0]) * percentage,
      start[1] + (end[1] - start[1]) * percentage,
    ]
  }

  public setPath(points: [number, number][]): void {
    const lngLats = points.map(([lng, lat]) => new T.LngLat(lng, lat))
    this.polyline.setLngLats(lngLats)

    // 更新标注位置
    if (this.label) {
      const newPosition = this.calculateLabelPosition()
      this.label.setPosition(newPosition)
    }
  }

  public getPath(): [number, number][] {
    return this.polyline.getLngLats().map((lngLat) => [lngLat.lng, lngLat.lat])
  }

  public setColor(color: string): void {
    this.polyline.setColor(color)
  }

  public getColor(): string {
    return this.polyline.getColor()
  }

  public setWeight(weight: number): void {
    this.polyline.setWeight(weight)
  }

  public getWeight(): number {
    return this.polyline.getWeight()
  }

  public setOpacity(opacity: number): void {
    this.polyline.setOpacity(opacity)
  }

  public getOpacity(): number {
    return this.polyline.getOpacity()
  }

  public setLineStyle(style: 'solid' | 'dashed'): void {
    this.polyline.setLineStyle(style)
  }

  public getLineStyle(): 'solid' | 'dashed' {
    return this.polyline.getLineStyle() as 'solid' | 'dashed'
  }

  public getBounds(): [[number, number], [number, number]] {
    const bounds = this.polyline.getBounds()
    return [
      [bounds.getSouthWest().lng, bounds.getSouthWest().lat],
      [bounds.getNorthEast().lng, bounds.getNorthEast().lat],
    ]
  }

  public enableEdit(): void {
    const map = this.assertMapExists()

    this.polyline.enableEdit()
    this.isEditing = true
    this.lastPath = this.getPath()

    map.addEventListener('mousedown', this.handleMapMouseDown)
    map.addEventListener('mousemove', this.handleMapMouseMove)
    map.addEventListener('mouseup', this.handleMapMouseUp)
  }

  public disableEdit(): void {
    const map = this.assertMapExists()

    this.polyline.disableEdit()
    this.isEditing = false
    this.isMouseDown = false
    this.isRealEditing = false

    map.removeEventListener('mousedown', this.handleMapMouseDown)
    map.removeEventListener('mousemove', this.handleMapMouseMove)
    map.removeEventListener('mouseup', this.handleMapMouseUp)
  }

  public isEditable(): boolean {
    return this.polyline.isEditable()
  }

  public on(event: T.PolylineEventType, handler: (e: T.PolylineEvent) => void): void {
    this.polyline.addEventListener(event, handler)
  }

  public off(event: T.PolylineEventType, handler: (e: T.PolylineEvent) => void): void {
    this.polyline.removeEventListener(event, handler)
  }

  private handleMapMouseDown = (e: TMapEvent) => {
    if (!this.isEditing) return

    this.isMouseDown = true
    this.initialPath = this.getPath()
    this.lastPath = this.initialPath
    this.lastLngLat = e.lnglat
  }

  private handleMapMouseMove = (e: TMapEvent) => {
    if (!this.isEditing || !this.isMouseDown) return

    const currentPath = this.getPath()
    const pathChanged = JSON.stringify(currentPath) !== JSON.stringify(this.lastPath)

    if (pathChanged) {
      if (!this.isRealEditing) {
        this.isRealEditing = true
        const startEvent: TPolylineEditEvent = {
          type: 'editstart',
          target: this.polyline,
          lnglat: this.lastLngLat!,
          points: this.initialPath,
        }
        this.dispatchEditEvent('editstart', startEvent)
      }

      // 更新标注位置
      if (this.label) {
        const newPosition = this.calculateLabelPosition()
        this.label.setPosition(newPosition)
      }

      const editingEvent: TPolylineEditEvent = {
        type: 'editing',
        target: this.polyline,
        lnglat: e.lnglat,
        points: currentPath,
      }
      this.dispatchEditEvent('editing', editingEvent)

      this.lastPath = currentPath
      this.lastLngLat = e.lnglat
    }
  }

  private handleMapMouseUp = (e: TMapEvent) => {
    if (!this.isEditing || !this.isMouseDown) return

    this.isMouseDown = false

    if (this.isRealEditing) {
      const currentPath = this.getPath()
      const event: TPolylineEditEvent = {
        type: 'editend',
        target: this.polyline,
        lnglat: e.lnglat,
        points: currentPath,
      }
      this.dispatchEditEvent('editend', event)

      this.isRealEditing = false
    }

    this.lastLngLat = null
    this.initialPath = []
  }

  private dispatchEditEvent(type: string, event: TPolylineEditEvent) {
    const handlers = this.editEventHandlers.get(type)
    if (handlers) {
      handlers.forEach((handler) => handler(event))
    }
  }

  public onEdit(
    type: 'editstart' | 'editing' | 'editend',
    handler: (e: TPolylineEditEvent) => void,
  ): void {
    if (!this.editEventHandlers.has(type)) {
      this.editEventHandlers.set(type, [])
    }
    this.editEventHandlers.get(type)?.push(handler)
  }

  public offEdit(
    type: 'editstart' | 'editing' | 'editend',
    handler: (e: TPolylineEditEvent) => void,
  ): void {
    const handlers = this.editEventHandlers.get(type)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  public setLabel(label: string | null, options?: Partial<TLabelOptions>): void {
    // 移除现有标注
    if (this.label) {
      this.label.remove()
      this.label = null
    }

    // 更新选项
    this.options.label = label ?? undefined
    if (options?.offset) this.options.labelOffset = options.offset
    if (options?.style) this.options.labelStyle = options.style

    // 如果有文本，创建新标注
    if (label) {
      this.createLabel()
      if (this.map) {
        ;(this.label as unknown as TLabel)?.addTo(this.map)
      }
    }
  }

  public getLabel(): string | null {
    return this.label?.getText() ?? null
  }

  public setLabelStyle(style: Partial<CSSStyleDeclaration>): void {
    if (this.label) {
      this.label.setStyle(style)
      this.options.labelStyle = { ...this.options.labelStyle, ...style }
    }
  }

  public setLabelOffset(offset: [number, number]): void {
    if (this.label) {
      this.label.setOffset(offset)
      this.options.labelOffset = offset
    }
  }

  /**
   * 获取原生折线对象
   */
  public getNative() {
    return this.polyline
  }

  /**
   * 获取折线的总长度(米)
   */
  public getDistance(): number {
    const points = this.getPath()
    if (points.length < 2) return 0

    let totalDistance = 0
    for (let i = 0; i < points.length - 1; i++) {
      const start = new T.LngLat(points[i][0], points[i][1])
      const end = new T.LngLat(points[i + 1][0], points[i + 1][1])
      totalDistance += start.distanceTo(end)
    }

    return totalDistance
  }

  /**
   * 批量更新折线样式
   * @param style 样式对象
   */
  public updateStyle(style: Partial<TPolylineStyle>): void {
    if (!style) return

    if ('color' in style && style.color !== undefined) {
      this.setColor(style.color)
    }
    if ('weight' in style && style.weight !== undefined) {
      this.setWeight(style.weight)
    }
    if ('opacity' in style && style.opacity !== undefined) {
      this.setOpacity(style.opacity)
    }
    if ('lineStyle' in style && style.lineStyle !== undefined) {
      this.setLineStyle(style.lineStyle)
    }
  }

  /**
   * 获取当前样式
   */
  public getStyle(): TPolylineStyle {
    return {
      color: this.getColor(),
      weight: this.getWeight(),
      opacity: this.getOpacity(),
      lineStyle: this.getLineStyle()
    }
  }
}
