import type { TNativeLabel, TNativeMap, TLabelEventType, TLabelEventHandler } from '../../types'
import { TBaseOverlay } from '../base/TBaseOverlay'
import type { TOverlayOptions } from '../base/TOverlay'
import { validateTMapAPI } from '../../utils/validator'

export interface TLabelStyle {
  fontColor?: string
  fontSize?: number
  backgroundColor?: string
  borderColor?: string
  borderLine?: number
  opacity?: number
}

export interface TLabelOptions<T = any> extends TOverlayOptions {
  userData?: T
  /** 文本标注的内容 */
  text: string
  /** 文本标注的地理位置 */
  position: [number, number]
  /** 文本标注的位置偏移值 */
  offset?: [number, number]
  /** 标签样式 */
  style?: Partial<CSSStyleDeclaration>
  /** 事件配置 */
  events?: Partial<Record<TLabelEventType, TLabelEventHandler>>
  /** 是否可移除 */
  removeable?: boolean
  /** 删除按钮的样式 */
  removeButtonStyle?: Partial<CSSStyleDeclaration>
  /** 移除回调 */
  onRemove?: () => void
}

export class TLabel<T = any> extends TBaseOverlay {
  private label: TNativeLabel
  private pendingStyles: Partial<CSSStyleDeclaration>[] = []
  private pendingClassNames: { className: string; append: boolean }[] = []
  private eventHandlers: Map<TLabelEventType, Set<TLabelEventHandler>> = new Map()
  private removeButton: HTMLElement | null = null
  private removeable: boolean
  private readonly onRemoveCallback?: () => void

  constructor(options: TLabelOptions<T>) {
    validateTMapAPI()

    super({ id: options.id, userData: options.userData }, 'label')

    const { position, text, offset, events, style } = options

    const lngLat = new T.LngLat(position[0], position[1])
    const point = offset ? new T.Point(offset[0], offset[1]) : undefined
    this.label = new T.Label({
      position: lngLat,
      text,
      offset: point,
    })

    if (style) {
      this.setStyle(style)
    }

    if (events) {
      this.initEvents(events)
    }

    this.removeable = options.removeable ?? false
    this.onRemoveCallback = options.onRemove

    if (this.removeable) {
      this.createRemoveButton(options.removeButtonStyle)
    }
  }

  private createRemoveButton(style?: Partial<CSSStyleDeclaration>): void {
    this.removeButton = document.createElement('div')
    this.removeButton.className = 'tlabel-remove-button'

    Object.assign(this.removeButton.style, {
      position: 'absolute',
      top: '-8px',
      right: '-8px',
      width: '16px',
      height: '16px',
      background: '#fff',
      border: '1px solid #ddd',
      borderRadius: '50%',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '12px',
      color: '#666',
      ...style,
    })

    this.removeButton.innerHTML = '×'

    this.removeButton.addEventListener('click', (e) => {
      e.stopPropagation()
      this.onRemoveCallback?.()
      this.remove()
    })
  }

  protected onAdd(map: TNativeMap): void {
    map.addOverLay(this.label)

    const element = this.getElement()
    if (element) {
      // element.style.position = 'relative'

      if (this.pendingStyles.length > 0) {
        this.pendingStyles.forEach((styles) => {
          Object.assign(element.style, styles)
        })
        this.pendingStyles = []
      }

      if (this.pendingClassNames.length > 0) {
        this.pendingClassNames.forEach(({ className, append }) => {
          this._setClassName(element, className, append)
        })
        this.pendingClassNames = []
      }

      if (this.removeable && this.removeButton) {
        element.appendChild(this.removeButton)
      }
    }
  }

  protected onRemove(): void {
    if (this.removeButton) {
      this.removeButton.remove()
      this.removeButton = null
    }
    this.clearEvents()
    const map = this.assertMapExists()
    map.removeOverLay(this.label)
  }

  protected onShow(): void {
    this.label.show()
  }

  protected onHide(): void {
    this.label.hide()
  }

  // 获取叠加层类型
  public getType(): number {
    return this.label.getType()
  }

  public setLabel(content: string): void {
    this.label.setLabel(content)
  }

  public getLabel(): string {
    return this.label.getLabel()
  }

  public setTitle(title: string): void {
    this.label.setTitle(title)
  }

  public getTitle(): string {
    return this.label.getTitle()
  }

  // z-index相关方法
  public setZindex(): void {
    this.label.setZindex()
  }

  // 字体相关方法
  public setFontSize(size: number): void {
    this.label.setFontSize(size)
  }

  public getFontSize(): number {
    return this.label.getFontSize()
  }

  public setFontColor(color: string): void {
    this.label.setFontColor(color)
  }

  public getFontColor(): string {
    return this.label.getFontColor()
  }

  // 背景相关方法
  public setBackgroundColor(color: string): void {
    this.label.setBackgroundColor(color)
  }

  public getBackgroundColor(): string {
    return this.label.getBackgroundColor()
  }

  // 边框相关方法
  public setBorderLine(width: number): void {
    this.label.setBorderLine(width)
  }

  public getBorderLine(): number {
    return this.label.getBorderLine()
  }

  public setBorderColor(color: string): void {
    this.label.setBorderColor(color)
  }

  public getBorderColor(): string {
    return this.label.getBorderColor()
  }

  // 透明度相关方法
  public setOpacity(opacity: number): void {
    this.label.setOpacity(opacity)
  }

  public getOpacity(): number {
    return this.label.getOpacity()
  }

  // 事件相关方法
  public addEventListener(event: TLabelEventType, handler: TLabelEventHandler): void {
    this.label.addEventListener(event, handler)
  }

  public removeEventListener(event: TLabelEventType, handler: TLabelEventHandler): void {
    this.label.removeEventListener(event, handler)
  }

  /**
   * 获取标签当前样式
   * @returns 标签样式配置
   */
  public getStyle(): TLabelStyle {
    return {
      fontColor: this.getFontColor(),
      fontSize: this.getFontSize(),
      backgroundColor: this.getBackgroundColor(),
      borderColor: this.getBorderColor(),
      borderLine: this.getBorderLine(),
      opacity: this.getOpacity(),
    }
  }

  /**
   * 设置标签位置
   * @param position 位置坐标 [经度, 纬度]
   */
  public setPosition(position: [number, number]): void {
    const lngLat = new T.LngLat(position[0], position[1])
    this.label.setLngLat(lngLat)
  }

  /**
   * 获取标签位置
   * @returns [经度, 纬度]
   */
  public getPosition(): [number, number] {
    const lngLat = this.label.getLngLat()
    return [lngLat.lng, lngLat.lat]
  }

  /**
   * 设置标签偏移量
   * @param offset 偏移量 [x, y]
   */
  public setOffset(offset: [number, number]): void {
    const point = new T.Point(offset[0], offset[1])
    this.label.setOffset(point)
  }

  /**
   * 获取标签偏移量
   * @returns [x, y]
   */
  public getOffset(): [number, number] {
    const point = this.label.getOffset()
    return [point.x, point.y]
  }

  /**
   * 设置标签文本内容
   * @param text 文本内容
   */
  public setText(text: string): void {
    this.label.setLabel(text)
  }

  /**
   * 获取标签文本内容
   */
  public getText(): string {
    return this.label.getLabel()
  }

  /**
   * 绑定事件监听
   * @param eventName 事件名称
   * @param handler 事件处理函数
   */
  public on(eventName: TLabelEventType, handler: TLabelEventHandler): void {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, new Set())
    }
    this.eventHandlers.get(eventName)!.add(handler)
    this.label.addEventListener(eventName, handler)
  }

  /**
   * 解绑事件监听
   * @param eventName 事件名称
   * @param handler 事件处理函数
   */
  public off(eventName: TLabelEventType, handler: TLabelEventHandler): void {
    const handlers = this.eventHandlers.get(eventName)
    if (handlers) {
      handlers.delete(handler)
      this.label.removeEventListener(eventName, handler)
    }
  }

  /**
   * 点击事件监听
   * @param handler 事件处理函数
   */
  public onClick(handler: TLabelEventHandler): void {
    this.on('click', handler)
  }

  /**
   * 双击事件监听
   * @param handler 事件处理函数
   */
  public onDblClick(handler: TLabelEventHandler): void {
    this.on('dblclick', handler)
  }

  /**
   * 鼠标按下事件监听
   * @param handler 事件处理函数
   */
  public onMouseDown(handler: TLabelEventHandler): void {
    this.on('mousedown', handler)
  }

  /**
   * 鼠标释放事件监听
   * @param handler 事件处理函数
   */
  public onMouseUp(handler: TLabelEventHandler): void {
    this.on('mouseup', handler)
  }

  /**
   * 鼠标离开事件监听
   * @param handler 事件处理函数
   */
  public onMouseOut(handler: TLabelEventHandler): void {
    this.on('mouseout', handler)
  }

  /**
   * 清除所有事件监听
   */
  public clearEvents(): void {
    this.eventHandlers.forEach((handlers, eventName) => {
      handlers.forEach((handler) => {
        this.label.removeEventListener(eventName, handler)
      })
    })
    this.eventHandlers.clear()
  }

  private initEvents(events: NonNullable<TLabelOptions['events']>): void {
    Object.entries(events).forEach(([eventName, handler]) => {
      if (handler) {
        this.on(eventName as TLabelEventType, handler)
      }
    })
  }

  /**
   * 获取标签的 DOM 元素
   * @returns HTMLElement | null
   */
  public getElement(): HTMLElement | null {
    return this.label.getElement()
  }

  /**
   * 设置标签样式
   * @param styles CSS 样式对象
   */
  public setStyle(styles: Partial<CSSStyleDeclaration>): void {
    const element = this.getElement()
    if (element) {
      Object.assign(element.style, styles)
    } else {
      this.pendingStyles.push(styles)
    }
  }

  /**
   * 设置标签的 CSS 类名
   * @param className CSS 类名
   * @param append 是否保留现有类名，默认为 true
   */
  public setClassName(className: string, append = true): void {
    const element = this.getElement()
    if (element) {
      this._setClassName(element, className, append)
    } else {
      this.pendingClassNames.push({ className, append })
    }
  }

  private _setClassName(element: HTMLElement, className: string, append: boolean): void {
    if (append) {
      const existingClasses = element.className.split(' ').filter((c) => c !== 'tdt-label')
      const newClasses = className.split(' ').filter(Boolean)
      element.className = ['tdt-label', ...existingClasses, ...newClasses]
        .filter((v, i, a) => a.indexOf(v) === i)
        .join(' ')
    } else {
      element.className = `tdt-label ${className}`
    }
  }

  /**
   * 清除所有待处理的样式和类名
   */
  public clearPendingStyles(): void {
    this.pendingStyles = []
    this.pendingClassNames = []
  }

  /**
   * 设置是否可移除
   * @param removeable 是否可移除
   * @param style 移除按钮样式
   */
  public setRemoveable(removeable: boolean, style?: Partial<CSSStyleDeclaration>): void {
    if (removeable === this.removeable) return

    if (removeable) {
      this.removeable = true
      this.createRemoveButton(style)
      const element = this.getElement()
      if (element && this.removeButton) {
        element.appendChild(this.removeButton)
      }
    } else {
      this.removeable = false
      if (this.removeButton) {
        this.removeButton.remove()
        this.removeButton = null
      }
    }
  }

  /**
   * 获取是否可移除
   */
  public isRemoveable(): boolean {
    return this.removeable
  }

  public getNative() {
    return this.label
  }
}
