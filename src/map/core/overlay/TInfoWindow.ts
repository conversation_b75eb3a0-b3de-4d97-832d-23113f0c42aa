import { createApp, h, markRaw, type App, type Component, type VNode } from 'vue'
import TInfoWindowContent from '../../components/TInfoWindowContent.vue'
import type { TNativeInfoWindow } from '../../types'
import { TBaseOverlay } from '../base/TBaseOverlay'
import { validateTMapAPI } from '@/map/utils/validator'
import type { TOverlay } from '../base/TOverlay'

export type TInfoWindowEventType = 'open' | 'close' | 'clickclose'

export type TInfoWindowEventHandler = (event: Event) => void

export interface TInfoWindowOptions<T = any> {
  userData?: T
  id?: string
  position?: [number, number]
  content: string | Component | VNode
  minWidth?: number
  maxWidth?: number
  maxHeight?: number
  offset?: [number, number]
  autoPan?: boolean
  closeButton?: boolean
  closeOnClick?: boolean
  contentProps?: Record<string, any>
  contentEvents?: Record<string, (ev: any) => void>
  windowEvents?: Partial<Record<TInfoWindowEventType, TInfoWindowEventHandler>>
  unstyled?: boolean
  hideCloseButton?: boolean
  hideTip?: boolean
  customStyle?: {
    className?: string
    style?: Partial<CSSStyleDeclaration>
    contentClass?: string
    contentStyle?: Partial<CSSStyleDeclaration>
  }
}

export class TInfoWindow<T = any> extends TBaseOverlay {
  private infoWindow: TNativeInfoWindow
  private eventHandlers: Map<TInfoWindowEventType, Set<TInfoWindowEventHandler>> = new Map()
  private options: TInfoWindowOptions
  private vueApp: App | null = null
  private content: string | Component | VNode
  private contentProps: Record<string, any>
  private contentEvents: Record<string, (ev: any) => void>
  private currentPosition: [number, number] | null = null

  constructor(options: TInfoWindowOptions<T>) {
    super({ id: options.id, userData: options.userData }, 'info-window')
    validateTMapAPI()

    this.options = {
      minWidth: 50,
      maxWidth: 300,
      offset: [0, 7],
      autoPan: true,
      closeButton: true,
      closeOnClick: false,
      ...options,
    }

    this.content = typeof options.content === 'string' ? options.content : markRaw(options.content)
    this.contentProps = options.contentProps ?? {}
    this.contentEvents = options.contentEvents ?? {}

    this.infoWindow = this.createInfoWindow()

    if (options.windowEvents) {
      this.initEvents(options.windowEvents)
    }

    this.renderContent()

    if (options.position) {
      this.setPosition(options.position)
    }

    this.on('close', () => this.handleClose())
  }

  protected onAdd(): void {}

  protected onRemove(): void {}

  protected onShow(): void {}

  protected onHide(): void {}

  private createInfoWindow(): TNativeInfoWindow {
    return new T.InfoWindow('', {
      minWidth: this.options.minWidth,
      maxWidth: this.options.maxWidth,
      maxHeight: this.options.maxHeight,
      offset: this.options.offset ? new T.Point(...this.options.offset) : undefined,
      autoPan: this.options.autoPan,
      closeButton: this.options.closeButton,
      closeOnClick: this.options.closeOnClick,
    })
  }

  private initEvents(
    windowEvents: Partial<Record<TInfoWindowEventType, TInfoWindowEventHandler>>,
  ): void {
    Object.entries(windowEvents).forEach(([type, handler]) => {
      if (handler) {
        this.on(type as TInfoWindowEventType, handler)
      }
    })
  }

  private clearEvents(): void {
    this.eventHandlers.forEach((handlers, type) => {
      handlers.forEach((handler) => {
        this.off(type, handler)
      })
    })
    this.eventHandlers.clear()
  }

  // Public API
  public setPosition(position: [number, number]): void {
    this.currentPosition = position
    const lngLat = new T.LngLat(position[0], position[1])
    this.infoWindow.setLngLat(lngLat)
  }

  public getPosition(): [number, number] {
    const lngLat = this.infoWindow.getLngLat()
    return [lngLat.lng, lngLat.lat]
  }

  public open(): void {
    const map = this.assertMapExists()

    if (!this.currentPosition) {
      throw new Error('InfoWindow position not set')
    }

    const lngLat = new T.LngLat(this.currentPosition[0], this.currentPosition[1])
    map.openInfoWindow(this.infoWindow, lngLat)

    this.applyCustomStyle()

    if (this.options.autoPan) {
      this.adjustPosition()
    }
  }

  public openAt(position: [number, number]): void {
    this.setPosition(position)
    this.open()
  }

  public close(): void {
    const map = this.assertMapExists()
    map.closeInfoWindow()
  }

  public destroy(): void {
    if (this.isOpen()) {
      this.close()
    }
    this.clearEvents()
  }

  public isOpen(): boolean {
    if (!this.map) return false
    return this.infoWindow.isOpen()
  }

  // Event handling
  public on(type: TInfoWindowEventType, handler: TInfoWindowEventHandler): void {
    if (!this.eventHandlers.has(type)) {
      this.eventHandlers.set(type, new Set())
    }
    this.eventHandlers.get(type)?.add(handler)
    this.infoWindow.addEventListener(type, handler)
  }

  public off(type: TInfoWindowEventType, handler: TInfoWindowEventHandler): void {
    this.eventHandlers.get(type)?.delete(handler)
    this.infoWindow.removeEventListener(type, handler)
  }

  private renderContent(): void {
    if (typeof this.content === 'string') {
      this.infoWindow.setContent(this.content)
    } else {
      const container = document.createElement('div')

      if (this.vueApp) {
        this.vueApp.unmount()
        this.vueApp = null
      }
      this.vueApp = createApp({
        render: () =>
          h(TInfoWindowContent, {
            content: this.content,
            contentProps: this.contentProps,
            contentEvents: this.contentEvents,
          }),
      })

      this.vueApp.mount(container)
      this.infoWindow.setContent(container)
    }
  }

  public setContent(
    content: string | Component | VNode,
    contentProps?: Record<string, any>,
    contentEvents?: Record<string, (ev: any) => void>,
  ): void {
    this.content = typeof content === 'string' ? content : markRaw(content)
    if (contentProps) {
      this.contentProps = contentProps
    }
    if (contentEvents) {
      this.contentEvents = contentEvents
    }
    this.renderContent()

    if (this.isOpen()) {
      this.applyCustomStyle()
    }
  }

  private adjustPosition(): void {
    if (!this.map || !this.currentPosition) return

    const nativeMap = this.map.getNativeMap()
    const mapBounds = nativeMap.getBounds()
    const position = new T.LngLat(this.currentPosition[0], this.currentPosition[1])

    if (!mapBounds.contains(position)) {
      nativeMap.panTo(position)
    }
  }

  private handleClose(): void {
    if (this.vueApp) {
      this.vueApp.unmount()
      this.vueApp = null
    }
  }

  private applyCustomStyle(): void {
    const element = this.infoWindow.getElement()
    if (!element) return

    const closeButton = element.querySelector('.tdt-infowindow-close-button')
    if (closeButton instanceof HTMLElement && this.options.hideCloseButton) {
      closeButton.style.display = 'none'
    }

    const tipContainer = element.querySelector('.tdt-infowindow-tip-container')
    if (tipContainer instanceof HTMLElement && this.options.hideTip) {
      tipContainer.style.display = 'none'
    }

    if (this.options.unstyled) {
      element.style.textAlign = 'initial'

      const contentWrapper = element.querySelector('.tdt-infowindow-content-wrapper')
      if (contentWrapper instanceof HTMLElement) {
        Object.assign(contentWrapper.style, {
          background: 'none',
          color: 'inherit',
          boxShadow: 'none',
          padding: '0',
          textAlign: 'initial',
        })
      }

      const content = element.querySelector('.tdt-infowindow-content')
      if (content instanceof HTMLElement) {
        Object.assign(content.style, {
          margin: '0',
          lineHeight: 'normal',
        })
      }
    }

    if (this.options.customStyle) {
      const { className, style, contentClass, contentStyle } = this.options.customStyle

      if (className) {
        element.classList.add(className)
      }

      if (style) {
        Object.assign(element.style, style)
      }

      const content = element.querySelector('.tdt-infowindow-content')
      if (content instanceof HTMLElement) {
        if (contentClass) {
          content.classList.add(contentClass)
        }
        if (contentStyle) {
          Object.assign(content.style, contentStyle)
        }
      }
    }
  }

  public getNative(): TOverlay {
    throw new Error('Method not implemented.')
  }
}
