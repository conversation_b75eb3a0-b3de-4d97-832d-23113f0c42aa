import type {
  TMarkerEventType,
  TMark<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TN<PERSON><PERSON><PERSON><PERSON>,
  TNativeMap,
  TNativeIcon,
} from '../../types'
import { TBaseOverlay } from '../base/TBaseOverlay'
import type { TOverlayOptions } from '../base/TOverlay'
import { TLabel, type TLabelOptions, type TLabelStyle } from './TLabel'
import { validateTMapAPI } from '../../utils/validator'

export interface TMarkerOptions<T = any> extends TOverlayOptions {
  userData?: T
  position: [number, number]
  icon?: string | null
  label?: string
  width?: number
  height?: number
  opacity?: number
  zIndexOffset?: number
  draggable?: boolean
  labelOffset?: [number, number]
  labelStyle?: Partial<CSSStyleDeclaration>
  labelRemoveable?: boolean
  labelRemoveButtonStyle?: Partial<CSSStyleDeclaration>
  onLabelRemove?: () => void
  className?: string
  markerStyle?: Partial<CSSStyleDeclaration>
  events?: Partial<{
    click: TMarkerEventHandler
    dblclick: TMarkerEventHandler
    mousedown: TMarkerEventHandler
    mouseup: TMarkerEventHandler
    mouseover: TMarkerEventHandler
    mouseout: TMarkerEventHandler
    dragstart: TMarkerEventHandler
    drag: TMarkerEventHandler
    dragend: TMarkerEventHandler
  }>
}

export class TMarker<T = any> extends TBaseOverlay<T> {
  private marker!: TNativeMarker
  private label: TLabel | null = null
  private markerElement: HTMLElement | null = null
  private elementInitialized = false
  private observer: MutationObserver | null = null
  private draggable: boolean
  private pendingClassName?: string
  private pendingStyle?: Partial<CSSStyleDeclaration>
  private dragHandlers: {
    start: ((event: any) => void) | null
    drag: ((event: any) => void) | null
    end: ((event: any) => void) | null
  } = {
    start: null,
    drag: null,
    end: null,
  }
  private eventHandlers: Map<TMarkerEventType, Set<TMarkerEventHandler>> = new Map()
  private onLabelRemoveCallback?: () => void

  private options?: TMarkerOptions<T>

  constructor(options: TMarkerOptions<T>) {
    super({ id: options.id, userData: options.userData }, 'marker')

    validateTMapAPI()

    this.options = options
    this.draggable = options.draggable || false
    this.createMarker(options)

    if (options.className) {
      this.pendingClassName = options.className
    }
    if (options.markerStyle) {
      this.pendingStyle = options.markerStyle
    }

    if (options.events) {
      this.initEvents(options.events)
    }
  }

  protected onAdd(map: TNativeMap): void {
    map.addOverLay(this.marker)
    this.initializeElement()
    if (this.draggable) {
      this.enableDragging()
    }
    if (this.label && this.map) {
      this.label.addTo(this.map)
    }
  }

  protected onRemove(): void {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }

    this.removeDragEvents()

    if (this.label) {
      this.label.remove()
      this.label = null
    }

    const map = this.assertMapExists()
    map.removeOverLay(this.marker)

    this.markerElement = null
    this.elementInitialized = false
    this.pendingStyle = {}
  }

  protected onShow(): void {
    this.marker.show()
    if (this.label) {
      this.label.show()
    }
  }

  protected onHide(): void {
    this.marker.hide()
    if (this.label) {
      this.label.hide()
    }
  }

  public setPosition(position: [number, number]): void {
    this.assertAddedToMap('set position')
    this.marker.setLngLat(new T.LngLat(position[0], position[1]))
    if (this.label) {
      this.label.setPosition(position)
    }
  }

  public getPosition(): [number, number] {
    const lngLat = this.marker.getLngLat()
    return [lngLat.lng, lngLat.lat]
  }

  // Style Management
  public setIcon(icon: string | null, width?: number, height?: number): void {
    const iconObj: TNativeIcon =
      icon === null
        ? new T.Icon({
            iconUrl:
              'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
            iconSize: new T.Point(1, 1),
            iconAnchor: new T.Point(0, 0),
          })
        : new T.Icon({
            iconUrl: icon,
            ...(width && height ? { iconSize: new T.Point(width, height) } : {}),
          })
    this.marker.setIcon(iconObj)
  }

  public setStyle(style: Partial<CSSStyleDeclaration>): void {
    if (!this.elementInitialized) {
      this.pendingStyle = { ...this.pendingStyle, ...style }
      return
    }

    if (!this.markerElement) return

    Object.assign(this.markerElement.style, style)
  }

  public setClassName(className: string): void {
    if (!this.elementInitialized) {
      this.pendingClassName = className
      return
    }

    if (!this.markerElement) return

    this.markerElement.className = className
  }

  // Drag Control
  public enableDragging(): void {
    this.assertAddedToMap('enable dragging')
    this.draggable = true
    this.marker.enableDragging()
    this.setupDragEvents()
  }

  public disableDragging(): void {
    this.assertAddedToMap('disable dragging')
    this.draggable = false
    this.marker.disableDragging()
    this.removeDragEvents()
  }

  // Event Handling
  public on(eventName: TMarkerEventType, handler: TMarkerEventHandler): void {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, new Set())
    }
    this.eventHandlers.get(eventName)!.add(handler)
    this.marker.addEventListener(eventName, handler)
  }

  public off(eventName: TMarkerEventType, handler: TMarkerEventHandler): void {
    const handlers = this.eventHandlers.get(eventName)
    if (handlers) {
      handlers.delete(handler)
      this.marker.removeEventListener(eventName, handler)
    }
  }

  // Getters
  public getElement(): HTMLElement | null {
    return this.markerElement
  }

  public getNative() {
    return this.marker
  }

  // Label 相关方法
  public getLabel(): TLabel | null {
    return this.label
  }

  public setLabel(
    label: string | null,
    labelOptions: Partial<
      TLabelOptions & {
        removeable?: boolean
        removeButtonStyle?: Partial<CSSStyleDeclaration>
        onRemove?: () => void
      }
    > = {},
  ): void {
    if (!label) {
      if (this.label) {
        this.label.remove()
        this.label = null
      }
      return
    }

    if (this.label) {
      this.label.setText(label)
      if (labelOptions.style) {
        this.label.setStyle(labelOptions.style)
      }
      if (labelOptions.offset) {
        this.label.setOffset(labelOptions.offset)
      }
      if (typeof labelOptions.removeable !== 'undefined') {
        this.label.setRemoveable(labelOptions.removeable, labelOptions.removeButtonStyle)
      }
    } else {
      const position = this.getPosition()
      this.label = new TLabel({
        position,
        text: label,
        offset: labelOptions.offset || [0, 0],
        style: labelOptions.style,
        removeable: labelOptions.removeable,
        removeButtonStyle: labelOptions.removeButtonStyle,
        onRemove: () => {
          labelOptions.onRemove?.()
          this.label = null
        },
      })

      if (this.hasAddedToMap()) {
        this.label.addTo(this.map!)
      }
    }
  }

  /**
   * 设置标签样式
   * @param style 标签样式
   */
  public setLabelStyle(style: Partial<CSSStyleDeclaration>): void {
    if (this.label) {
      this.label.setStyle(style)
    }
  }

  /**
   * 获取标签样式
   */
  public getLabelStyle(): Partial<TLabelStyle> | null {
    return this.label?.getStyle() || null
  }

  /**
   * 设置标注的透明度
   * @param opacity 透明度值，范围 0-1
   */
  public setOpacity(opacity: number): void {
    this.marker.setOpacity(opacity)
    if (this.markerElement) {
      this.markerElement.style.opacity = opacity.toString()
    }
  }

  /**
   * 获取标注的透明度
   */
  public getOpacity(): number {
    return this.marker.getOpacity()
  }

  // Event shortcuts
  public onClick(handler: TMarkerEventHandler): void {
    this.on('click', handler)
  }

  public onDblClick(handler: TMarkerEventHandler): void {
    this.on('dblclick', handler)
  }

  public onMouseOver(handler: TMarkerEventHandler): void {
    this.on('mouseover', handler)
  }

  public onMouseOut(handler: TMarkerEventHandler): void {
    this.on('mouseout', handler)
  }

  public onDragStart(handler: TMarkerEventHandler): void {
    this.on('dragstart', handler)
  }

  public onDrag(handler: TMarkerEventHandler): void {
    this.on('drag', handler)
  }

  public onDragEnd(handler: TMarkerEventHandler): void {
    this.on('dragend', handler)
  }

  // 清除所有事件监听
  public clearEvents(): void {
    const events: TMarkerEventType[] = [
      'click',
      'dblclick',
      'mousedown',
      'mouseup',
      'mouseover',
      'mouseout',
      'dragstart',
      'drag',
      'dragend',
    ]

    events.forEach((eventName) => {
      const handlers = this.eventHandlers.get(eventName)
      if (handlers) {
        handlers.forEach((handler) => {
          this.marker.removeEventListener(eventName, handler)
        })
        handlers.clear()
      }
    })

    this.eventHandlers.clear()
  }

  // =============== Private Methods ===============
  private createMarker(options: TMarkerOptions): void {
    const {
      position,
      icon,
      label = '',
      zIndexOffset = 0,
      opacity = 1.0,
      width,
      height,
      labelOffset,
      labelStyle,
      labelRemoveable,
      labelRemoveButtonStyle,
      markerStyle,
    } = options

    const lngLat = new T.LngLat(position[0], position[1])
    this.marker = new T.Marker(lngLat)
    this.setIcon(icon ?? null, width, height)
    this.marker.setZIndexOffset(zIndexOffset)
    this.marker.setOpacity(opacity)

    if (markerStyle) {
      this.pendingStyle = markerStyle
    }

    this.onLabelRemoveCallback = options.onLabelRemove

    if (label) {
      this.label = new TLabel({
        position,
        text: label,
        offset: labelOffset || [0, 0],
        style: labelStyle,
        removeable: labelRemoveable,
        removeButtonStyle: labelRemoveButtonStyle,
        onRemove: () => {
          this.onLabelRemoveCallback?.()
          this.label = null
        },
      })
    }
  }

  private initializeElement(): void {
    const element = this.marker.getElement()
    if (element) {
      this.handleMarkerElementFound(element)
    } else {
      this.setupElementObserver()
    }
  }

  private setupElementObserver(): void {
    if (this.observer) {
      this.observer.disconnect()
    }

    this.observer = new MutationObserver(() => {
      const element = this.marker.getElement()
      if (element) {
        this.handleMarkerElementFound(element)
      }
    })

    const container = this.assertMapExists().getContainer()
    this.observer.observe(container, {
      childList: true,
      subtree: true,
    })
  }

  private handleMarkerElementFound(element: HTMLElement): void {
    this.markerElement = element
    this.elementInitialized = true

    if (this.pendingStyle) {
      Object.assign(element.style, this.pendingStyle)
      this.pendingStyle = undefined
    }

    if (this.pendingClassName) {
      element.className = this.pendingClassName
      this.pendingClassName = undefined
    }

    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
  }

  private setupDragEvents(): void {
    this.dragHandlers = {
      start: () => {
        if (this.label) {
          this.label.setOpacity(0.5)
        }
      },
      drag: (event: any) => {
        if (this.label) {
          const position: [number, number] = [event.lnglat.lng, event.lnglat.lat]
          this.label.setPosition(position)
        }
      },
      end: (event: any) => {
        if (this.label) {
          const position: [number, number] = [event.lnglat.lng, event.lnglat.lat]
          this.label.setPosition(position)
          this.label.setOpacity(1.0)
        }
      },
    }

    const eventMap = {
      start: 'dragstart',
      drag: 'drag',
      end: 'dragend',
    }

    Object.entries(this.dragHandlers).forEach(([event, handler]) => {
      if (handler) {
        this.marker.addEventListener(
          eventMap[event as keyof typeof eventMap] as TMarkerEventType,
          handler,
        )
      }
    })
  }

  private removeDragEvents(): void {
    const eventMap = {
      start: 'dragstart',
      drag: 'drag',
      end: 'dragend',
    }

    Object.entries(this.dragHandlers).forEach(([event, handler]) => {
      if (handler) {
        this.marker.removeEventListener(
          eventMap[event as keyof typeof eventMap] as TMarkerEventType,
          handler,
        )
      }
    })
    this.dragHandlers = { start: null, drag: null, end: null }
  }

  private initEvents(events: TMarkerOptions['events']): void {
    if (!events) return

    Object.entries(events).forEach(([eventName, handler]) => {
      if (handler) {
        this.on(eventName as TMarkerEventType, handler)
      }
    })
  }

  /**
   * 设置标签是否可删除
   * @param removeable 是否可删除
   * @param style 删除按钮样式
   */
  public setLabelRemoveable(removeable: boolean, style?: Partial<CSSStyleDeclaration>): void {
    if (this.label) {
      this.label.setRemoveable(removeable, style)
    }
  }

  /**
   * 获取标签是否可删除
   */
  public isLabelRemoveable(): boolean {
    return this.label?.isRemoveable() ?? false
  }

  /**
   * 获取标记的尺寸
   * @returns 标记的宽度和高度 [width, height]
   */
  public getSize(): [number, number] {
    // 如果元素已初始化，尝试从DOM元素获取实际尺寸
    if (this.elementInitialized && this.markerElement) {
      return [this.markerElement.offsetWidth, this.markerElement.offsetHeight]
    }

    // 否则从选项中获取尺寸，或使用默认值
    const width = this.options?.width || 24
    const height = this.options?.height || 36

    return [width, height]
  }
}
