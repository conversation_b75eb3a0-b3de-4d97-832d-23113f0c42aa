import { TBaseOverlay } from '../base/TBaseOverlay'
import { TMarker, type TMarkerOptions } from './TMarker'
import { TSvgOverlay, type TSvgOverlayOptions } from './TSvgOverlay'
import type { TNativeMap } from '../../types'
import { validateTMapAPI } from '../../utils/validator'
import { createApp, h, type Component, type VNode } from 'vue'
import TOverlayContent from '../../components/TOverlayContent.vue'
import { isVueComponent, isVNode } from '../../utils/vue'

export interface TAnnotationMarkerOptions<T = any> {
  id?: string
  userData?: T
  position: [number, number]
  zIndexOffset?: number

  markerType?: 'marker' | 'svg'
  markerOptions: Omit<TMarkerOptions<T>, 'position'> | Omit<TSvgOverlayOptions<T>, 'position'>

  lineOptions: {
    length?: number
    angle?: number
    style?: {
      color?: string
      width?: number
      dashed?: boolean
    }
  }

  panelOptions: {
    content: string | HTMLElement | Component | VNode
    style?: Partial<CSSStyleDeclaration>
    offset?: [number, number]
    unstyled?: boolean
    contentProps?: Record<string, any>
    contentEvents?: Record<string, VoidFunction>
    anchorPosition?: 'auto' | 'free' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  }
}

const DEFAULT_LINE_OPTIONS = {
  length: 50,
  angle: 45,
  style: {
    color: '#EFE32A',
    width: 2,
    dashed: true,
  },
}

const DEFAULT_PANEL_STYLE = {
  backgroundColor: 'white',
  borderRadius: '4px',
  boxShadow: '0 2px 6px rgba(0,0,0,0.2)',
  padding: '8px',
  fontSize: '12px',
  width: '120px',
  height: 'auto',
}

export class TAnnotationMarker<T = any> extends TBaseOverlay<T> {
  private options: TAnnotationMarkerOptions<T>
  private marker: TMarker<T> | TSvgOverlay<T> | null = null
  private lineElement: HTMLElement | null = null
  private panelElement: HTMLElement | null = null
  private _visible = true
  private lineEndPoint: { x: number; y: number } = { x: 0, y: 0 }
  private nativeOverlay: any = null
  private vueApp: any = null
  private zIndexOffset: number = 0

  constructor(options: TAnnotationMarkerOptions<T>) {
    super({ id: options.id, userData: options.userData }, 'annotation-marker')

    validateTMapAPI()

    this.zIndexOffset = options.zIndexOffset ?? 0

    this.options = {
      ...options,
      markerType: options.markerType ?? 'svg',
      lineOptions: {
        ...DEFAULT_LINE_OPTIONS,
        ...options.lineOptions,
        style: {
          ...DEFAULT_LINE_OPTIONS.style,
          ...options.lineOptions?.style,
        },
      },
      panelOptions: {
        offset: [0, 0],
        ...options.panelOptions,
        style: options.panelOptions.unstyled
          ? {
              whiteSpace: 'nowrap',
            }
          : {
              ...DEFAULT_PANEL_STYLE,
              ...options.panelOptions.style,
            },
      },
    }

    this.createMarker()

    this.createLineElement()
    this.createPanelElement()

    this.createNativeOverlay()
  }

  /**
   * 创建原生覆盖物
   */
  private createNativeOverlay(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this

    const CustomOverlay = T.Overlay.extend({
      initialize: function (options: any) {
        this.position = options.position
        this.zIndexOffset = options.zIndexOffset
      },

      onAdd: function (map: TNativeMap) {
        this.map = map

        if (self.marker && self.map) {
          self.marker.addTo(self.map!)
        }
        map.getPanes().overlayPane.appendChild(self.lineElement!)
        map.getPanes().overlayPane.appendChild(self.panelElement!)

        if (self.lineElement) {
          self.lineElement.style.zIndex = String(this.zIndexOffset)
        }
        if (self.panelElement) {
          self.panelElement.style.zIndex = String(this.zIndexOffset)
        }

        this.update()

        return self.panelElement!
      },

      onRemove: function () {
        if (self.marker) {
          self.marker.remove()
        }

        if (self.lineElement?.parentNode) {
          self.lineElement.parentNode.removeChild(self.lineElement)
        }

        if (self.panelElement?.parentNode) {
          self.panelElement.parentNode.removeChild(self.panelElement)
        }

        this.map = null
      },

      update: function () {
        if (!this.map) return

        if (self.marker) {
          const position = self.options.position
          self.marker.setPosition(position)
        }

        self.updatePositions()
      },
    })

    this.nativeOverlay = new CustomOverlay({
      position: new T.LngLat(this.options.position[0], this.options.position[1]),
      zIndexOffset: this.zIndexOffset,
    })
  }

  protected onAdd(map: TNativeMap): void {
    map.addOverLay(this.nativeOverlay)

    this.setupMapEventListeners()
  }

  protected onRemove(): void {
    const map = this.assertMapExists()

    this.removeMapEventListeners()

    map.removeOverLay(this.nativeOverlay)
  }

  protected onShow(): void {
    this._visible = true

    if (this.nativeOverlay) {
      this.nativeOverlay.show()
    }

    if (this.marker) {
      this.marker.show()
    }

    if (this.lineElement) {
      this.lineElement.style.display = 'block'
    }

    if (this.panelElement) {
      this.panelElement.style.display = 'block'
    }
  }

  protected onHide(): void {
    this._visible = false

    if (this.nativeOverlay) {
      this.nativeOverlay.hide()
    }

    if (this.marker) {
      this.marker.hide()
    }

    if (this.lineElement) {
      this.lineElement.style.display = 'none'
    }

    if (this.panelElement) {
      this.panelElement.style.display = 'none'
    }
  }

  private createMarker(): void {
    const { position, markerType, markerOptions } = this.options

    if (markerType === 'marker') {
      this.marker = new TMarker({
        ...(markerOptions as Omit<TMarkerOptions<T>, 'position'>),
        zIndexOffset: this.zIndexOffset + 1,
        position,
      })
    } else {
      this.marker = new TSvgOverlay({
        ...(markerOptions as Omit<TSvgOverlayOptions<T>, 'position'>),
        zIndexOffset: this.zIndexOffset + 1,
        position,
      })
    }
  }

  private createLineElement(): void {
    this.lineElement = document.createElement('div')
    this.lineElement.className = 't-annotation-line'

    const { style } = this.options.lineOptions

    Object.assign(this.lineElement.style, {
      position: 'absolute',
      pointerEvents: 'none',
      transformOrigin: '0 0',
      borderTop: `${style?.width || 1}px ${style?.dashed ? 'dashed' : 'solid'} ${style?.color || '#1890ff'}`,
      zIndex: String(this.zIndexOffset),
    })
  }

  private createPanelElement(): void {
    const { content, style } = this.options.panelOptions

    this.panelElement = document.createElement('div')
    this.panelElement.className = 't-annotation-panel'

    Object.assign(this.panelElement.style, {
      position: 'absolute',
      zIndex: String(this.zIndexOffset),
      ...style,
    })

    this.setPanelContent(content)
  }

  /**
   * 更新线条和面板位置
   */
  private updatePositions(): void {
    if (!this.getMap() || !this.marker || !this.lineElement || !this.panelElement) return

    const nativeMap = this.getMap()!.getNativeMap()
    const markerPosition = this.marker.getPosition()
    const pixelPos = nativeMap.lngLatToLayerPoint(
      new T.LngLat(markerPosition[0], markerPosition[1]),
    )

    const startX = pixelPos.x
    const startY = pixelPos.y

    const { length = 50, angle = 45 } = this.options.lineOptions

    const radians = (angle * Math.PI) / 180
    const endX = startX + length * Math.cos(radians - Math.PI / 2)
    const endY = startY + length * Math.sin(radians - Math.PI / 2)

    this.lineEndPoint = { x: endX, y: endY }

    const lineLength = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2))
    const lineAngle = (Math.atan2(endY - startY, endX - startX) * 180) / Math.PI

    this.lineElement.style.width = `${lineLength}px`
    this.lineElement.style.left = `${startX}px`
    this.lineElement.style.top = `${startY}px`
    this.lineElement.style.transform = `rotate(${lineAngle}deg)`

    const panelWidth = this.panelElement.offsetWidth
    const panelHeight = this.panelElement.offsetHeight

    const { anchorPosition = 'free', offset = [0, 0] } = this.options.panelOptions

    let panelX = endX
    let panelY = endY
    let transformOrigin = ''
    let transform = ''

    if (anchorPosition === 'auto') {
      const normalizedAngle = ((angle % 360) + 360) % 360

      const isSpecialAngle = (target: number) =>
        Math.abs(normalizedAngle - target) <= 5 || Math.abs(normalizedAngle - target - 360) <= 5

      if (isSpecialAngle(0)) {
        panelX = endX - panelWidth / 2
        panelY = endY - panelHeight
        transformOrigin = '50% 100%'
      } else if (isSpecialAngle(90)) {
        panelX = endX
        panelY = endY - panelHeight / 2
        transformOrigin = '0% 50%'
      } else if (isSpecialAngle(180)) {
        panelX = endX - panelWidth / 2
        panelY = endY
        transformOrigin = '50% 0%'
      } else if (isSpecialAngle(270)) {
        panelX = endX - panelWidth
        panelY = endY - panelHeight / 2
        transformOrigin = '100% 50%'
      } else if (normalizedAngle > 0 && normalizedAngle < 90) {
        panelX = endX
        panelY = endY - panelHeight
        transformOrigin = '0% 100%'
      } else if (normalizedAngle > 90 && normalizedAngle < 180) {
        panelX = endX
        panelY = endY
        transformOrigin = '0% 0%'
      } else if (normalizedAngle > 180 && normalizedAngle < 270) {
        panelX = endX - panelWidth
        panelY = endY
        transformOrigin = '100% 0%'
      } else {
        panelX = endX - panelWidth
        panelY = endY - panelHeight
        transformOrigin = '100% 100%'
      }
    } else if (anchorPosition === 'top-left') {
      panelX = endX
      panelY = endY
      transformOrigin = '0% 0%'
    } else if (anchorPosition === 'top-right') {
      panelX = endX - panelWidth
      panelY = endY
      transformOrigin = '100% 0%'
    } else if (anchorPosition === 'bottom-left') {
      panelX = endX
      panelY = endY - panelHeight
      transformOrigin = '0% 100%'
    } else if (anchorPosition === 'bottom-right') {
      panelX = endX - panelWidth
      panelY = endY - panelHeight
      transformOrigin = '100% 100%'
    } else {
      panelX = endX + offset[0]
      panelY = endY + offset[1]
    }

    if (anchorPosition !== 'free') {
      transform = `translate(${offset[0]}px, ${offset[1]}px)`
    }

    this.panelElement.style.left = `${panelX}px`
    this.panelElement.style.top = `${panelY}px`

    if (transformOrigin) {
      this.panelElement.style.transformOrigin = transformOrigin
    }

    this.panelElement.style.transform = transform
  }

  private setupMapEventListeners(): void {
    const map = this.getMap()
    if (!map) return

    const nativeMap = map.getNativeMap()

    nativeMap.addEventListener('moveend', this.handleMapEvent)
    nativeMap.addEventListener('zoomend', this.handleMapEvent)
  }

  private handleMapEvent = (): void => {
    this.updatePositions()
  }

  private removeMapEventListeners(): void {
    const map = this.getMap()
    if (!map) return

    const nativeMap = map.getNativeMap()

    nativeMap.removeEventListener('moveend', this.handleMapEvent)
    nativeMap.removeEventListener('zoomend', this.handleMapEvent)
  }

  public setPosition(position: [number, number]): void {
    this.options.position = position

    if (this.nativeOverlay) {
      this.nativeOverlay.setPosition(new T.LngLat(position[0], position[1]))
    }

    if (this.marker) {
      this.marker.setPosition(position)
    }

    this.updatePositions()
  }

  public getPosition(): [number, number] {
    return this.options.position
  }

  public setVisible(visible: boolean): void {
    if (visible) {
      this.show()
    } else {
      this.hide()
    }
  }

  public isVisible(): boolean {
    return this._visible
  }

  public setPanelContent(content: string | HTMLElement | Component | VNode): void {
    if (!this.panelElement) return

    while (this.panelElement.firstChild) {
      this.panelElement.removeChild(this.panelElement.firstChild)
    }

    if (this.vueApp) {
      this.vueApp.unmount()
      this.vueApp = null
    }

    if (typeof content === 'string') {
      this.panelElement.innerHTML = content
    } else if (content instanceof HTMLElement) {
      this.panelElement.appendChild(content)
    } else if (isVueComponent(content) || isVNode(content)) {
      this.vueApp = createApp({
        render: () =>
          h(TOverlayContent, {
            content,
            contentProps: this.options.panelOptions.contentProps || {},
            contentEvents: this.options.panelOptions.contentEvents || {},
          }),
      })
      this.vueApp.mount(this.panelElement)
    }
  }

  public setPanelStyle(style: Partial<CSSStyleDeclaration>): void {
    if (this.panelElement) {
      Object.assign(this.panelElement.style, style)

      this.options.panelOptions.style = {
        ...this.options.panelOptions.style,
        ...style,
      }
    }
  }

  public setPanelProps(props: Record<string, any>): void {
    if (!this.options.panelOptions.contentProps) {
      this.options.panelOptions.contentProps = {}
    }

    Object.assign(this.options.panelOptions.contentProps, props)

    const content = this.options.panelOptions.content
    if (isVueComponent(content) || isVNode(content)) {
      this.setPanelContent(content)
    }
  }

  public setLineStyle(style: Partial<TAnnotationMarkerOptions<T>['lineOptions']['style']>): void {
    if (!this.lineElement) return

    const { color, width, dashed } = style!

    if (color) {
      this.lineElement.style.borderTopColor = color
    }

    if (width) {
      this.lineElement.style.borderTopWidth = `${width}px`
    }

    if (dashed !== undefined) {
      this.lineElement.style.borderTopStyle = dashed ? 'dashed' : 'solid'
    }
  }

  public setLineGeometry(options: { length?: number; angle?: number }): void {
    const { length, angle } = options

    if (length !== undefined) {
      this.options.lineOptions.length = length
    }

    if (angle !== undefined) {
      this.options.lineOptions.angle = angle
    }

    this.updatePositions()
  }

  public getMarker(): TMarker<T> | TSvgOverlay<T> | null {
    return this.marker
  }

  /**
   * 获取面板元素
   */
  public getPanelElement(): HTMLElement | null {
    return this.panelElement
  }

  /**
   * 获取线条元素
   */
  public getLineElement(): HTMLElement | null {
    return this.lineElement
  }

  /**
   * 获取原生覆盖物对象
   * @returns 原生覆盖物对象
   */
  public getNative() {
    return this.nativeOverlay
  }

  /**
   * 设置 z-index 偏移量
   * @param offset z-index 偏移量
   */
  public setZIndexOffset(offset: number): void {
    this.zIndexOffset = offset

    if (this.lineElement) {
      this.lineElement.style.zIndex = String(offset + 999)
    }

    if (this.panelElement) {
      this.panelElement.style.zIndex = String(offset + 1000)
    }

    if (this.marker instanceof TSvgOverlay) {
      this.marker.setZIndexOffset(offset)
    }
  }

  /**
   * 获取 z-index 偏移量
   * @returns z-index 偏移量
   */
  public getZIndexOffset(): number {
    return this.zIndexOffset
  }

  /**
   * 设置面板定位模式
   * @param anchorPosition 定位模式
   * @param offset 额外偏移量 [x, y]
   */
  public setPanelAnchor(
    anchorPosition: TAnnotationMarkerOptions['panelOptions']['anchorPosition'],
    offset?: [number, number],
  ): void {
    if (!this.options.panelOptions) {
      this.options.panelOptions = {
        content: '',
        style: {},
        offset: [0, 0],
        contentProps: {},
        contentEvents: {},
      }
    }

    this.options.panelOptions.anchorPosition = anchorPosition

    if (offset !== undefined) {
      this.options.panelOptions.offset = offset
    }

    this.updatePositions()
  }

  /**
   * 获取面板定位模式
   * @returns 当前的定位模式和偏移量
   */
  public getPanelAnchor(): {
    anchorPosition: TAnnotationMarkerOptions['panelOptions']['anchorPosition']
    offset: [number, number]
  } {
    return {
      anchorPosition: this.options.panelOptions.anchorPosition || 'free',
      offset: this.options.panelOptions.offset || [0, 0],
    }
  }
}
