import type { TMapEvent, TNativeMap, TNativePolygon } from '../../types'
import { TLabel, type TLabelOptions } from './TLabel'
import { TBaseOverlay } from '../base/TBaseOverlay'
import { validateTMapAPI } from '../../utils/validator'

type LngLatTuple = [number, number]
type LngLatPath = LngLatTuple[]
type LngLatPaths = LngLatPath[]

export interface TPolygonOptions<T = any> extends T.PolygonOptions {
  id?: string
  userData?: T
  path: LngLatPath | LngLatPaths
  label?: string
  labelOffset?: [number, number]
  labelStyle?: Partial<CSSStyleDeclaration>
  events?: Partial<{
    click: (evt: any) => void
    dblclick: (evt: any) => void
    mousedown: (evt: any) => void
    mouseup: (evt: any) => void
    mouseover: (evt: any) => void
    mouseout: (evt: any) => void
    remove: (evt: any) => void
  }>
}

export interface TPolygonEditEvent {
  type: 'editstart' | 'editing' | 'editend'
  target: T.Polygon
  lnglat: T.LngLat
  points: LngLatPaths
}

export interface TPolygonStyle {
  color?: string
  weight?: number
  opacity?: number
  fillColor?: string
  fillOpacity?: number
  lineStyle?: 'solid' | 'dashed'
}

export class TPolygon<T = any> extends TBaseOverlay<T> {
  private polygon: TNativePolygon
  private label: TLabel | null = null
  private options: TPolygonOptions<T>
  private isEditing = false
  private isMouseDown = false
  private editEventHandlers = new Map<string, ((e: TPolygonEditEvent) => void)[]>()
  private lastPath: LngLatPaths = []
  private lastLngLat: T.LngLat | null = null
  private isRealEditing = false
  private initialPath: LngLatPaths = []

  constructor(options: TPolygonOptions<T>) {
    super({ id: options.id, userData: options.userData }, 'polygon')

    validateTMapAPI()

    this.options = options

    const lngLatsArray = Array.isArray(options.path[0]?.[0])
      ? (options.path as LngLatPaths).map((path) =>
          path.map(([lng, lat]) => new T.LngLat(lng, lat)),
        )
      : [(options.path as LngLatPath).map(([lng, lat]) => new T.LngLat(lng, lat))]

    this.polygon = new T.Polygon(lngLatsArray, {
      color: options.color ?? '#0000FF',
      weight: options.weight ?? 3,
      opacity: options.opacity ?? 0.5,
      fillColor: options.fillColor ?? '#0000FF',
      fillOpacity: options.fillOpacity ?? 0.2,
      lineStyle: options.lineStyle ?? 'solid',
    })

    if (options.events) {
      Object.entries(options.events).forEach(([eventName, handler]) => {
        if (handler) {
          this.on(eventName as T.PolygonEventType, handler)
        }
      })
    }

    if (options.label) {
      this.createLabel()
    }
  }

  protected onAdd(map: TNativeMap): void {
    map.addOverLay(this.polygon)
    if (this.label && this.map) {
      this.label.addTo(this.map)
    }
  }

  protected onRemove(): void {
    const map = this.assertMapExists()
    map.removeOverLay(this.polygon)
    if (this.label) {
      this.label.remove()
    }
    this.disableEdit()
  }

  protected onShow(): void {
    this.polygon.show()
    if (this.label) {
      this.label.show()
    }
  }

  protected onHide(): void {
    this.polygon.hide()
    if (this.label) {
      this.label.hide()
    }
  }

  private calculateBounds(
    paths: LngLatPaths = this.getPath(),
  ): [[number, number], [number, number]] {
    let minLng = Infinity
    let maxLng = -Infinity
    let minLat = Infinity
    let maxLat = -Infinity

    paths.forEach((path) => {
      path.forEach(([lng, lat]) => {
        minLng = Math.min(minLng, lng)
        maxLng = Math.max(maxLng, lng)
        minLat = Math.min(minLat, lat)
        maxLat = Math.max(maxLat, lat)
      })
    })

    return [
      [minLng, minLat],
      [maxLng, maxLat],
    ]
  }

  private createLabel(): void {
    if (!this.options.label) return
    const [[minLng, minLat], [maxLng, maxLat]] = this.calculateBounds()
    const position: [number, number] = [(minLng + maxLng) / 2, (minLat + maxLat) / 2]

    this.label = new TLabel({
      position,
      text: this.options.label,
      offset: this.options.labelOffset ?? [0, 0],
      style: this.options.labelStyle,
    })

    if (this.map) {
      this.label.addTo(this.map)
    }
  }

  public setLabel(label: string | null, options?: Partial<TLabelOptions>): void {
    // 移除现有标注
    if (this.label) {
      this.label.remove()
      this.label = null
    }

    // 更新选项
    this.options.label = label ?? undefined
    if (options?.offset) this.options.labelOffset = options.offset
    if (options?.style) this.options.labelStyle = options.style

    // 如果有文本，创建新标注
    if (label) {
      this.createLabel()
    }
  }

  public getLabel(): string | null {
    return this.label?.getText() ?? null
  }

  public setLabelStyle(style: Partial<CSSStyleDeclaration>): void {
    if (this.label) {
      this.label.setStyle(style)
      this.options.labelStyle = { ...this.options.labelStyle, ...style }
    }
  }

  public setLabelOffset(offset: [number, number]): void {
    if (this.label) {
      this.label.setOffset(offset)
      this.options.labelOffset = offset
    }
  }

  public setPath(points: LngLatPath | LngLatPaths): void {
    const lngLatsArray = Array.isArray(points[0]?.[0])
      ? (points as LngLatPaths).map((path) => path.map(([lng, lat]) => new T.LngLat(lng, lat)))
      : [(points as LngLatPath).map(([lng, lat]) => new T.LngLat(lng, lat))]

    this.polygon.setLngLats(lngLatsArray)

    // 更新标签位置
    if (this.label) {
      const [[minLng, minLat], [maxLng, maxLat]] = this.calculateBounds(this.getPath())
      this.label.setPosition([(minLng + maxLng) / 2, (minLat + maxLat) / 2])
    }
  }

  public getPath(): LngLatPaths {
    return this.polygon
      .getLngLats()
      .map((path) => path.map((lngLat) => [lngLat.lng, lngLat.lat] as LngLatTuple))
  }

  public setColor(color: string): void {
    this.polygon.setColor(color)
  }

  public getColor(): string {
    return this.polygon.getColor()
  }

  public setWeight(weight: number): void {
    this.polygon.setWeight(weight)
  }

  public getWeight(): number {
    return this.polygon.getWeight()
  }

  public setOpacity(opacity: number): void {
    this.polygon.setOpacity(opacity)
  }

  public getOpacity(): number {
    return this.polygon.getOpacity()
  }

  public setLineStyle(style: 'solid' | 'dashed'): void {
    this.polygon.setLineStyle(style)
  }

  public getLineStyle(): 'solid' | 'dashed' {
    return this.polygon.getLineStyle() as 'solid' | 'dashed'
  }

  public setFillColor(color: string): void {
    this.polygon.setFillColor(color)
  }

  public getFillColor(): string {
    return this.polygon.getFillColor()
  }

  public setFillOpacity(opacity: number): void {
    this.polygon.setFillOpacity(opacity)
  }

  public getFillOpacity(): number {
    return this.polygon.getFillOpacity()
  }

  public getBounds(): T.LngLatBounds {
    return this.polygon.getBounds()
  }

  public on(event: T.PolygonEventType, handler: (e: T.PolygonEvent) => void): void {
    this.polygon.addEventListener(event, handler)
  }

  public off(event: T.PolygonEventType, handler: (e: T.PolygonEvent) => void): void {
    this.polygon.removeEventListener(event, handler)
  }

  public show(): void {
    this.polygon.show()
  }

  public hide(): void {
    this.polygon.hide()
  }

  public enableEdit(): void {
    const map = this.assertMapExists()

    this.polygon.enableEdit()
    this.isEditing = true
    this.lastPath = this.getPath()

    map.addEventListener('mousedown', this.handleMapMouseDown)
    map.addEventListener('mousemove', this.handleMapMouseMove)
    map.addEventListener('mouseup', this.handleMapMouseUp)
  }

  public disableEdit(): void {
    const map = this.assertMapExists()

    this.polygon.disableEdit()
    this.isEditing = false
    this.isMouseDown = false
    this.isRealEditing = false

    map.removeEventListener('mousedown', this.handleMapMouseDown)
    map.removeEventListener('mousemove', this.handleMapMouseMove)
    map.removeEventListener('mouseup', this.handleMapMouseUp)
  }

  public isEditable(): boolean {
    return this.polygon.isEditable()
  }

  private handleMapMouseDown = (e: TMapEvent) => {
    if (!this.isEditing) return

    this.isMouseDown = true
    this.initialPath = this.getPath()
    this.lastPath = this.initialPath
    this.lastLngLat = e.lnglat
  }

  private handleMapMouseMove = (e: TMapEvent) => {
    if (!this.isEditing || !this.isMouseDown) return

    const currentPath = this.getPath()
    const pathChanged = JSON.stringify(currentPath) !== JSON.stringify(this.lastPath)
    if (pathChanged) {
      if (!this.isRealEditing) {
        this.isRealEditing = true
        const startEvent: TPolygonEditEvent = {
          type: 'editstart',
          target: this.polygon,
          lnglat: this.lastLngLat!,
          points: this.initialPath,
        }
        this.dispatchEditEvent('editstart', startEvent)
      }

      // 使用当前路径计算边界并更新标签位置
      if (this.label) {
        const [[minLng, minLat], [maxLng, maxLat]] = this.calculateBounds(currentPath)
        this.label.setPosition([(minLng + maxLng) / 2, (minLat + maxLat) / 2])
      }

      const editingEvent: TPolygonEditEvent = {
        type: 'editing',
        target: this.polygon,
        lnglat: e.lnglat,
        points: currentPath,
      }
      this.dispatchEditEvent('editing', editingEvent)

      this.lastPath = currentPath
      this.lastLngLat = e.lnglat
    }
  }

  private handleMapMouseUp = (e: TMapEvent) => {
    if (!this.isEditing || !this.isMouseDown) return

    this.isMouseDown = false

    if (this.isRealEditing) {
      const currentPath = this.getPath()
      const event: TPolygonEditEvent = {
        type: 'editend',
        target: this.polygon,
        lnglat: e.lnglat,
        points: currentPath,
      }
      this.dispatchEditEvent('editend', event)

      if (this.label) {
        const [[minLng, minLat], [maxLng, maxLat]] = this.calculateBounds(currentPath)
        this.label.setPosition([(minLng + maxLng) / 2, (minLat + maxLat) / 2])
      }

      this.isRealEditing = false
    }

    this.lastLngLat = null
    this.initialPath = []
  }

  private dispatchEditEvent(type: string, event: TPolygonEditEvent) {
    const handlers = this.editEventHandlers.get(type)
    if (handlers) {
      handlers.forEach((handler) => handler(event))
    }
  }

  public onEdit(
    type: 'editstart' | 'editing' | 'editend',
    handler: (e: TPolygonEditEvent) => void,
  ): void {
    if (!this.editEventHandlers.has(type)) {
      this.editEventHandlers.set(type, [])
    }
    this.editEventHandlers.get(type)?.push(handler)
  }

  public offEdit(
    type: 'editstart' | 'editing' | 'editend',
    handler: (e: TPolygonEditEvent) => void,
  ): void {
    const handlers = this.editEventHandlers.get(type)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 获取原生多边形对象
   */
  public getNative() {
    return this.polygon
  }

  /**
   * 批量更新多边形样式
   * @param style 样式对象
   */
  public updateStyle(style: Partial<TPolygonStyle>): void {
    if (!style) return

    if ('color' in style && style.color !== undefined) {
      this.setColor(style.color)
    }
    if ('weight' in style && style.weight !== undefined) {
      this.setWeight(style.weight)
    }
    if ('opacity' in style && style.opacity !== undefined) {
      this.setOpacity(style.opacity)
    }
    if ('fillColor' in style && style.fillColor !== undefined) {
      this.setFillColor(style.fillColor)
    }
    if ('fillOpacity' in style && style.fillOpacity !== undefined) {
      this.setFillOpacity(style.fillOpacity)
    }
    if ('lineStyle' in style && style.lineStyle !== undefined) {
      this.setLineStyle(style.lineStyle)
    }
  }
}
