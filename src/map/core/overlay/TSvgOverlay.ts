import type { TNativeMap, TNativeOverlay } from '@/map/types'
import { TBaseOverlay } from '../base/TBaseOverlay'
import { validateTMapAPI } from '../../utils/validator'

export type TSvgOverlayEventType =
  | 'click'
  | 'dblclick'
  | 'mousedown'
  | 'mouseup'
  | 'mouseout'
  | 'mouseover'
  | 'dragstart'
  | 'drag'
  | 'dragend'
  | 'labelremove'
  | 'remove'

export type TSvgOverlayEventHandler = (event: Event, overlay: TSvgOverlay) => void

export enum TLabelPosition {
  TOP = 'top',
  BOTTOM = 'bottom',
  LEFT = 'left',
  RIGHT = 'right',
  TOP_LEFT = 'top-left',
  TOP_RIGHT = 'top-right',
  BOTTOM_LEFT = 'bottom-left',
  BOTTOM_RIGHT = 'bottom-right',
}

export enum TAnchorPosition {
  TOP_LEFT = 'top-left',
  TOP_CENTER = 'top-center',
  TOP_RIGHT = 'top-right',
  MIDDLE_LEFT = 'middle-left',
  CENTER = 'center',
  MIDDLE_RIGHT = 'middle-right',
  BOTTOM_LEFT = 'bottom-left',
  BOTTOM_CENTER = 'bottom-center',
  BOTTOM_RIGHT = 'bottom-right',
}

export interface TSvgOverlayOptions<T = any> {
  id?: string
  userData?: T
  position: [number, number]
  svgMarkup?: string
  iconName?: string
  size?: number | [number, number]
  anchor?: TAnchorPosition
  label?: string
  labelPosition?: TLabelPosition
  labelOffset?: [number, number]
  labelStyle?: Partial<CSSStyleDeclaration>
  iconStyle?: Partial<CSSStyleDeclaration>
  events?: Partial<Record<TSvgOverlayEventType, TSvgOverlayEventHandler>>
  labelRemoveable?: boolean
  labelRemoveButtonStyle?: Partial<CSSStyleDeclaration>
  onLabelRemove?: TSvgOverlayEventHandler
  // TODO: 其它覆盖物未实现
  removeWithLabel?: boolean
  zIndexOffset?: number
  container?: {
    className?: string | string[]
    style?: Partial<CSSStyleDeclaration>
    interactive?: boolean
    hidden?: boolean
  }
}

const DEFAULT_LABEL_STYLE: Partial<CSSStyleDeclaration> = {
  padding: '5px 10px',
  fontSize: '12px',
  lineHeight: '1.5',
  textAlign: 'center',
  backgroundColor: 'white',
  color: '#333',
  border: '1px solid  #cbcbcb',
  boxShadow: '1px 1px 4px #999',
  whiteSpace: 'nowrap',
  position: 'absolute',
  pointerEvents: 'auto',
  userSelect: 'none',
}

export class TSvgOverlay<T = any> extends TBaseOverlay<T> {
  private width: number = 24
  private height: number = 24
  private options: TSvgOverlayOptions<T>
  private containerElement: HTMLElement | null = null
  private svgElement: SVGElement | null = null
  private labelElement: HTMLElement | null = null
  private removeButton: HTMLElement | null = null
  private nativeOverlay: any = null
  private zIndexOffset: number = 0
  private eventHandlers: Map<
    TSvgOverlayEventType,
    Map<TSvgOverlayEventHandler, (e: Event) => void>
  > = new Map()
  private contentWrapper: HTMLElement | null = null
  private anchor: TAnchorPosition = TAnchorPosition.CENTER

  constructor(options: TSvgOverlayOptions<T>) {
    super({ id: options.id, userData: options.userData }, 'svg-overlay')

    validateTMapAPI()

    this.zIndexOffset = options.zIndexOffset ?? 0
    this.anchor = options.anchor || TAnchorPosition.CENTER

    let width = 32
    let height = 32

    if (Array.isArray(options.size)) {
      ;[width, height] = options.size
    } else if (options.size) {
      width = height = options.size
    }

    let content: string
    if (options.iconName) {
      content = `
        <svg
          aria-hidden="true"
          width="${width}"
          height="${height}"
          viewBox="0 0 ${width} ${height}"
          style="transform-origin: center${
            options.iconStyle
              ? ';' +
                Object.entries(options.iconStyle)
                  .map(([k, v]) => `${k}:${v}`)
                  .join(';')
              : ''
          }"
        >
          <use xlink:href="#icon-${options.iconName}" />
        </svg>
      `
    } else if (options.svgMarkup) {
      if (!options.svgMarkup.includes('<svg')) {
        throw new Error('TSvgOverlay svgMarkup must contain SVG markup')
      }
      content = options.svgMarkup
    } else {
      throw new Error('TSvgOverlay requires either svgMarkup or iconName')
    }

    this.width = width
    this.height = height
    this.options = { ...options }

    this.containerElement = document.createElement('div')
    this.containerElement.className = 't-overlay-content'

    if (options.container) {
      if (options.container.className) {
        const classNames = Array.isArray(options.container.className)
          ? options.container.className
          : [options.container.className]
        this.containerElement.className = ['t-overlay-content', ...classNames].join(' ')
      }

      // 处理样式
      Object.assign(this.containerElement.style, {
        position: 'absolute',
        ...(options.container.style || {}),
      })

      if (options.container.interactive !== undefined) {
        this.containerElement.style.pointerEvents = options.container.interactive ? 'auto' : 'none'
      }

      if (options.container.hidden) {
        this.containerElement.style.visibility = 'hidden'
      }
    } else {
      // 默认样式
      Object.assign(this.containerElement.style, {
        position: 'absolute',
      })
    }

    this.contentWrapper = document.createElement('div')
    this.contentWrapper.className = 't-overlay-wrapper'
    Object.assign(this.contentWrapper.style, {
      position: 'relative',
      width: `${width}px`,
      height: `${height}px`,
    })

    // 设置 SVG 内容
    this.contentWrapper.innerHTML = content
    this.svgElement = this.contentWrapper.querySelector('svg')
    if (this.svgElement) {
      this.svgElement.style.width = `${width}px`
      this.svgElement.style.height = `${height}px`
      this.svgElement.style.display = 'block'
    }

    // 标签元素
    if (options.label) {
      this.labelElement = document.createElement('div')
      this.labelElement.className = 't-overlay-label'
      Object.assign(
        this.labelElement.style,
        DEFAULT_LABEL_STYLE,
        this.getPositionStyle(options.labelPosition || TLabelPosition.TOP_RIGHT),
        options.labelStyle,
      )
      this.labelElement.textContent = options.label

      if (options.labelOffset) {
        const currentTransform = this.labelElement.style.transform
        this.labelElement.style.transform = `${currentTransform} translate(${options.labelOffset[0]}px, ${options.labelOffset[1]}px)`
      }

      if (options.labelRemoveable) {
        this.createLabelRemoveButton(options.labelRemoveButtonStyle)
      }

      this.contentWrapper.appendChild(this.labelElement)
    }

    if (options.iconStyle && this.svgElement) {
      Object.assign(this.svgElement.style, options.iconStyle)
    }

    this.containerElement.appendChild(this.contentWrapper)

    this.createNativeOverlay()

    if (options.events) {
      Object.entries(options.events).forEach(([eventName, handler]) => {
        if (handler) {
          this.on(eventName as TSvgOverlayEventType, handler)
        }
      })
    }
  }

  /**
   * 根据锚点位置计算偏移量
   * @returns 偏移量 [x, y]
   */
  private calculateAnchorOffset(): [number, number] {
    let xOffset = 0
    let yOffset = 0

    // 计算水平方向偏移
    if (this.anchor.includes('left')) {
      xOffset = 0
    } else if (this.anchor.includes('right')) {
      xOffset = -this.width
    } else {
      // center
      xOffset = -this.width / 2
    }

    // 计算垂直方向偏移
    if (this.anchor.includes('top')) {
      yOffset = 0
    } else if (this.anchor.includes('bottom')) {
      yOffset = -this.height
    } else {
      // middle or center
      yOffset = -this.height / 2
    }

    return [xOffset, yOffset]
  }

  private createNativeOverlay(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this

    const CustomOverlay = T.Overlay.extend({
      initialize: function (options: any) {
        this.position = options.position
        this.zIndexOffset = options.zIndexOffset
        this.anchorOffset = options.anchorOffset
      },

      onAdd: function (map: TNativeMap) {
        this.map = map
        map.getPanes().overlayPane.appendChild(self.containerElement!)
        if (self.containerElement) {
          self.containerElement.style.zIndex = String(this.zIndexOffset)
        }
        this.update()
        return self.containerElement!
      },

      onRemove: function () {
        if (self.containerElement?.parentNode) {
          self.containerElement.parentNode.removeChild(self.containerElement)
        }
        this.map = null
      },

      getPosition: function () {
        return this.position
      },

      setPosition: function (position: T.LngLat) {
        this.position = position
        this.update()
      },

      update: function () {
        if (!self.containerElement || !this.map) return
        const pos = this.map.lngLatToLayerPoint(this.position)

        // 应用锚点偏移
        const [xOffset, yOffset] = this.anchorOffset

        self.containerElement.style.left = pos.x + xOffset + 'px'
        self.containerElement.style.top = pos.y + yOffset + 'px'
      },
    })

    // 计算锚点偏移
    const anchorOffset = this.calculateAnchorOffset()

    this.nativeOverlay = new CustomOverlay({
      position: new T.LngLat(this.options.position[0], this.options.position[1]),
      zIndexOffset: this.zIndexOffset,
      anchorOffset: anchorOffset,
    })
  }

  protected onAdd(map: TNativeMap): void {
    map.addOverLay(this.nativeOverlay)
  }

  protected onRemove(): void {
    if (this.removeButton) {
      this.removeButton.remove()
      this.removeButton = null
    }
    const map = this.assertMapExists()
    map.removeOverLay(this.nativeOverlay)
  }

  public getPosition(): [number, number] {
    return this.options.position
  }

  public setPosition(position: [number, number]): void {
    this.options.position = position
    if (this.nativeOverlay) {
      this.nativeOverlay.setPosition(new T.LngLat(position[0], position[1]))
    }
  }

  private querySelector<T extends Element>(selector: string): T | null {
    return this.contentWrapper?.querySelector<T>(selector) ?? null
  }

  public setSize(size: number | [number, number]): void {
    const svgElement = this.querySelector<SVGElement>('svg')
    if (!svgElement) return

    if (Array.isArray(size)) {
      ;[this.width, this.height] = size
    } else {
      this.width = this.height = size
    }

    svgElement.setAttribute('width', `${this.width}`)
    svgElement.setAttribute('height', `${this.height}`)
    svgElement.setAttribute('viewBox', `0 0 ${this.width} ${this.height}`)

    if (this.containerElement) {
      this.containerElement.style.width = `${this.width}px`
      this.containerElement.style.height = `${this.height}px`
    }

    // 更新锚点偏移
    if (this.nativeOverlay) {
      const anchorOffset = this.calculateAnchorOffset()
      this.nativeOverlay.anchorOffset = anchorOffset
      this.nativeOverlay.update()
    }
  }

  public getSize(): [number, number] {
    return [this.width, this.height]
  }

  public setSvgScale(scale: number): void {
    const svgElement = this.querySelector<SVGElement>('svg')
    if (svgElement) {
      svgElement.style.transform = `scale(${scale})`
      svgElement.style.transformOrigin = 'bottom center'
      svgElement.style.transition = 'transform 0.3s'
    }
  }

  public setSvgColor(color: string): void {
    const svgElement = this.querySelector<SVGElement>('svg')
    if (svgElement) {
      svgElement.style.fill = color
    }
  }

  public getSvgElement(): SVGElement | null {
    return this.querySelector<SVGElement>('svg')
  }

  public setLabel(
    label: string | null,
    options?: {
      position?: TLabelPosition
      offset?: [number, number]
      style?: Partial<CSSStyleDeclaration>
      removeable?: boolean
      removeButtonStyle?: Partial<CSSStyleDeclaration>
      onRemove?: TSvgOverlayEventHandler
      removeWithLabel?: boolean
    },
  ): void {
    if (!label) {
      this.labelElement?.remove()
      this.labelElement = null
      this.removeButton = null
      return
    }

    if (!this.labelElement && this.contentWrapper) {
      this.labelElement = document.createElement('div')
      this.labelElement.className = 't-overlay-label'
      Object.assign(this.labelElement.style, DEFAULT_LABEL_STYLE)
      this.contentWrapper.appendChild(this.labelElement)
    }

    if (this.labelElement) {
      this.labelElement.innerHTML = label

      const position = options?.position || TLabelPosition.TOP_RIGHT
      this.updateLabelPosition(position, options?.offset)

      if (options?.style) {
        Object.assign(this.labelElement.style, options.style)
      }

      if (options?.removeable) {
        this.options.onLabelRemove = options.onRemove || this.options.onLabelRemove

        // 更新 removeWithLabel 选项
        if (options.removeWithLabel !== undefined) {
          this.options.removeWithLabel = options.removeWithLabel
        }

        this.createLabelRemoveButton(options.removeButtonStyle)
      } else if (this.removeButton) {
        this.removeButton.remove()
        this.removeButton = null
      }
    }
  }

  public setLabelPosition(position: TLabelPosition, offset?: [number, number]): void {
    if (this.labelElement) {
      this.updateLabelPosition(position, offset)
    }
  }

  private getPositionStyle(position: TLabelPosition): Partial<CSSStyleDeclaration> {
    const baseStyle: Partial<CSSStyleDeclaration> = {
      position: 'absolute',
      whiteSpace: 'nowrap',
    }

    switch (position) {
      case TLabelPosition.TOP:
        return {
          ...baseStyle,
          left: '50%',
          bottom: '100%',
          transform: 'translate(-50%, -2px)',
        }
      case TLabelPosition.BOTTOM:
        return {
          ...baseStyle,
          left: '50%',
          top: '100%',
          transform: 'translate(-50%, 2px)',
        }
      case TLabelPosition.LEFT:
        return {
          ...baseStyle,
          right: '100%',
          top: '50%',
          transform: 'translate(-2px, -50%)',
        }
      case TLabelPosition.RIGHT:
        return {
          ...baseStyle,
          left: '100%',
          top: '50%',
          transform: 'translate(2px, -50%)',
        }
      case TLabelPosition.TOP_LEFT:
        return {
          ...baseStyle,
          right: '100%',
          bottom: '100%',
          transform: 'translate(-2px, -2px)',
        }
      case TLabelPosition.TOP_RIGHT:
        return {
          ...baseStyle,
          left: '100%',
          bottom: '100%',
          transform: 'translate(2px, -2px)',
        }
      case TLabelPosition.BOTTOM_LEFT:
        return {
          ...baseStyle,
          right: '100%',
          top: '100%',
          transform: 'translate(-2px, 2px)',
        }
      case TLabelPosition.BOTTOM_RIGHT:
        return {
          ...baseStyle,
          left: '100%',
          top: '100%',
          transform: 'translate(2px, 2px)',
        }
      default:
        return {
          ...baseStyle,
          left: '50%',
          top: '100%',
          transform: 'translate(-50%, 2px)',
        }
    }
  }

  private updateLabelPosition(position: TLabelPosition, offset?: [number, number]): void {
    if (!this.labelElement) return

    const positionStyle = this.getPositionStyle(position)
    Object.assign(this.labelElement.style, positionStyle)

    if (offset) {
      const currentTransform = this.labelElement.style.transform
      this.labelElement.style.transform = `${currentTransform} translate(${offset[0]}px, ${offset[1]}px)`
    }
  }

  public setLabelOffset(offset: [number, number]): void {
    if (this.labelElement) {
      this.labelElement.style.transform = `translate(${offset[0]}px, ${offset[1]}px)`
    }
  }

  public setLabelStyle(style: Partial<CSSStyleDeclaration>): void {
    if (this.labelElement) {
      Object.assign(this.labelElement.style, style)
    }
  }

  public showLabel(): void {
    if (this.labelElement) {
      this.labelElement.style.display = ''
    }
  }

  public hideLabel(): void {
    if (this.labelElement) {
      this.labelElement.style.display = 'none'
    }
  }

  public setContainerStyle(style: Partial<CSSStyleDeclaration>): void {
    if (this.containerElement) {
      Object.assign(this.containerElement.style, style)
    }
  }

  public on(eventName: TSvgOverlayEventType, handler: TSvgOverlayEventHandler): void {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, new Map())
    }

    const wrapper = (e: Event) => {
      e.stopPropagation()
      handler(e, this)
    }
    this.eventHandlers.get(eventName)!.set(handler, wrapper)
    this.containerElement?.addEventListener(eventName, wrapper)
  }

  public off(eventName: TSvgOverlayEventType, handler?: TSvgOverlayEventHandler): void {
    const handlers = this.eventHandlers.get(eventName)
    if (!handlers) return

    if (handler) {
      const wrapper = handlers.get(handler)
      if (wrapper) {
        this.containerElement?.removeEventListener(eventName, wrapper)
        handlers.delete(handler)
      }
    } else {
      handlers.forEach((wrapper) => {
        this.containerElement?.removeEventListener(eventName, wrapper)
      })
      this.eventHandlers.delete(eventName)
    }

    if (handler && handlers.size === 0) {
      this.eventHandlers.delete(eventName)
    }
  }

  /**
   * 添加点击事件监听器
   * @param handler 事件处理函数
   */
  public onClick(handler: TSvgOverlayEventHandler): void {
    this.on('click', handler)
  }

  /**
   * 添加双击事件监听器
   * @param handler 事件处理函数
   */
  public onDblClick(handler: TSvgOverlayEventHandler): void {
    this.on('dblclick', handler)
  }

  /**
   * 添加鼠标移入事件监听器
   * @param handler 事件处理函数
   */
  public onMouseOver(handler: TSvgOverlayEventHandler): void {
    this.on('mouseover', handler)
  }

  /**
   * 添加鼠标移出事件监听器
   * @param handler 事件处理函数
   */
  public onMouseOut(handler: TSvgOverlayEventHandler): void {
    this.on('mouseout', handler)
  }

  protected onShow(): void {
    if (this.containerElement) {
      this.containerElement.style.display = ''
    }
  }

  protected onHide(): void {
    if (this.containerElement) {
      this.containerElement.style.display = 'none'
    }
  }

  /**
   * 设置图标样式
   */
  public setIconStyle(style: Partial<CSSStyleDeclaration>): void {
    const svgElement = this.querySelector<SVGElement>('svg')
    if (svgElement) {
      Object.assign(svgElement.style, style)
    }
  }

  private createLabelRemoveButton(style?: Partial<CSSStyleDeclaration>): void {
    if (!this.labelElement) return

    this.removeButton = document.createElement('div')
    this.removeButton.className = 't-overlay-label-remove-button'

    Object.assign(this.removeButton.style, {
      position: 'absolute',
      top: '-8px',
      right: '-8px',
      width: '16px',
      height: '16px',
      background: '#fff',
      border: '1px solid #ddd',
      borderRadius: '50%',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '12px',
      color: '#666',
      ...style,
    })

    this.removeButton.innerHTML = '×'

    this.removeButton.addEventListener('click', (e) => {
      e.stopPropagation()

      // 触发 labelremove 事件
      const handlers = this.eventHandlers.get('labelremove')
      if (handlers) {
        handlers.forEach((wrapper, handler) => {
          handler(e, this)
        })
      }

      // 调用专门的回调函数
      if (this.options.onLabelRemove) {
        this.options.onLabelRemove(e, this)
      }

      // 移除标签
      this.setLabel(null)

      if (this.options.removeWithLabel) {
        this.remove()
      }
    })

    this.labelElement.appendChild(this.removeButton)
  }

  public setLabelRemoveable(
    removeable: boolean,
    style?: Partial<CSSStyleDeclaration>,
    onRemove?: TSvgOverlayEventHandler,
    removeWithLabel?: boolean,
  ): void {
    if (!this.labelElement) return

    if (removeable && !this.removeButton) {
      if (onRemove) {
        this.options.onLabelRemove = onRemove
      }

      if (removeWithLabel !== undefined) {
        this.options.removeWithLabel = removeWithLabel
      }

      this.createLabelRemoveButton(style)
    } else if (!removeable && this.removeButton) {
      this.removeButton.remove()
      this.removeButton = null
    } else if (removeable && this.removeButton && style) {
      Object.assign(this.removeButton.style, style)

      if (removeWithLabel !== undefined) {
        this.options.removeWithLabel = removeWithLabel
      }
    }
  }

  /**
   * 获取标签是否可删除
   */
  public isLabelRemoveable(): boolean {
    return !!this.removeButton
  }

  /**
   * 设置删除按钮样式
   */
  public setLabelRemoveButtonStyle(style: Partial<CSSStyleDeclaration>): void {
    if (this.removeButton) {
      Object.assign(this.removeButton.style, style)
    }
  }

  public setZIndexOffset(offset: number): void {
    this.zIndexOffset = offset
    if (this.containerElement) {
      this.containerElement.style.zIndex = String(offset)
    }
  }

  public getZIndexOffset(): number {
    return this.zIndexOffset
  }

  /**
   * 设置锚点位置
   * @param anchor 锚点位置
   */
  public setAnchor(anchor: TAnchorPosition): void {
    this.anchor = anchor

    // 重新计算锚点偏移
    const anchorOffset = this.calculateAnchorOffset()

    // 更新原生覆盖物的锚点偏移
    if (this.nativeOverlay) {
      this.nativeOverlay.anchorOffset = anchorOffset
      this.nativeOverlay.update()
    }
  }

  /**
   * 获取当前锚点位置
   * @returns 锚点位置
   */
  public getAnchor(): TAnchorPosition {
    return this.anchor
  }

  public getNative(): TNativeOverlay {
    return this.nativeOverlay
  }

  /**
   * 生成圆形SVG标记的markup
   * @param size 尺寸
   * @param style 圆形样式
   * @returns SVG标记字符串
   */
  static generateCircleMarkup(
    size: number | [number, number] = 24,
    style: {
      fill?: string
      stroke?: string
      strokeWidth?: number
      fillOpacity?: number
      strokeOpacity?: number
    } = {},
  ): string {
    const width = Array.isArray(size) ? size[0] : size
    const height = Array.isArray(size) ? size[1] : size
    const radius = Math.min(width, height) / 2

    return `
      <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
        <circle
          cx="${width / 2}"
          cy="${height / 2}"
          r="${radius - (style.strokeWidth ?? 0) / 2}"
          fill="${style.fill ?? '#FF4444'}"
          fill-opacity="${style.fillOpacity ?? 1}"
          stroke="${style.stroke ?? 'white'}"
          stroke-width="${style.strokeWidth ?? 2}"
          stroke-opacity="${style.strokeOpacity ?? 1}"
        />
      </svg>
    `
  }

  /**
   * 生成矩形SVG标记的markup
   * @param size 尺寸
   * @param style 矩形样式
   * @returns SVG标记字符串
   */
  static generateRectMarkup(
    size: number | [number, number] = 24,
    style: {
      fill?: string
      stroke?: string
      strokeWidth?: number
      fillOpacity?: number
      strokeOpacity?: number
      rx?: number
      ry?: number
    } = {},
  ): string {
    const width = Array.isArray(size) ? size[0] : size
    const height = Array.isArray(size) ? size[1] : size

    return `
      <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
        <rect
          x="${(style.strokeWidth ?? 0) / 2}"
          y="${(style.strokeWidth ?? 0) / 2}"
          width="${width - (style.strokeWidth ?? 0)}"
          height="${height - (style.strokeWidth ?? 0)}"
          rx="${style.rx ?? 0}"
          ry="${style.ry ?? 0}"
          fill="${style.fill ?? '#FF4444'}"
          fill-opacity="${style.fillOpacity ?? 1}"
          stroke="${style.stroke ?? 'white'}"
          stroke-width="${style.strokeWidth ?? 2}"
          stroke-opacity="${style.strokeOpacity ?? 1}"
        />
      </svg>
    `
  }

  /**
   * 生成三角形SVG标记的markup
   * @param size 尺寸
   * @param style 三角形样式
   * @returns SVG标记字符串
   */
  static generateTriangleMarkup(
    size: number | [number, number] = 24,
    style: {
      fill?: string
      stroke?: string
      strokeWidth?: number
      fillOpacity?: number
      strokeOpacity?: number
      direction?: 'up' | 'down' | 'left' | 'right'
    } = {},
  ): string {
    const width = Array.isArray(size) ? size[0] : size
    const height = Array.isArray(size) ? size[1] : size

    let points: string
    switch (style.direction ?? 'up') {
      case 'up':
        points = `${width / 2},0 ${width},${height} 0,${height}`
        break
      case 'down':
        points = `0,0 ${width},0 ${width / 2},${height}`
        break
      case 'left':
        points = `${width},0 ${width},${height} 0,${height / 2}`
        break
      case 'right':
        points = `0,0 ${width},${height / 2} 0,${height}`
        break
    }

    return `
      <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
        <polygon
          points="${points}"
          fill="${style.fill ?? '#FF4444'}"
          fill-opacity="${style.fillOpacity ?? 1}"
          stroke="${style.stroke ?? 'white'}"
          stroke-width="${style.strokeWidth ?? 2}"
          stroke-opacity="${style.strokeOpacity ?? 1}"
        />
      </svg>
    `
  }

  /**
   * 生成涟漪效果SVG标记的markup
   * @param size 尺寸
   * @param style 涟漪样式
   * @returns SVG标记字符串
   */
  static generateRippleMarkup(
    size: number | [number, number] = 24,
    style: {
      fill?: string
      stroke?: string
      strokeWidth?: number
      fillOpacity?: number
      strokeOpacity?: number
      duration?: number
      count?: number
    } = {},
  ): string {
    const width = Array.isArray(size) ? size[0] : size
    const height = Array.isArray(size) ? size[1] : size
    const duration = style.duration ?? 2
    const count = style.count ?? 3
    let circles = `<circle cx="${width / 2}" cy="${height / 2}" r="4" fill="${style.stroke ?? '#F8E71C'}" filter="url(#glow)"/>`

    for (let i = 0; i < count; i++) {
      const delay = (i * duration) / count
      circles += `
        <circle cx="${width / 2}" cy="${height / 2}" r="0" fill="none" stroke="${style.stroke ?? '#F8E71C'}" stroke-width="${style.strokeWidth ?? 2}">
          <animate attributeName="r" from="0" to="${width / 4}" dur="${duration}s" begin="${delay}s" repeatCount="indefinite"/>
          <animate attributeName="stroke-opacity" from="1" to="0" dur="${duration}s" begin="${delay}s" repeatCount="indefinite"/>
        </circle>
      `
    }

    return `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
      <defs>
        <filter id="glow">
          <feGaussianBlur in="SourceGraphic" stdDeviation="3" result="blur"/>
          <feMerge>
            <feMergeNode in="blur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
      ${circles}
    </svg>`
  }

  /**
   * 创建圆形SVG标记
   * @param options 配置项
   * @param style 圆形样式
   */
  static createCircle<T>(
    options: Omit<TSvgOverlayOptions<T>, 'svgMarkup' | 'iconName'>,
    style: {
      fill?: string
      stroke?: string
      strokeWidth?: number
      fillOpacity?: number
      strokeOpacity?: number
    } = {},
  ): TSvgOverlay<T> {
    return new TSvgOverlay({
      ...options,
      svgMarkup: TSvgOverlay.generateCircleMarkup(options.size, style),
    })
  }

  /**
   * 创建矩形SVG标记
   * @param options 配置项
   * @param style 矩形样式
   */
  static createRect<T>(
    options: Omit<TSvgOverlayOptions<T>, 'svgMarkup' | 'iconName'>,
    style: {
      fill?: string
      stroke?: string
      strokeWidth?: number
      fillOpacity?: number
      strokeOpacity?: number
      rx?: number
      ry?: number
    } = {},
  ): TSvgOverlay<T> {
    return new TSvgOverlay({
      ...options,
      svgMarkup: TSvgOverlay.generateRectMarkup(options.size, style),
    })
  }

  /**
   * 创建三角形SVG标记
   * @param options 配置项
   * @param style 三角形样式
   */
  static createTriangle<T>(
    options: Omit<TSvgOverlayOptions<T>, 'svgMarkup' | 'iconName'>,
    style: {
      fill?: string
      stroke?: string
      strokeWidth?: number
      fillOpacity?: number
      strokeOpacity?: number
      direction?: 'up' | 'down' | 'left' | 'right'
    } = {},
  ): TSvgOverlay<T> {
    return new TSvgOverlay({
      ...options,
      svgMarkup: TSvgOverlay.generateTriangleMarkup(options.size, style),
    })
  }

  /**
   * 创建涟漪效果的SVG标记
   * @param options 配置项
   * @param style 涟漪样式
   */
  static createRipple<T>(
    options: Omit<TSvgOverlayOptions<T>, 'svgMarkup' | 'iconName'>,
    style: {
      fill?: string
      stroke?: string
      strokeWidth?: number
      fillOpacity?: number
      strokeOpacity?: number
      duration?: number
      count?: number
    } = {},
  ): TSvgOverlay<T> {
    return new TSvgOverlay({
      ...options,
      svgMarkup: TSvgOverlay.generateRippleMarkup(options.size, style),
    })
  }

  public remove(): void {
    const event = new Event('remove')
    const handlers = this.eventHandlers.get('remove')
    if (handlers) {
      handlers.forEach((wrapper, handler) => {
        handler(event, this)
      })
    }

    super.remove()
  }

  /**
   * 设置当标签被删除时是否自动删除覆盖物
   * @param removeWithLabel 是否自动删除
   */
  public setRemoveWithLabel(removeWithLabel: boolean): void {
    this.options.removeWithLabel = removeWithLabel
  }

  /**
   * 获取当标签被删除时是否自动删除覆盖物
   * @returns 是否自动删除
   */
  public getRemoveWithLabel(): boolean {
    return !!this.options.removeWithLabel
  }
}
