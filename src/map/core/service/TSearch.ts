import { TMAP_CONFIG, TSearchType } from '../../constants'
import type {
  TNativeMap,
  TNativeLocalSearch,
  TNativeLocalSearchOptions,
  TNativeLocalSearchResult,
} from '../../types'

/** 搜索服务配置选项 */
export interface TSearchOptions extends TNativeLocalSearchOptions {
  timeout?: number
  pageSize?: number
}

export class TSearch {
  private searchInstance: TNativeLocalSearch | null = null
  private readonly currentTimeout: number
  private currentPageSize: number
  private lastKeyword: string | null = null
  private lastType: TSearchType | null = null
  private lastBounds: T.LngLatBounds | null = null
  private lastCenter: { lng: number; lat: number } | null = null
  private lastRadius: number | null = null

  constructor(
    private readonly map: TNativeMap,
    options?: TSearchOptions,
  ) {
    this.currentTimeout = options?.timeout ?? TMAP_CONFIG.SEARCH_TIMEOUT
    this.currentPageSize = options?.pageSize ?? TMAP_CONFIG.SEARCH_PAGE_SIZE
  }

  /**
   * 获取原生搜索实例
   * @returns TNativeLocalSearch 天地图原生搜索实例
   */
  public getNativeSearch(): TNativeLocalSearch | null {
    return this.searchInstance
  }

  private initialize(options?: TNativeLocalSearchOptions): void {
    if (!this.searchInstance) {
      this.searchInstance = new T.LocalSearch(this.map, {
        pageCapacity: options?.pageCapacity ?? this.currentPageSize,
      })
    }
    if (options?.onSearchComplete) {
      this.searchInstance.setSearchCompleteCallback(options.onSearchComplete)
    }
  }

  private createSearchPromise<T>(operation: () => void, customTimeout?: number): Promise<T> {
    return new Promise((resolve, reject) => {
      // eslint-disable-next-line prefer-const
      let timeoutId: number

      const handleResult = (result: T) => {
        clearTimeout(timeoutId)
        resolve(result)
      }

      timeoutId = window.setTimeout(() => {
        reject(new Error('搜索超时'))
      }, customTimeout ?? this.currentTimeout)

      this.initialize({
        onSearchComplete: handleResult as (result: TNativeLocalSearchResult) => void,
      })

      operation()
    })
  }

  public setPageSize(size: number): void {
    this.currentPageSize = size
    if (this.searchInstance) {
      this.searchInstance.setPageCapacity(size)
    }
  }

  public search(
    keyword: string,
    type: TSearchType = TSearchType.NORMAL,
    options?: {
      timeout?: number
      pageSize?: number
      pageNum?: number
    },
  ): Promise<TNativeLocalSearchResult> {
    let needSearch = !this.lastKeyword || this.lastKeyword !== keyword || this.lastType !== type

    if (options?.pageSize && options.pageSize !== this.currentPageSize) {
      this.setPageSize(options.pageSize)
      needSearch = true
    }

    return this.createSearchPromise<TNativeLocalSearchResult>(() => {
      if (needSearch) {
        this.lastKeyword = keyword
        this.lastType = type
        this.searchInstance?.search(keyword, type)
      } else if (options?.pageNum) {
        this.searchInstance?.gotoPage(options.pageNum)
      }
    }, options?.timeout)
  }

  public searchInBounds(
    keyword: string,
    bounds: T.LngLatBounds,
    options?: {
      timeout?: number
      pageSize?: number
      pageNum?: number
    },
  ): Promise<TNativeLocalSearchResult> {
    let needSearch =
      !this.lastKeyword ||
      this.lastKeyword !== keyword ||
      !this.lastBounds ||
      this.lastBounds.equals(bounds) === false

    if (options?.pageSize && options.pageSize !== this.currentPageSize) {
      this.setPageSize(options.pageSize)
      needSearch = true
    }

    return this.createSearchPromise<TNativeLocalSearchResult>(() => {
      if (needSearch) {
        this.lastKeyword = keyword
        this.lastBounds = bounds
        this.lastCenter = null
        this.lastRadius = null
        this.searchInstance?.searchInBounds(keyword, bounds)
      } else if (options?.pageNum) {
        this.searchInstance?.gotoPage(options.pageNum)
      }
    }, options?.timeout)
  }

  public searchNearby(
    keyword: string,
    center: { lng: number; lat: number },
    radius: number,
    options?: {
      timeout?: number
      pageSize?: number
      pageNum?: number
    },
  ): Promise<TNativeLocalSearchResult> {
    let needSearch =
      !this.lastKeyword ||
      this.lastKeyword !== keyword ||
      !this.lastCenter ||
      this.lastCenter.lng !== center.lng ||
      this.lastCenter.lat !== center.lat ||
      this.lastRadius !== radius

    if (options?.pageSize && options.pageSize !== this.currentPageSize) {
      this.setPageSize(options.pageSize)
      needSearch = true
    }

    return this.createSearchPromise<TNativeLocalSearchResult>(() => {
      if (needSearch) {
        this.lastKeyword = keyword
        this.lastCenter = center
        this.lastRadius = radius
        this.lastBounds = null
        const centerPoint = new T.LngLat(center.lng, center.lat)
        this.searchInstance?.searchNearby(keyword, centerPoint, radius)
      } else if (options?.pageNum) {
        this.searchInstance?.gotoPage(options.pageNum)
      }
    }, options?.timeout)
  }

  public setPageCapacity(count: number): void {
    this.initialize()
    this.searchInstance?.setPageCapacity(count)
  }

  public clearResults(): void {
    this.searchInstance?.clearResults()
  }

  public destroy(): void {
    this.clearResults()
    this.searchInstance = null
  }
}
