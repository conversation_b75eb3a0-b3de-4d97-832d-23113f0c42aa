import type { TNativeMap } from '../../types'

export class TGeocoder {
  private geocoder: T.Geocoder
  private map: TNativeMap

  constructor(map: TNativeMap) {
    this.map = map
    this.geocoder = new T.Geocoder()
  }

  /**
   * 对指定的地址进行地址解析
   * @param location 地址字符串
   */
  public getPoint(location: string): Promise<T.GeocoderResult> {
    return new Promise((resolve, reject) => {
      this.geocoder.getPoint(location, (result) => {
        if (result) {
          resolve(result)
        } else {
          reject(new Error('地址解析失败'))
        }
      })
    })
  }

  /**
   * 对指定的坐标点进行反地址解析
   * @param point 坐标点
   */
  public getLocation(point: T.LngLat): Promise<T.GeocoderResult> {
    return new Promise((resolve, reject) => {
      this.geocoder.getLocation(point, (result) => {
        if (result) {
          resolve(result)
        } else {
          reject(new Error('反地址解析失败'))
        }
      })
    })
  }
}