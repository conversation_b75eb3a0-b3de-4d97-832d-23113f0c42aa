export const TMAP_CONFIG = {
  API_URL: 'https://api.tianditu.gov.cn/api',
  VERSION: '4.0',
  KEY: import.meta.env.VITE_TIANDITU_KEY,
  TILE_URL: 'http://t{s}.tianditu.gov.cn/{type}/wmts',
  SEARCH_TIMEOUT: 10000,
  SEARCH_PLACEHOLDER: '搜索地点',
  SEARCH_PAGE_SIZE: 10,
} as const

export const DEFAULT_MAP_CONFIG = {
  CENTER: {
    lng: 107.237743,
    lat: 34.363184,
  },
  ZOOM: 12,
  MIN_ZOOM: 1,
  MAX_ZOOM: 18,
} as const

export const LAYER_TYPES = {
  VECTOR: 'vec',
  IMAGE: 'img',
  TERRAIN: 'ter',
} as const

export const LAYER_CONFIG = {
  [LAYER_TYPES.VECTOR]: {
    base: {
      layer: 'vec',
      type: 'vec_w',
    },
    annotation: {
      layer: 'cva',
      type: 'cva_w',
    },
  },
  [LAYER_TYPES.IMAGE]: {
    base: {
      layer: 'img',
      type: 'img_w',
    },
    annotation: {
      layer: 'cia',
      type: 'cia_w',
    },
  },
  [LAYER_TYPES.TERRAIN]: {
    base: {
      layer: 'ter',
      type: 'ter_w',
    },
    annotation: {
      layer: 'cta',
      type: 'cta_w',
    },
  },
} as const

/**
 * 叠加物类型枚举
 */
export enum TOverlayType {
  /** 文本标注 */
  LABEL = 1,
  /** 点标注 */
  MARKER = 2,
  /** 信息窗口 */
  INFO_WINDOW = 3,
  /** 折线 */
  POLYLINE = 4,
  /** 多边形 */
  POLYGON = 5,
  /** 矩形 */
  RECTANGLE = 6,
  /** 圆形 */
  CIRCLE = 8,
}

// 搜索类型定义
export enum TSearchType {
  /** 普通搜索 */
  NORMAL = 1,
  /** 视野内搜索 */
  VIEWPORT = 2,
  /** 普通建议词搜索 */
  SUGGEST = 4,
  /** 公交规划建议词搜索 */
  BUS_SUGGEST = 5,
  /** 纯地名搜索(不搜公交线) */
  LOCATION_ONLY = 7,
  /** 拉框搜索 */
  BOX = 10,
}

/**
 * 控件位置枚举
 */
export enum TControlPosition {
  /** 左上角 */
  TOP_LEFT = 'topleft',
  /** 右上角 */
  TOP_RIGHT = 'topright',
  /** 左下角 */
  BOTTOM_LEFT = 'bottomleft',
  /** 右下角 */
  BOTTOM_RIGHT = 'bottomright',
}
