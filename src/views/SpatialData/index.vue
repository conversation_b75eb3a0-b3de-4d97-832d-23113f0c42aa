<template>
  <div class="map-container">
   <div class="map" id="map"></div>
  </div>
</template>

<script setup>
import { onMounted,ref } from 'vue';
import {GIS} from "@/utils/gis.js";
import { Location, Edit, Connection, CircleCheck, Delete, Picture } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
onMounted(() => {
      gis.initMap("image")
})

const gis = reactive(new GIS({
  apiKey: "f4d0553a23372a2f48c74851c7e46f4d",
  container: "map",
}));
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
}
</style>
