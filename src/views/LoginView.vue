<template>
  <div class="bg h-[100vh] flex justify-center items-center">
    <div class="flex bg-white rounded-[10px] h-[608px]">
      <img src="@/assets/imgs/login-ch.png" alt="" class="w-[528px] h-full hidden lg:block">
      <div class="px-[40px] py-[45px] h-full box-border flex flex-col justify-between">
        <div class="flex items-center">
          <img src="@/assets/logo.png" alt="" class="w-[48px] h-[48px]">
          <span class="text-[26px] ml-[10px] alibaba-semiBold">{{ appTitle }}</span>
        </div>
        <div class="flex flex-col my-8">
          <span class="text-[20px] alibaba-semiBold">欢迎您登录</span>
          <span class="text-[#A1ADBC] text-[14px]">请输入账号密码</span>
        </div>
        <el-form ref="formRef" :model="formData" :rules="rules" class="login-form" @keyup.enter="handleSubmit">
          <el-form-item prop="accountNumber">
            <el-input v-model="formData.accountNumber" :prefix-icon="User" placeholder="请输入账号" class="h-[60px]" />
          </el-form-item>
          <el-form-item prop="accountPassword">
            <el-input v-model="formData.accountPassword" :prefix-icon="Lock" type="password" placeholder="请输入密码" show-password class="h-[60px]"/>
          </el-form-item>
          <el-form-item prop="verifyCode">
            <div class="flex w-full">
              <el-input v-model="formData.verifyCode" :prefix-icon="Key" placeholder="请输入验证码" />
              <img :src="verifyCodeBase64" alt="验证码" class="w-[160px] h-[60px] rounded-[4px] cursor-pointer ml-[20px] bg-#EFF1F3" @click="refreshVerifyCode">
            </div>
          </el-form-item>
        </el-form>
        <el-button type="primary" class="w-full h-[48px] text-[18px] mt-11" :loading="loading" @click="handleSubmit">
          {{ loading ? '登录中...' : '登录' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { User, Lock, Key } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import AuthApi from '@/api/auth'
import uniqueId from 'lodash/uniqueId'
import { ElMessage } from 'element-plus'

const appTitle = import.meta.env.VITE_APP_TITLE
const router = useRouter()
const route = useRoute()
const { login } = useUserStore()

const formRef = ref<FormInstance>()
const loading = ref(false)
const verifyCodeBase64 = ref<string>()

const formData = reactive({
  accountNumber: '',
  accountPassword: '',
  verifyCode: '',
  uniqueIdentifier: '',
  loginType: 'ACCOUNT_PASSWORD'
})

const rules: FormRules = {
  accountNumber: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  accountPassword: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  verifyCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为 4 位', trigger: 'blur' }
  ]
}

const refreshVerifyCode = async () => {
  const uuid = uniqueId()
  try {
    const res = await AuthApi.getVerifyCode(uuid)
    formData.uniqueIdentifier = uuid
    verifyCodeBase64.value = res.data
  } catch {}
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    formRef.value.validate((isVaid => {
      if (isVaid) {
        loading.value = true
        login(formData).then(()=>{
          router.push({
          path: route.query.redirect?.toString() ?? '/',
          replace: true
        })
        })
          .catch(() => {
            refreshVerifyCode()
          })
          .finally(() => {
          loading.value = false
        })
      }
    }))
  } catch {
    ElMessage.error('登录失败，请检查账号密码')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  refreshVerifyCode()
})
</script>

<style lang="scss" scoped>
.bg {
  background: url("@/assets/imgs/login-bg.png") no-repeat center / cover;
}
.login-form{
  :deep(.el-input__wrapper) {
    background-color: #EFF1F3;
    border: none;
    box-shadow: none;
  }
}
</style>
