<template>
  <z-screen-container :loading="loading" :wrapperStyle="{ background: '#1A3A6E' }">
    <Header />
    <div class="h-[calc(100%-120px)] box-border mt-[120px] px-[44px] pb-[30px] overflow-auto">
      <SCard class="px-[30px] py-[20px]">
        <div class="flex flex-col h-[50px]">
          <div class="h-full min-w-[272px] bg-[url(@/assets/imgs/screen/hx-1.png)] bg-no-repeat bg-[length:160px_52px] flex items-center pl-[58px]">
            <span class="text-[26px] c-white alibaba-semiBold">{{ dataInfo.projectName }}</span>
          </div>
          <div class="border-b-1 border-b-solid border-[#4A79B7] ml-[32px]"></div>
        </div>
        <div class="c-white text-[20px] mt-[20px] pl-[10px]" v-html="dataInfo.projectIntroduction"></div>
      </SCard>
      <div class="flex gap-x-[24px] mt-[24px]">
        <SCard class="w-[947px]">
          <el-carousel trigger="click" height="660px" indicator-position="outside">
            <el-carousel-item v-for="(v, i) in projectImgList" :key="i">
              <el-image
                :src="v"
                :preview-src-list="projectImgList"
                :initial-index="i"
                fit="cover"
                preview-teleported
                class="w-full h-full rounded-6"
              />
            </el-carousel-item>
          </el-carousel>
        </SCard>
        <div class="flex flex-col w-[870px]">
          <div class="bg-[url(@/assets/imgs/screen/card-border-8.png)] bg-[length:100%_100%] h-[411px] relative">
            <span class="absolute top-[-15px] left-4 right-7 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">基本信息</span>
            <div class="px-[30px] py-[40px] flex flex-col">
              <div class="flex c-white alibaba-semiBold text-lg gap-y-[30px] mt-[20px]">
                <span class="flex-1">项目类型：{{ classNames }}</span>
                <span class="flex-1">投资类型：{{ investType}}</span>
              </div>
              <div class="flex c-white alibaba-semiBold text-lg gap-y-[30px] mt-[20px]">
                <span class="flex-1">所属产业：{{ dataInfo.projectIndustry }}</span>
                <span class="flex-1">所属行业：{{ dataInfo.projectBusiness }}</span>
              </div>
            </div>
            <div class="px-[30px]">
              <div class="bg-[url(@/assets/imgs/screen/dt-tc.png)] bg-[length:100%_100%] h-[35px] pl-[30px]">
                <span class="c-white text-[20px]">项目进度</span>
              </div>
            </div>
            <div class="flex px-[5px] mt-[40px]">
              <div class="flex pl-[20px]">
                <div v-for="(v, i) in projectProgressData" :key="v.dictionaryCode" class="flex flex-col items-center relative">
                  <div class="flex items-center w-full">
                    <div
                      class="h-[2px] flex-1"
                      :class="[{'opacity-0': i === 0}, dataInfo.projectProgress.includes(v.dictionaryCode) ? 'bg-[#E6C365]' : 'bg-[#588FDA]']">
                    </div>
                    <div v-if="dataInfo.projectProgress.includes(v.dictionaryCode)" class="border-[2px] border-solid border-[#E6C365] w-[22px] h-[22px] rounded-full flex justify-center items-center">
                      <div class="w-[14px] h-[14px] bg-[#E6C365] rounded-full"></div>
                    </div>
                    <div v-else class="border-[2px] border-solid border-transparent w-[22px] h-[22px] rounded-full flex justify-center items-center">
                      <div class="w-[16px] h-[16px] bg-[#588FDA] rounded-full"></div>
                    </div>
                    <div
                      class="h-[2px] flex-1"
                      :class="[{'opacity-0': i === projectProgressData.length - 1}, dataInfo.projectProgress.includes(v.dictionaryCode) ? 'bg-[#E6C365]' : 'bg-[#588FDA]']">
                    </div>
                  </div>
                  <div class="border-[1px] border-dashed border-[#E6C365] h-[35px]" :class="{ 'opacity-0': !dataInfo.projectProgress.includes(v.dictionaryCode) }"></div>
                  <div
                    class="mx-[20px] border-[1px] border-solid bg-opacity-75 rounded-[4px] c-white px-[20px] py-[5px]"
                    :class="[dataInfo.projectProgress.includes(v.dictionaryCode) ? 'border-[#E6C365] label-selected' : 'border-[#6DADFF] label-no-selected']"
                  >
                    <span>{{ v.dictionaryName }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex justify-end px-[30px]">
              <el-icon color="#4596FF" :size="26" class="cursor-pointer"><DArrowRight /></el-icon>
            </div>
          </div>
          <div class="bg-[url(@/assets/imgs/screen/card-border-9.png)] bg-[length:100%_100%] h-[251px] mt-[20px] relative flex justify-between items-center px-[56px]">
            <span class="absolute top-[-15px] left-4 right-8 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">核心指标</span>
            <div class="w-[159px] h-[137px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
              <div class="flex items-end alibaba-semiBold mt-2 c-white">
                <span class="text-[26px]">{{ dataInfo.projectInvestment || 0 }}</span>
              </div>
              <span class="c-[#E6C365] text-lg alibaba-semiBold">投资规模</span>
            </div>
            <div class="w-[159px] h-[137px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
              <div class="flex items-end alibaba-semiBold mt-2 c-white">
                <span class="text-[26px]">{{ dataInfo.projectArea || 0 }}</span>
              </div>
              <span class="c-[#E6C365] text-lg alibaba-semiBold">用地面积</span>
            </div>
            <div class="w-[159px] h-[137px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
              <div class="flex items-end alibaba-semiBold mt-2 c-white">
                <span class="text-[26px]">{{ dataInfo.projectNumber || 0 }}</span>
              </div>
              <span class="c-[#E6C365] text-lg alibaba-semiBold">从业人数</span>
            </div>
            <div class="w-[159px] h-[137px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
              <div class="flex items-end alibaba-semiBold mt-2 c-white">
                <span class="text-[26px]">{{ dataInfo.projectEnergy || 0 }}</span>
              </div>
              <span class="c-[#E6C365] text-lg alibaba-semiBold">能源消耗</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </z-screen-container>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import Header from './components/Header.vue'
import SCard from '@/views/Screen/components/SCard.vue'
import ScreenApi from '@/api/screen.ts'
import { useRoute } from 'vue-router'
import DictApI from '@/api/dict.ts'
import { DArrowRight } from '@element-plus/icons-vue'

const route = useRoute()

const loading = ref<boolean>(false)

const projectImgList = computed(() => {
  return dataInfo.value.projectImgList?.map((it:any) => import.meta.env.VITE_STATIC_ASSETS_URL + it.fileUrl)
})

const classNames = computed(() => {
  return dataInfo.value.classList?.map((it:any) => it.className).join(',')
})

const investData = ref<any[]>([])
const investType = computed(() => {
  return investData.value.find((it:any) => it.dictionaryCode === dataInfo.value.investType)?.dictionaryName
})

const projectProgressData = ref<any[]>([])

const dataInfo = ref<any>({})
const getData = () => {
  ScreenApi.getKeyProjectDetail(route.query.id as string).then(res => {
    dataInfo.value = {
      ...res.data,
      projectProgress:  res.data.projectProgress.split(',')
    }
  })
}

onMounted(async () => {
  investData.value = await DictApI.getDictItemsByCode('investTypes').then((res) => res.data)
  projectProgressData.value = await DictApI.getDictItemsByCode('projectProgress').then((res) => res.data)
  getData()
})
</script>

<style scoped lang="scss">
.label-selected{
  background: linear-gradient( 270deg, rgba(230,195,101,0.25) 0%, #E6C365 100%);
}
.label-no-selected{
  background: linear-gradient( 270deg, rgba(9,53,116,0.74) 0%, rgba(34,116,243,0.8) 100%);
}
:deep(.el-carousel__button) {
  background: #3474C9;
  opacity: 1;
}
:deep(.el-carousel__indicator.is-active button) {
  background: #E6C365;
}
</style>
