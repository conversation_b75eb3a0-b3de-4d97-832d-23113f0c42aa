<template>
  <div class="absolute h-full w-full">
    <TMapView
      class="z-1"
      ref="mapRef"
      :options="mapOptions"
      @ready="onMapReady"
    />

    <Classify ref="classifyRef" @change-classify="onChangeClassify" />

    <ToolBox @map-event="mapEvent" />

    <div class="absolute z-1 left-0 top-0 bottom-0 w-[670px] left-bg pointer-events-none"></div>
    <div class="absolute z-1 right-0 top-0 bottom-0 w-[670px] right-bg pointer-events-none"></div>

    <z-dynamic-route
      ref="projectDetailsRouteRef"
      :route="{
        path: '/projectDetails',
        viewPath: '@/views/Screen/modules/KeyProjectModule/pages/projectDetails.vue'
      }"
    />
  </div>
</template>

<script setup lang="ts">
import {
  TAnchorPosition,
  TGeoJSONLayer,
  TMap,
  TMapView,
  TSvgOverlay
} from '@/map'
import { ref } from 'vue'
import { BJ_CENTER_POINT } from '@/config/default-settings.ts'
import ToolBox from './components/ToolBox.vue'
import geoJsonData from '@/map/assets/geo/baoji.json'
import Classify from './components/Classify.vue'
import { ElLoading } from 'element-plus'
import ScreenApi from '@/api/screen.ts'

const projectDetailsRouteRef = ref()

const classifyRef = ref<InstanceType<typeof Classify> | null>(null)
const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const classifyIds = ref<number[]>([])
const onChangeClassify = (ids: number[]) => {
  classifyIds.value = ids
  getMapData()
}

const mapOptions = {
  center: {
    lng: BJ_CENTER_POINT.lng,
    lat: BJ_CENTER_POINT.lat,
  },
  zoom: 8.5,
  minZoom: 3,
  maxZoom: 18,
  search: {
    show: false
  }
}

const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')

  drawLayer()
  // 加载分类树
  classifyRef.value?.loadData()
}

const mapEvent = (data: any) => {
  const { event } = data
  if (event === 'zoomIn') {
    mapRef.value?.zoomIn()
  }
  if (event === 'zoomOut') {
    mapRef.value?.zoomOut()
  }
  if (event === 'resetView') {
    mapRef.value?.resetView()
  }
}

const drawLayer = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  const geoJSONLayer = new TGeoJSONLayer(map, {
    drilldown: {
      enable: true,
      maxDepth: 1,
      inactiveStyle: {
        opacity: 0,
        fillOpacity: 0,
      },
    },
    label: {
      show: true,
      offset: [0, 0],
      style: {
        fontSize: '12px',
        color: '#ffffff',
        fontWeight: 'bold',
        padding: '0px',
        border: 'none',
        boxShadow: 'none',
      },
    },
    onMouseOver(polygon) {
      polygon.setFillColor('#6FF9ED')
    },
    onMouseOut(polygon) {
      polygon.setFillColor('#5CA0FA')
    }
  })
  geoJSONLayer?.load(geoJsonData as any)
}

const drawPoint = (list: any[]) => {
  const map = mapRef.value?.getMap()
  if (!map) return
  if(!list?.length) return
  list.forEach(item => {
    if(item.projectLocation) {
      const location = JSON.parse(item.projectLocation)
      const circleSvg = new TSvgOverlay({
        position: location,
        iconName: `marker-${item.thematicClassIcon}`,
        size: [35, 50],
        zIndexOffset: 1000,
        anchor: TAnchorPosition.BOTTOM_CENTER,
        events: {
          click: () => {
            projectDetailsRouteRef.value?.navigate({
              query: {
                id: item.projectId
              }
            })
          }
        }
      })
      map.addOverlay(circleSvg)
    }
  })
}

const getMapData = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  map.clearOverlays()
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  ScreenApi.getKeyProjectPointDataList({
    thematicClassIds: classifyIds.value?.toString() || undefined
  }).then(res => {
    drawPoint(res?.data || [])
  }).finally(() => {
    loading.close()
  })
}
</script>

<style scoped lang="scss">
.left-bg{
  background: linear-gradient(90deg, rgba(3,21,50,0.3) 0%, rgba(26,58,110,0) 100%);
}
.right-bg{
  background: linear-gradient(90deg, rgba(26,58,110,0) 0%, rgba(3,21,50,0.3) 100%);
}
</style>
