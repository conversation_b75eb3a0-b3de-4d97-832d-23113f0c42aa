<template>
  <div class="absolute w-full h-full">
    <TMapView
      class="z-1"
      ref="mapRef"
      :options="mapOptions"
      @ready="onMapReady"
    />
    <DataCard v-show="chartShow" ref="dataCardRef" :tId="tId" />
    <ToolBox
      @map-event="mapEvent"
      @change-chart-show="(bool:boolean) => chartShow = bool"
    />
    <Classify ref="classifyRef" :tId="tId" @change-classify="onChangeClassify" />

    <div class="absolute z-1 left-0 top-0 bottom-0 w-[670px] left-bg pointer-events-none"></div>
    <div class="absolute z-1 right-0 top-0 bottom-0 w-[670px] right-bg pointer-events-none"></div>

    <z-dynamic-route
      ref="productDetailsRef"
      :route="{
        path: '/productDetails',
        viewPath: '@/views/Screen/modules/FeaturedProductsModule/pages/ProductDetails.vue'
      }"
    />
    <z-dynamic-route
      ref="regionDetailsRef"
      :route="{
        path: '/regionDetails',
        viewPath: '@/views/Screen/modules/FeaturedProductsModule/pages/RegionDetails.vue'
      }"
    />
  </div>
</template>

<script setup lang="ts">
import {
  TAnchorPosition,
  TAnnotationMarker,
  TGeoJSONLayer,
  TMap,
  TMapView,
  TSvgOverlay
} from '@/map'
import { inject, ref } from 'vue'
import { BJ_CENTER_POINT } from '@/config/default-settings.ts'
import DataCard from './components/DataCard.vue'
import ToolBox from './components/ToolBox.vue'
import Classify from './components/Classify.vue'
import ScreenApi from '@/api/screen.ts'
import geoJsonData from '@/map/assets/geo/baoji.json'
import { ElLoading } from 'element-plus'
import MapPop from './components/MapPop.vue'
import { MAP_POP_CONFIG } from '@/views/Screen/utils/conts.ts'
import { ScreenStateKey } from '@/views/Screen/utils/injection.ts'

const screenState = inject(ScreenStateKey)
if (!screenState) throw new Error('screenState must be provided')
const { tId } = screenState

const chartShow = ref<boolean>(true)

const productDetailsRef = ref()
const regionDetailsRef = ref()

const classifyRef = ref<InstanceType<typeof Classify> | null>(null)
const dataCardRef = ref<InstanceType<typeof DataCard> | null>(null)
const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const onChangeClassify = (ids: number[]) => {
  classifyIds.value = ids
  getMapData()
}

const mapOptions = {
  center: {
    lng: BJ_CENTER_POINT.lng,
    lat: BJ_CENTER_POINT.lat,
  },
  zoom: 8.5,
  minZoom: 3,
  maxZoom: 18,
  search: {
    show: false
  }
}

const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')
  // 绘制区域轮廓
  drawLayer()
  // 加载分类树
  classifyRef.value?.loadData()
  // 卡片统计
  dataCardRef.value?.loadData()
}

const mapEvent = (data: any) => {
  const { event } = data
  if (event === 'zoomIn') {
    mapRef.value?.zoomIn()
  }
  if (event === 'zoomOut') {
    mapRef.value?.zoomOut()
  }
  if (event === 'resetView') {
    mapRef.value?.resetView()
  }
}

const drawLayer = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  const geoJSONLayer = new TGeoJSONLayer(map, {
    drilldown: {
      enable: true,
      maxDepth: 1,
      inactiveStyle: {
        opacity: 0,
        fillOpacity: 0,
      },
    },
    label: {
      show: true,
      offset: [0, 0],
      style: {
        fontSize: '12px',
        color: '#ffffff',
        fontWeight: 'bold',
        padding: '0px',
        border: 'none',
        boxShadow: 'none',
      },
    },
    onClick(_polygon, feature) {
      const regionCode = MAP_POP_CONFIG.find(it => it.areaName === feature.properties.name)
      regionDetailsRef.value?.navigate({
        query: {
          tid: tId.value,
          code: regionCode?.areaCode,
          name: regionCode?.areaName
        }
      })
    },
    onMouseOver(polygon) {
      polygon.setFillColor('#6FF9ED')
    },
    onMouseOut(polygon) {
      polygon.setFillColor('#5CA0FA')
    }
  })
  geoJSONLayer?.load(geoJsonData as any)
}

const drawPoint = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  mapData.value?.forEach(item => {
    if(item.productLocation) {
      const thematicClassIcon = item.thematicClassIcon ? item.thematicClassIcon.split(',')[0] : ''
      const location = JSON.parse(item.productLocation)
      const { position } = location[0]
      const circleSvg = new TSvgOverlay({
        position: [position[0], position[1]],
        iconName: `marker-${thematicClassIcon}`,
        size: [35, 50],
        zIndexOffset: 1000,
        anchor: TAnchorPosition.BOTTOM_CENTER,
        events: {
          click: () => {
            productDetailsRef.value?.navigate({
              query: {
                id: item.productId
              }
            })
          }
        }
      })
      map.addOverlay(circleSvg)

      if(item.hasShow === '0') {
        const pop = MAP_POP_CONFIG.find(it => it.areaCode === item.regionCode)
        const a = new TAnnotationMarker({
          position: pop?.location as [number, number],
          zIndexOffset: 900,
          markerOptions: {
            size: 80,
            svgMarkup: TSvgOverlay.generateRippleMarkup(80, { strokeWidth: 2 }),
            // label: 'svg 标记',
            labelRemoveable: true,
            iconStyle: {
              // fill: 'red',
            },
          },
          lineOptions: {
            length: pop?.length,
            angle: pop?.angle,
            style: {
              color: '#EBE02D',
              width: 2,
              dashed: true
            },
          },
          panelOptions: {
            style: {
              background: 'rgba(255, 255, 255, 0)',
              boxShadow: 'none',
              padding: '0',
            },
            content: MapPop,
            offset: pop?.offset ?? [0, 0],
            anchorPosition: 'auto',
            contentProps: {
              item: item
            }
          },
        })
        map.addOverlay(a)
      }
    }
  })
}
const classifyIds = ref<number[]>([])
const mapData = ref<any[]>([])
const getMapData = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  map.clearOverlays()
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  ScreenApi.getFeatureProductDataList({
    thematicClassIds: classifyIds.value?.toString() || undefined
  }).then(res => {
    mapData.value = res?.data || []
    drawPoint()
  }).finally(() => {
    loading.close()
  })
}
</script>

<style scoped lang="scss">
.left-bg{
  background: linear-gradient(90deg, rgba(3,21,50,0.50) 0%, rgba(26,58,110,0) 100%);
}
.right-bg{
  background: linear-gradient(90deg, rgba(26,58,110,0) 0%, rgba(3,21,50,0.5) 100%);
}
</style>
