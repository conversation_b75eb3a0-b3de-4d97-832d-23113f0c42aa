<template>
  <div class="w-[425px] h-[310px] bg-[url(@/assets/imgs/screen/card-border-5.png)] bg-[length:100%_100%] relative">
    <span class="absolute top-[-5px] left-4 right-4 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">近5年年产量发展趋势</span>
    <div ref="chartRef" class="h-full"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'

const props = defineProps<{
  dataInfo: any
}>()

type EChartsOption = echarts.EChartsOption

const chartRef = ref()
let chart: any = null
const initChart = () => {
  chart = echarts.init(chartRef.value)
  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '7%',
      right: '7%',
      bottom: '8%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(228, 252, 255, 0.6)',
        fontSize: 14
      },
      axisLine: {
        lineStyle: {
          color: '#3776A1'
        }
      },
      data: props.dataInfo?.productOutputList?.map((it: any) => it.dataYear) || []
    },
    yAxis: {
      type: 'value',
      // name: '万斤',
      axisLine: {
        lineStyle: {
          color: 'rgba(228, 252, 255, 0.6)'
        }
      },
      axisLabel: {
        color: 'rgba(228, 252, 255, 0.6)',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#3776A1'
        }
      }
    },
    series: [
      {
        type: 'line',
        smooth: true,
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          color: '#169CFF',
          borderColor: '#0E2D5D',
          borderWidth: 1
        },
        label: {
          show: true,
          position: "top",
          color: '#169CFF',
          fontSize: 14
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: `rgba(22, 156, 255, 0.6)`
              },
              {
                offset: 1,
                color: `rgba(22, 229, 255, 0)`
              },
            ],
            false
          )
        },
        data: props.dataInfo?.productOutputList?.map((it: any) => it.dataValue) || []
      }
    ]
  }
  chart.setOption(option)
}

watch(() => props.dataInfo, () => {
  initChart()
},  { deep: true })

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
  }
})
</script>

<style scoped lang="scss">

</style>
