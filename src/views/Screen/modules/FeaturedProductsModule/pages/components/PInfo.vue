<template>
  <div class="w-[425px] h-[623px] bg-[url(@/assets/imgs/screen/card-border-3.png)] bg-[length:100%_100%] relative">
    <span class="absolute top-[-5px] left-4 right-4 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">产品信息</span>
    <div class="px-[20px] pt-[40px]">
      <div class="flex items-center px-6">
        <z-svg :name="`marker-${classIcon}`" width="29px" height="43px"></z-svg>
        <span class="c-[#E6C365] text-[24px] ml-3 alibaba-semiBold">{{ dataInfo.productName }}</span>
      </div>
      <div class="w-full h-[34px] bg-[url(@/assets/imgs/screen/db-border.png)] bg-[length:100%_100%] flex items-center mt-5">
        <span class="c-white text-lg ml-8">分类</span>
      </div>
      <div class="flex pl-8 mt-2 c-white">{{ dataInfo.className }}</div>
      <div class="w-full h-[34px] bg-[url(@/assets/imgs/screen/db-border.png)] bg-[length:100%_100%] flex items-center mt-5">
        <span class="c-white text-lg ml-8">简介</span>
      </div>
      <div class="p-4 c-white overflow-auto max-h-[350px]" v-html="dataInfo.productIntroduction"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  dataInfo: any
}>()

const classIcon = computed(() => {
  return props.dataInfo.classIcon ? props.dataInfo.classIcon.split(',')[0] : ''
})
</script>

<style scoped lang="scss">

</style>
