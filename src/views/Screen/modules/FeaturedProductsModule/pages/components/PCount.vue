<template>
  <div class="w-[425px] h-[310px] bg-[url(@/assets/imgs/screen/card-border-4.png)] bg-[length:100%_100%] relative">
    <span class="absolute top-[-5px] left-4 right-4 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">核心指标</span>
    <div class="p-[36px]">
      <div class="w-[349px] h-[111px] bg-[url(@/assets/imgs/screen/gmbg.png)] bg-[length:100%_100%] flex flex-col justify-center items-center">
        <span class="c-white text-lg">种植/养殖规模</span>
        <div class="flex items-end alibaba-semiBold c-[#C6E3FF] mt-2 font-italic">
          <span class="text-[30px]">{{ productScale.dataValue || 0 }}</span>
          <span class="ml-1 mb-[6px] text-sm">{{ productScale.dataUnitName }}</span>
        </div>
      </div>
      <div class="flex justify-between">
        <div class="w-[159px] h-[133px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
          <div class="flex items-end alibaba-semiBold c-white mt-2">
            <span class="text-[26px]">{{ productValue.dataValue || 0 }}</span>
            <span class="ml-1 mb-[6px] text-xs">{{ productValue.dataUnitName }}</span>
          </div>
          <span class="c-[#E6C365] text-lg">年产值</span>
        </div>
        <div class="w-[159px] h-[133px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
          <div class="flex items-end alibaba-semiBold c-white mt-2">
            <span class="text-[26px]">{{ productOutput.dataValue || 0 }}</span>
            <span class="ml-1 mb-[6px] text-xs">{{ productOutput.dataUnitName }}</span>
          </div>
          <span class="c-[#E6C365] text-lg">年产量</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  dataInfo: any,
  dataYear: string
}>()

const productScale = computed(() => {
  return props.dataInfo?.productScaleList?.find((it: any) => it.dataYear === props.dataYear) || {}
})
const productOutput = computed(() => {
  return props.dataInfo?.productOutputList?.find((it: any) => it.dataYear === props.dataYear) || {}
})
const productValue = computed(() => {
  return props.dataInfo?.productValueList?.find((it: any) => it.dataYear === props.dataYear) || {}
})
</script>

<style scoped lang="scss">

</style>
