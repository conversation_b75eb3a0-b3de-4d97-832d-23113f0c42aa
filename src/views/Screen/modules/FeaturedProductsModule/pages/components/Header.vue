<template>
  <div
    class="h-[149px] absolute left-0 right-0 top-0 bg-[url(@/assets/imgs/screen/topbg-2.png)] bg-[length:100%_100%] z-1"
  >
    <div class="flex justify-between items-center pl-[36px] pr-[62px] pt-[25px] relative">
      <img src="@/assets/imgs/screen/back.png" alt="" class="w-[40px] h-[40px] cursor-pointer" @click="router.back()">
      <div class="flex gap-x-10 items-center">
        <span class="c-white text-[30px] alibaba-semiBold">{{ dataInfo.productName }}</span>
        <img src="@/assets/imgs/screen/xzjh.png" alt="" class="w-[65px] h-[10px]">
      </div>
      <el-date-picker
        class="w-[110px]! h-[40px]! border-1 border-solid border-[#6DADFF] rounded-1 custom-date"
        v-model="dataYear"
        type="year"
        placeholder="请选择年份"
        :editable="false"
        :clearable="false"
        format="YYYY"
        value-format="YYYY"
        @change="onChangeDate"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import dayjs from '@/utils/dayjs.ts'

const router = useRouter()

defineProps<{
  dataInfo: any
}>()

const emit = defineEmits<{s
  (e: 'change-year', values: string): void,
}>()

const dataYear = ref<string>(`${dayjs().year() - 1}`)
const onChangeDate = (val: any) => {
  dataYear.value = val
  emit('change-year', val)
}
emit('change-year', dataYear.value)
</script>

<style scoped lang="scss">
:deep(.el-input__wrapper) {
  border: none;
  background: transparent;
  color: #fff;
  box-shadow: none;
  background: linear-gradient( 270deg, rgba(9,53,116,0.74) 0%, rgba(34,116,243,0.8) 100%);
  .el-input__inner {
    color: #fff;
    font-size: 20px;
  }
  .el-input__prefix{
    color: #fff;
    font-size: 20px;
  }
}
</style>
