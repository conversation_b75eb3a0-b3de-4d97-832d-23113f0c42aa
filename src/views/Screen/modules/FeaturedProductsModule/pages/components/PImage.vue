<template>
  <div class="w-[425px] h-[310px] bg-[url(@/assets/imgs/screen/card-border-4.png)] bg-[length:100%_100%] relative">
    <span class="absolute top-[-5px] left-4 right-4 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">产品图片</span>
    <div class="px-[20px] pt-[32px]">
      <el-carousel trigger="click" height="250px" indicator-position="outside">
        <el-carousel-item v-for="(v, i) in productImg" :key="i">
          <el-image
            :src="v"
            :preview-src-list="productImg"
            :initial-index="i"
            fit="cover"
            preview-teleported
            class="w-full h-full rounded-6"
          />
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  dataInfo: any
}>()

const productImg = computed(() => {
  if (!props.dataInfo.productImg) return []
  return JSON.parse(props.dataInfo.productImg).map((it: any) => import.meta.env.VITE_STATIC_ASSETS_URL + it.fileUrl)
})
</script>

<style scoped lang="scss">
:deep(.el-carousel__button) {
  background: #3474C9;
  opacity: 1;
}
:deep(.el-carousel__indicator.is-active button) {
  background: #E6C365;
}
</style>
