<template>
  <div class="h-full mx-[24px] relative border-1 border-solid border-[#3D9DFF] bg-[#0E2C5D] rounded-6">
    <img src="@/assets/imgs/screen/j-lt.png" alt="" class="w-[29px] h-[28px] absolute left-[-2px] top-[-2px] z-2">
    <img src="@/assets/imgs/screen/j-rt.png" alt="" class="w-[29px] h-[28px] absolute right-[-3px] top-[-3px] z-2">
    <img src="@/assets/imgs/screen/j-lb.png" alt="" class="w-[29px] h-[28px] absolute left-[-2px] bottom-[-2px] z-2">
    <img src="@/assets/imgs/screen/j-rb.png" alt="" class="w-[29px] h-[28px] absolute right-[-2px] bottom-[-2px] z-2">
    <div class="flex flex-col absolute top-[38px] left-[38px] gap-y-3 z-2">
      <div class="w-[160px] h-[52px] bg-[url(@/assets/imgs/screen/hx-1.png)] bg-[length:100%_100%] flex items-center justify-center">
        <span class="text-lg c-white alibaba-semiBold font-italic ml-8">重点生产企业</span>
      </div>
      <div class="w-[160px] h-[67px] bg-[url(@/assets/imgs/screen/hx-4.png)] bg-[length:100%_100%] flex items-center justify-center">
        <span class="text-lg c-white alibaba-semiBold font-italic ml-8">重点种植园区</span>
      </div>
    </div>
    <TMapView
      class="rounded-6 overflow-hidden z-1"
      ref="mapRef"
      :lazyDelay="500"
      :options="mapOptions"
      @ready="onMapReady"
    />

    <z-dynamic-route
      ref="enterpriseRouteRef"
      :route="{
        path: '/enterpriseDetails',
        viewPath: '@/views/Screen/modules/IndustryChainModule/pages/enterpriseDetails/enterpriseDetails.vue'
      }"
    />
  </div>
</template>

<script setup lang="ts">
import { TAnchorPosition, TGeoJSONLayer, TMap, TMapView, TSvgOverlay } from '@/map'
import { BJ_CENTER_POINT } from '@/config/default-settings.ts'
import { ref } from 'vue'
import geoJsonData from '@/map/assets/geo/baoji.json'

const props = defineProps<{
  dataInfo: any
}>()

const enterpriseRouteRef = ref()

const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const mapOptions = {
  center: {
    lng: BJ_CENTER_POINT.lng,
    lat: BJ_CENTER_POINT.lat,
  },
  zoom: 8.5,
  minZoom: 3,
  maxZoom: 18,
  search: {
    show: false
  }
}

const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')
  // 绘制区域轮廓
  drawLayer()

  drawPoint()
}

const drawLayer = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  const geoJSONLayer = new TGeoJSONLayer(map, {
    drilldown: {
      enable: true,
      maxDepth: 1,
      inactiveStyle: {
        opacity: 0,
        fillOpacity: 0,
      },
    },
    label: {
      show: true,
      offset: [0, 0],
      style: {
        fontSize: '12px',
        color: '#ffffff',
        fontWeight: 'bold',
        padding: '0px',
        border: 'none',
        boxShadow: 'none',
      },
    },
    onMouseOver(polygon) {
      polygon.setFillColor('#6FF9ED')
    },
    onMouseOut(polygon) {
      polygon.setFillColor('#5CA0FA')
    }
  })
  geoJSONLayer?.load(geoJsonData as any)
}

const drawPoint = () => {
  const map = mapRef.value?.getMap()
  if (!map) return

  if(props.dataInfo?.productLocation) {
    const productLocation = JSON.parse(props.dataInfo?.productLocation)
    productLocation.forEach((it: any) => {
      const circleSvg = new TSvgOverlay({
        position: it.position,
        iconName: `marker-zdyq`,
        size: [32, 61],
        zIndexOffset: 9000,
        anchor: TAnchorPosition.BOTTOM_CENTER
      })
      map.addOverlay(circleSvg)
    })
  }

  if(props.dataInfo?.companyList?.length) {
    props.dataInfo.companyList.forEach((it: any) => {
      if(it.latitudeLongitude) {
        const circleSvg = new TSvgOverlay({
          position: JSON.parse(it.latitudeLongitude),
          iconName: `marker-zdqy`,
          size: [31, 40],
          zIndexOffset: 9000,
          anchor: TAnchorPosition.BOTTOM_CENTER,
          events: {
            click: () => {
              enterpriseRouteRef.value?.navigate({
                query: {
                  id: it.companyId
                }
              })
            }
          }
        })
        map.addOverlay(circleSvg)
      }
    })
  }

}
</script>

<style scoped lang="scss">

</style>
