<template>
  <z-screen-container :loading="loading" :wrapperStyle="{ background: '#1A3A6E' }">
    <div
      class="h-[149px] absolute left-0 right-0 top-0 bg-[url(@/assets/imgs/screen/topbg-2.png)] bg-[length:100%_100%] z-2"
    >
      <img src="@/assets/imgs/screen/back.png" alt="" class="w-[40px] h-[40px] absolute left-[36px] top-[25px] z-2 cursor-pointer" @click="router.back()">
      <div class="flex items-center justify-center gap-x-10 pl-[36px] pr-[62px] pt-[25px] relative">
        <span class="c-white text-[30px] alibaba-semiBold">{{ route.query.name }}详情</span>
        <img src="@/assets/imgs/screen/xzjh.png" alt="" class="w-[65px] h-[10px]">
      </div>
    </div>
    <TMapView
      class="z-1"
      ref="mapRef"
      :lazy-delay="10"
      :options="mapOptions"
      @ready="onMapReady"
    />
    <DataCard v-show="chartShow" ref="dataCardRef" :tId="route.query.tid as string" />
    <ToolBox
      @map-event="mapEvent"
      @change-chart-show="(bool:boolean) => chartShow = bool"
    />
    <Classify ref="classifyRef" :tId="route.query.tid as string" @change-classify="onChangeClassify" />

    <div class="absolute z-1 left-0 top-0 bottom-0 w-[670px] left-bg pointer-events-none"></div>
    <div class="absolute z-1 right-0 top-0 bottom-0 w-[670px] right-bg pointer-events-none"></div>

    <z-dynamic-route
      ref="productDetailsRef"
      :route="{
        path: '/productDetails',
        viewPath: '@/views/Screen/modules/FeaturedProductsModule/pages/ProductDetails.vue'
      }"
    />
    <z-dynamic-route
      ref="regionDetailsRef"
      :route="{
        path: '/regionDetails',
        viewPath: '@/views/Screen/modules/FeaturedProductsModule/pages/RegionDetails.vue'
      }"
    />
  </z-screen-container>
</template>

<script setup lang="ts">
import {
  TAnchorPosition,
  TAnnotationMarker,
  TGeoJSONLayer,
  TMap,
  TMapView,
  TSvgOverlay
} from '@/map'
import { ref } from 'vue'
import DataCard from '../components/DataCard.vue'
import ToolBox from '../components/ToolBox.vue'
import Classify from '../components/Classify.vue'
import ScreenApi from '@/api/screen.ts'
import MapPop from '../components/MapPop.vue'
import { MAP_POP_CONFIG } from '@/views/Screen/utils/conts.ts'
import { useRoute, useRouter } from 'vue-router'
import geojsonData from '@/map/assets/geo/baoji.json'

const router = useRouter()
const route = useRoute()

const loading = ref<boolean>(false)
const chartShow = ref<boolean>(true)

const productDetailsRef = ref()
const regionDetailsRef = ref()

const classifyRef = ref<InstanceType<typeof Classify> | null>(null)
const dataCardRef = ref<InstanceType<typeof DataCard> | null>(null)
const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const onChangeClassify = (ids: number[]) => {
  classifyIds.value = ids
  getMapData()
}

const location = MAP_POP_CONFIG.find(it => it.areaCode === route.query.code)?.location || []
const mapOptions = {
  center: {
    lng: location[0] as number,
    lat: location[1] as number,
  },
  zoom: 10,
  minZoom: 3,
  maxZoom: 18,
  search: {
    show: false
  }
}

const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')
  // 绘制区域轮廓
  drawLayer()
  // 加载分类树
  classifyRef.value?.loadData()
  // 卡片统计
  dataCardRef.value?.loadData()
}

const mapEvent = (data: any) => {
  const { event } = data
  if (event === 'zoomIn') {
    mapRef.value?.zoomIn()
  }
  if (event === 'zoomOut') {
    mapRef.value?.zoomOut()
  }
  if (event === 'resetView') {
    mapRef.value?.resetView()
  }
}

const drawLayer = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  const geoJSONLayer = new TGeoJSONLayer(map, {
    drilldown: {
      enable: true,
      maxDepth: 1,
      inactiveStyle: {
        opacity: 0,
        fillOpacity: 0,
      },
    },
    label: {
      show: true,
      offset: [0, 0],
      style: {
        fontSize: '12px',
        color: '#ffffff',
        fontWeight: 'bold',
        padding: '0px',
        border: 'none',
        boxShadow: 'none',
      },
    },
    onClick(_polygon, feature) {
      const regionCode = MAP_POP_CONFIG.find(it => it.areaName === feature.properties.name)?.areaCode
      regionDetailsRef.value?.navigate({
        query: {
          code: regionCode
        }
      })
    },
    onMouseOver(polygon) {
      polygon.setFillColor('#6FF9ED')
    },
    onMouseOut(polygon) {
      polygon.setFillColor('#5CA0FA')
    }
  })
  geoJSONLayer?.loadByName(geojsonData as any, route.query.name as string)
}

const drawPoint = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  mapData.value?.forEach(item => {
    if(item.productLocation) {
      const location = JSON.parse(item.productLocation)
      const { position } = location[0]
      const circleSvg = new TSvgOverlay({
        position: [position[0], position[1]],
        iconName: `marker-${item.thematicClassIcon}`,
        size: [35, 50],
        zIndexOffset: 1000,
        anchor: TAnchorPosition.BOTTOM_CENTER,
        events: {
          click: () => {
            productDetailsRef.value?.navigate({
              query: {
                id: item.productId
              }
            })
          }
        }
      })
      map.addOverlay(circleSvg)

      if(item.hasShow === '0') {
        const pop = MAP_POP_CONFIG.find(it => it.areaCode === item.regionCode)
        const a = new TAnnotationMarker({
          position: pop?.location as [number, number],
          zIndexOffset: 900,
          markerOptions: {
            size: 80,
            svgMarkup: TSvgOverlay.generateRippleMarkup(80, { strokeWidth: 2 }),
            // label: 'svg 标记',
            labelRemoveable: true,
            iconStyle: {
              // fill: 'red',
            },
          },
          lineOptions: {
            length: pop?.length,
            angle: pop?.angle,
            style: {
              color: '#EBE02D',
              width: 2,
              dashed: true
            },
          },
          panelOptions: {
            style: {
              background: 'rgba(255, 255, 255, 0)',
              boxShadow: 'none',
              padding: '0',
            },
            content: MapPop,
            offset: pop?.offset ?? [0, 0],
            anchorPosition: 'auto',
            contentProps: {
              item: item
            }
          },
        })
        map.addOverlay(a)
      }
    }
  })
}
const classifyIds = ref<number[]>([])
const mapData = ref<any[]>([])
const getMapData = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  map.clearOverlays()
  loading.value  = true
  ScreenApi.getFeatureProductDataList({
    regionCode: route.query.code as string,
    thematicClassIds: classifyIds.value?.toString() || undefined
  }).then(res => {
    mapData.value = res?.data || []
    drawPoint()
  }).finally(() => {
    loading.value = false
  })
}
</script>

<style scoped lang="scss">
.left-bg{
  background: linear-gradient(90deg, rgba(3,21,50,0.50) 0%, rgba(26,58,110,0) 100%);
}
.right-bg{
  background: linear-gradient(90deg, rgba(26,58,110,0) 0%, rgba(3,21,50,0.5) 100%);
}
</style>
