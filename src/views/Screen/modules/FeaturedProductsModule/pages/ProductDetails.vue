<template>
  <z-screen-container :loading="loading" :wrapperStyle="{ background: '#1A3A6E' }">
    <Header :data-info="dataInfo" @change-year="changeYear" />
    <div class="h-[calc(100%-120px)] box-border mt-[120px] px-[24px] pb-[24px] flex relative z-2">
      <div class="flex flex-col justify-between">
        <PImage :data-info="dataInfo" />
        <PInfo :data-info="dataInfo" />
      </div>
      <div class="flex-1 pt-[5px] box-border">
        <Map :data-info="dataInfo" />
      </div>
      <div class="flex flex-col justify-between">
        <PCount :data-info="dataInfo" :data-year="dataYear" />
        <PLine1 :data-info="dataInfo" />
        <PLine2 :data-info="dataInfo" />
      </div>
    </div>
  </z-screen-container>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Header from './components/Header.vue'
import PImage from './components/PImage.vue'
import PInfo from './components/PInfo.vue'
import PCount from './components/PCount.vue'
import PLine1 from './components/PLine1.vue'
import PLine2 from './components/PLine2.vue'
import Map from './components/Map.vue'
import ScreenApi from '@/api/screen.ts'
import { useRoute } from 'vue-router'
import DictApI from '@/api/dict.ts'

const route = useRoute()

const loading = ref<boolean>(false)

const dataYear = ref<string>('')
const dataInfo = ref<any>({})
const getDetail = async () => {
  loading.value = true
  const unit = await DictApI.getDictItemsByCode('systemUnit').then((res) => res.data)
  const { data } = await ScreenApi.getFeatureProductDetail(route.query.id)
  function getUnitName(data: any[]) {
    return data?.map((item: any) => {
      return {
        ...item,
        dataUnitName: unit.find((it: any) => it.dictionaryCode === item.dataUnit)?.dictionaryName
      }
    }) || []
  }
  dataInfo.value = {
    ...data,
    productOutputList: getUnitName(data.productOutputList),
    productScaleList: getUnitName(data.productScaleList),
    productValueList: getUnitName(data.productValueList)
  }
  loading.value = false
}
getDetail()

const changeYear = async (year: string) => {
  dataYear.value = year
}
</script>

<style scoped lang="scss">

</style>
