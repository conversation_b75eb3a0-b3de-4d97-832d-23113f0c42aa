<template>
<div class="absolute right-[36px] top-[150px] z-2">
  <div
    class="w-[250px] h-[56px] box-border flex justify-between items-center px-[20px] bg-[#274474] bg-opacity-75 border-1 border-solid border-[#6DADFF] cursor-pointer"
    @click="isExpanded = !isExpanded"
  >
    <span class="c-white text-lg">产品分类</span>
    <el-icon color="#fff" :size="20">
      <ArrowUp v-if="isExpanded" />
      <ArrowDown v-else />
    </el-icon>
  </div>
  <transition name="el-zoom-in-top">
    <div v-show="isExpanded" class="w-[248px] classify-tree bg-[#274474] bg-opacity-75 border-1 border-solid border-[#6DADFF] border-t-0">
      <el-tree
        class="bg-transparent c-[#EDF8FF] text-lg py-3"
        :data="dataTree"
        show-checkbox
        node-key="thematicClassId"
        :highlight-current="false"
        :icon="ArrowRightBold"
        :default-checked-keys="checkedKeys"
        :props="{
          children: 'children',
          label: 'thematicClassName'
        }"
        @check="check"
      />
    </div>
  </transition>
</div>
</template>

<script setup lang="ts">
import { ArrowDown, ArrowRightBold, ArrowUp } from '@element-plus/icons-vue'
import { ref } from 'vue'
import ScreenApi from '@/api/screen.ts'

const props = defineProps<{
  tId: string | number
}>()

const emit = defineEmits<{
  (e: 'change-classify', values: number[]): void
}>()

const isExpanded = ref<boolean>(false)
const checkedKeys = ref<number[]>([])
const dataTree = ref<any[]>([])

const getCategoryTree = () => {
  ScreenApi.getThematicFeatureCategoryTree({
    thematicId: props.tId
  }).then(res => {
    dataTree.value = res.data
    checkedKeys.value = getAllIds(dataTree.value)
    emit('change-classify', checkedKeys.value)
  })
}
// 递归获取所有id
const getAllIds = (data: any) => {
  const ids: number[] = []
  data.forEach((item: any) => {
    ids.push(item.thematicClassId)
    if (item.children?.length) {
      ids.push(...getAllIds(item.children))
    }
  })
  return ids
}

const check = (_data: any, checked: any) => {
  checkedKeys.value = checked.checkedKeys
  emit('change-classify', checkedKeys.value)
}

defineExpose({
  loadData: getCategoryTree
})
</script>

<style scoped lang="scss">
.classify-tree{
  :deep(.el-tree-node__content:hover) {
    background: transparent;
  }
  :deep(.el-tree-node:focus>.el-tree-node__content) {
    background: transparent;
  }
  :deep(.el-tree-node__content) {
    height: 38px;
  }
  :deep(.el-tree-node__expand-icon) {
    color: #6DADFF;
    font-size: 16px;
  }
  :deep(.el-checkbox__input .el-checkbox__inner) {
    background: transparent;
    border-color: #6DADFF;
  }
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background: #007EF2;
    border-color: #007EF2;
  }
}
</style>
