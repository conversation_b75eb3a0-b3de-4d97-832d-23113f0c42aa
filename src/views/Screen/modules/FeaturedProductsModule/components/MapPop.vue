<template>
  <div class="w-[244px] h-[77px] box-border bg-[url(@/assets/imgs/screen/zxb-bg.png)] bg-[length:100%_100%] px-[17px] py-[10px]">
    <div class="flex items-center">
      <z-svg :name="`marker-${item.thematicClassIcon}`" width="20px" height="20px" class="pos-inherit!"></z-svg>
      <span class="c-white ml-1">{{ item.thematicClassName }}</span>
    </div>
    <span class="text-[20px] c-[#FFE8D0] ml-[20px]">{{ item.productName }}</span>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  item: any
}>()
</script>

<style scoped lang="scss">

</style>
