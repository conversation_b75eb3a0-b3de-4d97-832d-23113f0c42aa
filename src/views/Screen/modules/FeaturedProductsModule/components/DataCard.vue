<template>
  <div class="absolute left-[36px] top-[150px] bottom-[50px] z-2">
    <ZScrollMarquee
      :pause="dataList.length <= 7"
      :vertical="true"
      :pause-on-hover="true"
    >
    <div
      v-for="v in dataList"
      :key="v.thematicClassId"
      class="bg-[#001542] bg-opacity-40 flex flex-col pr-3 pb-1 w-[220px] mt-[40px]"
      :style="{ borderLeft: `2px solid ${v.border}` }"
    >
      <div class="flex items-center">
        <div class="w-[30px] h-[30px] flex justify-center items-center mr-2" :style="{ backgroundColor: v.bg }">
          <z-svg :name="`marker-${v.thematicClassIcon}`" width="20px" height="20px"></z-svg>
        </div>
        <span class="c-white text-lg">{{ v.thematicClassName }}</span>
      </div>
      <span class="youshe-bold text-[28px] c-[#C6E3FF] pl-[30px]" :style="{ color: v.color }">{{ v.count || 0 }}</span>
    </div>
    </ZScrollMarquee>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { Vue3Marquee } from 'vue3-marquee'

export default defineComponent({
  components: {
    ZScrollMarquee: Vue3Marquee
  }
})
</script>

<script setup lang="ts">
import { computed, ref } from 'vue'
import ScreenApi from '@/api/screen.ts'
import { useRoute } from 'vue-router'

const props = defineProps<{
  tId: string | number
}>()

const route = useRoute()

const COLORS = [
  { border: '#57ADFF', color: '#C6E3FF', bg: 'rgba(87, 173, 255, 0.3)' },
  { border: '#EC973E', color: '#FFE8D0', bg: 'rgba(244, 161, 75, 0.3)' },
  { border: '#4FD3DE', color: '#CCF2F5', bg: 'rgba(75, 231, 244, 0.3)' },
  { border: '#E3DA62', color: '#EDE8B2', bg: 'rgba(255, 240, 56, 0.3)' }
]

const dataList = computed(() => {
  return sumList.value.map((item: any, index: number) => {
    const color = COLORS[index % COLORS.length]
    return {
      ...item,
      border: color.border,
      color: color.color,
      bg: color.bg
    }
  })
})

const sumList = ref<any[]>([])
const getSumData = () => {
  ScreenApi.getFeatureClassifyStatistics({
    regionCode: route.query.code as string,
    thematicId: props.tId
  }).then((res) => {
    sumList.value = res.data
  })
}

defineExpose({
  loadData: getSumData
})

</script>

<style scoped lang="scss">

</style>
