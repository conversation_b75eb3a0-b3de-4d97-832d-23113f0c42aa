<template>
  <div class="absolute bottom-[30px] right-[30px] z-2 flex flex-col items-end">
    <transition name="el-zoom-in-bottom">
      <div v-show="isExpanded" class="border-1 border-solid border-[#6DADFF] bg-[#1A3A6E] bg-opacity-75 min-w-[233px] px-4 py-3">
        <div class="flex items-center">
          <img src="@/assets/imgs/screen/shuangjt.png" alt="" class="w-[14px] h-[9px]">
          <span class="c-white ml-2">地图操作</span>
        </div>
        <div class="flex mt-5 px-4 gap-3">
          <div class="w-[40px] h-[32px] rounded-[4px] bg-[#007EF2] bg-opacity-60 border-1 border-solid border-[#6DADFF] flex justify-center items-center cursor-pointer" @click="emit('map-event', { event: 'zoomIn' })">
            <img src="@/assets/imgs/screen/zoom-in-2.png" alt="" class="w-[30px] h-[30px]">
          </div>
          <div class="w-[40px] h-[32px] rounded-[4px] bg-[#007EF2] bg-opacity-60 border-1 border-solid border-[#6DADFF] flex justify-center items-center cursor-pointer" @click="emit('map-event', { event: 'zoomOut' })">
            <img src="@/assets/imgs/screen/zoom-out-2.png" alt="" class="w-[30px] h-[30px]">
          </div>
          <div class="w-[40px] h-[32px] rounded-[4px] bg-[#007EF2] bg-opacity-60 border-1 border-solid border-[#6DADFF] flex justify-center items-center cursor-pointer" @click="emit('map-event', { event: 'resetView' })">
            <img src="@/assets/imgs/screen/restoration-2.png" alt="" class="w-[30px] h-[30px]">
          </div>
        </div>
      </div>
    </transition>
    <div
      class="bg-[linear-gradient(270deg,rgba(9,53,126,0.74)_0%,rgba(32,116,216,0.8)_100%)] flex justify-between items-center px-3 py-2 border-1 border-solid box-border border-[#6DADFF] rounded-[1px] w-[150px] cursor-pointer"
      @click="isExpanded = !isExpanded">
      <img src="@/assets/imgs/screen/tool.png" alt="" class="w-[25px] h-[27px]">
      <span class="c-white text-lg">工具箱</span>
      <el-icon color="#fff" :size="20">
        <ArrowUp v-if="isExpanded" />
        <ArrowDown v-else />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { ref } from 'vue'

const emit = defineEmits<{
  (e: 'map-event', values: any): void
}>()

const isExpanded = ref<boolean>(false)
</script>

<style scoped lang="scss">
.custom-date{
  :deep(.el-input__wrapper) {
    border: none;
    background-color: transparent;
    box-shadow: none;
    .el-input__prefix{
      color: #ffffff;
    }
    .el-input__inner{
      color: #ffffff;
    }
  }
}
.classify-tree{
  :deep(.el-tree-node__content:hover) {
    background: transparent;
  }
  :deep(.el-tree-node:focus>.el-tree-node__content) {
    background: transparent;
  }
  :deep(.el-tree-node__content) {
    height: 38px;
  }
  :deep(.el-tree-node__expand-icon) {
    color: #6DADFF;
    font-size: 16px;
  }
  :deep(.el-checkbox__input .el-checkbox__inner) {
    background: transparent;
    border-color: #6DADFF;
  }
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background: #007EF2;
    border-color: #007EF2;
  }
}
</style>
