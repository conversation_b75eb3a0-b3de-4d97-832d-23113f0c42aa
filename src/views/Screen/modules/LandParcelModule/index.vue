<template>
  <div class="absolute h-full w-full">
    <TMapView
      class="z-1"
      ref="mapRef"
      :options="mapOptions"
      @ready="onMapReady"
    />

    <ToolBox @map-event="mapEvent" />

    <div class="absolute z-1 left-0 top-0 bottom-0 w-[670px] left-bg pointer-events-none"></div>
    <div class="absolute z-1 right-0 top-0 bottom-0 w-[670px] right-bg pointer-events-none"></div>

    <z-dynamic-route
      ref="parcelDetailsRouteRef"
      :route="{
        path: '/parcelDetails',
        viewPath: '@/views/Screen/modules/LandParcelModule/pages/parcelDetails.vue'
      }"
    />
  </div>
</template>

<script setup lang="ts">
import { TMap, TMapView, TFeatureLayer } from '@/map'
import { BJ_CENTER_POINT } from '@/config/default-settings.ts'
import { ref } from 'vue'
import ToolBox from './components/ToolBox.vue'
import ScreenApi from '@/api/screen.ts'
import dayjs from '@/utils/dayjs.ts'
import { parse as parseWKT } from 'terraformer-wkt-parser'

const parcelDetailsRouteRef = ref()
const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const mapOptions = {
  center: {
    lng: BJ_CENTER_POINT.lng,
    lat: BJ_CENTER_POINT.lat,
  },
  zoom: 9,
  minZoom: 3,
  maxZoom: 18,
  search: {
    show: false
  }
}

const onMapReady = (map: TMap) => {
  map.switchBaseLayer('img')

  getList()
}

const mapEvent = (data: any) => {
  const { event } = data
  if (event === 'zoomIn') {
    mapRef.value?.zoomIn()
  }
  if (event === 'zoomOut') {
    mapRef.value?.zoomOut()
  }
  if (event === 'resetView') {
    mapRef.value?.resetView()
  }
}

const drawFeatureLayerWKT = async (list:any[]) => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  try {
    const featureLayer = new TFeatureLayer(map, {
      polygonStyle: {
        fillColor: '#FF6B35',
        fillOpacity: 0.5,
        color: '#FF4757',
        weight: 2,
        opacity: 0.8,
        labelText: (feature:any) => feature.properties.analysisText,
        labelOffset: [0, 0],
        labelStyle: {
          fontSize: '12px',
          fontWeight: 'bold',
          border: 'none',
          boxShadow: 'none',
        },
      },
      pointStyle: {
        type: 'marker',
        icon: 'https://api.tianditu.gov.cn/img/map/markerA.png',
        size: [28, 28],
      },
      lineStyle: {
        color: '#FF4757',
        weight: 3,
        opacity: 0.8,
        showArrow: false,
      },
      events: {
        onClick: (feature) => {
          const props = feature.properties
          parcelDetailsRouteRef.value?.navigate({
            query: {
              id: props.id
            }
          })
        },
      },
      fieldMapping: {
        nameField: 'analysisText',
        codeField: 'regionCode',
      },
      loadAllGeometryTypes: true,
    })

    const geojsonData = {
      type: 'FeatureCollection' as const,
      features: list,
    }
    await featureLayer.load(geojsonData, true)
  } catch (error) {
    console.error('加载WKT数据失败:', error)
  }
}

const getList = () => {
  ScreenApi.getMonitorPatternList({
    status: 1,
    year: dayjs().startOf('year').format('YYYY-MM-DD')
  }).then(res => {
    if(res.data?.length) {
      const list = res.data.map((item:any) => {
        const geometry = item.geom ? parseWKT(item.geom) : ''
        return {
          type: 'Feature',
          properties: item,
          geometry,
        }
      })
     drawFeatureLayerWKT(list)
    }
  })
}
</script>

<style scoped lang="scss">
.left-bg{
  background: linear-gradient(90deg, rgba(3,21,50,0.3) 0%, rgba(26,58,110,0) 100%);
}
.right-bg{
  background: linear-gradient(90deg, rgba(26,58,110,0) 0%, rgba(3,21,50,0.3) 100%);
}
</style>
