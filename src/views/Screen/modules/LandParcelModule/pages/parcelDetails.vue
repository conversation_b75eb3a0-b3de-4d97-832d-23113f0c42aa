<template>
  <z-screen-container :loading="loading" :wrapperStyle="{ background: '#1A3A6E' }">
    <Header />
    <div class="h-[calc(100%-120px)] box-border mt-[120px] px-[44px] pb-[30px] overflow-auto">
      <div class="flex gap-x-10 h-[640px]">
        <SCard class="flex-1 h-full">
          <div class="bg-#0A2858 bg-opacity-70 h-[62px] flex justify-center items-center absolute top-0 left-0 right-0 z-1 rounded-tl-6 rounded-tr-6">
            <span class="alibaba-semiBold text-white text-[30px]">前时相影像</span>
          </div>
          <img :src="assetsUrl + dataInfo.beforePicPath" alt="" class="w-full h-full rounded-6">
        </SCard>
        <SCard class="flex-1 h-full">
          <div class="bg-#0A2858 bg-opacity-70 h-[62px] flex justify-center items-center absolute top-0 left-0 right-0 z-1 rounded-tl-6 rounded-tr-6">
            <span class="alibaba-semiBold text-white text-[30px]">后时相影像</span>
          </div>
          <img :src="assetsUrl + dataInfo.endPicPath" alt="" class="w-full h-full rounded-6">
        </SCard>
      </div>
      <SCard class="mt-7">
        <div class="bg-#13438E h-[70px] px-7 flex items-center relative z-1 rounded-tl-6 rounded-tr-6">
          <div class="flex items-center">
            <img src="@/assets/imgs/screen/jcsj.png" alt="" class="w-[30px] h-[30px]">
            <span class="text-[26px] text-white alibaba-semiBold ml-2">监测时间：{{ dataInfo.createTime }}</span>
          </div>
          <div class="flex items-center ml-12">
            <img src="@/assets/imgs/screen/ssqy.png" alt="" class="w-[30px] h-[30px]">
            <span class="text-[26px] text-white alibaba-semiBold ml-2">所属区域：{{ regionName }}</span>
          </div>
        </div>
        <div class="flex flex-col text-white alibaba-semiBold text-[20px] gap-y-3 px-16 py-5">
<!--          <span>详细位置：上城路街道2339号左侧河岸附</span>-->
          <span>图斑类型：{{ dataTypeName }}</span>
          <span>面积：{{ dataInfo.area }} m²</span>
          <span>疑似问题：{{ dataInfo.analysisText }}</span>
        </div>
      </SCard>
    </div>
  </z-screen-container>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import Header from './components/Header.vue'
import SCard from '@/views/Screen/components/SCard.vue'
import ScreenApi from '@/api/screen.ts'
import { useRoute } from 'vue-router'
import DictApI from '@/api/dict.ts'
import ComApi from '@/api/common.ts'

const route = useRoute()

const assetsUrl = import.meta.env.VITE_STATIC_ASSETS_URL

const loading = ref<boolean>(false)

const areaData = ref<any[]>([])
const regionName = computed(() => {
  return areaData.value.find(item => item.areaCode === dataInfo.value.regionCode)?.areaName
})
const questionTypes = ref<any[]>([])
const dataTypeName = computed(() => {
  return questionTypes.value.find(item => item.dictionaryCode === dataInfo.value.dataType)?.dictionaryName
})

const dataInfo = ref<any>({})
const getData = () => {
  loading.value = true
  ScreenApi.getMonitorPatternDetail(route.query.id as string).then(res => {
    dataInfo.value = res.data
  }).finally(() => {
    loading.value = false
  })
}

onMounted(async () => {
  questionTypes.value = await DictApI.getDictItemsByCode('questiontypes').then((res) => res.data)
  areaData.value = await ComApi.getAreaList().then((res) => res.data)
  getData()
})
</script>

<style scoped lang="scss">
.label-selected{
  background: linear-gradient( 270deg, rgba(230,195,101,0.25) 0%, #E6C365 100%);
}
.label-no-selected{
  background: linear-gradient( 270deg, rgba(9,53,116,0.74) 0%, rgba(34,116,243,0.8) 100%);
}
:deep(.el-carousel__button) {
  background: #3474C9;
  opacity: 1;
}
:deep(.el-carousel__indicator.is-active button) {
  background: #E6C365;
}
</style>
