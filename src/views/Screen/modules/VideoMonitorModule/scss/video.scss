:deep(.el-tabs__nav) {
  width: 100%;
  .el-tabs__item {
    width: 33%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
}
/* 隐藏父节点的复选框 */
:deep(.el-tree-node__content) {
  .is-disabled {
    display: none;
  }
}

// 隐藏input颜色
:deep(.el-select__wrapper) {
  background: #1a3a6e;
  border-radius: 2px;
  border: 1px solid #6dadff;
  opacity: 0.8;
  box-shadow: none;
  color: #fafeff;
}

:deep(.el-select__placeholder) {
  color: #fafeff;
}

:deep(.el-select-dropdown) {
  background-color: #1a3a6e !important;
  color: red;
}

/* 自定义输入框样式 */
:deep(.el-input__wrapper) {
  background: #1a3a6e;
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
}
:deep(.el-input__inner) {
  color: white;
}
:deep(.input__placeholder) {
  color: white;
}
#video-page {
  position: relative;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .node-select-action {
    background: #e8f1fd;
  }
}
.custom-tree-node-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  padding-right: 8px;
}
.node-name {
  display: flex;
  align-items: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: white;
}
.page-layout {
  width: 120px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  div {
    width: 30px;
    height: 30px;
    cursor: pointer;
  }
  .page-layout-1 {
    background: url('../../../../../assets/imgs/screen/monitor1-1.png') no-repeat 100%;
  }
  .page-layout-4 {
    background: url('../../../../../assets/imgs/screen/monitor2-1.png') no-repeat 100%;
  }
  .page-layout-9 {
    background: url('../../../../../assets/imgs/screen/monitor3-1.png') no-repeat 100%;
  }
  .page-layout-1-1 {
    background: url('../../../../../assets/imgs/screen/monitor1-2.png') no-repeat 100%;
  }
  .page-layout-4-1 {
    background: url('../../../../../assets/imgs/screen/monitor2-2.png') no-repeat 100%;
  }
  .page-layout-9-1 {
    background: url('../../../../../assets/imgs/screen/monitor3-2.png') no-repeat 100%;
  }
}

.grid-container {
  flex: 1;
  display: grid;
  gap: 12px;
  padding: 0;
  overflow: auto;
}

.grid-container.layout-single {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
}

.grid-container.layout-quad {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

.grid-container.layout-nine {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
}

.grid-item {
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 2px solid rgba($color: #6dadff, $alpha: 0.3);
}
.grid-item-active {
  border-color: #409eff;
}

.grid-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.grid-header {
  width: 100%;
  height: 40px;
  background: transparent;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 100;
}

.grid-title {
  min-width: 60px;
  padding: 8px 12px;
  background: #000000;
  opacity: 0.8;
  color: white;
}

.grid-title span {
  margin-left: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.close-icon {
  width: 46px;
  height: 46px;
  position: absolute;
  right: 0;
  top: 0;
  cursor: pointer;
  color: #c0c4cc;
  transition: color 0.2s;
  background: rgba($color: #000000, $alpha: 0.8);
  font-size: 22px;
}

.monitor-view {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  color: #fff;
  position: relative;
}

.grid-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  cursor: pointer;
  transition: all 0.3s;
}

.grid-empty:hover {
  color: #409eff;
}

.grid-empty span {
  margin-top: 8px;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .grid-container.layout-nine {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .monitoring-container {
    flex-direction: column;
  }

  .point-list {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #e6e8eb;
  }

  .grid-container {
    height: calc(100vh - 260px);
  }
}
