/* 自定义下拉框样式 */
.custom-select-dropdown.el-select__popper.el-popper {
  background: #1a3a6e !important;
}
.custom-select-dropdown.el-popper.is-light,
.custom-select-dropdown.el-popper.is-light > .el-popper__arrow:before {
  background: #1a3a6e !important;
  border-color: #6dadff !important;
}
.custom-select-dropdown .el-select-dropdown__item.is-hovering {
  background: #37527d !important;
}

/* 自定义下拉框样式 */
.custom-select-dropdown {
  background-color: #1a3a6e !important;
  .el-select-dropdown__item {
    color: white;
  }
  /* 选中项样式 */
  .el-select-dropdown__item.is-selected {
    background-color: #37527d !important;
    color: #409eff !important;
  }
  /* 悬停项样式 */
  .el-select-dropdown__item:hover {
    background-color: #37527d !important;
    color: white;
  }
}

/* 自定义输入框样式 */

.custom-input-style {
  /* 边框样式 */
  --el-input-border-color: #6dadff;
  --el-input-hover-border-color: #2a5a9e;
  --el-input-focus-border-color: #3a7ace;

  /* 文本样式 */
  --el-input-text-color: #333;
  --el-input-font-size: 16px;

  /* 背景样式 */
  --el-input-bg-color: #f8f8f8;
}

.custom-input-style .el-input__inner {
  font-family: 'Arial', sans-serif;
  letter-spacing: 0.5px;
}

.custom-input-style .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--el-input-border-color) inset;
}

.custom-input-style .el-input__wrapper:hover {
  box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset;
}

.custom-input-style .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
}

/* 自定义tabs样式 */
.custom-tabs-style {
  .el-tabs__item {
    color: #8e9db3;
    &.is-active {
      color: white;
    }
  }
  .el-tabs__nav-wrap:after {
    background: rgba($color: #6dadff, $alpha: 0.3);
  }
}

/* 自定义树样式 */
.custom-tree-style {
  /* 树整体背景 */
  &.el-tree {
    background: transparent;
  }
  /* 节点文字颜色 */
  --el-tree-node-content-text-color: #333;
  /* 节点悬停效果 */
  .el-tree-node__content:hover {
    background: #1a3a6e;
  }
  /* 选中节点样式 */
  .el-tree-node.is-current > .el-tree-node__content {
    background-color: #1a3a6e !important;
    color: #fff !important;
  }
  /* 节点展开/折叠图标颜色 */
  .el-tree-node__expand-icon {
    color: #666;
  }

  /* 复选框样式 */
  .el-checkbox__inner {
    border-color: #1a3a6e;
  }
  /* 禁用状态节点 */
  .el-tree-node.is-disabled > .el-tree-node__content {
    color: #999;
    cursor: not-allowed;
  }
  .el-tree-node:focus>.el-tree-node__content {
    background-color: #1a3a6e;
  }
}
