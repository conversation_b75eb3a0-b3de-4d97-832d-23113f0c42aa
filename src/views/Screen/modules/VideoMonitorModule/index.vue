<template>
  <div
    id="video-page"
    class="absolute w-full h-full bg-[#1A3A6E] overflow-auto"
  >
    <div class="absolute z-3 w-full h-[calc(100%-110px)] box-border p-[20px] mt-[90px]">
      <div class="w-full p-2 pb-[0px] rounded-md box-border">
        <div class="flex justify-between">
          <div class="form">
            <el-select
              v-model="ruleForm.monitorDeviceRegion"
              placeholder="区域选择"
              clearable
              class="w-[350px] mr-[10px]"
              popper-class="custom-select-dropdown"
            >
              <el-option
                v-for="item in areaData"
                :key="item.areaCode"
                :label="item.areaName"
                :value="item.areaCode"
              />
            </el-select>
            <el-input
              v-model="ruleForm.searchName"
              style="width: 240px"
              clearable
              placeholder="请输入点位/监控名称查询"
              class="custom-input-style"
            />
            <el-button
              type="primary"
              @click="handleSearch"
              >查询</el-button
            >
          </div>
          <div class="page-layout">
            <div
              @click="changeLayout('single')"
              :class="{ 'page-layout-1': true, 'page-layout-1-1': activeLayout === 'single' }"
            ></div>
            <div
              @click="changeLayout('quad')"
              :class="{ 'page-layout-4': true, 'page-layout-4-1': activeLayout === 'quad' }"
            ></div>
            <div
              @click="changeLayout('nine')"
              :class="{ 'page-layout-9': true, 'page-layout-9-1': activeLayout === 'nine' }"
            ></div>
          </div>
        </div>
      </div>
      <div class="w-full h-[calc(100%-62px)] flex mt-[20px] gap-5">
        <SCard class="w-1/5 h-full">
          <div class="w-full h-[100%] p-2 box-border">
            <el-tabs
              v-model="activeName"
              class="custom-tabs-style"
              @tab-change="handleTabChange"
            >
              <el-tab-pane
                label="全部点位"
                name="1"
              >
                <el-tree
                  ref="treeRef"
                  :data="allDataSource"
                  node-key="monitorDeviceCode"
                  default-expand-all
                  :expand-on-click-node="false"
                  class="custom-tree-style"
                  :props="{
                    children: 'monitorDeviceViews',
                    label: 'nodeName',
                  }"
                >
                  <template #default="{ node, data }">
                    <div class="custom-tree-node">
                      <span
                        class="node-name"
                        :draggable="true"
                        @dragstart="onDragStart($event, data)"
                        @dblclick="onDblclick(data)"
                        >{{ node.label }}</span
                      >
                      <template v-if="data.monitorDeviceName">
                        <img
                          v-if="checkedState(data)"
                          :src="select_action"
                          width="16px"
                          height="16px"
                          alt=""
                        />
                      </template>
                    </div>
                  </template>
                </el-tree>
              </el-tab-pane>
              <el-tab-pane
                label="收藏点位"
                name="2"
              >
                <el-tree
                  :data="monitorFavoriteList"
                  node-key="monitorFavoritesId"
                  default-expand-all
                  :expand-on-click-node="false"
                  class="custom-tree-style"
                  :props="{
                    children: 'nodes',
                    label: 'nodeName',
                    disabled: (data) => data.monitorFavoritesName || data.monitorDevicePosition,
                  }"
                >
                  <template #default="{ node, data }">
                    <div
                      :class="{
                        'custom-tree-node': data.monitorDevicePosition ? true : false,
                        'custom-tree-node-left': data.monitorDeviceName ? true : false,
                      }"
                    >
                      <span
                        class="node-name"
                        :draggable="true"
                        @dragstart="onDragStart($event, data)"
                        @dblclick="onDblclick(data)"
                      >
                        {{ node.label }}
                      </span>
                      <template v-if="data.monitorDeviceName">
                        <img
                          v-if="checkedState(data)"
                          :src="select_action"
                          width="16px"
                          height="16px"
                          alt=""
                        />
                      </template>
                    </div>
                  </template>
                </el-tree>
              </el-tab-pane>
              <el-tab-pane
                label="预警点位"
                name="3"
              >
                <el-tree
                  :data="monitorWarningList"
                  node-key="monitorFavoritesId"
                  default-expand-all
                  :expand-on-click-node="false"
                  class="custom-tree-style"
                  :props="{
                    children: 'nodes',
                    label: 'nodeName',
                    disabled: (data) => data.monitorFavoritesName || data.monitorDevicePosition,
                  }"
                >
                  <template #default="{ node, data }">
                    <div
                      :class="{
                        'custom-tree-node': data.monitorDevicePosition ? true : false,
                        'custom-tree-node-left': data.monitorDeviceName ? true : false,
                      }"
                    >
                      <span
                        class="node-name"
                        :draggable="true"
                        @dragstart="onDragStart($event, data)"
                        @dblclick="onDblclick(data)"
                      >
                        {{ node.label }}
                      </span>
                      <template v-if="data.monitorDeviceName">
                        <img
                          v-if="checkedState(data)"
                          :src="select_action"
                          width="16px"
                          height="16px"
                          alt=""
                        />
                      </template>
                    </div>
                  </template>
                </el-tree>
              </el-tab-pane>
            </el-tabs>
          </div>
        </SCard>
        <!-- 右侧监控区域 -->
        <div class="w-4/5 h-[100%]">
          <div
            class="grid-container h-[100%] box-border"
            :class="[`layout-${activeLayout}`]"
          >
            <div
              v-for="grid in visibleGrids"
              :key="grid.id"
              class="grid-item"
              :class="{ 'grid-item-active': selectedGrid?.id === grid.id }"
              @dragover.prevent
              @dragenter.prevent
              @drop="onDrop($event, grid)"
              @click="selectGrid(grid)"
            >
              <div
                v-if="grid.point"
                class="grid-content"
              >
                <div class="grid-header">
                  <el-icon
                    class="close-icon"
                    @click.stop="removeFromGrid(grid)"
                  >
                    <Close />
                  </el-icon>
                </div>
                <div class="monitor-view">
                  <FlvVideo
                    :previewUrl="grid.point.previewUrl"
                    :is-live="false"
                  />
                </div>
              </div>
              <div
                v-else
                class="grid-empty"
              >
                <el-icon :size="24"><Plus /></el-icon>
                <span>请在左侧监控列表中拖拽或双击选择监控</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <ControlPanel /> -->
  </div>
</template>

<script setup lang="ts">
import { useBus } from '@/composables/useBus'
import type { ElTree } from 'element-plus'
import ComApi from '@/api/common'
import dataResApi from '@/api/dataResources'
import { Close, Plus } from '@element-plus/icons-vue'
import { ref, reactive, computed, watch, onMounted, provide } from 'vue'
import type { MonitoringPoint, MonitoringGrid, LayoutType } from './types'
import SCard from '../../components/SCard.vue'
import ControlPanel from './module/ControlPanel.vue'
import select_action from '@/assets/imgs/screen/select_action.png'
import FlvVideo from '@/views/DataResources/BusinessManagement/VideoMonitoring/module/FlvVideo.vue'

interface RuleForm {
  monitorDeviceRegion: string
  searchName: string
}

const ruleForm = reactive<RuleForm>({
  monitorDeviceRegion: '',
  searchName: '',
})

const emitter = useBus()

const treeRef = ref<InstanceType<typeof ElTree>>()
const activeLayout = ref<LayoutType>('quad')
const areaData = ref<any[]>([])
const allDataSource = ref<any[]>([])
const monitorFavoriteList = ref<any[]>([])
const monitorWarningList = ref<any[]>([])

const activeName = ref('1')

// 对话框状态
const selectedGrid = ref<MonitoringGrid | null>(null)

const draggedPoint = ref<MonitoringPoint | null>(null)

// 监控格子数据
const allGrids = ref<MonitoringGrid[]>([
  { id: 'grid1', point: undefined },
  { id: 'grid2', point: undefined },
  { id: 'grid3', point: undefined },
  { id: 'grid4', point: undefined },
  { id: 'grid5', point: undefined },
  { id: 'grid6', point: undefined },
  { id: 'grid7', point: undefined },
  { id: 'grid8', point: undefined },
  { id: 'grid9', point: undefined },
])

// 当前可见的格子
const visibleGrids = computed(() => {
  switch (activeLayout.value) {
    case 'single':
      return allGrids.value.slice(0, 1)
    case 'quad':
      return allGrids.value.slice(0, 4)
    case 'nine':
      return allGrids.value
    default:
      return allGrids.value.slice(0, 4)
  }
})

// 是否选中设备
const checkedState = computed(() => {
  return (data: any) => {
    const grid = allGrids.value.find((g) => g.point?.monitorDeviceCode === data.monitorDeviceCode)
    return grid
  }
})

// 切换tab
const handleTabChange = (tabName: any) => {
  activeName.value = tabName
  switch (tabName) {
    case '1':
      getEquipomentData()
      break
    case '2':
      getMonitorFavoriteList()
      break
    case '3':
      getMonitorWarningList()
      break
  }
}

// 拖拽开始
const onDragStart = (event: DragEvent, point: MonitoringPoint) => {
  draggedPoint.value = point
  event.dataTransfer?.setData('text/plain', point.monitorDeviceCode)
}

// 从格子中移除点位
const removeFromGrid = (grid: MonitoringGrid) => {
  grid.point = undefined
}
// 拖拽放置
const onDrop = (event: DragEvent, grid: MonitoringGrid) => {
  event.preventDefault()
  if (!draggedPoint.value) return
  assignPoint(draggedPoint.value, grid, false)
  draggedPoint.value = null
}
// 选择格子（点击事件）
const selectGrid = (grid: MonitoringGrid) => {
  selectedGrid.value = grid
}

// 分配点位到格子
const assignPoint = async (point: MonitoringPoint, grid: MonitoringGrid, auto = true) => {
  // 从其他格子中移除该点位
  allGrids.value.forEach((g) => {
    if (g.point?.monitorDeviceCode === point.monitorDeviceCode) {
      g.point = undefined
    }
  })
  // 分配到新格子
  grid.point = { ...point }

  if (auto) {
    updateSelectedGrid(grid)
  }
}
const updateSelectedGrid = (grid: MonitoringGrid) => {
  const index = allGrids.value.findIndex((g) => g.id === grid.id)
  // 根据 activeLayout 的类型确定最大允许索引
  let maxIndex
  switch (activeLayout.value) {
    case 'single':
      maxIndex = 0
      break
    case 'quad':
      maxIndex = 3
      break
    case 'nine':
      maxIndex = 8
      break
    default:
      maxIndex = allGrids.value.length - 1 // 默认无限制
  }
  // 计算下一个索引，不超过最大索引
  const nextIndex = Math.min(index + 1, maxIndex)
  selectedGrid.value = allGrids.value[nextIndex]
}
// 切换布局
const changeLayout = (layout: LayoutType) => {
  activeLayout.value = layout
  localStorage.setItem('monitoringLayout', layout)
  selectedGrid.value = allGrids.value[0]
}
const onDblclick = (data: any) => {
  if (selectedGrid.value) {
    assignPoint(data, selectedGrid.value)
  }
}

const handleSearch = () => {
  switch (activeName.value) {
    case '1':
      getEquipomentData()
      break
    case '2':
      getMonitorFavoriteList()
      break
    case '3':
      getMonitorWarningList()
      break
  }
}

const getAreaList = async () => {
  areaData.value = await ComApi.getAreaList().then((res) => res.data)
}

const getEquipomentData = async () => {
  try {
    const data = await dataResApi.getDeviceList({ ...ruleForm }).then((res) => res.data)
    allDataSource.value = data
  } catch (error) {
    console.log(error)
  }
}
const getMonitorFavoriteList = async (ifShowPosition = true) => {
  try {
    const data = await dataResApi
      .getMonitorFavoriteList({ ...ruleForm, ifShowPosition: ifShowPosition })
      .then((res) => res.data)
    monitorFavoriteList.value = data
  } catch (error) {
    console.log(error)
  }
}
const getMonitorWarningList = async () => {
  // try {
  //   const data = await dataResApi
  //     .getMonitorFavoriteList({ ...ruleForm })
  //     .then((res) => res.data)
  //   monitorWarningList.value = data
  // } catch (error) {
  //   console.log(error)
  // }
}

watch(
  selectedGrid,
  (newGrid) => {
    emitter.emit('SETCMAERAINFO', selectedGrid.value as MonitoringGrid)
  },
  {
    deep: true,
  },
)

onMounted(() => {
  // 从本地存储加载布局
  const savedLayout = localStorage.getItem('monitoringLayout') as LayoutType
  if (savedLayout) changeLayout(savedLayout)
  Promise.all([
    getAreaList(),
    getEquipomentData(),
    getMonitorFavoriteList(),
    getMonitorWarningList(),
  ])
  selectedGrid.value = allGrids.value[0]
})
</script>

<style scoped lang="scss">
@import url('./scss/video.scss');

</style>
<style lang="scss">
@import url('./scss/element.scss');
</style>
