<template>
  <!-- 云台操作按钮 -->
  <div
    class="ptz-button"
    ref="ptzButton"
    v-show="!showPanel && tId == 53"
    @mousedown.stop="startDrag"
    @mouseup.stop="endDrag"
    @click="togglePanel"
  >
     <img :src="PTZ" alt="">
  </div>
  <!-- 云台操作面板 -->
  <div
    class="ptz-panel"
    v-show="showPanel"
    :style="panelStyle"
  >
    <div class="ptz-row-close">
      <el-icon @click="togglePanel" size="22px" color="white"><Close /></el-icon>
    </div>
    <div class="ptz-row-circle">
      <DirectionPanel :speed="speed" />
    </div>
    <div class="ptz-row pl-[40px] pr-[40px]">
      <div
        class="ptz-control-reduce"
        @click="controlAngle('ZOOM_OUT')"
      >
        <img
          src="@/assets/imgs/screen/reduce.png"
          alt=""
        />
      </div>
      <span>变焦</span>
      <div
        class="ptz-control-amplify"
        @click="controlAngle('ZOOM_IN')"
      >
        <img
          src="@/assets/imgs/screen/amplify.png"
          alt=""
        />
      </div>
    </div>
    <div class="ptz-row pl-[40px] pr-[40px]">
      <div
        class="ptz-control-reduce"
        @click="controlAngle('IRIS_REDUCE')"
      >
        <img
          src="@/assets/imgs/screen/reduce.png"
          alt=""
        />
      </div>
      <span>光圈</span>
      <div
        class="ptz-control-amplify"
        @click="controlAngle('IRIS_ENLARGE')"
      >
        <img
          src="@/assets/imgs/screen/amplify.png"
          alt=""
        />
      </div>
    </div>
    <div class="ptz-row pl-[40px] pr-[40px]">
      <div
        class="ptz-control-reduce"
        @click="controlAngle('FOCUS_NEAR')"
      >
        <img
          src="@/assets/imgs/screen/reduce.png"
          alt=""
        />
      </div>
      <span>缩放</span>
      <div
        class="ptz-control-amplify"
        @click="controlAngle('FOCUS_FAR')"
      >
        <img
          src="@/assets/imgs/screen/amplify.png"
          alt=""
        />
      </div>
    </div>
    <div class="ptz-row">
      <div class="ptz-control-water">
        <span class="mr-[10px]">雨刷</span>
        <el-switch v-model="value1" />
      </div>
      <div class="ptz-control-light">
        <span class="mr-[10px]">灯光</span>
        <el-switch v-model="value2" />
      </div>
    </div>
    <div class="ptz-row ptz-speed">
      <p class="ptz-speed-label">云台转速</p>
      <el-slider
        v-model="speed"
        :min="10"
        :max="100"
        :step="1"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { ref, onMounted, onUnmounted, computed, inject, watch, type Ref } from 'vue'
import { Close } from '@element-plus/icons-vue'
import DirectionPanel from './DirectionPanel.vue'
import type { MonitoringGrid, ControlCommand } from '../types'
import dataResApi from '@/api/dataResources'
import PTZ from '@/assets/imgs/screen/PTZ.png'

import { ScreenStateKey } from '../../../utils/injection.ts';

const screenKey = inject(ScreenStateKey)
if (!screenKey) throw new Error('screenState must be provided')
const { tId } = screenKey

const selectedGrid = inject<Ref<MonitoringGrid | null>>('SELECT_GRID')
// 云台按钮和面板状态
const ptzButton = ref<HTMLElement | null>(null)
const showPanel = ref(false)
const panelStyle = ref({
  top: '0px',
  left: '0px',
})
const speed = ref(50)

const value1 = ref(false)
const value2 = ref(false)
// 拖拽相关变量
const isDragging = ref(false)
const offsetX = ref(0)
const offsetY = ref(0)

// 开始拖拽
const startDrag = (e: MouseEvent) => {
  if (!ptzButton.value) return

  isDragging.value = true
  offsetX.value = e.clientX - ptzButton.value.getBoundingClientRect().left
  offsetY.value = e.clientY - ptzButton.value.getBoundingClientRect().top

  // 防止文本选中
  e.preventDefault()
}
// 拖拽移动
const onDragMove = (e: MouseEvent) => {
  if (!isDragging.value || !ptzButton.value) return
  // 计算新的位置
  let newLeft = e.clientX - offsetX.value
  let newTop = e.clientY - offsetY.value

  // 获取视口尺寸
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  // 保证按钮不超出左和上边界
  newLeft = Math.max(newLeft, 0)
  newTop = Math.max(newTop, 0)

  // 保证按钮不超出右和下边界
  newLeft = Math.min(newLeft, viewportWidth - ptzButton.value.offsetWidth)
  newTop = Math.min(newTop, viewportHeight - ptzButton.value.offsetHeight)

  // 更新按钮位置
  ptzButton.value.style.left = `${newLeft}px`
  ptzButton.value.style.top = `${newTop}px`

  // 更新面板位置
  updatePanelPosition()
}
// 结束拖拽
const endDrag = () => {
  isDragging.value = false
}

// 控制摄像头角度
const controlAngle = async (command: ControlCommand) => {
  const data = {
    action: 0,
    cameraIndexCode: JSON.parse(selectedGrid?.value?.point?.monitorDeviceInfo || '')?.indexCode,
    command: command,
    speed: speed.value,
  }
  const res = await dataResApi.controlCamera(data)
  console.log('控制摄像头角度', res)
}

// 更新面板位置
const updatePanelPosition = () => {
  if (!ptzButton.value) return

  const rect = ptzButton.value.getBoundingClientRect()
  const panelWidth = 260 // 对应 .ptz-panel 的 min-width
  const panelHeight = 510 // 根据实际面板高度调整
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let left = rect.left + rect.width
  let top = rect.top

  // 如果右侧超出，则显示在按钮左侧
  if (left + panelWidth > viewportWidth) {
    left = rect.left - panelWidth - 20
  }

  // 如果下侧超出，则向上调整，确保不被截断
  if (top + panelHeight > viewportHeight) {
    top = Math.max(0, viewportHeight - panelHeight - 20)
  }

  panelStyle.value = {
    left: `${left}px`,
    top: `${top}px`,
  }
}
// 切换面板显示
const togglePanel = () => {
  if (isDragging.value) {
    isDragging.value = false
    return
  }
  console.log('33333')
  if (!selectedGrid?.value?.point) {
    ElMessage({
      type: 'warning',
      message: '请先选中监控画面以启用云台控制',
    })
    return
  }
  if (!selectedGrid?.value?.point?.havePtz) {
    ElMessage({
      type: 'warning',
      message: '该摄像头不支持云台功能',
    })
    return
  }
  showPanel.value = !showPanel.value
  if (showPanel.value) {
    updatePanelPosition()
  }
}

// 生命周期钩子
onMounted(() => {
  document.addEventListener('mousemove', onDragMove)
  document.addEventListener('mouseup', endDrag)

  // 初始位置
  if (ptzButton.value) {
    ptzButton.value.style.right = '66px'
    ptzButton.value.style.top = '370px'
  }
})

onUnmounted(() => {
  document.removeEventListener('mousemove', onDragMove)
  document.removeEventListener('mouseup', endDrag)
})
</script>

<style scoped lang="scss">
.ptz-button {
  position: absolute;
  width: 66px;
  height: 66px;
  cursor: move;
  z-index: 1000;
  user-select: none;
  will-change: transform;
  img {
    width: 66px;
    height: 66px;
  }
}

.ptz-panel {
  position: absolute;
  background: rgba($color: #001542, $alpha: 0.9);
  border-radius: 8px;
  padding: 5px;
  z-index: 999;
  min-width: 260px;
  height: 510px;
  will-change: transform;
  border: 1px solid #6DADFF;
}

.ptz-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 10px;
  span {
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
  }
  .ptz-control-reduce {
    width: 28px;
    height: 28px;
    background-color: #007EF2;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 4px;
    img {
      width: 18px;
      height: 18px;
    }
  }
  .ptz-control-amplify {
    width: 28px;
    height: 28px;
    background-color: #007EF2;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 4px;
    img {
      width: 18px;
      height: 18px;
    }
  }
}
.ptz-row-close {
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
}
.ptz-row-circle {
  width: 100%;
  height: 220px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 0 20px;
  font-family: Arial, sans-serif;
}

.ptz-row.ptz-speed {
  display: block;
  font-size: 12px;
  padding: 0 24px;
}
.ptz-speed-label {
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 0px;
  color: white;
}

.ptz-control {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 12px;
  color: #606266;
}

.ptz-control:hover {
  background-color: #ebedf0;
  border-color: #c0c4cc;
}
</style>
