<template>
  <z-screen-container :loading="loading">
    <TMapView
      v-if="!loading"
      class="absolute left-0 right-0 w-full h-full z-1"
      ref="mapRef"
      :options="mapOptions"
      @ready="onMapReady"
    />

    <TopTabs @change="tabChange" />

    <div v-show="chartShow">
      <div class="absolute z-3 top-[105px] left-[24px] flex flex-col gap-2">
        <Card v-for="v in leftList" :key="v.chartId" :title="v.className">
          <ChartIndex :year="dataYear" :chart-data="v"  />
        </Card>
      </div>
      <div class="absolute z-3 top-[105px] right-[24px] flex flex-col gap-2">
        <Card v-for="v in rightList" :key="v.chartId" :title="v.className">
          <ChartIndex :year="dataYear" :chart-data="v" />
        </Card>
      </div>
      <div v-if="countList?.length" class="absolute z-3 top-[135px] left-[479px] right-[479px]">
        <ChartCount :year="dataYear" :chart-list="countList" />
      </div>
    </div>
    <ToolBox
      ref="toolRef"
      @map-event="mapEvent"
      @change-chart-show="(bool:boolean) => chartShow = bool"
      @change-classify="(ids:number[]) => getMapData(ids)"
      @change-year="(year:string) => yearChange(year)"
    />

    <Details ref="detailsRef" />

    <div class="absolute z-1 left-0 top-0 bottom-0 w-[670px] left-bg pointer-events-none"></div>
    <div class="absolute z-1 right-0 top-0 bottom-0 w-[670px] right-bg pointer-events-none"></div>
  </z-screen-container>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import TopTabs from './components/TopTabs.vue'
import Card from './components/Card.vue'
import ChartIndex from './components/charts/ChartIndex.vue'
import ChartCount from './components/charts/ChartCount.vue'
import ToolBox from './components/ToolBox.vue'
import { ChartType } from '../../../utils/option.ts'
import { useRoute } from 'vue-router'
import ScreenApi from '@/api/screen'
import { TAnchorPosition, TMap, TMapView, TSvgOverlay } from '@/map'
import { ElLoading } from 'element-plus'
import Details from '../components/Details.vue'
import { MAP_POLYGON_COLOR, MAP_POLYLINE_COLOR } from '@/views/Screen/utils/conts.ts'
import { BJ_CENTER_POINT } from '@/config/default-settings.ts'

const route = useRoute()

const loading = ref<boolean>(true)
const chartShow = ref<boolean>(true)

const countList = computed<any[]>(() => dataList.value.filter((v: any) => v.chartType === ChartType.Count))
const chartList = computed<any[]>(() => dataList.value.filter((v: any) => v.chartType !== ChartType.Count))
const leftList = computed<any[]>(() => chartList.value.slice(0, 3))
const rightList = computed<any[]>(() => chartList.value.slice(3))

const dataYear = ref<string>()
const dataList = ref<any[]>([])
const getClassifyData = () => {
  dataList.value = []
  loading.value = true
  ScreenApi.getNormClassifyList({
    thematicId: tabId.value || route.query.tid
  }).then((res) => {
    dataList.value = res.data || []
  }).finally(() => {
    loading.value = false
  })
}

const tabId = ref<number>()
const tabChange = (item: any) => {
  tabId.value = item.thematicId
  getClassifyData()
}
const yearChange = (year: string) => {
  dataYear.value = year
  getClassifyData()
}

const toolRef = ref<InstanceType<typeof ToolBox> | null>(null)
const detailsRef = ref<InstanceType<typeof Details> | null>(null)
const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const mapOptions = {
  center: {
    lng: BJ_CENTER_POINT.lng,
    lat: BJ_CENTER_POINT.lat,
  },
  search: {
    show: false
  }
}
const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')

  toolRef.value?.loadTree()
}

const mapEvent = (data: any) => {
  const { event } = data
  if (event === 'zoomIn') {
    mapRef.value?.zoomIn()
  }
  if (event === 'zoomOut') {
    mapRef.value?.zoomOut()
  }
  if (event === 'resetView') {
    mapRef.value?.resetView()
  }
}

const drawPoint = (list: any[], map: TMap) => {
  if (!list?.length) return
  list.forEach(item => {
    if(item.location) {
      const [lng, lat] = JSON.parse(item.location)
      const circleSvg = new TSvgOverlay({
        position: [lng, lat],
        iconName: `marker-${item.thematicClassIcon}`,
        size: [35, 50],
        anchor: TAnchorPosition.BOTTOM_CENTER,
        events: {
          click: () => {
            detailsRef.value?.open(item)
          },
        }
      })
      map.addOverlay(circleSvg)
    }
  })
}

const drawLine = (list: any[], map: TMap) => {
  if (!list?.length) return
  list.forEach(item => {
    if(item.location) {
      const location = JSON.parse(item.location)
      map.createPolyline({
        path: location,
        color: MAP_POLYLINE_COLOR.color,
        weight: 3,
        opacity: 1
      })
    }
  })
}

const drawPolygon = (list: any[], map: TMap) => {
  if (!list?.length) return
  list.forEach(item => {
    if(item.location) {
      const location = JSON.parse(item.location)
      map.createPolygon({
        path: location,
        color: MAP_POLYGON_COLOR.borderColor,
        weight: 2,
        opacity: 1,
        fillColor: MAP_POLYGON_COLOR.fillColor,
        fillOpacity: 0.7
      })
    }
  })
}

const draw = (data: any[]) => {
  const map = mapRef.value?.getMap()
  if (!map) return
  map.clearOverlays()
  drawPoint(data.filter((e: any) => e.locationType === '1'), map)
  drawLine(data.filter((e: any) => e.locationType === '2'), map)
  drawPolygon(data.filter((e: any) => e.locationType === '3'), map)
}

const getMapData = (ids: number[]) => {
  if (!ids?.length) {
    const map = mapRef.value?.getMap()
    if (!map) return
    map.clearOverlays()
    return
  }
  const elLoading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  ScreenApi.getThematicFeatureDataList({
    thematicClassIds: ids?.toString()
  }).then(res => {
    draw(res?.data || [])
  }).finally(() => {
    elLoading.close()
  })
}
</script>

<style scoped lang="scss">
.left-bg{
  background: linear-gradient(90deg, rgba(3,21,50,0.87) 0%, rgba(26,58,110,0) 100%);
}
.right-bg{
  background: linear-gradient(90deg, rgba(26,58,110,0) 0%, rgba(3,21,50,0.87) 100%);
}
</style>
