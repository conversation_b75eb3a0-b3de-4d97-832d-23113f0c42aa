<template>
  <div class="w-[425px] h-[310px] bg-[url(@/assets/imgs/screen/card-border-1.png)] bg-[length:100%_100%] relative">
    <span class="absolute top-[-5px] left-4 right-4 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">{{ props.title }}</span>
    <slot />
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  title: string
}>()
</script>

<style scoped lang="scss">

</style>
