<template>
  <template v-if="list?.length">
    <ChartList v-if="chartData.chartType === ChartType.List" :list="list" />
    <ChartPie v-if="chartData.chartType === ChartType.Pie" :list="list" />
    <ChartLine v-if="chartData.chartType === ChartType.Line" :list="list" />
  </template>
  <el-empty v-else image=" " description="暂无数据" />
</template>

<script setup lang="ts">
import ChartList from './ChartList.vue'
import ChartPie from './ChartPie.vue'
import ChartLine from './ChartLine.vue'
import { ChartType } from '@/views/Screen/utils/option.ts'
import { onMounted, ref } from 'vue'
import ScreenApi from '@/api/screen.ts'
import { BJ_AREA_CODE } from '@/config/default-settings.ts'

interface ChartData {
  chartType: ChartType,
  [key: string]: any
}
const { year, chartData } = defineProps<{
  year: string | undefined,
  chartData: ChartData
}>()

const list = ref<any[]>([])
const fetchData = (params?: any) => {
  const data = {
    classId: chartData.classId,
    regionCode: BJ_AREA_CODE,
    ...params
  }
  if (chartData.chartType !== ChartType.Line) {
    data.dataYear = year
  }
  ScreenApi.getNormDataList(data).then(res => {
    list.value = res.data || []
  })
}
onMounted(() => {
  fetchData()
})
</script>

<style scoped lang="scss">

</style>
