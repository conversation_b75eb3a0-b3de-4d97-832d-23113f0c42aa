<template>
  <div class="mt-[30px] h-[calc(100%-40px)] flex">
    <div ref="chartRef" class="h-full w-1/2"></div>
    <div class="flex flex-col ml-2 pt-10">
      <div
        v-for="(v, i) in dataList" :key="v.baseId"
        class="flex items-center h-[31px] px-3 border-1 border-solid border-transparent"
        :class="{ 'bg-[#007EF2] bg-opacity-50 border-1 border-solid border-[#6DADFF]!': tabIndex === i }"
      >
        <span class="w-[8px] h-[8px] rounded-full mr-3" :style="{ backgroundColor: v.color }"></span>
        <span class="c-white">{{ v.indexName }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue'
import * as echarts from 'echarts';
import { CHART_COLORS } from '@/views/Screen/utils/conts.ts'


type EChartsOption = echarts.EChartsOption

const { list } = defineProps<{
  list: any[]
}>()

const chartRef = ref()
const dataList = ref<any[]>([])

let chart: any = null
const tabIndex = ref(0)
const initChart = () => {
  chart = echarts.init(chartRef.value)
  const option: EChartsOption = {
    title: {
      text: '0%',
      subtext: "占比",
      top: '41%',
      textAlign: "center",
      left: "49%",
      textStyle: {
        fontSize: 20,
        color: "#FFFFFF"
      },
      subtextStyle: {
        color: "#FFFFFF",
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['55%', '75%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: false
          },
          scale: true,
          scaleSize: 8
        },
        data: dataList.value.map(e => {
          return {
            value: e.dataValue,
            name: e.indexName,
            itemStyle: {
              color: e.color
            }
          }
        })
      }
    ]
  }
  chart.setOption(option)

  const length = dataList.value.length - 1
  function fun() {
    // 动态设置百分比
    chart.setOption({
      title: {
        text: `${dataList.value[tabIndex.value]?.percent}%`,
      }
    })
    // 取消高亮指定的数据图形
    chart.dispatchAction({
      type: "downplay",
      seriesIndex: 0,
      dataIndex: tabIndex.value == 0 ? length : tabIndex.value - 1,
    })
    chart.dispatchAction({
      type: "highlight",
      seriesIndex: 0,
      dataIndex: tabIndex.value,
    })
  }
  fun()
  setInterval(() => {
    tabIndex.value++
    if (tabIndex.value > length) {
      tabIndex.value = 0
    }
    fun()
  }, 3000)
}

onMounted(() => {
  tabIndex.value = Math.floor(Math.random() * list.length)
  const sum = list?.reduce((acc, cur) => acc + (+cur.dataValue || 0), 0) || 0
  dataList.value = list?.map((it, i) => {
    return {
      ...it,
      color: CHART_COLORS[i],
      percent: (it.dataValue / sum * 100).toFixed(2)
    }
  }) || []
  initChart()
})

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
  }
})
</script>

<style scoped lang="scss">

</style>
