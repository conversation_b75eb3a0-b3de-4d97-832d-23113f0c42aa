<template>
  <div ref="chartRef" class="mt-[30px] h-[calc(100%-40px)]"></div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue'
import * as echarts from 'echarts';
import { CHART_COLORS } from '@/views/Screen/utils/conts.ts'
import { toRGBA } from '@/views/Screen/utils/utils.ts'
import { groupBy } from 'lodash'

type EChartsOption = echarts.EChartsOption

const { list } = defineProps<{
  list: any[]
}>()

const chartRef = ref()

let chart: any = null
const initChart = () => {
  const xData = Object.keys(groupBy(list || [], 'dataYear'))
  const names = groupBy(list || [], 'indexName')
  const seriesData = Object.keys(names).map((key, index) => {
    const it = names[key]
    return {
      name: key,
      type: 'line',
      smooth: true,
      symbol: "circle",
      symbolSize: 5,
      // showSymbol: false,
      itemStyle: {
        color: CHART_COLORS[index]
      },
      label: {
        show: true,
        position: "top",
        color: CHART_COLORS[index]
      },
      // areaStyle: {
      //   color: new echarts.graphic.LinearGradient(
      //     0,
      //     0,
      //     0,
      //     1,
      //     [
      //       {
      //         offset: 0,
      //         color: `${toRGBA(CHART_COLORS[index], 0.6)}`
      //       },
      //       {
      //         offset: 1,
      //         color: `${toRGBA(CHART_COLORS[index], 0)}`
      //       },
      //     ],
      //     false
      //   )
      // },
      data: it.map((e: any) => e.dataValue)
    }
  })
  chart = echarts.init(chartRef.value)
  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '8%',
      right: '10%',
      bottom: '8%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(228, 252, 255, 0.6)',
        fontSize: 14
      },
      axisLine: {
        lineStyle: {
          color: '#3776A1'
        }
      },
      data: xData
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: 'rgba(228, 252, 255, 0.6)',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#3776A1'
        }
      }
    },
    series: seriesData as any
  }
  chart.setOption(option)
}

onMounted(async () => {
  initChart()
})

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
  }
})
</script>

<style scoped lang="scss">

</style>
