<template>
  <div class="mt-[30px] flex flex-col items-center overflow-hidden h-[calc(100%-40px)]">
    <ZScrollMarquee
      :vertical="true"
      :pause-on-hover="true"
    >
      <div v-for="v in list" :key="v.baseId" class="flex justify-between relative px-6 pb-3 mt-4 w-[364px] box-border mx-auto">
        <div class="bg-[url(@/assets/imgs/screen/list-line-bg.png)] bg-[length:100%_100%] w-[364px] h-[25px] absolute left-0 bottom-0"></div>
        <div class="flex items-center">
          <span class="c-white">{{ v.indexName }}</span>
          <img src="@/assets/imgs/screen/list-title.png" alt="" class="w-[31px] h-[8px] ml-3">
        </div>
        <span class="c-[#FFD325] alibaba-semiBold">{{ v.dataValue || 0 }} {{ v.indexUnit }}</span>
      </div>
    </ZScrollMarquee>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { Vue3Marquee } from 'vue3-marquee'

export default defineComponent({
  components: {
    ZScrollMarquee: Vue3Marquee
  }
})
</script>

<script setup lang="ts">
defineProps<{
  list: any[]
}>()
</script>

<style scoped lang="scss">

</style>
