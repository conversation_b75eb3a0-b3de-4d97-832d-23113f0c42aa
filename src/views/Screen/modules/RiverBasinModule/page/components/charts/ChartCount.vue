<template>
  <ZScrollMarquee
    :duration="55"
    :dela="2"
    :animate-on-overflow-only="true"
    :pause-on-hover="true"
  >
    <div
      v-for="v in dataList"
      :key="v.baseId"
      class="bg-[url(@/assets/imgs/screen/card-border-2.png)] bg-[length:100%_100%] px-3 min-w-[158px] h-[73px] ml-2 flex flex-col justify-center items-center"
    >
      <span class="c-[#FFD325] text-[24px] alibaba-semiBold">{{ v.dataValue }}</span>
      <span class="c-[#97C5FF] text-sm">{{ v.indexName }}({{ v.indexUnit }})</span>
    </div>
  </ZScrollMarquee>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { Vue3Marquee } from 'vue3-marquee'

export default defineComponent({
  components: {
    ZScrollMarquee: Vue3Marquee,
  },
})
</script>

<script setup lang="ts">
import { ref } from 'vue'
import ScreenApi from '@/api/screen.ts'
import { BJ_AREA_CODE } from '@/config/default-settings.ts'

const { chartList } = defineProps<{
  chartList: any[]
}>()

const dataList = ref<any[]>([])

const pList = chartList.map((it) => ScreenApi.getNormDataList({
  classId: it.classId,
  regionCode: BJ_AREA_CODE,
  dataYear: '2024'
}))

Promise.all(pList).then((res) => {
  let result: any[] = []
  for (const v of res) {
    result = [...result, ...v.data]
  }
  dataList.value = result
})
</script>

<style scoped lang="scss"></style>
