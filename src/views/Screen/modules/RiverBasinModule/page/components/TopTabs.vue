<template>v>
  <div
    class="h-[149px] absolute left-0 right-0 top-0 bg-[url(@/assets/imgs/screen/topbg-2.png)] bg-[length:100%_100%] z-3"
  >
    <div class="flex justify-between items-center px-[36px] pt-[13px] relative">
      <img src="@/assets/imgs/screen/back.png" alt="" class="w-[40px] h-[40px] cursor-pointer" @click="router.back()">
      <div class="flex gap-x-3">
        <div
          v-for="v in list"
          :key="v.thematicId"
          class="h-[56px] rounded-[10px] flex justify-center items-center cursor-pointer px-[30px] skew-x--20 tabs-item"
          :class="{ active: v.thematicId === curId }"
          @click="handleTabsClick(v)"
        >
          <span class="text-[26px] c-white skew-x-20 youshe-bold select-none">{{
              v.thematicName
            }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import ScreenApi from '@/api/screen.ts'
import { ref } from 'vue'

const router = useRouter()
const route = useRoute()

const emit = defineEmits<{
  (e: 'change', data: any): void
}>()

const curId = ref(Number(route.query.tid))

const handleTabsClick = (item: any) => {
  curId.value = item.thematicId
  emit('change', item)
}

const list = ref<any[]>([])
const getFeatureData = () => {
  ScreenApi.getThematicFeatureTree({
    thematicParent: route.query.fid as string
  }).then((res) => {
    list.value = (res.data || []).slice(1)
  })
}
getFeatureData()
</script>

<style scoped lang="scss">
.tabs-item {
  background: linear-gradient(180deg, rgba(32, 115, 191, 0.13) 0%, #1a65ab 100%);
  &.active {
    background: linear-gradient(180deg, rgba(0, 126, 242, 0.16) 0%, #007ef2 100%);
  }
}
</style>
