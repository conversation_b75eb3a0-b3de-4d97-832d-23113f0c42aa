<template>
  <el-drawer
    size="460px"
    v-model="showDrawer"
    :direction="direction"
    :show-close="false"
    :modal="false"
    class="drawer-custom-class"
    ref="drawerCustomRef"
  >
    <template #header>

      <div class="flex justify-between items-center h-[50px]">
        <span class="text-[20px] c-white">快速定位</span>
        <img
          src="@/assets/imgs/screen/dt-close.png"
          alt=""
          class="w-[23px] h-[22px] cursor-pointer"
          @click="handleClose"
        />
      </div>
    </template>
    <template #default>
      <el-collapse
        v-model="activeNames"
        class="collapse-custom-class"
      >
        <el-collapse-item
          title="图层"
          name="1"
        >
          <div class="collapse-content"></div>
        </el-collapse-item>
        <el-collapse-item
          title="书签"
          name="2"
        >
          <div class="collapse-content">
            <ul>
              <li
                v-for="item in bookmarkData"
                :key="item.id"
                class="collapse-list w-full h-[48px] flex color-[#ffffff] items-center justify-between px-[16px] hover:bg-[#0071f281] border-b-1 border-b-solid border-[#65a0ed33]"
                :class="{ actiove: activeBookmark === item.id }"
                @click="handleBookmarkClick(item)"
              >
                <div class="w-[44px] h-[48px] flex items-center justify-center">
                  <img
                    v-if="activeBookmark === item.id"
                    src="@/assets/imgs/screen/positioning-icon.png"
                    alt=""
                  />
                </div>
                <span class="flex-1">{{ item.bookmarkName }}</span>
                <div class="w-[44px] h-[48px] flex items-center justify-center cursor-pointer">
                  <img
                    v-if="activeBookmark === item.id"
                    src="@/assets/imgs/screen/shanchu.png"
                    @click.stop="removeBookmark(item.id)"
                  />
                  <img
                    class="remove-icon"
                    v-if="activeBookmark !== item.id"
                    src="@/assets/imgs/screen/shanchu.png"
                    @click.stop="removeBookmark(item.id)"
                  />
                </div>
              </li>
            </ul>
          </div>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #footer> </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { TMap, TOverlayLayer } from '@/map'
import { ElMessage } from 'element-plus'
import type { DrawerProps, ElDrawer } from 'element-plus'
import ScreenApi from '@/api/screen'
import { MAP_POLYGON_COLOR } from '@/views/Screen/utils/conts.ts'
const props = defineProps<{
  map: TMap | undefined
}>()
const showDrawer = ref(false)
const direction = ref<DrawerProps['direction']>('rtl')
const activeNames = ref(['1', '2'])
const activeBookmark = ref(0)
const bookmarkData = ref<any[]>([])

const drawerCustomRef = ref<InstanceType<typeof ElDrawer>>()
// 添加自定义图层的引用
const customLayer = ref<TOverlayLayer | null>(null)
const handleClose = () => {
  removeLayer()
  showDrawer.value = false
}


const handleBookmarkClick = (row: any) => {
  if (activeBookmark.value === row.id) {
    activeBookmark.value = 0
    removeLayer()
    return
  }
  activeBookmark.value = row.id
  removeLayer()
  const position = row.bookmarkLatLon ? JSON.parse(row.bookmarkLatLon) : []
  if (position.length && Array.isArray(position[0])) {
    //  回显面
    addPolygon(position)
  } else {
    // 回显点
    addPoint(position, row.bookmarkName)
  }
}
// 删除图层
const removeLayer = () => {
  const map = props.map
  if (!map) return
  if (customLayer.value) {
    map.removeOverlayLayer('regression-layer')
    customLayer.value = null
  }
}
// 显示点
const addPoint = async (position: any[], name: string) => {
  const map =  props.map
  if (!map) return

  if (!customLayer.value) {
    customLayer.value = map.createOverlayLayer({ id: 'regression-layer' })
  }

  customLayer.value.createSvgOverlay({
    userData: 'point',
    iconName: 'marker-ly-2',
    position: [position[0], position[1]],
    label: name,
    iconStyle: {
      color: 'red',
    },
  })
  map.setCenter(
    {
      lng: position[0],
      lat: position[1],
    },
    12
  )
}
// 显示面
const addPolygon = (position: any[]) => {
  const map = props.map
  if (!map) return

  if (!customLayer.value) {
    customLayer.value = map.createOverlayLayer({ id: 'regression-layer' })
  }
  customLayer.value.createPolygon({
    userData: 'polygon',
    path: position,
    lineStyle: 'solid',
    color: MAP_POLYGON_COLOR.borderColor,
    weight: 2,
    opacity: 1,
    fillColor: MAP_POLYGON_COLOR.fillColor,
    fillOpacity: 0.7,
  })
  map.setCenter(
    {
      lng: position[0][0][0],
      lat: position[0][0][1],
    },
    12
  )
}
// 获取书签列表
const getBookmarkList = async () => {
  const res = await ScreenApi.getBookmarkList().then((res) => res.data)
  bookmarkData.value = res.data
}
// 删除书签
const removeBookmark = async (id: number) => {
  if (activeBookmark.value === id && customLayer.value) {
    removeLayer()
  }
  const res = await ScreenApi.deleteBookmark(id)
  ElMessage.success(res.message)
  getBookmarkList()
}
// 显示抽屉
const show = () => {
  activeBookmark.value = 0
  showDrawer.value = true
  getBookmarkList()
}
watch(showDrawer, (newVal) => {
  if (!drawerCustomRef.value) return;
  const modal = document.querySelector('.drawer-custom-class')
  if (modal?.parentElement) {
    (modal?.parentElement as HTMLElement).style.pointerEvents = newVal ? 'none' : '';
    (modal as HTMLElement).style.pointerEvents = 'auto';
  }
});

defineExpose({
  show,
})

</script>

<style lang="scss">
.drawer-custom-class {
  pointer-events: none;
  background: rgba($color: #1a3a6e, $alpha: 0.8);
  .el-drawer__body {
    padding-top: 0;
  }
}
.collapse-custom-class {
  border: none;
  .el-collapse-item__header {
    background: transparent;
    border: none;
    color: white;
    font-size: 20px;
    padding: 0 25px;
  }
  .el-collapse-item__wrap {
    background: transparent;
    border: none;
  }
  .el-collapse-item__content {
    padding-bottom: 10px;
  }
  .collapse-content {
    width: 100%;
    background: rgba($color: #001542, $alpha: 0.4);
    ul,
    li {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    li.actiove {
      background: #0071f281;
    }
    .remove-icon {
      display: none;
    }
    .collapse-list:hover {
      .remove-icon {
        display: block;
      }
    }
  }
}
</style>
