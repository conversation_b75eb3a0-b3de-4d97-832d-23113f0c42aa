<template>
  <el-dialog
    v-model="visible"
    width="640"
    :show-close="false"
    :close-on-click-modal="false"
    class="border-1 border-solid border-[#6DADFF] rounded-[4px] bg-[#1A3A6E] bg-opacity-90 p-0"
    top="40vh"
    @close="close"
  >
    <template #header>
      <div
        class="flex justify-between items-center px-6 h-[50px] border-b-1 border-b-solid border-[#6DADFF] bg-[linear-gradient(180deg,rgba(34,149,248,0.66)_0%,rgba(0,126,242,0.3)_100%)]"
      >
        <span class="text-[20px] c-white">添加书签</span>
        <img
          src="@/assets/imgs/screen/dt-close.png"
          alt=""
          class="w-[23px] h-[22px] cursor-pointer"
          @click="close"
        />
      </div>
    </template>
    <div class="p-5 pt-0 pb-2 flex gap-[14px] mt-[12px]">
      <el-form
        :model="labelForm"
        style="width: 100%"
      >
        <el-form-item>
          <el-input
            class="custom-input-style"
            v-model="labelForm.bookmarkName"
            placeholder="请输入书签名称"
          />
        </el-form-item>
        <el-form-item class="mt-[30px]">
          <div class="w-full flex justify-end">
            <el-button
              type="primary"
              class="bg-[#0963C1] color-[#fff] border-none"
              @click="submit"
              >确认</el-button
            >
            <el-button
              class="bg-[#415E7D] color-[#fff]"
              style="border-color: #415e7d"
              @click="close"
              >取消</el-button
            >
          </div>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ScreenApi from '@/api/screen'

const emit = defineEmits(['removeLabel'])
const visible = ref<boolean>(false)
const labelForm = ref({
  bookmarkName: '',
  bookmarkLatLon: [],
})
const open = (item: any) => {
  labelForm.value = item
  visible.value = true
}
const close = () => {
  visible.value = false
  emit('removeLabel')
}

const submit = async () => {
  if (!labelForm.value.bookmarkName) {
    ElMessage.warning('书签名称不能为空')
    return
  }
  try {
    await ScreenApi.addBookmark({...labelForm.value})
    ElMessage.success('书签已保存！点击底部 <快速定位> 随时查找该位置')
    close()
  } catch (error) {
    console.log(error)
  }
}

defineExpose({
  open,
})
</script>

<style lang="scss">
.custom-input-style {
  /* 边框样式 */
  --el-input-border-color: #1756a8;
  --el-input-hover-border-color: #2a5a9e;
  --el-input-focus-border-color: #3a7ace;

  /* 文本样式 */
  --el-input-text-color: #333;
  --el-input-font-size: 16px;

  /* 背景样式 */
  --el-input-bg-color: #f8f8f8;
}

.custom-input-style .el-input__inner {
  font-family: 'Arial', sans-serif;
  letter-spacing: 0.5px;
  color: white;
}

.custom-input-style .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--el-input-border-color) inset;
  background: #1a3a6e;
}

.custom-input-style .el-input__wrapper:hover {
  box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset;
}

.custom-input-style .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
}
.custom-input-style .input__placeholder {
  color: white;
}
</style>
