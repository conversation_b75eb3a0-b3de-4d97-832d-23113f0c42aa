<template>
  <div class="absolute left-[60px] top-[150px] z-2 border-1 border-solid border-[#6DADFF] rounded-[4px] bg-[#1A3A6E] bg-opacity-75">
    <div
      v-for="(v, index) in colList"
      :key="v.thematicId"
      class="w-[76px] h-[76px] border-b border-solid border-[#2D607A] box-border relative"
      :class="{ active: index === 0 }"
    >
      <z-dynamic-route
        :route="{
          path: '/river',
          viewPath: '@/views/Screen/modules/RiverBasinModule/page/river.vue'
        }"
      >
        <template #default="{ navigate }">
          <div class="w-full h-full flex justify-center px-2 box-border items-center cursor-pointer" @click="onTabClick(v, index, navigate)">
            <span class="c-white text-[20px] text-center alibaba-semiBold">{{ v.thematicName }}</span>
          </div>
        </template>
      </z-dynamic-route>
      <template v-if="index === 0">
        <div
          class="w-[16px] h-[40px] bg-black bg-opacity-60 absolute right-[-18px] bottom-0 flex justify-center items-center cursor-pointer"
          @click="isShowClassify = !isShowClassify"
        >
          <el-icon color="#6DADFF" size="20px">
            <ArrowLeft v-if="isShowClassify" />
            <ArrowRight v-else />
          </el-icon>
        </div>
        <transition name="el-zoom-in-left">
          <div v-show="isShowClassify" class="absolute left-[93px] top-[34px] z-2">
            <div class="border-1 border-solid border-[#6DADFF] rounded-[4px] bg-[#1A3A6E] bg-opacity-75 w-[300px] classify-tree">
              <div class="flex justify-between px-4 py-2 bg-[#006FF2] bg-opacity-32">
                <span class="c-[#5BC9FF] ml-1 text-lg alibaba-semiBold">分类选择</span>
                <span class="c-[#D4A373]">已选{{ checkedKeys?.length }}项</span>
              </div>
              <el-tree
                class="w-full bg-transparent c-[#EDF8FF] text-lg p-3"
                :data="dataTree"
                show-checkbox
                node-key="thematicClassId"
                :highlight-current="false"
                :icon="ArrowRightBold"
                :default-checked-keys="checkedKeys"
                :props="{
                  children: 'children',
                  label: 'thematicClassName'
                }"
                @check="check"
              />
            </div>
          </div>
        </transition>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, ArrowRight, ArrowRightBold } from '@element-plus/icons-vue'
import { computed, inject, ref } from 'vue'
import { ScreenStateKey } from '@/views/Screen/utils/injection.ts'
import ScreenApi from '@/api/screen.ts'

const emit = defineEmits<{
  (e: 'ready', values: number[]): void
}>()

const screenState = inject(ScreenStateKey)
if (!screenState) throw new Error('screenState must be provided')
const { tId, featuredTree } = screenState

const colList = computed(() => {
  return featuredTree?.value.find(e => e.thematicId === tId.value)?.children || []
})

const isShowClassify = ref(false)

const onTabClick = (v: any, i: number, navigate: any) => {
  if (i === 0) return
  navigate({
    query: {
      fid: tId.value,
      tid: v.thematicId
    }
  })
}

const dataTree = ref<any[]>([])
const checkedKeys = ref<number[]>([])
const getCategoryTree = () => {
  ScreenApi.getThematicFeatureCategoryTree({
    thematicId: colList.value[0].thematicId
  }).then(res => {
    dataTree.value = res.data
    // 递归获取所有id
    const getAllIds = (data: any) => {
      const ids: number[] = []
      data.forEach((item: any) => {
        ids.push(item.thematicClassId)
        if (item.children?.length) {
          ids.push(...getAllIds(item.children))
        }
      })
      return ids
    }
    checkedKeys.value = getAllIds(dataTree.value)
    emit('ready', checkedKeys.value)
  })
}


const check = (data: any, checked: any) => {
  checkedKeys.value = checked.checkedKeys
  emit('ready', checkedKeys.value)
}

defineExpose({
  loadTree: getCategoryTree
})
</script>

<style scoped lang="scss">
.active{
  background: radial-gradient(circle at center, rgba(0,126,242,0.4) 0%, #007EF2 100%);
}
.classify-tree{
  :deep(.el-tree-node__content:hover) {
    background: transparent;
  }
  :deep(.el-tree-node:focus>.el-tree-node__content) {
    background: transparent;
  }
  :deep(.el-tree-node__content) {
    height: 38px;
  }
  :deep(.el-tree-node__expand-icon) {
    color: #6DADFF;
    font-size: 16px;
  }
  :deep(.el-checkbox__input .el-checkbox__inner) {
    background: transparent;
    border-color: #6DADFF;
  }
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background: #007EF2;
    border-color: #007EF2;
  }
}
</style>
