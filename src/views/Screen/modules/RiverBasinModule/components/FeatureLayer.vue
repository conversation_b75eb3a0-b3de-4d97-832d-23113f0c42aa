<template>
  <div class="absolute bottom-[100px] left-[100px] z-3 bg-white px-3">
    <el-checkbox-group v-model="checkList">
      <el-checkbox v-for="v in list" :key="v.value" :label="v.label" :value="v.value" />
    </el-checkbox-group>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const list = [
  {
    label: '河流面',
    value: '1',
  },
  {
    label: '河流线',
    value: '2',
  },
  {
    label: '森林公园',
    value: '3',
  },
  {
    label: '自然保护区',
    value: '4',
  }
]
const checkList = ref<string[]>(['1', '2', '3', '4'])


// 测试线要素 - 加载河流线数据
// const setFeatureLayerLines = async (mapRef,) => {
//   if (!mapRef.value) return
//   const map = mapRef.value.getMap()
//   if (!map) return
//   const featureLayer = ref<TFeatureLayer>()
//
//   try {
//     if (featureLayer.value) {
//       featureLayer.value.clear()
//     }
//
//     featureLayer.value = new TFeatureLayer(map, {
//       lineStyle: {
//         color: '#28ade1',
//         weight: 2,
//         opacity: 0.8,
//         showArrow: false,
//       },
//       events: {
//         onClick: (feature, geometryType, overlay) => {
//           const name = feature.properties?.name || feature.properties?.NAME || '未知河流'
//           ElMessage.info(`点击了线要素: ${name}`)
//           console.log('线要素详情:', feature)
//         },
//         onMouseOver: (feature, geometryType, overlay) => {},
//         onMouseOut: (feature, geometryType, overlay) => {},
//       },
//       fieldMapping: {
//         nameField: 'name',
//       },
//       geometryTypes: ['LineString', 'MultiLineString'], // 只加载线要素
//     })
//
//     // 加载河流线数据
//     const response = await fetch('/river_line.geojson')
//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`)
//     }
//     const geojsonData = await response.json()
//
//     await featureLayer.value.load(geojsonData, true)
//
//     ElMessage.success('线要素图层加载成功！')
//     console.log('已加载的要素数量:', geojsonData.features.length)
//   } catch (error) {
//     console.error('加载线要素失败:', error)
//     ElMessage.error('加载线要素失败')
//   }
// }
</script>

<style scoped lang="scss">

</style>
