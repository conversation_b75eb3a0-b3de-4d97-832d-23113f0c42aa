<template>
  <el-dialog
    v-model="visible"
    width="1600"
    :show-close="false"
    class="border-1 border-solid border-[#6DADFF] rounded-[4px] bg-[#1A3A6E] bg-opacity-90 p-0"
    top="10vh"
    @close="visible = false"
  >
    <template #header>
      <div class="flex justify-between items-center px-6 h-[80px] border-b-1 border-b-solid border-[#6DADFF] bg-[linear-gradient(180deg,rgba(34,149,248,0.66)_0%,rgba(0,126,242,0.3)_100%)]">
        <div class="h-[52px] min-w-[272px] bg-[url(@/assets/imgs/screen/hx-1.png)] bg-no-repeat bg-[length:160px_52px] flex items-center pl-[55px]">
          <span class="text-[26px] c-white youshe-bold">{{ curData.thematicFeatureDataName }}</span>
        </div>
        <img src="@/assets/imgs/screen/dt-close.png" alt="" class="w-[23px] h-[22px] cursor-pointer" @click="visible = false">
      </div>
    </template>
    <div class="p-5 pt-0 flex gap-[14px]">
      <div class="w-[900px] border-1 border-solid border-[#6DADFF] bg-[#1A3A6E] flex flex-col">
        <div class="px-[15px] py-[20px]">
          <div class="bg-[url(@/assets/imgs/screen/dt-tc.png)] bg-[length:100%_100%] h-[35px] pl-[30px]">
            <span class="c-white text-[20px]">基本信息</span>
          </div>
          <div class="h-[340px] mt-2 relative">
            <TMapView
              class="z-1"
              ref="mapRef"
              :options="mapOptions"
              @ready="onMapReady"
            />
            <div class="flex items-center px-3 py-2 cursor-pointer absolute z-2 right-[30px] bottom-[30px] border-1 border-solid border-[#6DADFF] rounded-[4px] bg-[#1A3A6E] bg-opacity-75">
              <img src="@/assets/imgs/screen/dt-jk.png" alt="" class="w-[26px] h-[26px]">
              <span class="c-white text-lg ml-2">查看监控视频</span>
            </div>
          </div>
        </div>
        <div class="p-[30px] bg-[#124E95] border-t-1 border-t-solid border-[#6DADFF] flex-1">
          <div class="flex mb-3">
            <span class="c-white text-lg min-w-[90px]">站点名称：</span>
            <span class="c-[#5BC9FF] text-lg">{{ curData.locationName }}</span>
          </div>
          <div class="flex mb-3">
            <span class="c-white text-lg min-w-[90px]">所属区域：</span>
            <span class="c-[#5BC9FF] text-lg">{{ curData.areaName }}</span>
          </div>
          <div class="flex mb-3">
            <span class="c-white text-lg min-w-[90px]">类型：</span>
            <span class="c-[#5BC9FF] text-lg">{{ curData.thematicClassName }}</span>
          </div>
          <div class="flex">
            <span class="c-white text-lg min-w-[90px]">简介：</span>
            <span class="c-[#5BC9FF] text-lg" v-html="curData.summary"></span>
          </div>
        </div>
      </div>
      <div class="w-[634px] border-1 border-solid border-[#6DADFF] bg-[#1A3A6E] px-[15px] py-[20px] box-border">
        <div class="bg-[url(@/assets/imgs/screen/dt-td.png)] bg-[length:100%_100%] h-[35px] pl-[30px]">
          <span class="c-white text-[20px]">站点图片</span>
        </div>
        <div class="flex flex-wrap gap-[15px] mt-2">
          <div v-for="(item, index) in pictureList" :key="index" class="w-[160px] h-[160px] relative cursor-pointer" @click="onImgView(index)">
            <el-image :src="item" fit="cover" class="w-full h-full" />
            <div class="absolute top-0 left-0 w-full h-full bg-black bg-opacity-30 flex items-center justify-center">
              <el-icon :size="40" color="rgba(255,255,255, 0.6)"><ZoomIn /></el-icon>
            </div>
          </div>
        </div>
        <div class="bg-[url(@/assets/imgs/screen/dt-td.png)] bg-[length:100%_100%] h-[35px] pl-[30px] mt-5">
          <span class="c-white text-[20px]">站点视频</span>
        </div>
        <div class="flex flex-wrap gap-[15px] mt-2">
          <div v-for="(item, index) in videoList" :key="index" class="w-[180px] h-[109px] relative cursor-pointer" @click="onVideoView(item)">
            <video :src="item.videoUrl" :controls="false" class="w-full h-full"></video>
            <div class="absolute top-0 left-0 w-full h-full bg-black bg-opacity-30 flex items-center justify-center">
              <el-icon :size="40" color="rgba(255,255,255, 0.6)"><VideoPlay /></el-icon>
            </div>
          </div>
        </div>
        <div class="bg-[url(@/assets/imgs/screen/dt-td.png)] bg-[length:100%_100%] h-[35px] pl-[30px] mt-5">
          <span class="c-white text-[20px]">站点文件</span>
        </div>
        <div class="flex flex-wrap gap-[15px] mt-2 flex flex-col ">
          <div v-for="(item, index) in curData.attachmentList" :key="index" class="cursor-pointer">
            <span class="c-[#5BC9FF] text-lg underline">{{ item.attachmentName }}</span>
            <span class="c-white text-lg ml-3">查看</span>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="videoVisible"
      width="900"
      title="视频预览"
      :show-close="false"
      class="border-1 border-solid border-[#6DADFF] rounded-[4px] bg-[#1A3A6E] bg-opacity-90 p-0"
      @close="videoVisible = false"
    >
      <template #header>
        <div class="flex justify-between items-center px-6 h-[80px] border-b-1 border-b-solid border-[#6DADFF] bg-[linear-gradient(180deg,rgba(34,149,248,0.66)_0%,rgba(0,126,242,0.3)_100%)]">
          <div class="h-[52px] min-w-[272px] bg-[url(@/assets/imgs/screen/hx-1.png)] bg-no-repeat bg-[length:160px_52px] flex items-center pl-[55px]">
            <span class="text-[26px] c-white youshe-bold">视频预览</span>
          </div>
          <img src="@/assets/imgs/screen/dt-close.png" alt="" class="w-[23px] h-[22px] cursor-pointer" @click="videoVisible = false">
        </div>
      </template>
      <div class="p-5 pt-0 flex justify-center items-center h-[450px]">
        <video :src="videoItem.videoUrl" controls autoplay muted class="w-full h-full"></video>
      </div>
    </el-dialog>
    <el-image-viewer
      v-if="showImgPreview"
      :url-list="pictureList"
      show-progress
      :initial-index="imgIndex"
      @close="showImgPreview = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, nextTick, ref } from 'vue'
import { TAnchorPosition, TMap, TMapView, TSvgOverlay } from '@/map'
import { ZoomIn, VideoPlay } from '@element-plus/icons-vue'
import { MAP_POLYGON_COLOR, MAP_POLYLINE_COLOR } from '@/views/Screen/utils/conts.ts'
import { BJ_CENTER_POINT } from '@/config/default-settings.ts'

const visible = ref<boolean>(false)
const curData  = ref<any>({})
const open = (item: any) => {
  curData.value = item
  visible.value = true
  nextTick(() => {
    const map = mapRef.value?.getMap()
    if (!map) return
    map.clearOverlays()
    if(curData.value.locationType === '1') {
      drawPoint(map)
    } else if(curData.value.locationType === '2') {
      drawLine(map)
    } else if(curData.value.locationType === '3') {
      drawPolygon(map)
    }
  })
}

const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const mapOptions = {
  center: {
    lng: BJ_CENTER_POINT.lng,
    lat: BJ_CENTER_POINT.lat,
  },
  search: {
    show: false
  }
}
const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')
}

const drawPoint = (map: TMap) => {
  if(curData.value.location) {
    const [lng, lat] = JSON.parse(curData.value.location)
    const circleSvg = new TSvgOverlay({
      position: [lng, lat],
      text: curData.value.locationName,
      iconName: `marker-${curData.value.thematicClassIcon}`,
      size: [35, 50],
      anchor: TAnchorPosition.BOTTOM_CENTER,
      textOffset: [5, -5]
    })
    map.addOverlay(circleSvg)
    map.setCenter({
      lng,
      lat
    }, 15)
  }
}

const drawLine = (map: TMap) => {
  if(curData.value.location) {
    const location = JSON.parse(curData.value.location)
    map.createPolyline({
      path: location,
      color: MAP_POLYLINE_COLOR.color,
      weight: 3,
      opacity: 1
    })
  }
}

const drawPolygon = (map: TMap) => {
  if(curData.value.location) {
    const location = JSON.parse(curData.value.location)
    map.createPolygon({
      path: location,
      color: MAP_POLYGON_COLOR.borderColor,
      weight: 2,
      opacity: 1,
      fillColor: MAP_POLYGON_COLOR.fillColor,
      fillOpacity: 0.7
    })
  }
}

const showImgPreview = ref(false)
const imgIndex = ref<number>(0)
const pictureList = computed(() => {
  return (curData.value.pictureList || []).map((it: any) => import.meta.env.VITE_STATIC_ASSETS_URL + it.pictureUrl)
})
const videoList = computed(() => {
  return (curData.value.videoList || []).map((it: any) => ({
    ...it,
    videoUrl: import.meta.env.VITE_STATIC_ASSETS_URL + it.videoUrl
  }))
})
const onImgView = (index: number) => {
  imgIndex.value = index
  showImgPreview.value = true
}

const videoVisible = ref<boolean>(false)
const videoItem = ref<any>()
const onVideoView = (item: any) => {
  videoItem.value = item
  videoVisible.value = true
}

defineExpose({
  open
})
</script>

<style scoped lang="scss">

</style>
