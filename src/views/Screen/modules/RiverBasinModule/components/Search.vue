<template>
  <div class="absolute right-[60px] top-[150px] h-[56px] z-2 border-1 border-solid border-[#6DADFF] rounded-[4px] bg-[#1A3A6E] bg-opacity-75">
    <div class="h-full flex items-center pr-[16px]">
      <div class="w-0 transition-all duration-300 overflow-hidden mr-4" :class="{ 'expanded ml-4': isExpanded }">
<!--        <el-input v-model="keyword" size="large" clearable placeholder="请输入搜索内容" />-->
        <el-select
          v-model="keyword"
          filterable
          remote
          :reserve-keyword="false"
          clearable
          fit-input-width
          placeholder="请输入监测点名称查询"
          append-to=".screen-wrapper"
          :remote-method="remoteMethod"
          @change="onChange"
        >
          <el-option
            v-for="item in options"
            :key="item.thematicFeatureDataId"
            :value="item.thematicFeatureDataId"
            :label="item.thematicFeatureDataName"
          >
            <div class="flex items-center">
              <div>{{ item.thematicFeatureDataName }}</div>
            </div>
          </el-option>
        </el-select>
      </div>
      <el-icon :size="20" color="#fff" class="cursor-pointer" @click="() => isExpanded = !isExpanded">
        <Search />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { ref } from 'vue'

const props = defineProps<{
  mapData: any[]
}>()

const emit = defineEmits<{
  (e: 'search', val: number): void
}>()

const keyword = ref<string>('')
const isExpanded = ref<boolean>(false)
const options = ref<any[]>([])

const remoteMethod = (query: string) => {
  if (query) {
    setTimeout(() => {
      options.value = props.mapData.filter((it) => {
        return it.thematicFeatureDataName.includes(query)
      })
    }, 100)
  } else {
    options.value = []
  }
}

const onChange = (val: number) => {
  if (val) {
    emit('search', val)
  }
}

</script>

<style scoped lang="scss">
.active{
  background: radial-gradient(circle at center, rgba(0,126,242,0.4) 0%, #007EF2 100%);
}

.expanded {
  width: 400px;
}
</style>
