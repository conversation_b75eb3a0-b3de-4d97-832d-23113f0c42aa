<template>
  <div class="absolute z-2 right-[56px] bottom-[59px] border-1 border-solid border-[#6DADFF] rounded-[4px] bg-[#1A3A6E] bg-opacity-75 flex">
    <div
      v-for="v in tools" :key="v.value"
      class="px-3 h-[50px] relative select-none tool-item"
    >
      <div class="w-full h-full cursor-pointer flex items-center" @click="v.onclick(v)">
        <img :src="v.icon" alt="" class="w-[30px] h-[30px]">
        <span class="c-white text-[18px] ml-2 alibaba-semiBold">{{ v.label }}</span>
      </div>
      <transition name="el-fade-  in">
        <div v-show="v.value === current">
          <div class="triangle"></div>
          <div class="bg-[#00A1FF] bg-opacity-40 absolute left-0 top-0 w-full h-full cursor-pointer" @click="v.onclick(v)"></div>
          <div v-if="v.value === '1'" class="absolute left-0 top-[-60px] w-full h-full flex items-center justify-center gap-2">
            <div class="w-[40px] h-[40px] rounded-[4px] bg-white flex justify-center items-center cursor-pointer" @click="emit('map-event', { event: 'zoomIn' })">
              <img src="@/assets/imgs/screen/zoom-in.png" alt="" class="w-[30px] h-[30px]">
            </div>
            <div class="w-[40px] h-[40px] rounded-[4px] bg-white flex justify-center items-center cursor-pointer" @click="emit('map-event', { event: 'zoomOut' })">
              <img src="@/assets/imgs/screen/zoom-out.png" alt="" class="w-[30px] h-[30px]">
            </div>
            <div class="w-[40px] h-[40px] rounded-[4px] bg-white flex justify-center items-center cursor-pointer" @click="emit('map-event', { event: 'resetView' })">
              <img src="@/assets/imgs/screen/restoration.png" alt="" class="w-[30px] h-[30px]">
            </div>
          </div>
          <div v-if="v.value === '2'" class="absolute left-0 top-[-60px] w-[226px] h-full flex items-center justify-center gap-2">
            <div class="w-[100px] h-[40px] rounded-[4px] bg-white flex justify-center items-center cursor-pointer" @click="emit('map-event', { event: 'point' })">
              <img src="@/assets/imgs/screen/tool-point.png" alt="" class="w-[24px] h-[24px]">
              <span class="ml-[2px]">绘制点</span>
            </div>
            <div class="w-[116px] h-[40px] rounded-[4px] bg-white flex justify-center items-center cursor-pointer" @click="emit('map-event', { event: 'region' })">
              <img src="@/assets/imgs/screen/tool-region.png" alt="" class="w-[24px] h-[24px]">
              <span class="ml-[2px]">绘制区域</span>
            </div>
          </div>
          <div v-if="v.value === '4'" class="absolute left-0 top-[-60px] w-[170px] h-full flex items-center justify-center gap-2">
            <div class="w-[80px] h-[40px] rounded-[4px] bg-white flex justify-center items-center cursor-pointer" @click="emit('map-event', { event: 'ranging' })">
              <img src="@/assets/imgs/screen/tool-ranging.png" alt="" class="w-[24px] h-[24px]">
              <span class="ml-[2px]">测距</span>
            </div>
            <div class="w-[80px] h-[40px] rounded-[4px] bg-white flex justify-center items-center cursor-pointer" @click="emit('map-event', { event: 'measuringSurface' })">
              <img src="@/assets/imgs/screen/tool-measuring-surface.png" alt="" class="w-[24px] h-[24px]">
              <span class="ml-[2px]">测面</span>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const emit = defineEmits<{
  (e: 'map-event', values: any): void
}>()

const current = ref<string>()
const tools = [
  {
    value: '1',
    label: '地图展示',
    icon: new URL('@/assets/imgs/screen/layers-icon.png', import.meta.url).href,
    onclick: (item: any) => {
      if(current.value === item.value) return current.value = ''
      current.value = item.value
    }
  },
  {
    value: '2',
    label: '书签',
    icon: new URL('@/assets/imgs/screen/bookmarks-icon.png', import.meta.url).href,
    onclick: (item: any) => {
      if(current.value === item.value) return current.value = ''
      current.value = item.value

    }
  },
  {
    value: '3',
    label: '快速定位',
    icon: new URL('@/assets/imgs/screen/positioning-icon.png', import.meta.url).href,
    onclick: (item: any) => {
      // if(current.value === item.value) return current.value = ''
      // current.value = item.value
      emit('map-event', { event: 'rapidPositioning' })
    }
  },
  {
    value: '4',
    label: '二维量算',
    icon: new URL('@/assets/imgs/screen/calculation-icon.png', import.meta.url).href,
    onclick: (item: any) => {
      if(current.value === item.value) return current.value = ''
      current.value = item.value
    }
  },
  {
    value: '5',
    label: '二维分析',
    icon: new URL('@/assets/imgs/screen/analyze-icon.png', import.meta.url).href,
    onclick: (item: any) => {
      console.log('书签', item)
    }
  },
  {
    value: '6',
    label: '多屏对比',
    icon: new URL('@/assets/imgs/screen/multi-screen-icon.png', import.meta.url).href,
    onclick: (item: any) => {
      console.log('书签', item)
    }
  }
]
</script>

<style scoped lang="scss">
.tool-item{
  &::after{
    content: '';
    width: 1px;
    height: 30px;
    background-color: #5A8F7B;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
  &:last-child{
    &::after{
      display: none;
    }
  }
  .triangle {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #6DADFF; /* 边框色 */
  }

  .triangle::after {
    content: '';
    position: absolute;
    top: 1px;
    left: -6px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid rgba(26, 58, 110, 0.75); /* 背景色 */
  }
}
</style>
