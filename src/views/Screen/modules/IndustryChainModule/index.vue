<template>
  <div class="absolute w-full h-full">
    <div class="flex flex-col absolute top-[117px] left-[358px] gap-y-3 z-2">
      <div class="w-[174px] h-[52px] bg-[url(@/assets/imgs/screen/hx-1.png)] bg-[length:100%_100%] flex items-center justify-center">
        <span class="text-lg c-white alibaba-semiBold font-italic ml-12">重点产业链企业</span>
      </div>
      <div class="w-[174px] h-[52px] bg-[url(@/assets/imgs/screen/hx-2.png)] bg-[length:100%_100%] flex items-center">
        <span class="text-lg c-white alibaba-semiBold font-italic ml-12">重点产业链</span>
      </div>
    </div>
    <TMapView
      class="z-1"
      ref="mapRef"
      :options="mapOptions"
      @ready="onMapReady"
    />
    <DataCard v-show="chartShow" />
    <ToolBox
      @map-event="mapEvent"
      @change-chart-show="(bool:boolean) => chartShow = bool"
    />
    <div class="absolute z-1 left-0 top-0 bottom-0 w-[670px] left-bg pointer-events-none"></div>
    <div class="absolute z-1 right-0 top-0 bottom-0 w-[670px] right-bg pointer-events-none"></div>

    <z-dynamic-route
      ref="enterpriseRouteRef"
      :route="{
        path: '/enterpriseDetails',
        viewPath: '@/views/Screen/modules/IndustryChainModule/pages/enterpriseDetails/enterpriseDetails.vue'
      }"
    />
  </div>
</template>

<script setup lang="ts">
import {
  TAnchorPosition,
  TAnnotationMarker,
  TGeoJSONLayer,
  TMap,
  TMapView,
  TSvgOverlay
} from '@/map'
import { ref } from 'vue'
import { BJ_CENTER_POINT } from '@/config/default-settings.ts'
import geoJsonData from '@/map/assets/geo/baoji.json'
import ToolBox from './components/ToolBox.vue'
import DataCard from './components/DataCard.vue'
import ScreenApi from '@/api/screen.ts'
import { MAP_POP_CONFIG } from '@/views/Screen/utils/conts.ts'
import MapPop from './components/MapPop.vue'

const chartShow = ref<boolean>(true)

const enterpriseRouteRef = ref()

const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const mapOptions = {
  center: {
    lng: BJ_CENTER_POINT.lng,
    lat: BJ_CENTER_POINT.lat,
  },
  zoom: 8.5,
  minZoom: 3,
  maxZoom: 18,
  search: {
    show: false
  }
}

const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')

  getMapData()
}

const mapEvent = (data: any) => {
  const { event } = data
  if (event === 'zoomIn') {
    mapRef.value?.zoomIn()
  }
  if (event === 'zoomOut') {
    mapRef.value?.zoomOut()
  }
  if (event === 'resetView') {
    mapRef.value?.resetView()
  }
}

const geoJSONLayer = ref<TGeoJSONLayer | null>(null)
const drawLayer = (names:string[]) => {
  const map = mapRef.value?.getMap()
  if (!map) return
  geoJSONLayer.value = new TGeoJSONLayer(map, {
    drilldown: {
      enable: true,
      maxDepth: 1,
      inactiveStyle: {
        opacity: 0,
        fillOpacity: 0,
      },
    },
    label: {
      show: true,
      offset: [0, 0],
      style: {
        fontSize: '12px',
        color: '#ffffff',
        fontWeight: 'bold',
        padding: '0px',
        border: 'none',
        boxShadow: 'none',
      },
    },
    onMouseOver(polygon) {
      polygon.setFillColor('#6FF9ED')
    },
    onMouseOut(polygon, feature) {
      polygon.setFillColor(names.includes(feature.properties.name) ? '#D4A373' : '#5CA0FA')
    },
    computeFeatureStyle(feature) {
      return {
        fillColor: names.includes(feature.properties.name) ? '#D4A373' : '#5CA0FA'
      }
    },
  })
  geoJSONLayer.value?.load(geoJsonData as any)
}

const drawCyPoint = (list: any[]) => {
  const map = mapRef.value?.getMap()
  if (!map) return
  if (!list?.length) return
  list.forEach(pop => {
    if(pop.list?.length) {
      const popMarker = new TAnnotationMarker({
        position: pop?.location as [number, number],
        zIndexOffset: 900,
        markerOptions: {
          size: 80,
          svgMarkup: TSvgOverlay.generateRippleMarkup(80, { strokeWidth: 2 }),
          // label: 'svg 标记',
          labelRemoveable: true,
          iconStyle: {
            // fill: 'red',
          },
        },
        lineOptions: {
          length: pop?.length,
          angle: pop?.angle,
          style: {
            color: '#EBE02D',
            width: 2,
            dashed: true
          },
        },
        panelOptions: {
          style: {
            background: 'rgba(255, 255, 255, 0)',
            boxShadow: 'none',
            padding: '0',
          },
          content: MapPop,
          offset: pop?.offset ?? [0, 0],
          anchorPosition: 'auto',
          contentProps: {
            item: pop
          }
        },
      })
      map.addOverlay(popMarker)
    }
  })
}

const drawQyPoint = (list: any[]) => {
  const map = mapRef.value?.getMap()
  if (!map) return
  if (!list?.length) return
  list.forEach(it => {
    if(it.latitudeLongitude) {
      const location = JSON.parse(it.latitudeLongitude)
      const circleSvg = new TSvgOverlay({
        position: location,
        iconName: `marker-zdqy`,
        size: [31, 40],
        zIndexOffset: 9000,
        anchor: TAnchorPosition.BOTTOM_CENTER,
        events: {
          click: () => {
            enterpriseRouteRef.value?.navigate({
              query: {
                id: it.companyId
              }
            })
          }
        }
      })
      map.addOverlay(circleSvg)
    }
  })
}

const getMapData = () => {
  ScreenApi.getIndustryChainPointDataList().then(res => {
    const regions = MAP_POP_CONFIG.map(pop => {
      const list = [] as any[]
      (res.data || []).forEach((it:any) => {
        if(it.regionCode) {
          const regionCode = JSON.parse(it.regionCode)
          if(regionCode.includes(pop.areaCode)) {
            list.push(it)
          }
        }
      })
      return {
        ...pop,
        list
      }
    })
    drawCyPoint(regions || [])
    drawLayer(regions.filter(it => it.list.length).map(it => it.areaName))
  })
  ScreenApi.getIndustryChainEnterpriseDataList().then(res => {
    drawQyPoint(res.data || [])
  })
}
</script>

<style scoped lang="scss">
.left-bg{
  background: linear-gradient(90deg, rgba(3,21,50,0.50) 0%, rgba(26,58,110,0) 100%);
}
.right-bg{
  background: linear-gradient(90deg, rgba(26,58,110,0) 0%, rgba(3,21,50,0.5) 100%);
}
</style>
