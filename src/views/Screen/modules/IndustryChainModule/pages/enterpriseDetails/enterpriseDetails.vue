<template>
  <z-screen-container :loading="loading" :wrapperStyle="{ background: '#1A3A6E' }">
    <Header />
    <div class="h-[calc(100%-120px)] box-border mt-[120px] px-[44px] pb-[30px] overflow-auto">
      <SCard class="px-[30px] py-[20px]">
        <div class="flex flex-col h-[50px]">
          <div class="h-full min-w-[272px] bg-[url(@/assets/imgs/screen/hx-1.png)] bg-no-repeat bg-[length:160px_52px] flex items-center pl-[58px]">
            <span class="text-[26px] c-white alibaba-semiBold">{{ dataInfo.companyName }}</span>
          </div>
          <div class="border-b-1 border-b-solid border-[#4A79B7] ml-[32px]"></div>
        </div>
        <div class="c-white text-[20px] mt-[20px] pl-[10px]" v-html="dataInfo.companyIntroduction"></div>
      </SCard>
      <div class="flex gap-x-[24px] mt-[24px]">
        <SCard class="w-[947px]">
          <el-carousel trigger="click" height="660px" indicator-position="outside">
            <el-carousel-item v-for="(v, i) in companyImg" :key="i">
              <el-image
                :src="v"
                :preview-src-list="companyImg"
                :initial-index="i"
                fit="cover"
                preview-teleported
                class="w-full h-full rounded-6"
              />
            </el-carousel-item>
          </el-carousel>
        </SCard>
        <div class="flex flex-col w-[870px]">
          <div class="bg-[url(@/assets/imgs/screen/card-border-10.png)] bg-[length:100%_100%] h-[307px] relative">
            <span class="absolute top-[-15px] left-4 right-7 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">基本信息</span>
            <div class="px-[46px] py-[40px] flex">
              <div class="flex flex-col c-white alibaba-semiBold text-lg gap-y-[30px]">
                <span>法定代表人：{{ dataInfo.companyLegal }}</span>
                <span>成立日期：{{ dataInfo.companyDate }}</span>
                <span>注册资本：{{ dataInfo.companyCapital }}{{ capitalUnit }}</span>
                <span>总资产：{{ dataInfo.companyAsset }}{{ assetUnit }}</span>
              </div>
              <div class="flex flex-col c-white alibaba-semiBold text-lg gap-y-[30px] ml-[25px]">
                <span>企业性质：{{ companyType}}</span>
                <span>行政区划：{{ regionName }}</span>
                <span>注册地址：{{ dataInfo.companyAddress }}</span>
                <span>员工总数：{{ dataInfo.companyEmployee || 0 }}</span>
              </div>
              <div class="flex flex-col ml-auto">
                <div class="flex flex-col items-center">
                  <el-progress :width="80" type="circle" stroke-linecap="square" :stroke-width="8" :percentage="dataInfo.marketShare || 0" color="#E6C365" />
                  <span class="c-white mt-[10px]">市场占有率</span>
                </div>
                <div class="w-[112px] h-[96px] bg-[url(@/assets/imgs/screen/sztp.png)] bg-[length:100%_100%] flex flex-col justify-between items-center box-border">
                  <div class="flex items-end alibaba-semiBold c-white mt-2">
                    <span class="text-[26px] c-[#E6C365]">{{ dataInfo.employmentNum || 0 }}</span>
                  </div>
                  <span class="c-white mb-[-10px]">带动就业人数</span>
                </div>
              </div>
            </div>
          </div>
          <div class="flex gap-x-[20px] mt-[20px]">
            <div class="bg-[url(@/assets/imgs/screen/card-border-5.png)] bg-[length:100%_100%] w-[425px] h-[368px] relative">
              <span class="absolute top-[-5px] left-4 right-4 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">近5年营业收入趋势</span>
              <div ref="chartRef1" class="h-full"></div>
            </div>
            <div class="bg-[url(@/assets/imgs/screen/card-border-5.png)] bg-[length:100%_100%] w-[425px] h-[368px] relative">
              <span class="absolute top-[-5px] left-4 right-4 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">近5年净利润趋势</span>
              <div ref="chartRef2" class="h-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </z-screen-container>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import Header from './components/Header.vue'
import SCard from '@/views/Screen/components/SCard.vue'
import * as echarts from 'echarts'
import ScreenApi from '@/api/screen.ts'
import { useRoute } from 'vue-router'
import DictApI from '@/api/dict.ts'
import ComApi from '@/api/common.ts'

const route = useRoute()

type EChartsOption = echarts.EChartsOption

const loading = ref<boolean>(false)

const chartRef1 = ref<HTMLDivElement | null>(null)
const chartRef2 = ref<HTMLDivElement | null>(null)

const initChart1 = () => {
  const companyRevenue = dataInfo.value.companyRevenue ? JSON.parse(dataInfo.value.companyRevenue) : []
  const chart = echarts.init(chartRef1.value)
  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '7%',
      right: '7%',
      bottom: '8%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(228, 252, 255, 0.6)',
        fontSize: 14
      },
      axisLine: {
        lineStyle: {
          color: '#3776A1'
        }
      },
      data: companyRevenue.map((it: any) => it.year)
    },
    yAxis: {
      type: 'value',
      // name: '万元',
      axisLine: {
        lineStyle: {
          color: 'rgba(228, 252, 255, 0.6)'
        }
      },
      axisLabel: {
        color: 'rgba(228, 252, 255, 0.6)',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#3776A1'
        }
      }
    },
    series: [
      {
        type: 'line',
        smooth: false,
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          color: '#16E5FF',
          borderColor: '#0E2D5D',
          borderWidth: 1
        },
        label: {
          show: true,
          position: "top",
          color: '#16E5FF',
          fontSize: 14
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: `rgba(22, 229, 255, 0.6)`
              },
              {
                offset: 1,
                color: `rgba(22, 229, 255, 0)`
              },
            ],
            false
          )
        },
        data: companyRevenue.map((it: any) => it.operatingRevenue)
      }
    ]
  }
  chart.setOption(option)
}

const initChart2 = () => {
  const companyProfit = dataInfo.value.companyProfit ? JSON.parse(dataInfo.value.companyProfit) : []
  const chart = echarts.init(chartRef2.value)
  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '7%',
      right: '7%',
      bottom: '8%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(228, 252, 255, 0.6)',
        fontSize: 14
      },
      axisLine: {
        lineStyle: {
          color: '#3776A1'
        }
      },
      data: companyProfit.map((it:any) => it.year)
    },
    yAxis: {
      type: 'value',
      // name: '万斤',
      axisLine: {
        lineStyle: {
          color: 'rgba(228, 252, 255, 0.6)'
        }
      },
      axisLabel: {
        color: 'rgba(228, 252, 255, 0.6)',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#3776A1'
        }
      }
    },
    series: [
      {
        type: 'line',
        smooth: false,
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          color: '#169CFF',
          borderColor: '#0E2D5D',
          borderWidth: 1
        },
        label: {
          show: true,
          position: "top",
          color: '#169CFF',
          fontSize: 14
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: `rgba(22, 156, 255, 0.6)`
              },
              {
                offset: 1,
                color: `rgba(22, 229, 255, 0)`
              },
            ],
            false
          )
        },
        data: companyProfit.map((it:any) => it.operatingRevenue)
      }
    ]
  }
  chart.setOption(option)
}

const companyImg = computed(() => {
  return (dataInfo.value.companyImg ? JSON.parse(dataInfo.value.companyImg) : []).map((it:any) => import.meta.env.VITE_STATIC_ASSETS_URL + it.pictureUrl)
})

const unitData = ref<any[]>([])
const capitalUnit = computed(() => {
  return unitData.value.find(it => it.dictionaryCode === dataInfo.value.capitalUnit)?.dictionaryName
})
const assetUnit = computed(() => {
  return unitData.value.find(it => it.dictionaryCode === dataInfo.value.assetUnit)?.dictionaryName
})

const typesData = ref<any[]>([])
const companyType = computed(() => {
  return typesData.value.find(it => it.dictionaryCode === dataInfo.value.companyType)?.dictionaryName
})

const areaData = ref<any[]>([])
const regionName = computed(() => {
  return areaData.value.find(it => it.areaCode === dataInfo.value.regionCode)?.areaName
})

const dataInfo = ref<any>({})
const getData = () => {
  ScreenApi.getThematicIndustryEnterpriseDetail(route.query.id as string).then(res => {
    dataInfo.value = res.data || {}
    initChart1()
    initChart2()
  })
}

onMounted(async () => {
  unitData.value = await DictApI.getDictItemsByCode('systemUnit').then((res) => res.data)
  typesData.value = await DictApI.getDictItemsByCode('companyTypes').then((res) => res.data)
  areaData.value = await ComApi.getAreaList().then((res) => res.data)
  getData()
})
</script>

<style scoped lang="scss">
:deep(.el-progress__text) {
  color: #ffffff;
  font-family: alibaba-semiBold;
  font-size: 22px !important;
}
:deep(.el-carousel__button) {
  background: #3474C9;
  opacity: 1;
}
:deep(.el-carousel__indicator.is-active button) {
  background: #E6C365;
}
</style>
