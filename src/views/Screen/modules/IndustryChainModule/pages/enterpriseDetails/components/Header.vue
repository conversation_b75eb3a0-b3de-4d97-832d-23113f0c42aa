<template>
  <div
    class="h-[149px] absolute left-0 right-0 top-0 bg-[url(@/assets/imgs/screen/topbg-2.png)] bg-[length:100%_100%] z-1"
  >
    <img src="@/assets/imgs/screen/back.png" alt="" class="w-[40px] h-[40px] absolute left-[36px] top-[25px] z-2 cursor-pointer" @click="router.back()">
    <div class="flex items-center justify-center gap-x-10 pl-[36px] pr-[62px] pt-[25px] relative">
      <span class="c-white text-[30px] alibaba-semiBold">企业详情信息</span>
      <img src="@/assets/imgs/screen/xzjh.png" alt="" class="w-[65px] h-[10px]">
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()
</script>

<style scoped lang="scss">
:deep(.el-input__wrapper) {
  border: none;
  background: transparent;
  color: #fff;
  box-shadow: none;
  background: linear-gradient( 270deg, rgba(9,53,116,0.74) 0%, rgba(34,116,243,0.8) 100%);
  .el-input__inner {
    color: #fff;
    font-size: 20px;
  }
  .el-input__prefix{
    color: #fff;
    font-size: 20px;
  }
}
</style>
