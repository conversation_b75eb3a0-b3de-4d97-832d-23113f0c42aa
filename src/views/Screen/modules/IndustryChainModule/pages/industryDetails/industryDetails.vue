<template>
  <z-screen-container :loading="loading" :wrapperStyle="{ background: '#1A3A6E' }">
    <Header />
    <div class="h-[calc(100%-120px)] box-border mt-[120px] px-[24px] pb-[24px] flex relative z-2">
      <DInfo />
      <div class="flex-1 pt-[5px] box-border">
        <Map />
      </div>
      <div class="flex flex-col justify-between">
        <DCount />
        <DList />
      </div>
    </div>
  </z-screen-container>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Header from './components/Header.vue'
import DInfo from './components/DInfo.vue'
import DCount from './components/DCount.vue'
import DList from './components/DList.vue'
import Map from './components/Map.vue'

const loading = ref<boolean>(false)
</script>

<style scoped lang="scss">

</style>
