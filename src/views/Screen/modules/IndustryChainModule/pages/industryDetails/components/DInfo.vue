<template>
  <div class="w-[425px] h-full bg-[url(@/assets/imgs/screen/card-border-6.png)] bg-[length:100%_100%] alibaba-semiBold relative">
    <span class="absolute top-[-5px] left-4 right-4 text-center c-[#5EA4FF] text-[24px]">产业链信息</span>
    <div class="px-[20px] pt-[40px] flex-1">
      <div class="bg-[#001542] bg-opacity-40 p-[15px] flex flex-col">
        <div class="rounded-[4px] border-1 border-solid border-[#6DADFF] w-fit px-2 tag">
          <span class="text-lg c-[#AFD2FF] font-italic">{{ dataInfo.thematicClassName }}</span>
        </div>
        <span class="c-white text-[26px] mt-[14px]">{{ dataInfo.industryName }}</span>
      </div>
      <div class="flex items-center gap-x-6 mt-5">
        <span class="flex-1 h-[1px] bg-#979797"></span>
        <span class="c-[#AFD2FF] text-[20px]">产业链图谱</span>
        <span class="flex-1 h-[1px] bg-#979797"></span>
      </div>
      <el-image :src="industryImg" :preview-src-list="[industryImg]" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import ScreenApi from '@/api/screen.ts'
import { useRoute } from 'vue-router'

const route = useRoute()

const industryImg = computed(() => {
  if(!dataInfo.value.industryImg) return ''
  const imgs = JSON.parse(dataInfo.value.industryImg)
  return import.meta.env.VITE_STATIC_ASSETS_URL + imgs[0].pictureUrl
})

const dataInfo = ref<any>({})
const getData = () => {
  ScreenApi.getThematicIndustryDetail({
    industryId: route.query.id
  }).then(res => {
    dataInfo.value = res.data || {}
  })
}
getData()
</script>

<style scoped lang="scss">
.tag{
  background: linear-gradient( 270deg, rgba(9,53,116,0.74) 0%, rgba(34,116,243,0.8) 100%);
}
</style>
