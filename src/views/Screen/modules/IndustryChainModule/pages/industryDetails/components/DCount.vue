<template>
  <div class="w-[425px] h-[310px] bg-[url(@/assets/imgs/screen/card-border-4.png)] bg-[length:100%_100%] relative">
    <span class="absolute top-[-5px] left-4 right-4 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">产值情况</span>
    <div class="p-[36px] flex flex-col items-center">
      <div class="bg-[#001542] bg-opacity-40 h-[105px] flex flex-col items-center justify-center w-full relative">
        <span class="c-white">总产值</span>
        <div class="flex items-end alibaba-semiBold c-[#C6E3FF] mt-2">
          <span class="text-[30px] font-italic">{{ dataInfo.outputSum || 0 }}</span>
        </div>
        <img src="@/assets/imgs/screen/qj.png" alt="" class="w-[71px] h-[12px] absolute bottom-[10px]">
      </div>
      <img src="@/assets/imgs/screen/mbjt.png" class="w-[36px] h-[30px] my-2" alt="">
      <div class="bg-[#001542] bg-opacity-40 h-[105px] flex flex-col items-center justify-center w-full relative">
        <span class="c-white">目标总产值</span>
        <div class="flex items-end alibaba-semiBold c-[#C6E3FF] mt-2">
          <span class="c-[#E6C365] text-[30px] font-italic">{{ dataInfo.targetOutput || 0 }}</span>
        </div>
        <img src="@/assets/imgs/screen/qj.png" alt="" class="w-[71px] h-[12px] absolute bottom-[10px]">
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ScreenApi from '@/api/screen.ts'
import { ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const dataInfo = ref<any>({})
const getData = () => {
  ScreenApi.getThematicIndustryData({
    industryId: route.query.id
  }).then(res => {
    dataInfo.value = res.data || {}
  })
}
getData()
</script>

<style scoped lang="scss">

</style>
