<template>
  <div class="w-[293px] box-border bg-[url(@/assets/imgs/screen/zxb-bg-2.png)] bg-[length:100%_100%] px-[20px] py-[10px] flex flex-col">
    <span class="c-[#E6C365] text-[24px] alibaba-semiBold">{{ item.industryInformation }}</span>
    <span class="c-white text-lg">{{ item.industryDescription }}</span>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  item: any
}>()
</script>

<style scoped lang="scss">

</style>
