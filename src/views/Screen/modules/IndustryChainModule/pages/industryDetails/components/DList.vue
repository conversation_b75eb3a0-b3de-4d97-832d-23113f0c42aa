<template>
  <div class="w-[425px] h-[623px] bg-[url(@/assets/imgs/screen/card-border-3.png)] bg-[length:100%_100%] relative relative">
    <span class="absolute top-[-5px] left-4 right-4 text-center c-[#5EA4FF] text-[24px] alibaba-semiBold">核心指标</span>
    <div class="p-[40px] flex flex-wrap gap-x-[24px] gap-y-[50px]">
      <div class="w-[159px] h-[137px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
        <div class="flex items-end alibaba-semiBold mt-2 c-white">
          <span class="text-[26px]">{{ dataInfo.industryTaxation || 0 }}</span>
        </div>
        <span class="c-[#E6C365] text-lg alibaba-semiBold">税收贡献</span>
      </div>
      <div class="w-[159px] h-[137px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
        <div class="flex items-end alibaba-semiBold mt-2 c-white">
          <span class="text-[26px]">{{ dataInfo.patentSum || 0 }}</span>
        </div>
        <span class="c-[#E6C365] text-lg alibaba-semiBold">专利数量</span>
      </div>
      <div class="w-[159px] h-[137px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
        <div class="flex items-end alibaba-semiBold mt-2 c-white">
          <span class="text-[26px]">{{ dataInfo.projectSum || 0 }}</span>
        </div>
        <span class="c-[#E6C365] text-lg alibaba-semiBold">重点项目数</span>
      </div>
      <div class="w-[159px] h-[137px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
        <div class="flex items-end alibaba-semiBold mt-2 c-white">
          <span class="text-[26px]">{{ dataInfo.scaleCompanySum || 0 }}</span>
        </div>
        <span class="c-[#E6C365] text-lg alibaba-semiBold">规上企业数</span>
      </div>
      <div class="w-[159px] h-[137px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
        <div class="flex items-end alibaba-semiBold mt-2 c-white">
          <span class="text-[26px]">{{ dataInfo.privateCompanySum || 0 }}</span>
        </div>
        <span class="c-[#E6C365] text-lg alibaba-semiBold">民企数</span>
      </div>
      <div class="w-[159px] h-[137px] bg-[url(@/assets/imgs/screen/czbg.png)] bg-[length:100%_100%] flex flex-col justify-between items-center py-2 box-border">
        <div class="flex items-end alibaba-semiBold mt-2 c-white">
          <span class="text-[26px]">{{ dataInfo.employmentNum || 0 }}</span>
        </div>
        <span class="c-[#E6C365] text-lg alibaba-semiBold">就业人数</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ScreenApi from '@/api/screen.ts'
import { ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const dataInfo = ref<any>({})
const getData = () => {
  ScreenApi.getThematicIndustryData({
    industryId: route.query.id
  }).then(res => {
    dataInfo.value = res.data || {}
  })
}
getData()
</script>

<style scoped lang="scss">

</style>
