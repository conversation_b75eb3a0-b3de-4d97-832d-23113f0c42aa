<template>
  <div class="absolute left-[20px] top-[117px] bottom-[20px] z-3 min-w-[280px] flex flex-col gap-y-[20px] overflow-auto hidden-scrollbar">
    <div v-for="(v, index) in dataList" :key="index" class="flex flex-col gap-y-[2px]">
      <div class="h-[57px] flex items-center border-l-[3px] border-l-solid border-[#57ADFF] bg-[#092757] bg-opacity-50 relative pr-[20px]">
        <span class="c-white text-[24px] ml-[74px] font-italic alibaba-semiBold">{{ v.name }}</span>
        <div class="w-[57px] h-[57px] absolute left-0 top-0 flex items-center justify-center">
          <div class="absolute top-0 w-full h-full bg-[#57ADFF] bg-opacity-30 bg-clip"></div>
          <z-svg :name="`marker-${v.icon}`" width="40px" height="40px" class="absolute"></z-svg>
        </div>
      </div>
      <div class="px-[5px] py-[10px] bg-[#092757] bg-opacity-50">
        <div v-for="(n, i) in v.value" :key="i" class="h-[50px] flex items-center px-[10px] transition-all hover:bg-[#3D9DFF] hover:bg-opacity-40 cursor-pointer" @click="industryRouteRef?.navigate({
              query: {
                id: n.industryId
              }
            })">
          <div class="flex flex-col items-center h-full">
            <span class="w-[2px] flex-1 bg-[#57ADFF] bg-opacity-50" :class="{ 'bg-opacity-0!': i === 0 }"></span>
            <span class="w-[6px] h-[6px] rounded-full bg-[#57ADFF]"></span>
            <span class="w-[2px] flex-1 bg-[#57ADFF] bg-opacity-50" :class="{ 'bg-opacity-0!': v.value.length - 1 === i }"></span>
          </div>
          <div class="w-[20px] h-[20px] rounded-[2px] ml-[15px] mr-[10px] flex justify-center items-center" :class="n.bgClass">
            <z-svg :name="`marker-${n.thematicClassIcon}`" width="17px" height="17px"></z-svg>
          </div>
          <span class="c-white text-lg">{{ n.industryName }}{{ n.detailId }}</span>
        </div>
      </div>
    </div>
    <z-dynamic-route
      ref="industryRouteRef"
      :route="{
        path: '/industryDetails',
        viewPath: '@/views/Screen/modules/IndustryChainModule/pages/industryDetails/industryDetails.vue'
      }"
    />
  </div>
</template>

<script setup lang="ts">
import ScreenApi from '@/api/screen.ts'
import { ref } from 'vue'
import { groupBy } from 'lodash'

const COLORS = ['bg1', 'bg2', 'bg3', 'bg4', 'bg5']

const industryRouteRef = ref()


const dataList = ref<any[]>([])
const getList = () => {
  ScreenApi.getIndustryChainDataList().then(res => {
    if(res.data?.length) {
      const grouped = groupBy(res.data, 'thematicClassName')
      const list = Object.entries(grouped).map(([key, value]) => ({
        name: key,
        icon: value?.[0].thematicClassIcon,
        value
      }))
      dataList.value = list.map((item: any) => {
        return {
          ...item,
          value: item.value.map((v: any, index: number) => {
            return {
              ...v,
              bgClass: COLORS[index % COLORS.length]
            }
          })
        }
      })
    }
  })
}
getList()
</script>

<style scoped lang="scss">
.bg-clip{
  clip-path: polygon(0% 0%, 100% 68.67%, 100% 100%, 0% 100%);
}
.hidden-scrollbar {
  overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}
.hidden-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome、Safari 和 Opera */
}
.bg1{
  background: linear-gradient( 180deg, #FBD249 0%, #66686A 100%);
}
.bg2{
  background: linear-gradient( 180deg, #B4EC51 0%, #5F605E 100%);
}
.bg3{
  background: linear-gradient( 180deg, #5DD7D4 0%, #4B4C4C 100%);
}
.bg4{
  background: linear-gradient( 180deg, #31A0FF 0%, #595959 100%);
}
.bg5{
  background: linear-gradient( 180deg, #6669FF 0%, #595959 100%);
}
</style>
