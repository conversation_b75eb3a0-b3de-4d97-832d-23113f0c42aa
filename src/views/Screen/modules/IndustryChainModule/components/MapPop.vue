<template>
  <div class="w-[293px] box-border bg-[url(@/assets/imgs/screen/zxb-bg-2.png)] bg-[length:100%_100%] px-[20px] py-[10px]">
    <span class="c-[#E6C365] text-[24px] alibaba-semiBold">{{ item.areaName }}产业</span>
    <div v-for="(v, i) in item.list" :key="i" class="flex items-center mt-2">
      <img src="@/assets/imgs/screen/cyjt.png" alt="" class="w-[14px] h-[10px]">
      <span class="c-white text-lg ml-[10px]">{{ v.industryInformation }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  item: any
}>()
</script>

<style scoped lang="scss">

</style>
