import { defineAsyncComponent } from 'vue'

export interface ModuleComponent {
  name: string;
  component: any;
}

/**
 * 动态加载模块组件
 * @param modulePrefix 模块名前缀，例如 'Module'
 * @returns 返回包含组件名和组件的数组
 */
export async function loadModuleComponents(modulePrefix: string = 'Module'): Promise<ModuleComponent[]> {
  // 1. 动态导入所有匹配的.vue文件
  const modules = import.meta.glob('../modules/*Module/index.vue')

  // 2. 处理模块并创建异步组件
  const moduleComponents: ModuleComponent[] = []

  for (const path in modules) {
    const componentName = path.split('/')?.find(e => e.includes(modulePrefix)) ?? ''
    // 创建异步组件
    const asyncComponent = defineAsyncComponent(modules[path] as any)

    moduleComponents.push({
      name: componentName,
      component: asyncComponent
    })
  }

  return moduleComponents;
}
