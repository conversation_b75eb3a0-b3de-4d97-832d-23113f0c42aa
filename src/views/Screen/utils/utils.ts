/**
 * 将颜色值和透明度转换为RGBA格式
 * @param color 十六进制(#RRGGBB)、RGB数组或颜色名称
 * @param opacity 透明度 (0~1)
 * @returns RGBA颜色字符串
 */
type ColorInput = string | [number, number, number];
export function toRGBA(color: ColorInput, opacity: number = 1): string {
  // 校验透明度
  if (opacity < 0 || opacity > 1) {
    throw new Error('Opacity must be between 0 and 1');
  }

  let r: number, g: number, b: number;

  // 处理RGB数组
  if (Array.isArray(color)) {
    [r, g, b] = color;
    if (color.some(c => c < 0 || c > 255)) {
      throw new Error('RGB values must be between 0 and 255');
    }
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  // 处理十六进制
  if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(color)) {
    const hex = color.substring(1);
    const len = hex.length;
    r = parseInt(hex.substring(0, len / 3), 16);
    g = parseInt(hex.substring(len / 3, (2 * len) / 3), 16);
    b = parseInt(hex.substring((2 * len) / 3), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  // 处理颜色名称（简化版）
  const colorMap: Record<string, [number, number, number]> = {
    red: [255, 0, 0],
    green: [0, 255, 0],
    blue: [0, 0, 255],
    // 可以添加更多颜色...
  };

  if (color in colorMap) {
    [r, g, b] = colorMap[color];
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  throw new Error('Unsupported color format');
}
