<template>
  <z-page v-loading="loading">
    <div class="flex justify-center items-center">
      <span class="text-[30px]">{{  curData.evaluateName  }}</span>
      <el-icon
        color="#2C92FB"
        :size="26"
        class="ml-[10px] cursor-pointer"
        @click="onSwitch"
        ><Switch
      /></el-icon>
    </div>
    <div class="h-[2px] w-full bg-[#13123C] my-[28px]"></div>
    <div class="flex">
      <div class="mr-4">
        <el-date-picker
          v-model="dateArr"
          type="yearrange"
          :editable="false"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="false"
          format="YYYY年"
          value-format="YYYY"
          style="width: 200px"
          @change="getDataInfo"
        />
      </div>
      <el-select
        v-model="areaCode"
        placeholder="请选择"
        style="width: 120px"
        @change="getDataInfo"
      >
        <el-option
          v-for="item in areaList"
          :key="item.areaCode"
          :label="item.areaName"
          :value="item.areaCode"
        ></el-option>
      </el-select>
    </div>
    <div class="mt-[20px]">
      <div class="relative h-[35px] px-[20px] flex items-center">
        <div class="clip h-full w-[70px] absolute left-0 top-0 bg-[#D4E9FF]"></div>
        <span class="youshe-bold c-[#2C92FB] text-[22px] relative z-1">简介</span>
      </div>
      <div class="c-[#485361] lh-loose mt-[10px]">{{ curData.evaluateIntroduction }}</div>
      <div class="h-[1px] w-full bg-[#13123C] mt-[20px]"></div>
    </div>
    <div class="mt-[20px]">
      <div class="relative h-[35px] px-[20px] flex items-center">
        <div class="clip h-full w-[102px] absolute left-0 top-0 bg-[#D4E9FF]"></div>
        <span class="youshe-bold c-[#2C92FB] text-[22px] relative z-1">评价指标</span>
      </div>
      <div class="mt-[10px]">
        <TableInfo :cur="curData" :date="dateArr" :data="indexList" />
      </div>
      <div class="h-[1px] w-full bg-[#13123C] mt-[20px]"></div>
    </div>
    <div class="mt-[20px]">
      <div class="relative h-[35px] px-[20px] flex items-center">
        <div class="clip h-full w-[70px] absolute left-0 top-0 bg-[#D4E9FF]"></div>
        <span class="youshe-bold c-[#2C92FB] text-[22px] relative z-1">评价结果</span>
      </div>
      <div class="c-[#485361] mt-[20px]">{{ resultInfo.evaluateResult }}</div>
      <div v-for="(v, i) in resultInfo.oldYearEvaluateResultList" :key="i" class="border-1 border-solid border-[#CFCFCF] mt-[20px]">
        <div class="h-[50px] bg-[#F1F2F3] px-[20px] flex items-center">
          <span class="c-[#485361]">{{ v.year }} 评价结果</span>
        </div>
        <div class="px-[30px] py-[20px]">
          <div v-for="(n, j) in v.dimensionIndicatorList" :key="j" class="flex flex-col">
            <div class="flex items-center">
              <img src="@/assets/imgs/weidu.png" alt="" class="w-[21px] h-[12px]">
              <span class="c-[#485361] ml-2">{{ n.indicator }} 维度下：</span>
            </div>
            <div class="px-[30px]">
              <div v-for="(m, o) in n.notStandard" :key="o" class="bg-[#F1F2F3] px-[23px] py-[7px] rounded-[4px] c-[#485361] flex items-center text-[14px] w-fit mt-[18px]">{{ m }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="dialogVisible"
      title="选择指标评价体系"
      width="400px"
      :before-close="dialogClose"
    >
      <div class="flex flex-col">
        <div
          v-for="v in dataList" :key="v.evaluateId"
          class="flex items-center justify-between cursor-pointer transition-all hover:bg-[#42A1FF] hover:bg-op-10 p-[10px]"
          :class="{ 'bg-[#42A1FF] bg-op-10': v.evaluateId === dialogData.evaluateId }"
          @click="dialogData = v"
        >
          <span>{{ v.evaluateName }}</span>
          <el-icon v-show="v.evaluateId === dialogData.evaluateId" color="#42A1FF" size="16"><Select /></el-icon>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogClose">取消</el-button>
          <el-button type="primary" @click="dialogConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </z-page>
</template>

<script setup lang="ts">
import { Switch, Select } from '@element-plus/icons-vue'
import ComApi from '@/api/common.ts'
import { ref } from 'vue'
import dayjs from '@/utils/dayjs.ts'
import HighApi from '@/api/highQuality.ts'
import TableInfo from './components/TableInfo.vue'
import { BJ_AREA_CODE } from '@/config/default-settings.ts'

const curData = ref<any>({})
const dateArr = ref<string[]>([`${dayjs().subtract(5, 'year').year()}`, `${dayjs().year()}`])
const areaCode = ref<string>('all')

const dialogData = ref<any>({})
const dialogVisible = ref<boolean>(false)
const dialogConfirm = () => {
  curData.value = dialogData.value
  getDataInfo()
  dialogClose()
}
const dialogClose = () => {
  dialogVisible.value = false
}
const onSwitch = () => {
  dialogData.value = curData.value
  dialogVisible.value = true
}

const areaList = ref<any[]>([])
const getAreaList = async () => {
  const data = await ComApi.getAreaList().then((res) => res.data)
  areaList.value = [{ areaCode: 'all', areaName: '宝鸡市' }, ...data]
}
getAreaList()

const loading = ref<boolean>(false)
const indexList = ref<any[]>([])
const resultInfo = ref<any>({})
const getDataInfo = async () => {
  try {
    loading.value = true
    indexList.value = await HighApi.getIndexAnalysisData({
      evaluateId: curData.value.evaluateId,
      regionCode: areaCode.value === 'all' ? BJ_AREA_CODE : areaCode.value,
      startYear: dateArr.value[0],
      endYear: dateArr.value[1],
    }).then((res) => res.data || [])

    resultInfo.value = await HighApi.getIndexAnalysisEvaluation({
      evaluateId: curData.value.evaluateId,
      regionCode: areaCode.value === 'all' ? BJ_AREA_CODE : areaCode.value,
      startYear: dateArr.value[0],
      endYear: dateArr.value[1],
    }).then((res) => res.data || {})
    loading.value = false
  } catch {
    loading.value = false
  }
}

const dataList = ref<any[]>([])
const getDataList = async () => {
  try {
    loading.value = true
    dataList.value = await HighApi.getIndexAnalysisList().then((res) => res.data)
    curData.value = dataList.value?.[0] || {}
    await getDataInfo()
  } catch {
    loading.value = false
  }
}
getDataList()
</script>

<style scoped lang="scss">
.clip {
  clip-path: polygon(0% 0%, 61% 0%, 100% 100%, 0% 100%);
}
</style>
