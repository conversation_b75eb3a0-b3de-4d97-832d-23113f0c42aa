<script setup lang="ts">
import { computed, ref, watchEffect } from 'vue'
import DictApI from '@/api/dict.ts'

const props = defineProps<{
  cur: any,
  date: any[],
  data: any[]
}>()

const yearsBetween = computed(() => {
  const years = []
  for (let i = props.date[0]; i <= props.date[1]; i++) {
    years.push(i)
  }
  return years
})

const tableData = ref<any[]>([])
watchEffect(() => {
  if(props.cur.indicatorHierarchy === '2') {
    tableData.value = props.data?.map(dim => {
      dim.dimensionRowspan = dim.mapList?.length || 1
      return dim
    })
  } else if(props.cur.indicatorHierarchy === '3') {
    tableData.value = props.data?.map(dim => {
      dim.dimensionRowspan = 0
      dim.indexAnalysisTwoLists?.forEach((elem:any) => {
        elem.elementRowspan = elem.mapList?.length || 1
        dim.dimensionRowspan += elem.elementRowspan
      })
      return dim
    })
  }
})

const getUnitName = (code: string) => {
  return unitData.value.find(item => item.dictionaryCode === code)?.dictionaryName
}

const unitData = ref<any[]>([])
const getUnitData = async () => {
  unitData.value = await DictApI.getDictItemsByCode('systemUnit').then((res) => res.data)
}
getUnitData()
</script>

<template>
  <table v-if="props.cur.indicatorHierarchy === '2'" class="w-full border-collapse c-[#485361] text-[15px]">
    <thead class="bg-[#F1F2F3] h-[36px]">
      <tr>
        <th>维度指标</th>
        <th>基础指标</th>
        <th v-for="y in yearsBetween" :key="y">{{ y }}</th>
        <th>目标值</th>
        <th>指标属性</th>
      </tr>
    </thead>
    <tbody>
      <template v-for="(dimension, dimIndex) in tableData" :key="'dim-'+dimIndex">
        <tr v-for="(basic, basicIndex) in dimension.mapList" :key="'basic-'+basicIndex">
          <td v-if="basicIndex === 0" :rowspan="dimension.dimensionRowspan">{{ dimension.evaluateName }}({{ dimension.evaluateWeight || 0 }})</td>
          <td>{{ basic.indexName }}({{ getUnitName(basic.indexUnit) }})</td>
          <td v-for="y in yearsBetween" :key="y">{{ basic[y] || '-' }}</td>
          <td>{{ basic.con }}</td>
          <td>{{ basic.indexProperty }}</td>
        </tr>
      </template>
    </tbody>
  </table>
  <table v-if="props.cur.indicatorHierarchy === '3'" class="w-full border-collapse c-[#485361] text-[15px]">
    <thead class="bg-[#F1F2F3] h-[36px]">
    <tr>
      <th>维度指标</th>
      <th>要素指标</th>
      <th>基础指标</th>
      <th v-for="y in yearsBetween" :key="y">{{ y }}</th>
      <th>目标值</th>
      <th>指标属性</th>
    </tr>
    </thead>
    <tbody>
    <template v-for="(dimension, dimIndex) in tableData" :key="'dim-'+dimIndex">
      <template v-for="(element, elemIndex) in dimension.indexAnalysisTwoLists" :key="'elem-'+elemIndex">
        <tr v-for="(basic, basicIndex) in element.mapList" :key="'basic-'+basicIndex">
          <td v-if="elemIndex === 0 && basicIndex === 0" :rowspan="dimension.dimensionRowspan">{{ dimension.evaluateName }}({{ dimension.evaluateWeight || 0 }})</td>
          <td v-if="basicIndex === 0" :rowspan="element.elementRowspan">{{ element.evaluateName }}</td>
          <td>{{ basic.indexName }}({{ getUnitName(basic.indexUnit) }})</td>
          <td v-for="y in yearsBetween" :key="y">{{ basic[y] || '-' }}</td>
          <td>{{ basic.con }}</td>
          <td>{{ basic.indexProperty }}</td>
        </tr>
      </template>
    </template>
    </tbody>
  </table>
</template>

<style scoped lang="scss">
table{
  border: 1px solid #CFCFCF;
  th{
    border: 1px solid #CFCFCF;
    text-align: center;
  }
  tr{
    border: 1px solid #CFCFCF;
    text-align: center;
  }
  td{
    border: 1px solid #CFCFCF;
    text-align: center;
    padding: 7px 0;
  }
}
</style>
