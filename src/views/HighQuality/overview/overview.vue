<template>
  <div class="flex">
    <div class="w-[280px] mr-[20px] p-[20px] box-border bg-[#fff] rounded-[4px]">
      <el-select
        v-model="keyword"
        filterable
        remote
        :reserve-keyword="false"
        clearable
        fit-input-width
        placeholder="输入指标关键词查询"
        :remote-method="remoteMethod"
        @change="onChange"
      >
        <el-option
          v-for="item in selOptions"
          :key="item.id"
          :value="item.id"
          :label="item.label"
        />
      </el-select>
      <div class="mt-[10px]">
        <el-tree
          node-key="id"
          :data="treeData"
          :default-expanded-keys="currentNode.id ? [currentNode.id] : []"
          :props="{
            class: (node) => {
              return node.isLeaf ? `sel-node-${node.id}` : `dis-node-${node.id}`
            },
          }"
          @node-click="handleNodeClick"
        />
      </div>
    </div>
    <div class="w-[calc(100%-280px)] bg-[#fff] rounded-[4px] p-[20px] box-border">
      <div class="flex">
        <div>
          <el-date-picker
            v-model="dateArr"
            type="yearrange"
            :editable="false"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            format="YYYY年"
            value-format="YYYY"
            style="width: 200px"
            @change="onChangeDate"
          />
        </div>
        <el-button
          type="primary"
          plain
          :icon="Download"
          class="ml-[10px]"
          :disabled="!currentNode.baseId"
          :loading="downLoading"
          @click="onDownload"
          >下载</el-button
        >
        <el-button
          type="primary"
          plain
          :icon="Download"
          class="ml-[10px]"
          :disabled="!currentNode.baseId"
          :loading="exportLoading"
          @click="onExport"
          >导出报告</el-button
        >
      </div>
      <div class="flex justify-between c-[#485361] alibaba-semiBold mt-[20px]">
        <span>数据展示</span>
        <span>单位：{{ unitName }}</span>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        class="mt-[10px]"
      >
        <el-table-column
          prop="dataYear"
          label="日期"
          width="80"
        />
        <el-table-column
          v-for="v in tableCol"
          :key="v.regionCode"
          :prop="v.regionCode"
          :label="v.regionName"
        />
      </el-table>
      <div class="flex justify-between c-[#485361] alibaba-semiBold mt-[20px]">
        <span>图表展示</span>
        <div class="flex gap-x-3">
          <el-tooltip
            v-for="v in CHART_TYPE"
            :key="v.type"
            :content="v.label"
            placement="top"
          >
            <img
              :src="v.icon"
              alt=""
              class="w-[25px] h-[25px] cursor-pointer"
              @click="onChart(v.type)"
            />
          </el-tooltip>
        </div>
      </div>
      <div class="mt-[20px]">
        <component
          ref="dynamicComponent"
          :is="curChart.component"
          :key="curType"
          :list="dataList"
          :unit="unitName"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type Component, computed, nextTick, onMounted, ref } from 'vue'
import HighApi from '@/api/highQuality.ts'
import { Download } from '@element-plus/icons-vue'
import DictApI from '@/api/dict.ts'
import ChartBar from './components/ChartBar.vue'
import ChartLine from './components/ChartLine.vue'
import ChartPie from './components/ChartPie.vue'
import ChartScatter from './components/ChartScatter.vue'
import ChartRadar from './components/ChartRadar.vue'
import ChartHeatmap from './components/ChartHeatmap.vue'
import ChartMap from './components/ChartMap.vue'
import { isEmpty } from 'lodash'
import dayjs from '@/utils/dayjs.ts'

interface Chart {
  type: string
  label: string
  icon: string
  component: Component
}
const dynamicComponent = ref<any>()
const curType = ref<string>('bar')
const curChart = computed<Chart>(() => {
  return CHART_TYPE.find((item) => item.type === curType.value) as Chart
})
const CHART_TYPE: Chart[] = [
  {
    type: 'bar',
    label: '柱状图',
    icon: new URL('@/assets/imgs/chart-1.png', import.meta.url).href,
    component: ChartBar,
  },
  {
    type: 'line',
    label: '折线图',
    icon: new URL('@/assets/imgs/chart-2.png', import.meta.url).href,
    component: ChartLine,
  },
  {
    type: 'pie',
    label: '饼图',
    icon: new URL('@/assets/imgs/chart-3.png', import.meta.url).href,
    component: ChartPie,
  },
  {
    type: 'scatter',
    label: '散点图',
    icon: new URL('@/assets/imgs/chart-4.png', import.meta.url).href,
    component: ChartScatter,
  },
  {
    type: 'radar',
    label: '雷达图',
    icon: new URL('@/assets/imgs/chart-5.png', import.meta.url).href,
    component: ChartRadar,
  },
  {
    type: 'heatmap',
    label: '热力图',
    icon: new URL('@/assets/imgs/chart-6.png', import.meta.url).href,
    component: ChartHeatmap,
  },
  {
    type: 'map',
    label: '地图',
    icon: new URL('@/assets/imgs/chart-7.png', import.meta.url).href,
    component: ChartMap,
  },
]

const onChart = (type: string) => {
  if (curType.value === type) return
  curType.value = type
  nextTick(() => {
    dynamicComponent.value.initChart()
  })
}

const loading = ref<boolean>(false)
const keyword = ref<string>('')
const dateArr = ref<string[]>([`${dayjs().subtract(4, 'year').year()}`, `${dayjs().year()}`])
const currentNode = ref<any>({})

const handleNodeClick = (data: any) => {
  if (data.isLeaf) {
    const nodes: NodeListOf<HTMLElement> = document.querySelectorAll(
      '.el-tree-node__content',
    ) as NodeListOf<HTMLElement>
    nodes.forEach((node) => {
      node.style.backgroundColor = ''
      node.style.color = ''
    })
    const node: HTMLElement = document.querySelector(
      `.sel-node-${data.id} .el-tree-node__content`,
    ) as HTMLElement
    if (node) {
      node.style.backgroundColor = '#F9F9FA'
      node.style.color = '#3D9DFF'
    }
    currentNode.value = data
    getTableData()
  }
}

const onChangeDate = () => {
  getTableData()
}

const downLoading = ref<boolean>(false)
const onDownload = () => {
  downLoading.value = true
  HighApi.getIndexOverviewExport({
    baseId: currentNode.value?.baseId,
    beginDataYear: dateArr.value[0],
    endDataYear: dateArr.value[1],
  })
    .finally(() => {
      downLoading.value = false
    })
}

const exportLoading = ref<boolean>(false)
const onExport = () => {
  exportLoading.value = true
  HighApi.getIndexOverviewReport({
    baseId: currentNode.value?.baseId,
    beginDataYear: dateArr.value[0],
    endDataYear: dateArr.value[1],
  })
    .finally(() => {
      exportLoading.value = false
    })
}

// 递归构建树结构
function buildTree(data: any[]): any[] {
  return (
    data?.map((item) => {
      // 创建当前节点
      return {
        ...item,
        id: item.classId,
        label: item.className,
        isLeaf: false,
        // 如果有children且不为空，则递归处理
        children:
          item.children && item.children.length > 0
            ? buildTree(item.children)
            : (item.indexList || []).map((it: any) => {
                return {
                  ...it,
                  id: `${it.baseId}-${item.classId}`,
                  label: it.indexName,
                  isLeaf: true, // 标记为叶子节点
                }
              }),
      }
    }) || []
  )
}
const findDeepestLeaf = (node: any) => {
  if (node.children && node.children.length > 0) {
    return findDeepestLeaf(node.children[0])
  }
  return node
}

const getLeafNodes = (node: any[]) => {
  const leafNodes: any[] = []
  if (!isEmpty(node)) {
    node.forEach((item: any) => {
      if (isEmpty(item.children) && item.isLeaf) {
        leafNodes.push(item)
      } else {
        leafNodes.push(...getLeafNodes(item.children))
      }
    })
  }
  return leafNodes
}

const tableCol = ref<any[]>([])
const dataList = ref<any[]>([])
const tableData = computed(() => {
  if (!dataList.value?.length) return []
  return dataList.value.map((item: any) => {
    item.regionList.forEach((region: any) => {
      item[region.regionCode] = region.dataValue || '-'
    })
    return item
  })
})
const getTableData = () => {
  loading.value = true
  HighApi.getIndexOverviewData({
    baseId: currentNode.value?.baseId,
    beginDataYear: dateArr.value[0],
    endDataYear: dateArr.value[1],
  })
    .then((res) => {
      if (res.data?.length) {
        dataList.value = res.data
        tableCol.value = res.data[0].regionList
        nextTick(() => {
          dynamicComponent.value.initChart()
        })
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const treeData = ref<any[]>([])
const indexAllList = ref<any[]>([])
const getCategoryTree = () => {
  HighApi.getIndexOverviewCategoryTree().then((res) => {
    if (res.data?.length) {
      const data: any[] = buildTree(res.data || [])
      treeData.value = data
      indexAllList.value = getLeafNodes(data)
      currentNode.value = findDeepestLeaf(data[0])
      if(currentNode.value?.baseId) {
        nextTick(() => {
          handleNodeClick(currentNode.value)
        })
      }
    }
  })
}

const systemUnitData = ref<any[]>([])
const unitName = computed(() => {
  return systemUnitData.value.find((it: any) => it.dictionaryCode === currentNode.value.indexUnit)
    ?.dictionaryName
})
const getSysUnit = async () => {
  systemUnitData.value = await DictApI.getDictItemsByCode('systemUnit').then((res) => res.data)
}

const selOptions = ref<any[]>([])
const remoteMethod = (query: string) => {
  if (query) {
    setTimeout(() => {
      selOptions.value = indexAllList.value.filter((it: any) => {
        return it.label.includes(query)
      })
    }, 100)
  } else {
    selOptions.value = []
  }
}
const onChange = (val: string) => {
  if (val) {
    currentNode.value = indexAllList.value.find((it: any) => it.id === val)
    getTableData()
    nextTick(() => {
      handleNodeClick(currentNode.value)
    })
  }
}

onMounted(() => {
  getSysUnit()
  getCategoryTree()
})
</script>

<style scoped lang="scss"></style>
