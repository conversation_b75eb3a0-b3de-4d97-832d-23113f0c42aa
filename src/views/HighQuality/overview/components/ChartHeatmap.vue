<template>
  <div ref="chartRef" class="w-full h-[450px]"></div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, ref } from 'vue'
import * as echarts from 'echarts';

type EChartsOption = echarts.EChartsOption

const props = defineProps<{
  list: any[],
  unit: string
}>()

const COLORS = ['#DEECFF', '#91C2FC', '#3D9DFF', '#1165CE', '#0A2494']

const chartRef = ref()

let chart: any = null
const initChart = () => {
  if (!props.list?.length) return
  if (!chart) {
    chart = echarts.init(chartRef.value)
  }
  const regionList = props.list[0].regionList
  const seriesList = regionList.map((it:any, y: number) => {
    return props.list.map((v: any, x: number) => {
      return [x, y, +v.regionList.find((n: any) => n.regionCode === it.regionCode)?.dataValue || 0]
    })
  }).flat()
  const nums = props.list.map((v: any) => v.regionList.map((n: any) => n.dataValue || 0)).flat()
  const pieces = calculateDynamicRanges(nums).map((it: any, index: number) => {
    return {
      min: it[0],
      max: it[1],
      label: `${it[0]}-${it[1]}`,
      color: COLORS[index]
    }
  })
  const option: EChartsOption = {
    tooltip: {
    },
    xAxis: {
      type: 'category',
      data: props.list.map((v: any) => v.dataYear),
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: regionList.map((v: any) => v.regionName),
      splitArea: {
        show: true
      }
    },
    visualMap: {
      orient: 'horizontal',
      type: 'piecewise',
      left: 'center',
      pieces: pieces,
    },
    series: [{
      type: 'heatmap',
      data: seriesList,
      tooltip: {
        valueFormatter: function(value) {
          return value + props.unit;
        }
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  chart.setOption(option)
}

/**
 * 根据数组的最小值和最大值计算5个均匀分布的区间值
 * @param data 一维数字数组
 * @param length 默认5
 * @returns 包含5个区间值的数组
 */
function calculateDynamicRanges(data: number[], length: number = 5) {
  const min = Math.min(...data);
  const max = Math.max(...data);
  const step = (max - min) / length;
  const ranges: number[] = [];
  for (let i = 0; i < length + 1; i++) {
    ranges.push(parseFloat((min + i * step).toFixed(0))); // 保留两位小数
  }
  const resultArray = [];
  for (let i = 0; i < ranges.length - 1; i++) {
    resultArray.push([ranges[i], ranges[i + 1]]);
  }
  return resultArray;
}

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
  }
})

defineExpose({
  initChart
})
</script>

<style scoped lang="scss">

</style>
