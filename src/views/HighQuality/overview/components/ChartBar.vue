<template>
  <div ref="chartRef" class="w-full h-[450px]"></div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, ref } from 'vue'
import * as echarts from 'echarts';

type EChartsOption = echarts.EChartsOption

const props = defineProps<{
  list: any[],
  unit: string
}>()

const COLORS = ['#587FFB', '#F97C6B', '#3D9DFF','#69C6C0', '#E6C365', '#B8E986', '#FB9C01', '#F96BAF', '#8B76FF', '#6BBF46', '#FFA1A1', '#F8E71C', '#AC4EFF']

const chartRef = ref()

let chart: any = null
const initChart = () => {
  if (!props.list?.length) return
  if (!chart) {
    chart = echarts.init(chartRef.value)
  }
  const regionList = props.list[0].regionList
  const seriesList = regionList.map((it:any, index: number) => {
    const data = props.list.map((v: any) => {
      return v.regionList.find((n: any) => n.regionCode === it.regionCode)?.dataValue || 0
    })
    return {
      name: it.regionName,
      type: 'bar',
      tooltip: {
        valueFormatter: function(value: number) {
          return (value as number) + props.unit;
        }
      },
      itemStyle: {
        color: COLORS[index]
      },
      data
    }
  })
  const option: EChartsOption = {
    grid: {
      left: '3%',
      right: '3%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      icon: 'rect',
      itemWidth: 14,
      itemHeight: 14,
      itemGap: 18,
      textStyle: {
        color: '#2D333B',
        fontSize: 12
      }
    },
    xAxis: [
      {
        type: 'category',
        data: props.list.map((item: any) => item.dataYear),
        axisPointer: {
          type: 'shadow'
        },
        axisTick: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#CFD6DF'
          }
        },
        axisLabel: {
          color: '#2D333B'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: '#A1ADBC'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      }
    ],
    series: seriesList
  }
  chart.setOption(option)
}

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
  }
})

defineExpose({
  initChart
})
</script>

<style scoped lang="scss">

</style>
