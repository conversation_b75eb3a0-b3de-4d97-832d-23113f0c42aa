<template>
  <div ref="chartRef" class="w-full h-[450px]"></div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, ref } from 'vue'
import * as echarts from 'echarts';
import geoJsonData from '@/map/assets/geo/baoji.json'

type EChartsOption = echarts.EChartsOption

const props = defineProps<{
  list: any[],
  unit: string
}>()

const COLORS = ['#DEECFF', '#91C2FC', '#3D9DFF', '#1165CE', '#0A2494']

const chartRef = ref()

let chart: any = null
const initChart = () => {
  if (!props.list?.length) return
  if (!chart) {
    chart = echarts.init(chartRef.value)
  }
  echarts.registerMap('宝鸡市', geoJsonData as any)
  const regionList = props.list[0].regionList
  const seriesList = regionList.map((it:any) => {
    const value = props.list.reduce((sum: number, item: any) => {
      return sum + Number(item.regionList.find((n: any) => n.regionCode === it.regionCode)?.dataValue || 0)
    }, 0)
    return {
      name: it.regionName,
      value: value
    }
  })
  const nums = props.list.map((v: any) => v.regionList.map((n: any) => n.dataValue || 0)).flat()
  const pieces = calculateDynamicRanges(nums).map((it: any, index: number) => {
    return {
      min: it[0],
      max: it[1],
      label: `${it[0]}-${it[1]}`,
      color: COLORS[index]
    }
  })
  const option: EChartsOption = {
    tooltip: {
      show: false
    },
    visualMap: {
      type: 'piecewise',
      outOfRange: {
        color: '#AAAAAA'
      },
      pieces: pieces
    },
    series: [
      {
        type: "map",
        map: "宝鸡市",
        zoom: 1.2,
        tooltip: {
          show: true,
          formatter: (params: any) => {
            return `${params.name} ${params.value} ${props.unit}`
          }
        },
        label: {
          show: true,
          color: '#2D333B',
          fontSize: 12
        },
        itemStyle: {
          areaColor: '#3894ec',
          borderColor: '#2D333B'
        },
        data: seriesList
      }
    ]
  }
  chart.setOption(option)
}

/**
 * 根据数组的最小值和最大值计算5个均匀分布的区间值
 * @param data 一维数字数组
 * @param length 默认5
 * @returns 包含5个区间值的数组
 */
function calculateDynamicRanges(data: number[], length: number = 5) {
  const min = Math.min(...data);
  const max = Math.max(...data);
  const step = (max - min) / length;
  const ranges: number[] = [];
  for (let i = 0; i < length + 1; i++) {
    ranges.push(parseFloat((min + i * step).toFixed(0))); // 保留两位小数
  }
  const resultArray = [];
  for (let i = 0; i < ranges.length - 1; i++) {
    resultArray.push([ranges[i], ranges[i + 1]]);
  }
  return resultArray;
}

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
  }
})

defineExpose({
  initChart
})
</script>

<style scoped lang="scss">

</style>
