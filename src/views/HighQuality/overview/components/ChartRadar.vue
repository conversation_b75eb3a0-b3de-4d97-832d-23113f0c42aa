<template>
  <div ref="chartRef" class="w-full h-[450px]"></div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, ref } from 'vue'
import * as echarts from 'echarts';

type EChartsOption = echarts.EChartsOption

const props = defineProps<{
  list: any[],
  unit: string
}>()

const COLORS = ['#587FFB', '#F97C6B', '#3D9DFF','#69C6C0', '#E6C365', '#B8E986', '#FB9C01', '#F96BAF', '#8B76FF', '#6BBF46', '#FFA1A1', '#F8E71C', '#AC4EFF']

const chartRef = ref()

let chart: any = null
const initChart = () => {
  if (!props.list?.length) return
  if (!chart) {
    chart = echarts.init(chartRef.value)
  }
  const regionList = props.list[0].regionList
  const seriesList = props.list.map((it:any, index: number) => {
    return {
      value: it.regionList.map((v: any) => v.dataValue || 0),
      name: it.dataYear,
      tooltip: {
        valueFormatter: function(value: number) {
          return (value as number) + props.unit;
        }
      },
      itemStyle: {
        color: COLORS[index]
      }
    }
  })
  const option: EChartsOption = {
    grid: {
      left: '3%',
      right: '3%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      show: true
    },
    legend: {
      icon: 'rect',
      itemWidth: 14,
      itemHeight: 14,
      itemGap: 18,
      textStyle: {
        color: '#2D333B',
        fontSize: 12
      },
      bottom: 0
    },
    radar: {
      indicator: regionList.map((it:any) => ({ name: it.regionName }))
    },
    series: [
      {
        type: 'radar',
        data: seriesList
      }
    ]
  }
  chart.setOption(option)
}

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
  }
})

defineExpose({
  initChart
})
</script>

<style scoped lang="scss">

</style>
