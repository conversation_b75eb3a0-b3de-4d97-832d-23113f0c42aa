<template>
  <div ref="chartRef" class="w-full h-[450px]"></div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, ref } from 'vue'
import * as echarts from 'echarts';

type EChartsOption = echarts.EChartsOption

const props = defineProps<{
  list: any[],
  unit: string
}>()

const COLORS = ['#587FFB', '#F97C6B', '#3D9DFF','#69C6C0', '#E6C365', '#B8E986', '#FB9C01', '#F96BAF', '#8B76FF', '#6BBF46', '#FFA1A1', '#F8E71C', '#AC4EFF']

const chartRef = ref()

let chart: any = null
const initChart = () => {
  if (!props.list?.length) return
  if (!chart) {
    chart = echarts.init(chartRef.value)
  }
  const regionList = props.list[0].regionList
  const seriesList = regionList.map((it:any, index: number) => {
    const value = props.list.reduce((sum: number, item: any) => {
      return sum + Number(item.regionList.find((n: any) => n.regionCode === it.regionCode)?.dataValue || 0)
    }, 0)
    return {
      name: it.regionName,
      value: value,
      itemStyle: {
        color: COLORS[index]
      }
    }
  })
  const option: EChartsOption = {
    tooltip: {
      trigger: 'item',
      formatter:`{b}: {c}${props.unit} ({d}%)`,
    },
    legend: {
      orient: "horizontal",
      bottom: 0,
      icon: 'rect',
      itemWidth: 14,
      itemHeight: 14,
      itemGap: 18,
      textStyle: {
        color: '#2D333B',
        fontSize: 12
      }
    },
    series: [
      {
        type: 'pie',
        radius: '70%',
        label: {
          position: 'inner',
          formatter: '{d}%',
          color: '#fff',
          fontSize: 14
        },
        data: seriesList
      }
    ]
  }
  chart.setOption(option)
}

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
  }
})

defineExpose({
  initChart
})
</script>

<style scoped lang="scss">

</style>
