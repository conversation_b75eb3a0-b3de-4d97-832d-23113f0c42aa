<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
  </z-form>
</template>

<script setup lang="ts">
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField } from '@/ui/components/ZForm/types'
import { Status } from '@/utils/enums'
import { OptionUtils } from '@/utils/option'
import { RegexValidator } from '@/utils/regex'
import { reactive, ref } from 'vue'

const { onOpen } = useDialogFormContext()
const props = defineProps<{ model: Record<string, any> | null }>()

const initValue = {
  roleId: undefined,
  roleName: undefined,
  roleCode: undefined,
  roleSort: 1,
  roleStatus: Status.Enabled,
  remark: undefined,
}

const form = reactive(initValue)

const fields = ref<ZFormField[]>([
  {
    name: 'roleId',
    show: false,
    type: 'input',
  },
  {
    name: 'roleName',
    label: '角色名称',
    type: 'input',
    rules: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
    props: {
      placeholder: '请输入角色名称',
      maxLength: 20,
    },
  },
  {
    name: 'roleCode',
    label: '角色编码',
    type: 'input',
    rules: [
      { required: true, message: '请输入角色编码', trigger: 'blur' },
      {
        pattern: RegexValidator.getPattern('alphanumeric'),
        message: '请输入字母数字',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入角色编码',
      maxLength: 20,
    },
  },
  {
    name: 'roleSort',
    label: '显示排序',
    type: 'number',
    rules: [{ required: true, message: '请输入角色编码', trigger: 'blur' }],
    props: {
      placeholder: '显示排序',
      min: 1,
      max: 999,
    },
  },
  {
    name: 'roleStatus',
    label: '状态',
    type: 'radio',
    options: OptionUtils.getOptions('status'),
  },
  {
    name: 'remark',
    label: '备注',
    type: 'input',
    props: {
      placeholder: '请输入备注',
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 },
    },
  },
])

onOpen(() => {
  Object.assign(form, props.model ?? {})
})
</script>
