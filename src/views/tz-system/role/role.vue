<template>
  <z-page>
    <z-table
      row-key="roleId"
      ref="tableRef"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      selectable
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #applicationStatus="scope">
        <component :is="OptionUtils.renderTag(scope.row.dictionaryKeyNode, 'yesNo')"></component>
      </template>
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          @click="onEdit(row)"
          link
          >{{ t('common.edit') }}</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="goToRoleAuth(row)"
          link
          >权限维护</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="openUserDrawer(row)"
          link
          >已分配用户</el-button
        >
        <el-popconfirm
          :title="t('message.confirmDelete')"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}</el-button
            >
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <RoleForm :model="dialog.model.value" />
    </z-dialog-form>

    <RoleUserDrawer
      v-model="userDrawer.visible"
      :role-id="userDrawer.roleId"
      :role-info="userDrawer.roleInfo"
      @closed="userDrawer.visible = false"
    />
  </z-page>
  <z-dynamic-route
    :route="{
      name: 'RoleAuth',
      path: '/roleAuth',
      viewPath: '@/views/tz-system/role/RoleAuth.vue',
      meta: {
        title: '授权',
      },
    }"
  />
</template>
<script setup lang="ts">
import { ElButton, ElMessage } from 'element-plus'
import { ref } from 'vue'
import { OptionUtils } from '@/utils/option'
import { useTypedI18n } from '@/i18n'
import { useRouter } from 'vue-router'
import { useDialog } from '@/composables/useDialog'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'
import type {
  ZTableInstance,
  ZTableToolbarRow,
  ZTableColumn,
  ZTableParams,
} from '@/ui/components/ZTable/types'
import RoleApi from '@/api/role'
import RoleForm from './RoleForm.vue'
import RoleUserDrawer from './RoleUserDrawer.vue'

const { t } = useTypedI18n()
const router = useRouter()

// 跳转到角色授权页面
const goToRoleAuth = (role: any) => {
  router.push({ name: 'RoleAuth', query: { id: role.roleId } })
}

const userDrawer = ref({
  visible: false,
  roleId: undefined as number | undefined,
  roleInfo: undefined as { roleName: string; roleCode: string } | undefined,
})

const openUserDrawer = (role: any) => {
  userDrawer.value.roleId = role.roleId
  userDrawer.value.roleInfo = {
    roleName: role.roleName,
    roleCode: role.roleCode,
  }
  userDrawer.value.visible = true
}

const dialog = useDialog({
  name: '角色',
  modeMap: { addChild: '新增字典' },
  onConfirm(mode, ctx) {
    if (mode === 'create' || mode === 'addChild') {
      ctx.form?.validate((isValid, values) => {
        if (isValid) {
          ctx.isSubmitting.value = true
          RoleApi.addRole(values)
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.saveSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      })
    } else if (mode === 'edit') {
      ctx.form?.validate((isValid, values) => {
        if (isValid) {
          ctx.isSubmitting.value = true
          RoleApi.updateRole(values)
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.updateSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      })
    }
  },
})

const tableRef = ref<ZTableInstance | null>(null)

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => {
          dialog.open('create')
        },
        // show: has(perm.value),
      }),
      ZTableToolbarFactory.createDelete({
        onClick: (ctx) => {
          if (ctx.selectedRows.length) {
            RoleApi.deleteRoleByIds(ctx.selectedRows.map((item: any) => item.roleId)).then(() => {
              tableRef.value?.refresh()
              ElMessage.success(t('message.deleteSuccess'))
            })
          }
        },
        // show: has(perm.value),
      }),
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          tools: [
            {
              key: 'roleName',
              type: 'input',
              label: '角色名称',
              placeholder: '请输入角色名称',
              width: 240,
            },
            {
              key: 'roleCode',
              type: 'input',
              label: '角色编码',
              placeholder: '请输入角色编码',
              width: 240,
            },
            {
              key: 'roleStatus',
              type: 'select',
              label: '角色状态',
              placeholder: '请选择',
              width: 240,
              options: OptionUtils.getOptions('status'),
            },
            ZTableToolbarFactory.createSearch(),
            ZTableToolbarFactory.createReset(),
          ],
        },
      },
    ],
  },
]

const query = ref()

const columns: ZTableColumn[] = [
  { prop: 'roleName', label: '角色名称' },
  { prop: 'roleCode', label: '角色编码' },
  { prop: 'roleSort', label: '排序' },
  {
    prop: 'roleStatus',
    label: '状态',
    formatter(row, column, cellValue) {
      return OptionUtils.renderTag(cellValue, 'status')!
    },
  },
  { prop: 'createTime', label: '创建时间' },
  { prop: 'operation', label: '操作', align: 'center', width: 280, fixed: 'right' },
]

const fetchTableData = async (params: ZTableParams) => {
  return RoleApi.getRolesPage(params).then((res) => res.data)
}

const onEdit = (row: any) => {
  dialog.open('edit', row)
}

const onDelete = (row: any) => {
  RoleApi.deleteRoleByIds([row.roleId]).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}
</script>
