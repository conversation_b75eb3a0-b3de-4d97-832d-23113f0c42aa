<template>
  <z-page class="bg-transparent p-0 role-auth-page">
    <div class="flex gap-4">
      <div class="shrink-0 w-[280px]">
        <z-sticky
          class="transition-all duration-300 ease-in-out bg-card w-[280px]"
          :class="{
            'left-[96px]!': isCollapse && isVertical,
            'left-[272px]!': !isCollapse && isVertical,
          }"
          ref="stickyRef"
          :margin-top="isNavbarFixed ? 74 : 10"
        >
          <template v-slot="{ isSticky }">
            <div
              class="transition-height duration-300 ease-in-out"
              :style="{
                height: isSticky
                  ? 'calc(100vh - 74px - 16px)'
                  : 'calc(100vh - 70px - 29px - 16px - 16px)',
              }"
            >
              <z-scrollbar class="ps-0">
                <div class="p-1">
                  <el-tree
                    ref="treeRef"
                    style="max-width: 600px"
                    class="filter-tree mt-2"
                    :data="data"
                    :props="defaultProps"
                    node-key="applicationId"
                    show-checkbox
                    default-expand-all
                    @check="handleAppCheckChange"
                  />
                  <el-divider
                    content-position="left"
                    border-style="dashed"
                    class="mb-2"
                    >{{ roleInfo?.roleName }}</el-divider
                  >
                  <div class="flex justify-center py-4">
                    <el-button
                      class="rounded-none"
                      @click="goBack"
                      size="small"
                      >返回</el-button
                    >
                    <el-button
                      type="primary"
                      class="rounded-none"
                      @click="handleSubmit"
                      size="small"
                      >保存</el-button
                    >
                  </div>
                </div>
              </z-scrollbar>
            </div>
          </template>
        </z-sticky>
      </div>
      <div class="flex-1 w-0">
        <div
          v-if="tabs.length"
          class="bg-card"
          :style="{
            minHeight: 'calc(100vh - 70px - 29px - 16px - 16px)',
          }"
        >
          <div v-loading="authCache.get(activeTab)?.loading">
            <z-sticky
              ref="stickyRef2"
              class="block w-full z-[100]"
              :margin-top="isNavbarFixed ? 74 : 10"
              wrap
            >
              <div class="relative auth-tabs">
                <el-tabs
                  class="px-4 bg-card"
                  v-model="activeTab"
                >
                  <template
                    v-for="tab in tabs"
                    :key="tab.applicationId"
                  >
                    <el-tab-pane
                      :label="tab.applicationName"
                      :name="tab.applicationId"
                    >
                      <div class="py-2 px-2">
                        <el-checkbox v-model="checkAll"> 全选 </el-checkbox>
                      </div>
                    </el-tab-pane>
                  </template>
                </el-tabs>
              </div>
            </z-sticky>

            <div class="auth-tree">
              <el-tree
                ref="treeRef2"
                :data="authCache.get(activeTab)?.data || []"
                show-checkbox
                node-key="functionId"
                default-expand-all
                :props="{
                  children: 'children',
                  label: 'functionName',
                  class: customNodeClass as any,
                }"
                :expand-on-click-node="false"
                :check-on-click-leaf="false"
                :render-content="renderContent"
                @check="handleAuthCheck"
              />
            </div>
          </div>
        </div>
        <div
          :style="{
            minHeight: 'calc(100vh - 70px - 29px - 16px - 16px)',
          }"
          v-else
          class="bg-card"
        >
          <el-empty description="您还未选择应用" />
        </div>
      </div>
    </div>
  </z-page>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, toRaw, watch } from 'vue'
import { ElButton, ElMessage, ElOption, ElSelect, type TreeInstance } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import SysApi from '@/api/sys'
import RoleApi from '@/api/role'
import { MenuType } from '@/utils/enums'

import type { ZStickyInstance } from '@/ui/components/ZSticky/types'
import { useSettingsStore } from '@/stores/settings'

type ScopeFunction = {
  authName: string
  authorityDataRangeType: string
  authorityResources: string
}
const dataScopeOptions = [
  {
    value: '0',
    label: '自定义',
  },
  {
    value: '1',
    label: '全部',
  },
  {
    value: '2',
    label: '本部门',
  },
  {
    value: '3',
    label: '本部门及以下',
  },
  {
    value: '4',
    label: '本人',
  },
]

const defaultDataScope = '2'

const customNodeClass = ({ functionType }: any) => `class-${functionType}`
const renderContent = (
  h,
  {
    node,
    data,
    store,
  }: {
    node: any
    data: any
    store: any
  },
) => {
  const scopedFunctionList = authCache.get(activeTab.value)?.scopedFunctionList ?? []
  const checkedKeys = authCache.get(activeTab.value)?.checkedKeys ?? []
  debugger
  return h(
    'div',
    {
      class: `custom-tree-node flex tree-node--${data.functionType}`,
    },
    h('span', null, node.label),
    data.functionType === MenuType.Menu
      ? h(
          ElSelect,
          {
            class: 'menu-type-tag ml-auto w-[200px]',
            disabled: !checkedKeys.find((item) => String(item) === String(data.functionId)),
            modelValue: (() => {
              return (
                scopedFunctionList.find(
                  (item) => String(item.authorityResources) === String(data.functionId),
                )?.authorityDataRangeType || defaultDataScope
              )
            })(),
            'onUpdate:modelValue': (val: string) => {
              const item = scopedFunctionList.find(
                (item) => String(item.authorityResources) === String(data.functionId),
              )
              if (item) {
                item.authorityDataRangeType = val
              } else {
                scopedFunctionList.push({
                  authName: data.functionName,
                  authorityDataRangeType: val,
                  authorityResources: String(data.functionId),
                })
              }
            },
          },
          () =>
            dataScopeOptions.map((item) => h(ElOption, { value: item.value, label: item.label })),
        )
      : null,
  )
}

const tabs = ref<any[]>([])
const activeTab = ref<number>(0)
const stickyRef = ref<ZStickyInstance>()
const stickyRef2 = ref<ZStickyInstance>()
const treeRef = ref<TreeInstance>()
const treeRef2 = ref<TreeInstance>()

const settingsStore = useSettingsStore()
const isCollapse = computed(() => settingsStore.settings.layout.sidebarCollapse)
const isNavbarFixed = computed(() => settingsStore.settings.layout.navbarFixed)
const isVertical = computed(() => settingsStore.settings.layout.sidebarMode === 'vertical')

const defaultProps = {
  children: 'children',
  label: 'applicationName',
}

const data = ref<any[]>([])
const checkAll = computed({
  get() {
    if (!authCache.has(activeTab.value)) return false
    const { checkedKeys, allNodes } = authCache.get(activeTab.value)!
    return checkedKeys.length > 0 && checkedKeys.length === allNodes.length
  },
  set(checked) {
    if (checked) {
      authCache.get(activeTab.value)!.checkedKeys = authCache
        .get(activeTab.value)!
        .allNodes.map((item: any) => item.functionId)
    } else {
      authCache.get(activeTab.value)!.checkedKeys = []
    }
    treeRef2.value?.setCheckedKeys(authCache.get(activeTab.value)!.checkedKeys)
  },
})

const loadDeptTree = async () => {
  const res = await SysApi.getApplicationTree({ applicationStatus: 1 })
  if (res.data) {
    data.value = res.data
  }
}

const handleAppCheckChange = (data: any, { checkedKeys }: any) => {
  const checked = checkedKeys.includes(data.applicationId)
  if (checked) {
    loadAppAuthData(data.applicationId)
    if (tabs.value.find((item) => item.applicationId === data.applicationId)) {
      return
    }
    tabs.value.push(data)
    activeTab.value = data.applicationId
  } else {
    if (activeTab.value === data.applicationId && tabs.value.length) {
      const index = tabs.value.findIndex((item) => item.applicationId === data.applicationId)
      activeTab.value =
        tabs.value[(index - 1 + tabs.value.length) % tabs.value.length]?.applicationId || 0
    }
    tabs.value = tabs.value.filter((item) => item.applicationId !== data.applicationId)
  }
}

const handleAuthCheck = () => {
  const checkedKeys = treeRef2.value?.getCheckedKeys() as number[]
  authCache.get(activeTab.value)!.checkedKeys = checkedKeys || []
}

watch(activeTab, (newVal) => {
  if (!authCache.has(newVal)) return
  treeRef2.value?.setCheckedKeys(authCache.get(newVal)!.checkedKeys)
})

const route = useRoute()
const router = useRouter()

const roleId = ref<number | undefined>(undefined)
const roleInfo = ref<{ roleName: string; roleCode: string } | undefined>(undefined)
const roleAuth = ref<{
  applicationList: number[]
  functionList: any[]
}>({
  applicationList: [],
  functionList: [],
})
const authCache = reactive(
  new Map<
    number,
    {
      loading: boolean
      data: any[]
      checkedKeys: number[]
      scopedFunctionList: ScopeFunction[]
      responseFunctionList: ScopeFunction[]
      allNodes: any[]
    }
  >(),
)

const goBack = () => {
  router.go(-1)
}

const handleSubmit = () => {
  const applicationList = tabs.value.map((item: any) => String(item.applicationId))

  function normalize(functionId: number, nodes: any[], scopedFunctionList: ScopeFunction[] = []) {
    const node = nodes.find((item) => item.functionId === functionId)
    return {
      authName: node.functionName,
      authorityDataRangeType:
        scopedFunctionList.find(
          (item) => String(item.authorityResources) === String(node.functionId),
        )?.authorityDataRangeType || defaultDataScope,
      authorityResources: node.functionId,
    }
  }
  RoleApi.updateRoleAuth(roleId.value!, {
    applicationList,
    functionList: applicationList.reduce((acc, id) => {
      acc.push(
        ...authCache
          .get(Number(id))!
          .checkedKeys.map((node) =>
            normalize(
              node,
              authCache.get(Number(id))!.allNodes,
              authCache.get(Number(id))?.scopedFunctionList,
            ),
          ),
      )
      return acc
    }, [] as any[]),
  }).then((res) => {
    ElMessage.success(res.message)
    goBack()
  })
}

const loadRoleInfo = async () => {
  if (!roleId.value) return
  try {
    const role = await RoleApi.getRoleById(roleId.value, true).then((res) => res.data)
    if (!role) {
      throw new Error()
    }
    roleInfo.value = role
    RoleApi.getRoleAuth(roleId.value).then((res) => {
      roleAuth.value = {
        applicationList: res.data.applicationList || [],
        functionList: res.data.functionList || [],
      }

      treeRef.value?.setCheckedKeys(roleAuth.value.applicationList ?? [])
      tabs.value = treeRef.value?.getCheckedNodes() || []

      Promise.all(tabs.value.map((item: any) => loadAppAuthData(item.applicationId))).then(() => {
        activeTab.value = tabs.value[0]?.applicationId || 0
      })
    })
  } catch {
    goBack()
  }
}

const loadAppAuthData = async (functionApplication: number) => {
  function getAllNodes(tree: any) {
    const ids = []
    const stack = [...tree]

    while (stack.length > 0) {
      const node = stack.pop()
      if (!node) continue
      ids.push(node)
      if (node.children && node.children.length > 0) {
        stack.push(...node.children)
      }
    }

    return ids
  }

  function resetCheckedKeys() {
    debugger
    const checkedKeys = (authCache.get(functionApplication)?.responseFunctionList || []).map(
      (item) => Number(item.authorityResources),
    )

    if (functionApplication === activeTab.value) {
      setTimeout(() => {
        treeRef2.value?.setCheckedKeys(checkedKeys)
      }, 0)
    }
    authCache.get(functionApplication)!.checkedKeys = checkedKeys
    authCache.get(functionApplication)!.scopedFunctionList = authCache
      .get(functionApplication)!
      .responseFunctionList.map((item) => ({
        ...item,
      }))
  }

  if (authCache.has(functionApplication)) {
    resetCheckedKeys()
    return
  }

  authCache.set(functionApplication, {
    loading: true,
    data: [],
    checkedKeys: [],
    allNodes: [],
    responseFunctionList: [],
    scopedFunctionList: [],
  })

  SysApi.getApplicationMenus({ functionApplication })
    .then((res) => {
      const allNodes = getAllNodes(res.data)
      const responseFunctionList = toRaw(roleAuth.value).functionList.filter((item) =>
        allNodes.find((it2) => it2.functionId === Number(item.authorityResources)),
      )

      authCache.set(functionApplication, {
        loading: false,
        data: res.data,
        checkedKeys: [],
        scopedFunctionList: [],
        responseFunctionList,
        allNodes,
      })

      resetCheckedKeys()
    })
    .finally(() => {
      authCache.get(functionApplication)!.loading = false
    })
}

onMounted(() => {
  const id = route.query.id
  if (id) {
    roleId.value = Number(id)
    if (isNaN(roleId.value)) {
      ElMessage.error('角色ID不正确')
      goBack()
      return
    }
    loadRoleInfo()
    loadDeptTree()
  } else {
    ElMessage.error('角色ID不能为空')
    goBack()
  }
})

watch(
  () => settingsStore.settings.layout.sidebarCollapse,
  () => {
    setTimeout(() => {
      stickyRef.value?.update()
    }, 350)
  },
)

watch(
  () => settingsStore.settings.layout.sidebarMode,
  () => {
    setTimeout(() => {
      stickyRef.value?.update()
    }, 0)
  },
)
</script>

<style lang="scss">
.auth-tree {
  .el-tree {
    --el-tree-node-content-height: 44px;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    height: 40px;
  }
  .class-3 {
    display: inline-block !important;
  }
  .class-2 {
    .el-tree-node__children {
      border-left: 1px solid var(--el-border-color);
      border-right: 1px solid var(--el-border-color);
      &:not(:empty) {
        height: 45px;
      }
    }
  }
  .el-tree-node__content {
    // background: #f9f9fa !important;
    border: 1px solid var(--el-border-color);
    margin-top: -1px;
  }
  .class-3 {
    position: relative;
    top: 2px !important;
    .el-tree-node__content {
      background: none !important;
      border: 0 !important;
      margin-top: -1px;
    }
  }
}
.auth-tabs {
  .el-tabs {
    --el-tabs-header-height: 50px;
    border: 1px solid var(--el-border-color);
    // border-bottom: 0;

    .el-tabs__header {
      margin-bottom: 0;
    }
    .el-tabs__nav-wrap:after {
      height: 0;
    }
  }
}
</style>
