<template>
  <el-drawer
    v-model="visible"
    :title="title"
    :size="size"
    :destroy-on-close="true"
    @closed="onClosed"
  >
    <div class="role-user-drawer">
      <z-table
        ref="tableRef"
        v-model:query="query"
        :fetch-data="fetchTableData"
        :columns="columns"
        row-key="accountId"
      >
      </z-table>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { ZTableInstance, ZTableColumn, ZTableParams } from '@/ui/components/ZTable/types'
import RoleApi from '@/api/role'
import { OptionUtils } from '@/utils/option'

const props = defineProps<{
  modelValue: boolean
  roleId?: number
  roleInfo?: {
    roleName: string
    roleCode: string
    [key: string]: any
  }
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'closed'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const title = computed(() => `角色用户列表 - ${props.roleInfo?.roleName || ''}`)
const size = ref('60%')
const tableRef = ref<ZTableInstance | null>(null)
const query = ref({})

const columns: ZTableColumn[] = [
  { prop: 'accountNumber', label: '账号', minWidth: 120 },
  { prop: 'accountName', label: '姓名', minWidth: 120 },
  { prop: 'accountPhone', label: '手机号码', minWidth: 150 },
  {
    prop: 'accountDepartmentName',
    label: '所属部门',
    minWidth: 180,
    showOverflowTooltip: true,
  },
  // {
  //   prop: 'accountPost',
  //   label: '职务',
  //   minWidth: 120,
  //   showOverflowTooltip: true,
  // },
  {
    prop: 'accountStatus',
    label: '状态',
    minWidth: 100,
    formatter(row, column, cellValue) {
      return OptionUtils.renderTag(cellValue, 'status')!
    },
  },
]

const fetchTableData = async (params: ZTableParams) => {
  if (!props.roleId) {
    return { data: [], total: 0 }
  }

  try {
    const response = await RoleApi.getRoleAssignedUsers(props.roleId, params)
    return response.data
  } catch (error) {
    console.error('获取角色用户列表失败:', error)
    return { data: [], total: 0 }
  }
}

const onClosed = () => {
  emit('closed')
  query.value = {}
}

watch(
  () => props.roleId,
  (newVal) => {
    if (newVal && visible.value) {
      tableRef.value?.refresh(true)
    }
  },
)

onMounted(() => {
  if (props.roleId && visible.value) {
    tableRef.value?.refresh(true)
  }
})
</script>

<style lang="scss" scoped>
.role-user-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;

  &__header {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .role-info {
      display: flex;
      gap: 24px;

      &__item {
        .label {
          font-weight: bold;
          margin-right: 4px;
        }
      }
    }

    .search-bar {
      width: 300px;
    }
  }

  :deep(.el-table) {
    flex: 1;
  }
}
</style>
