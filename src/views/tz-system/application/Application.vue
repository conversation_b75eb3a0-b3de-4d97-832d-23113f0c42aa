<template>
  <z-page>
    <z-table
      ref="tableRef"
      v-model:query="query"
      :columns="columns"
      :toolbar-rows="toolbarRows"
      :fetch-data="fetchTableData"
      pagination-mode="local"
      show-toolbar
      selectable
    >
      <template #operation="{ row }">
        <el-button
          v-permission="perm"
          size="small"
          type="primary"
          link
          @click="onAddChild(row)"
        >
          {{ t('addChild') }}
        </el-button>
        <el-button
          size="small"
          type="primary"
          link
          @click="onEdit(row)"
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          :title="t('message.confirmDelete')"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}</el-button
            >
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <ApplicationForm :model="dialog.model.value" />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import { onBeforeMount, ref } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import SysApi from '@/api/sys'
import { useDialog } from '@/composables/useDialog'
import { usePermission } from '@/directives/permission'
import { useTypedI18n } from '@/i18n'
import { OptionUtils } from '@/utils/option'
import ApplicationForm from './ApplicationForm.vue'

import { useLayout } from '@/composables/useLayout'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'
import type {
  ZTableInstance,
  ZTableColumn,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types'

const { t } = useTypedI18n()
const { has } = usePermission()
useLayout()

const tableRef = ref<ZTableInstance | null>(null)

const query = ref({
  applicationName: undefined,
  applicationStatus: undefined,
  applicationPoint: undefined,
})

const perm = ref('tzufo:dwdbrsgl:add')

const dialog = useDialog({
  name: '应用',
  modeMap: { addChild: '新增子${_name}' },
  onConfirm(mode, ctx) {
    if (mode === 'create' || mode === 'addChild') {
      ctx.form?.validate((isValid) => {
        if (isValid) {
          ctx.isSubmitting.value = true
          SysApi.addApplication(ctx.form?.getValues())
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.saveSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      })
    } else if (mode === 'edit') {
      ctx.form?.validate((isValid) => {
        if (isValid) {
          ctx.isSubmitting.value = true
          SysApi.updateApplication({
            dictionaryId: dialog.model.value?.applicationId,
            ...ctx.form?.getValues(),
          })
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.updateSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      })
    }
  },
  onCancel() {
    console.log('取消')
  },
  onClose() {
    console.log('关闭')
  },
})

const columns: ZTableColumn[] = [
  { prop: 'applicationName', label: '应用名称', align: 'center' },
  { prop: 'applicationCode', label: '应用编码' },
  { prop: 'applicationSort', label: '排序' },
  {
    prop: 'applicationStatus',
    label: '应用状态',
    formatter(row, column, cellValue) {
      return OptionUtils.renderTag(cellValue, 'status')!
    },
  },
  {
    prop: 'applicationPoint',
    label: '应用端',
    formatter(row, column, cellValue) {
      return OptionUtils.renderTag(cellValue, 'platform')!
    },
  },
  { prop: 'createTime', label: '创建时间', width: 180 },
  { prop: 'operation', label: '操作', align: 'center', width: 180 },
]

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create'),
        show: has(perm.value),
      }),
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          tools: [
            {
              key: 'applicationName',
              type: 'input',
              field: 'applicationName',
              label: '应用名称',
              placeholder: '请输入应用名称',
            },
            {
              key: 'applicationStatus',
              type: 'select',
              label: '应用状态',
              placeholder: '请选择状态',
              options: OptionUtils.getOptions('status'),
              width: 240,
            },
            {
              key: 'applicationPoint',
              type: 'select',
              label: '应用类型',
              placeholder: '请选择类型',
              options: OptionUtils.getOptions('platform'),
              width: 240,
            },
            ZTableToolbarFactory.createSearch(),
            ZTableToolbarFactory.createReset(),
          ],
        },
      },
    ],
  },
] as const

const fetchTableData = async (params: ZTableParams<typeof query.value>) => {
  return SysApi.getApplicationAll(params).then((res) => res.data)
}

const onDelete = (row: any) => {
  SysApi.deleteApplication(row.applicationId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const onEdit = (row: any) => {
  dialog.open('edit', row)
}

const onAddChild = (row: any) => {
  dialog.open('addChild', { applicationParent: row.applicationId })
}
</script>
