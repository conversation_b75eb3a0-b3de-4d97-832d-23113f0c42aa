<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
    <template #item-applicationIconActive="{ validate, value, updateValue }">
      <z-upload
        :showFileInfo="false"
        tip=""
        :allowed-types="[FileType.IMAGE]"
        :model-value="value"
        @success="(r) => handleUploadSuccess(r, { validate, updateValue })"
      ></z-upload>
    </template>
    <template #composite-applicationId="{ children, formData, field, updateChildValue }">
      <div class="flex flex-col gap-2">
        <el-form-item
          v-for="child in children"
          :key="child.name"
          :label="child.label"
          :prop="`${field.name}.${child.name}`"
          :rules="child.rules"
        >
          <el-input-number
            v-if="child.type === 'number'"
            v-model="formData[field.name][child.name]"
            :min="child.props?.min"
            :max="child.props?.max"
            :placeholder="child.props?.placeholder"
            @change="(value) => updateChildValue(child.name, value)"
          />
        </el-form-item>
      </div>
    </template>
  </z-form>
</template>

<script setup lang="ts">
import SysApi from '@/api/sys'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField } from '@/ui/components/ZForm/types'
import { OptionUtils } from '@/utils/option'
import { RegexValidator } from '@/utils/regex'
import { ElInput } from 'element-plus'
import { reactive, ref } from 'vue'
import { FileType } from '@/ui/components/ZUpload/constants.ts'

const { onOpen } = useDialogFormContext()
const props = defineProps<{ model: Record<string, any> | null }>()

const initValue = {
  applicationStatus: '0',
  applicationPoint: 'MANAGE',
}

const form = reactive(initValue)

// 字段配置
const fields = ref<ZFormField[]>([
  {
    type: 'input',
    name: 'applicationId',
    show: false,
  },
  {
    name: 'applicationParent',
    label: '上级应用',
    type: 'tree-select',
    valueKey: 'applicationId',
    disabled: ({model}) => {
      return (model?.applicationParent && !model.applicationId) || model?.appicationId
    },
    treeProps: { label: 'applicationName', children: 'children' },
    treeOptions: () =>
      SysApi.getApplicationTree().then((res) => {
        return [{ applicationName: '根应用', applicationId: 0, children: res.data }] as any
      }),
    rules: [{ required: true, message: '请选择上级应用', trigger: 'change' }],
    props: {
      checkStrictly: true,
      placeholder: '请选择上级应用',
    },
  },
  {
    name: 'applicationName',
    label: '应用名称',
    type: 'input',
    rules: [{ required: true, message: '请输入应用名称', trigger: 'blur' }],
    props: {
      placeholder: '请输入应用名称',
      maxLength: 20,
    },
  },
  {
    name: 'applicationEnglishName',
    label: '应用英文名称',
    type: 'input',
    rules: [{ required: true, message: '请输入应用英文名称', trigger: 'blur' }],
    props: {
      placeholder: '请输入应用英文名称',
    },
  },
  {
    name: 'applicationCode',
    label: '应用编码',
    type: 'input',
    rules: [
      { required: true, message: '请输入应用编码', trigger: 'blur' },
      {
        pattern: RegexValidator.getPattern('alphanumeric'),
        message: '请输入字母数字',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入应用编码',
    },
    render(context) {
      return context.h(ElInput, {
        // ...context.field.props,
        modelValue: context.value,
        onInput: (v: string) => {
          context.updateValue(v)
        },
      })
    },
  },
  {
    name: 'applicationSort',
    label: '应用排序',
    type: 'number',
    rules: [{ required: true, message: '请输入应用排序', trigger: 'change' }],
    props: {
      placeholder: '请选择',
      min: 1,
      max: 150,
    },
  },
  {
    name: 'applicationIconActive',
    label: '应用主题',
    type: 'custom',
    rules: [{ required: false, message: '请上传主题', trigger: 'change' }],
    formatIn(value) {
      try {
        return JSON.parse(value as string).red
      } catch {
        return value
      }
    },
  },
  {
    name: 'applicationStatus',
    label: '应用状态',
    type: 'radio',
    options: OptionUtils.getOptions('status'),
  },
  {
    name: 'applicationPoint',
    label: '应用端',
    type: 'radio',
    options: OptionUtils.getOptions('platform'),
  },
  // {
  //   name: 'asyncField',
  //   label: '异步字段',
  //   type: 'custom',
  //   render: async ({ value, field, h }) => {
  //     await new Promise((resolve) => setTimeout(resolve, 1000))
  //     return h(
  //       ElTag,
  //       {
  //         type: 'success',
  //       },
  //       () => [h('span', `异步加载的值: ${value || '未输入'}`)],
  //     )
  //   },
  // },
])

const handleUploadSuccess = (r: any, { validate, updateValue }: any) => {
  updateValue(r.data.absUrl)
  validate()
}

onOpen(() => {
  Object.assign(form, props.model ?? {})
})
</script>
