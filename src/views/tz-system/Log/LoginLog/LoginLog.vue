<template>
  <z-page>
    <z-table
      row-key="loginId"
      ref="tableRef"
      v-model:query="query"
      :columns="columns"
      :fetch-data="fetchTableData"
      paginationMode="server"
      show-toolbar
      :toolbarRows="toolbarRows"
    >
    </z-table>
  </z-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ZTableColumn, ZTableParams, ZTableToolbarRow } from '@/ui/components/ZTable/types.ts'
import LogApi from '@/api/log.ts'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        key: 'accountNumber',
        type: 'input',
        label: '登录账号',
        placeholder: '请输入登录账号',
        width: 250
      },
      ZTableToolbarFactory.createSearch(),
      ZTableToolbarFactory.createReset()
    ]
  }
]

const query = ref()

const columns = ref<ZTableColumn[]>([
  { prop: 'accountId', label: '登录账号id' },
  { prop: 'accountNumber', label: '登录账号' },
  { prop: 'loginIp', label: '登录ip' },
  { prop: 'loginTime', label: '登录时间' },
  { prop: 'browser', label: '浏览器类型' },
  { prop: 'os', label: '操作系统' },
  { prop: 'loginType', label: '登录来源', formatter: (row) => row.loginType === '0' ? 'PC端' : '移动端' },
  { prop: 'loginStatus', label: '登录状态', formatter: (row) => row.loginStatus === '0' ? '成功' : '失败'  },
])

const fetchTableData = async (params: ZTableParams) => {
  return LogApi.getLoginLog(params).then(res => res.data)
}

</script>

<style scoped lang="scss">

</style>
