<template>
  <z-page>
    <z-table
      row-key="loginId"
      ref="tableRef"
      v-model:query="query"
      :columns="columns"
      :fetch-data="fetchTableData"
      paginationMode="server"
      show-toolbar
      :toolbarRows="toolbarRows"
    >
    </z-table>
  </z-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ZTableColumn, ZTableParams, ZTableToolbarRow } from '@/ui/components/ZTable/types.ts'
import LogApi from '@/api/log.ts'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        key: 'accountNumber',
        type: 'input',
        label: '登录账号',
        placeholder: '请输入登录账号',
        width: 250
      },
      {
        key: 'operatorType',
        type: 'select',
        label: '操作类型',
        placeholder: '请选择操作类型',
        options: [
          { label: '其他', value: '0' },
          { label: '后台用户', value: '1' },
          { label: '手机端用户', value: '2' },
        ]
      },
      ZTableToolbarFactory.createSearch(),
      ZTableToolbarFactory.createReset()
    ]
  }
]

const query = ref()

const columns = ref<ZTableColumn[]>([
  { prop: 'title', label: '模块标题' },
  {
    prop: 'businessType',
    label: '业务类型',
    formatter: (row) => {
      const label = { 0: '其他', 1: '新增', 2: '修改', 3: '删除' }
      return label[row.businessType as keyof typeof label]
    }
  },
  {
    prop: 'operatorType',
    label: '操作类型',
    formatter: (row) => {
      const label = { 0: '其他', 1: '后台用户', 2: '手机端用户' }
      return label[row.operatorType as keyof typeof label]
    }
  },
  { prop: 'requestMethod', label: '请求方式' },
  { prop: 'operationUrl', label: '请求URL' },
  { prop: 'operationIp', label: 'ip地址' },
  {
    prop: 'operationStatus',
    label: '操作状态',
    formatter: (row) => row.operationStatus === 0 ? '正常' : '异常'
  },
  { prop: 'accountNumber', label: '操作人员账号' },
  { prop: 'operationTime', label: '操作时间' },
])

const fetchTableData = async (params: ZTableParams) => {
  return LogApi.getOperationLog(params).then(res => res.data)
}

</script>

<style scoped lang="scss">

</style>
