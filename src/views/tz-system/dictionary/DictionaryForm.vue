<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
  </z-form>
</template>

<script setup lang="ts">
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZForm<PERSON>ield } from '@/ui/components/ZForm/types'
import { OptionUtils } from '@/utils/option'
import { RegexValidator } from '@/utils/regex'
import { ElInput } from 'element-plus'
import { reactive, ref } from 'vue'

const { onOpen } = useDialogFormContext()
const props = defineProps<{ model: Record<string, any> | null }>()

const initValue = {
  dictionaryId: undefined,
  dictionaryParentName: undefined,
  dictionaryParent: '0',
  dictionaryName: undefined,
  dictionaryCode: undefined,
  dictionarySort: undefined,
  dictionaryKeyNode: true,
  dictionarySummary: undefined,
}

const form = reactive(initValue)

const fields = ref<ZFormField[]>([
  {
    name: 'dictionaryId',
    show: false,
    type: 'input',
  },
  {
    name: 'dictionaryParent',
    label: '上级字典',
    type: 'custom',
    disabled: true,
    show({model}) {
      if (model?.dictionaryId) {
        return false
      }
      return parseInt(model?.dictionaryParent) > 0
    },
    render(context) {
      return context.h(ElInput, {
        ...context.field.props,
        modelValue: form.dictionaryParentName,
        disabled: true,
      })
    },
  },
  {
    name: 'dictionaryName',
    label: '字典名称',
    type: 'input',
    rules: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
    props: {
      placeholder: '请输入字典名称',
      maxLength: 20,
    },
  },
  {
    name: 'dictionaryCode',
    label: '字典编码',
    type: 'input',
    rules: [
      { required: true, message: '请输入字典编码', trigger: 'blur' },
      {
        pattern: RegexValidator.getPattern('lowercase'),
        message: '请输入小写字母',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入字典编码',
    },
  },
  {
    name: 'dictionarySort',
    label: '显示排序',
    type: 'number',
    rules: [{ required: true, message: '请输入显示排序', trigger: 'change' }],
    fieldClass: 'w-[200px]!',
    props: {
      placeholder: '请选择',
      min: 1,
      max: 150,
    },
  },
  {
    name: 'dictionaryKeyNode',
    label: '关键节点',
    type: 'radio',
    options: OptionUtils.getOptions('yesNo', 'boolean'),
  },
  {
    name: 'dictionarySummary',
    label: '备注',
    type: 'input',
    props: {
      placeholder: '请输入备注',
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 },
    },
  },
])

onOpen(() => {
  Object.assign(form, props.model ?? {})
})
</script>
