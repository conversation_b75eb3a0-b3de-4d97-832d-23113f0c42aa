<template>
  <z-page>
    <z-table
      row-key="dictionaryId"
      :use-pagination="false"
      ref="tableRef"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #applicationStatus="scope">
        <component :is="OptionUtils.renderTag(scope.row.dictionaryKeyNode, 'yesNo')"></component>
      </template>
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          @click="onAddChild(row)"
          link
          >{{ t('common.add') }}</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="onEdit(row)"
          link
          >{{ t('common.edit') }}</el-button
        >
        <el-popconfirm
          :title="t('message.confirmDelete')"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}</el-button
            >
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <DictionaryForm :model="dialog.model.value" />
    </z-dialog-form>
  </z-page>
</template>
<script setup lang="ts">
import { ElButton, ElMessage } from 'element-plus'
import { ref, onUnmounted } from 'vue'
import { OptionUtils } from '@/utils/option'
import { useTypedI18n } from '@/i18n'
import { usePermission } from '@/directives/permission'
import { useDialog } from '@/composables/useDialog'
import DictApI from '@/api/dict'
import DictionaryForm from './DictionaryForm.vue'
import { useLayout } from '@/composables/useLayout'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'
import type {
  ZTableInstance,
  ZTableToolbarRow,
  ZTableColumn,
  ZTableParams,
} from '@/ui/components/ZTable/types'

const { has } = usePermission()
const { t } = useTypedI18n()

const dialog = useDialog({
  name: '字典',
  modeMap: { addChild: '新增字典' },
  onConfirm(mode, ctx) {
    if (mode === 'create' || mode === 'addChild') {
      ctx.form?.validate((isValid) => {
        if (isValid) {
          ctx.isSubmitting.value = true
          DictApI.addDictionary(ctx.form?.getValues())
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.saveSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      })
    } else if (mode === 'edit') {
      ctx.form?.validate((isValid) => {
        if (isValid) {
          ctx.isSubmitting.value = true
          DictApI.updateDictionary({
            dictionaryId: dialog.model.value?.dictionaryId,
            ...ctx.form?.getValues(),
          })
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.updateSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      })
    }
  },
})

const perm = ref('tzufo:dwdbrsgl:add')
const tableRef = ref<ZTableInstance | null>(null)

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => {
          dialog.open('create')
        },
        // show: has(perm.value),
      }),
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          tools: [
            {
              key: 'dictionaryName',
              type: 'input',
              label: '字典名称',
              placeholder: '请输入字典名称',
              width: 240,
            },
            {
              key: 'dictionaryCode',
              type: 'input',
              label: '字典编码',
              placeholder: '请输入字典编码',
              width: 240,
            },
            {
              key: 'dictionaryKeyNode',
              type: 'select',
              label: '关键节点',
              placeholder: '请选择',
              width: 240,
              options: OptionUtils.getOptions('yesNo', 'boolean'),
            },
            ZTableToolbarFactory.createSearch(),
            ZTableToolbarFactory.createReset(),
          ],
        },
      },
    ],
  },
]

const query = ref()

const columns: ZTableColumn[] = [
  { prop: 'dictionaryName', label: '字典名称' },
  { prop: 'dictionaryCode', label: '字典编码' },
  { prop: 'dictionarySort', label: '排序' },
  {
    prop: 'dictionaryKeyNode',
    label: '是否关键节点',
    formatter(row, column, cellValue) {
      return OptionUtils.renderTag(cellValue, 'yesNo')!
    },
  },
  { prop: 'dictionaryCreateTime', label: '创建时间' },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
]

const fetchTableData = async (params: ZTableParams) => {
  return DictApI.getDictionaryTree(params).then((res) => res.data)
}

const onEdit = (row: any) => {
  dialog.open('edit', row)
}

const onAddChild = (row: any) => {
  dialog.open('addChild', {
    dictionaryParent: row.dictionaryId,
    dictionaryParentName: row.dictionaryName,
  })
}

const onDelete = (row: any) => {
  DictApI.deleteDictionary(row.dictionaryId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}
</script>
