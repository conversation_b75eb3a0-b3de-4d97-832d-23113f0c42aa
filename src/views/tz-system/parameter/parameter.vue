<template>
  <z-page>
    <z-table
      ref="tableRef"
      v-model:query="query"
      :columns="columns"
      :toolbar-rows="toolbarRows"
      :fetch-data="fetchTableData"
      show-toolbar
    >
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          link
          @click="onEdit(row)"
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          :title="t('message.confirmDelete')"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}</el-button
            >
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <ParameterForm :model="dialog.model.value" />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ParameterApi from '@/api/parameter'
import { useDialog } from '@/composables/useDialog'
import { useTypedI18n } from '@/i18n'
import ParameterForm from './ParameterForm.vue'

import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'
import type {
  ZTableInstance,
  ZTableColumn,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types'

const { t } = useTypedI18n()

const tableRef = ref<ZTableInstance | null>(null)

const query = ref({
  configName: undefined,
  configKey: undefined,
})

const dialog = useDialog({
  name: '参数',
  onConfirm(mode, ctx) {
    ctx.form?.validate((isValid, values) => {
      if (isValid) {
        if (mode === 'create') {
          ctx.isSubmitting.value = true
          ParameterApi.addParameter(values)
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.saveSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }

        if (mode === 'edit') {
          ctx.isSubmitting.value = true
          ParameterApi.updateParameterById(values)
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.updateSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      }
    })
  },
})

const columns: ZTableColumn[] = [
  { prop: 'configName', label: '参数名称' },
  { prop: 'configKey', label: '参数键名' },
  { prop: 'configValue', label: '参数值' },
  {
    prop: 'remark',
    label: '备注',
  },
  { prop: 'operation', label: '操作', align: 'center', width: 180 },
]

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create'),
      }),
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          tools: [
            {
              key: 'configName',
              type: 'input',
              label: '参数名称',
              placeholder: '请输入参数名称',
              width: 240,
            },
            {
              key: 'configKey',
              type: 'input',
              label: '参数键名',
              placeholder: '请输入参数键名',
              width: 240,
            },
            ZTableToolbarFactory.createSearch(),
            ZTableToolbarFactory.createReset(),
          ],
        },
      },
    ],
  },
]

const fetchTableData = async (params: ZTableParams<typeof query.value>) => {
  return ParameterApi.getParameterList(params).then((res) => res.data)
}

const onDelete = (row: any) => {
  ParameterApi.deleteParameterById(row.configId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const onEdit = (row: any) => {
  dialog.open('edit', row)
}
</script>
