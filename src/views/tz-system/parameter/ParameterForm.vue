<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
  </z-form>
</template>

<script setup lang="ts">
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField } from '@/ui/components/ZForm/types'
import { reactive, ref } from 'vue'

const props = defineProps<{ model: Record<string, any> | null }>()

const { onOpen } = useDialogFormContext()

const form = reactive({})

const fields = ref<ZFormField[]>([
  {
    name: 'configId',
    show: false,
  },
  {
    name: 'configName',
    label: '参数名称',
    type: 'input',
    rules: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
    props: {
      placeholder: '请输入参数名称',
    },
  },
  {
    name: 'configKey',
    label: '参数键名',
    type: 'input',
    disabled: ({ model }) => {
      return !!model.configId
    },
    rules: [{ required: true, message: '请输入参数键名', trigger: 'blur' }],
    props: {
      placeholder: '请输入参数键名',
    },
  },
  {
    name: 'configValue',
    label: '参数值',
    type: 'input',
    rules: [{ required: true, message: '请输入参数值', trigger: 'blur' }],
    props: {
      placeholder: '请输入参数值',
    },
  },
  {
    name: 'remark',
    label: '备注',
    type: 'input',
    props: {
      type: 'textarea',
      placeholder: '请输入备注',
    },
  },
])

onOpen(() => {
  Object.assign(form, props.model ?? {})
})
</script>
