<template>
  <z-page class="pt-2">
    <el-tabs
      @tab-change="handleTabChange"
      v-model="activeApp"
    >
      <el-tab-pane
        :label="app.applicationName"
        :name="app.applicationId"
        v-for="app in apps"
        :key="app.applicationId"
      >
      </el-tab-pane>
    </el-tabs>

    <z-table
      ref="tableRef"
      row-key="functionId"
      :auto-load="false"
      v-model:query="query"
      :columns="columns"
      :toolbar-rows="toolbarRows"
      :fetch-data="fetchTableData"
      show-toolbar
      selectable
      pagination-mode="local"
    >
      <template #applicationStatus="scope">
        <component :is="OptionUtils.renderTag(scope.row.applicationStatus, 'status')"></component>
      </template>

      <template #operation="{ row }">
        <!-- <el-button
          v-if="row.functionType === MenuType.Dir"
          size="small"
          type="primary"
          link
          @click="onAddMenu(row)"
        >
          新增菜单
        </el-button>
        <el-button
          v-if="row.functionType === MenuType.Menu"
          size="small"
          type="warning"
          link
          @click="onAddBtn(row)"
        >
          新增按钮
        </el-button> -->
        <el-button
          size="small"
          type="primary"
          link
          @click="onEdit(row)"
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          :title="t('message.confirmDelete')"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>
    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @close="dialog.handleClose"
    >
      <MenuForm
        ref="departmentFormRef"
        :model="dialog.model.value"
        :mode="dialog.mode.value"
      />
    </z-dialog-form>
  </z-page>
</template>
<script setup lang="ts">
import SysApi from '@/api/sys'
import { onMounted, ref } from 'vue'
import { OptionUtils } from '@/utils/option.ts'
import { ElButton, ElMessage } from 'element-plus'
import { useTypedI18n } from '@/i18n'
import type {
  ZTableColumn,
  ZTableInstance,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types.ts'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'
import { useDialog } from '@/composables/useDialog.ts'
import MenuForm from './MenuForm.vue'

const tableRef = ref<ZTableInstance>()
const activeApp = ref()
const query = ref()
const { t } = useTypedI18n()

const dialog = useDialog({
  name: '资源',
  modeMap: {
    addMenu: '新增菜单',
    addBtn: '新增按钮',
    edit: (mode) => `${t('common.edit')}${mode?.functionName}`,
  },
  onConfirm(mode, ctx) {
    ctx.form?.validate((isValid, values) => {
      if (isValid) {
        if (mode === 'create') {
          ctx.isSubmitting.value = true
          SysApi.addFunction({
            appliactionId: values.functionApplication,
            ...values,
          })
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.updateSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }

        if (mode === 'edit') {
          ctx.isSubmitting.value = true
          SysApi.updateFunctionById({
            appliactionId: values.functionApplication,
            ...values,
          })
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.updateSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      }
    })
  },
})

const apps = ref<any[]>([])
const columns: ZTableColumn[] = [
  { prop: 'functionName', label: '菜单名称', width: 180 },
  { prop: 'functionSort', label: '排序' },
  { prop: 'functionRoutePath', label: '路由地址' },
  {
    prop: 'functionStatus',
    label: '菜单状态',
    formatter(row, column, cellValue) {
      return OptionUtils.renderTag(cellValue, 'status')!
    },
  },
  {
    prop: 'functionType',
    label: '菜单类型',
    formatter(row, column, cellValue) {
      return OptionUtils.renderTag(cellValue, 'menuType')!
    },
  },
  {
    prop: 'functionShowStatus',
    label: '显示状态',
    formatter(row, column, cellValue) {
      return OptionUtils.renderTag(cellValue, 'visibility')!
    },
  },
  { prop: 'createTime', label: '创建时间', width: 180 },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
]

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create', { functionApplication: activeApp.value }),
      }),
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          tools: [
            {
              key: 'functionName',
              type: 'input',
              label: '菜单名称',
              placeholder: '请输入菜单名称',
              width: 240,
            },
            {
              key: 'functionStatus',
              type: 'select',
              label: '菜单状态',
              placeholder: '请选择状态',
              options: OptionUtils.getOptions('status'),
              width: 240,
            },
            ZTableToolbarFactory.createSearch(),
            ZTableToolbarFactory.createReset(),
          ],
        },
      },
    ],
  },
]

const fetchTableData = async (params: ZTableParams) => {
  return SysApi.getApplicationMenus({ ...params, functionApplication: activeApp.value }).then(
    (res) => res.data,
  )
}

const handleTabChange = (tab: any) => {
  activeApp.value = tab
  tableRef.value?.reset()
}

const onEdit = (row: any) => {
  dialog.open('edit', row)
}

const onAddBtn = (row: any) => {
  dialog.open('addBtn', { functionParent: row.functionId, functionApplication: activeApp.value })
}

const onAddMenu = (row: any) => {
  dialog.open('addMenu', { functionParent: row.functionId, functionApplication: activeApp.value })
}

const onDelete = (row: any) => {
  SysApi.deleteFunctionById(row.functionId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

onMounted(() => {
  SysApi.getApplicationTree().then((res) => {
    apps.value = res.data.filter((v: any) => v.applicationStatus !== '0')
    activeApp.value = apps.value[0].applicationId
    tableRef.value?.refresh()
  })
})
</script>
