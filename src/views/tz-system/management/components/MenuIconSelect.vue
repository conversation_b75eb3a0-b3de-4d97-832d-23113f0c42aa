<template>
  <el-popover
    v-model:visible="visible"
    trigger="click"
    :width="600"
    placement="top-start"
    @show="visible = true"
    @hide="visible = false"
  >
    <template #reference>
      <div
        class="menu-icon-select-trigger"
        :class="{ 'is-selected': !!modelValue }"
      >
        <div class="trigger-content">
          <el-icon
            v-if="modelValue"
            class="selected-icon"
          >
            <component :is="ElementPlusIconsVue[modelValue as keyof typeof ElementPlusIconsVue]" />
          </el-icon>
          <span
            v-else
            class="placeholder"
            >请选择图标</span
          >
          <span
            v-if="modelValue"
            class="icon-name"
            >{{ modelValue }}</span
          >
        </div>
        <el-icon
          class="arrow-icon"
          :class="{ 'is-reverse': visible }"
        >
          <ArrowDown />
        </el-icon>
      </div>
    </template>

    <div class="menu-icon-select">
      <div class="search-section">
        <el-input
          v-model="searchText"
          placeholder="搜索图标名称"
          clearable
          class="search-input"
        />
      </div>

      <div class="action-section">
        <el-button
          v-if="modelValue"
          size="small"
          type="danger"
          text
          @click="handleClear"
        >
          清除选择
        </el-button>
      </div>

      <div class="icon-container">
        <div
          v-for="(icons, category) in filteredIcons"
          :key="category"
          class="icon-group"
        >
          <div class="group-title">{{ getCategoryName(category as IconCategory) }}</div>
          <div class="icon-grid">
            <div
              v-for="icon in icons"
              :key="icon.name"
              class="icon-item"
              :class="{ 'is-selected': modelValue === icon.name }"
              @click="handleSelect(icon.name)"
              :title="icon.name"
            >
              <el-icon class="icon">
                <component :is="icon.component" />
              </el-icon>
              <span class="icon-label">{{ icon.name }}</span>
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="!hasResults"
        class="no-results"
      >
        <el-empty
          description="未找到匹配的图标"
          :image-size="80"
        />
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const MENU_ICON_CATEGORIES = {
  navigation: ['Menu', 'Fold', 'Expand', 'Grid', 'List', 'Operation', 'Platform', 'Switch'],
  common: ['House', 'User', 'Setting', 'Tools', 'Bell', 'Star', 'Flag', 'Trophy'],
  business: ['Management', 'Monitor', 'DataAnalysis', 'Histogram', 'PieChart', 'TrendCharts'],
  data: ['Document', 'Folder', 'Files', 'FolderOpened', 'Reading', 'Notebook', 'Collection'],
  system: ['Key', 'Lock', 'Unlock', 'View', 'Hide', 'RefreshRight', 'RefreshLeft', 'Refresh'],
  communication: ['Message', 'ChatDotRound', 'ChatLineRound', 'Phone', 'Promotion', 'Microphone'],
} as const

const getCategoryName = (category: keyof typeof MENU_ICON_CATEGORIES): string => {
  const names: Record<keyof typeof MENU_ICON_CATEGORIES, string> = {
    navigation: '导航类',
    common: '常用类',
    business: '业务类',
    data: '数据类',
    system: '系统类',
    communication: '通讯类',
  }
  return names[category]
}

interface IconOption {
  name: string
  component: any
}

type IconCategory = keyof typeof MENU_ICON_CATEGORIES

defineOptions({ name: 'MenuIconSelect' })

defineProps<{
  modelValue?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}>()

const visible = ref(false)
const searchText = ref('')

const filteredIcons = computed(() => {
  const groups: Record<IconCategory, IconOption[]> = {} as Record<IconCategory, IconOption[]>
  const search = searchText.value.toLowerCase()

  Object.entries(MENU_ICON_CATEGORIES).forEach(([category, iconNames]) => {
    const filtered = iconNames
      .filter((name) => !search || name.toLowerCase().includes(search))
      .map((name) => ({
        name,
        component: ElementPlusIconsVue[name as keyof typeof ElementPlusIconsVue],
      }))
      .filter((icon) => icon.component) // 确保图标组件存在

    if (filtered.length > 0) {
      groups[category as IconCategory] = filtered
    }
  })

  return groups
})

const hasResults = computed(() => {
  return Object.keys(filteredIcons.value).length > 0
})

const handleSelect = (iconName: string) => {
  emit('update:modelValue', iconName)
  emit('change', iconName)
  visible.value = false
}

const handleClear = () => {
  emit('update:modelValue', '')
  emit('change', '')
  visible.value = false
}
</script>

<style lang="scss" scoped>
.menu-icon-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 32px;
  padding: 4px 11px;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  background-color: var(--el-fill-color-blank);
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: var(--el-color-primary);
  }

  &.is-selected {
    border-color: var(--el-color-primary);
  }

  .trigger-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .selected-icon {
      font-size: 16px;
      color: var(--el-color-primary);
    }

    .placeholder {
      color: var(--el-text-color-placeholder);
    }

    .icon-name {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }

  .arrow-icon {
    font-size: 12px;
    color: var(--el-text-color-placeholder);
    transition: transform 0.2s;

    &.is-reverse {
      transform: rotate(180deg);
    }
  }
}

.menu-icon-select {
  .search-section {
    margin-bottom: 12px;

    .search-input {
      width: 100%;
    }
  }

  .action-section {
    margin-bottom: 12px;
    text-align: right;
  }

  .icon-container {
    max-height: 400px;
    overflow-y: auto;

    .icon-group {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .group-title {
        font-size: 13px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 8px;
        padding-bottom: 4px;
        border-bottom: 1px solid var(--el-border-color-lighter);
      }

      .icon-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 8px;

        .icon-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 8px 4px;
          border: 1px solid transparent;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background-color: var(--el-color-primary-light-9);
            border-color: var(--el-color-primary-light-7);
          }

          &.is-selected {
            background-color: var(--el-color-primary-light-8);
            border-color: var(--el-color-primary);

            .icon {
              color: var(--el-color-primary);
            }
          }

          .icon {
            font-size: 20px;
            margin-bottom: 4px;
            color: var(--el-text-color-regular);
          }

          .icon-label {
            font-size: 11px;
            color: var(--el-text-color-secondary);
            text-align: center;
            line-height: 1.2;
            word-break: break-all;
          }
        }
      }
    }
  }

  .no-results {
    padding: 20px;
    text-align: center;
  }
}
</style>
