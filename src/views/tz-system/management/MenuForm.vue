<template>
  <z-form
    v-model:model="form"
    :fetch-data="fetchData"
    :fields="fields"
    :validate-on-rule-change="false"
    label-width="120px"
  >
    <template #item-functionIcon="{ formData, field }">
      <MenuIconSelect v-model="formData[field.name]" />
    </template>
  </z-form>
</template>

<script lang="ts" setup>
import { computed, nextTick, reactive, readonly, ref } from 'vue'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField } from '@/ui/components/ZForm/types'
import { MenuType, Status, Visibility } from '@/utils/enums'
import { OptionUtils } from '@/utils/option'
import SysApi from '@/api/sys'
import MenuIconSelect from './components/MenuIconSelect.vue'

const props = defineProps<{
  model: Record<string, any> | null
  mode?: 'create' | 'edit' | 'addMenu' | 'addBtn' | string
}>()

const form = reactive({
  functionParent: undefined,
  functionApplication: undefined,
  functionType: MenuType.Dir,
  functionStatus: Status.Enabled,
  functionShowStatus: Visibility.Show,
  functionLayout: 'default',
})

const { formRef } = useDialogFormContext()

const isEdit = computed(() => props.mode === 'edit')
const isAddBtn = computed(() => props.mode === 'addBtn')
const isAddMenu = computed(() => props.mode === 'addMenu')
const isCreateRoot = computed(() => props.mode === 'create')

const fields = ref<ZFormField[]>([
  {
    name: 'functionId',
    show: false,
    type: 'input',
  },
  {
    name: 'functionApplication',
    label: '所属应用',
    type: 'select',
    labelKey: 'applicationName',
    valueKey: 'applicationId',
    disabled: !isCreateRoot.value && !isEdit.value,
    options: () => SysApi.getApplicationTree().then((res) => res.data),
    rules: [
      {
        required: true,
        message: '请选择所属应用',
        trigger: 'change',
      },
    ],
    props: {
      checkStrictly: true,
      placeholder: '请选择所属应用',
    },
  },
  {
    name: 'functionParent',
    label: '上级资源',
    type: 'tree-select',
    props: {
      checkStrictly: true,
      placeholder: '请选择上级资源',
    },

    // show: ({}) => !isCreateRoot.value,
    valueKey: 'functionId',
    treeProps: {
      label: 'functionName',
      children: 'children',
    },
    rules: [
      {
        required: true,
        message: '请选择上级资源',
        trigger: 'change',
      },
    ],
    disabled(ctx) {
      return !ctx.options?.length
    },
    noCache: true,
    treeOptions: async (context) => {
      if (context.model.functionApplication) {
        const children = await SysApi.getApplicationMenus({
          functionApplication: context.model.functionApplication,
        }).then((res) => {
          const filterButtonNodes = (nodes: any[]): any[] => {
            return nodes
              .filter((node) => node.functionType !== MenuType.Btn)
              .map((node) => ({
                ...node,
                children: node.children ? filterButtonNodes(node.children) : [],
              }))
          }
          return filterButtonNodes(res.data)
        })
        const treeOptions = [
          {
            functionName: '根资源',
            functionId: 0,
            children,
          },
        ]
        return treeOptions
      } else {
        return []
      }
    },
    dependencies: {
      fields: ['functionApplication'],
      async handler({ dependencyValues, updateModel, form, reason }) {
        if (!dependencyValues.functionApplication) return
        formRef.value?.refetchOptions('functionParent')
        if (reason.type === 'VALUE_CHANGE') {
          updateModel('')
          setTimeout(() => {
            form.clearValidate('functionParent')
          }, 0)
        }
      },
    },
  },
  {
    name: 'functionType',
    label: '资源类型',
    type: 'radio',
    rules: [
      {
        required: true,
        message: '请选择资源类型',
        trigger: 'change',
      },
    ],
    disabled() {
      return isAddBtn.value || isAddMenu.value
    },
    options: [
      { label: '目录', value: MenuType.Dir },
      { label: '菜单', value: MenuType.Menu },
      { label: '按钮', value: MenuType.Btn },
      { label: '单页', value: MenuType.Page },
    ],
    dependencies: {
      fields: ['functionParent'],
      async handler({ dependencyValues, updateField, updateModel, form }) {
        const treeOptions = form.getFieldOptions('functionParent')
        const allMenus = treeOptions[0]?.children || []

        const findNode = (nodes: any[], targetId: number): any => {
          for (const node of nodes) {
            if (node.functionId === targetId) return node
            if (node.children?.length) {
              const found = findNode(node.children, targetId)
              if (found) return found
            }
          }
          return null
        }

        const currentNode = findNode(allMenus, dependencyValues.functionParent)

        // 根据节点类型设置选项的禁用状态
        const updateFunctionTypeOptions = () => {
          const currentOptions = form.getFieldOptions('functionType')

          // 根节点（functionParent === 0）
          if (dependencyValues.functionParent === 0) {
            return currentOptions.map((opt) => ({
              ...opt,
              disabled: opt.value === MenuType.Btn,
            }))
          }

          // 目录节点
          if (currentNode?.functionType === MenuType.Dir) {
            return currentOptions.map((opt) => ({
              ...opt,
              disabled: opt.value === MenuType.Btn,
            }))
          }

          // 菜单节点
          if (currentNode?.functionType === MenuType.Menu) {
            return currentOptions.map((opt) => ({
              ...opt,
              disabled: opt.value !== MenuType.Btn,
            }))
          }

          // 单页节点
          if (currentNode?.functionType === MenuType.Page) {
            return currentOptions.map((opt) => ({
              ...opt,
              disabled: opt.value !== MenuType.Btn,
            }))
          }

          return currentOptions
        }

        // 更新选项
        const newOptions = updateFunctionTypeOptions()
        updateField({
          options: newOptions,
        })

        // 如果当前选中的值被禁用，清空选择并清除校验状态
        const currentValue = form.getValues().functionType
        if (currentValue && newOptions.find((opt) => opt.value === currentValue)?.disabled) {
          updateModel(undefined)
          setTimeout(() => {
            form.clearValidate('functionType')
          }, 0)
        }
      },
    },
  },
  {
    name: 'functionName',
    label: '资源名称',
    type: 'input',
    rules: [
      {
        required: true,
        message: '请输入资源名称',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入资源名称',
    },
  },
  {
    name: 'functionPermissionCode',
    label: '权限标识',
    type: 'input',
    props: {
      placeholder: '请输入权限标识',
    },
  },
  {
    name: 'functionRoutePath',
    label: '路由地址',
    type: 'input',
    show: ({ model }) => model.functionType !== MenuType.Btn,
    rules: [
      {
        required: true,
        message: '请输入路由地址',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入路由地址',
    },
  },
  {
    name: 'functionLayout',
    label: '路由布局',
    type: 'radio',
    options: OptionUtils.getOptions('layout'),
    show: ({ model }) => model.functionType === MenuType.Menu,
    props: {
      placeholder: '请选择路由布局',
    },
  },
  {
    name: 'functionComponentPath',
    label: '组件路径',
    type: 'input',
    show: ({ model }) => {
      return model.functionType === MenuType.Menu || model.functionType === MenuType.Page
    },
    rules: [
      {
        required: true,
        message: '请输入组件路径',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入组件路径',
    },
  },
  {
    name: 'functionSort',
    label: '显示排序',
    type: 'number',
    props: {
      placeholder: '请选择',
    },
  },
  {
    name: 'functionStatus',
    type: 'radio',
    options: OptionUtils.getOptions('status'),
    label: '启用状态',
    show: ({ model }) => model.functionType !== MenuType.Page,
  },
  {
    name: 'functionIcon',
    label: '菜单图标',
    type: 'custom',
    readonly: true,
    show: ({ model }) =>
      model.functionType === MenuType.Dir || model.functionType === MenuType.Menu,
    props: {
      placeholder: '请选择菜单图标',
    },
  },
  {
    name: 'functionShowStatus',
    label: '显示状态',
    type: 'radio',
    show: ({ model }) =>
      model.functionType !== MenuType.Btn && model.functionType !== MenuType.Page,
    options: OptionUtils.getOptions('visibility'),
  },
])

const fetchData = () => {
  return SysApi.getFunctionById(props.model?.functionId).then((res) => {
    return res.data
  })
}

const { onOpen } = useDialogFormContext()

onOpen(async (zform) => {
  zform?.dependencies.disableAuto('functionParent')

  if (isCreateRoot.value) {
    form.functionApplication = props.model?.functionApplication
    await nextTick()
    zform?.dependencies.enableAuto('functionParent')
    zform?.dependencies.trigger('functionParent', { action: 'init' })
  }

  if (isEdit.value) {
    await zform?.refetch()
    zform?.dependencies.enableAuto('functionParent')
    zform?.dependencies.trigger('functionParent', { action: 'init' })
  }
})
</script>
