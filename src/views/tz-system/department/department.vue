<template>
  <z-page>
    <z-table
      ref="tableRef"
      :auto-load="false"
      row-key="dataId"
      :use-pagination="false"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          @click="onAddChild(row)"
          link
        >
          {{ t('common.add') }}
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="onEdit(row)"
          link
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          :title="t('message.confirmDelete')"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
            >
              {{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <DepartmentForm
        ref="departmentFormRef"
        :data-id="dialog.model.value?.dataId"
      />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import type {
  ZTableInstance,
  ZTableToolbarTool,
  ZTableColumn,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types'
import type { ComputedRef } from 'vue'

import { ElButton, ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { useDialog } from '@/composables/useDialog'
import DepartmentForm from './DepartmentForm.vue'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'

import DeptApi from '@/api/dept'
import DictApI from '@/api/dict'

const tableRef = ref<ZTableInstance | null>(null)
const departmentFormRef = ref<InstanceType<typeof DepartmentForm> | null>(null)

const { t } = useTypedI18n()
const query = ref()
const columns = ref<ZTableColumn[]>([])
const toolbarTools = ref<ZTableToolbarTool[]>([])

const toolbarRows: ComputedRef<ZTableToolbarRow[]> = computed(() => [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create', { parentId: 0 }),
      }),
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          tools: [
            ...toolbarTools.value,
            ZTableToolbarFactory.createSearch(),
            ZTableToolbarFactory.createReset(),
          ],
        },
      },
    ],
  },
])

const getOptionValue = (v: string, options: any[]) => {
  return options.find((item: any) => item === v || item.value === v)
}

const parseValues = (values: Record<string, any>, fields: any[], rawFields: any[]) => {
  return fields.reduce((acc, field) => {
    if (field.type === 'select' || field.type === 'radio') {
      const rawOptions = rawFields.find((it: any) => it.fieldCode === field.name).fieldValue
        .valueList
      const fetchedOptions = field.options
      acc[field.name] = [
        getOptionValue(values[field.name], rawOptions.length ? rawOptions : fetchedOptions),
      ]
    } else {
      acc[field.name] = values[field.name]
    }
    return acc
  }, {} as any)
}

const fetchTableData = async (params: ZTableParams) => {
  const queryItemList = Object.entries(params).map(([key, value]) => ({
    fieldCode: key,
    queryValue: [value],
  }))
  return DeptApi.getDeptListFromForm({ queryItemList }).then((res) => res.data)
}

const onDelete = (row: any) => {
  DeptApi.deleteDeptById(row.dataId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const onEdit = (row: any) => {
  dialog.open('edit', { dataId: row.dataId })
}

const onAddChild = (row: any) => {
  dialog.open('addChild', { parentId: row.dataId })
}

const dialog = useDialog({
  name: '部门',
  modeMap: { addChild: '新增子部门' },
  onConfirm(mode, ctx) {
    if (!ctx.form) return

    const handleSubmit = (formData: any) => {
      if (mode === 'create' || mode === 'addChild') {
        return DeptApi.addDept({
          ...formData,
          parentId: dialog.model.value?.parentId ?? 0,
        })
      } else if (mode === 'edit') {
        return DeptApi.updateDeptById(dialog.model.value?.dataId, formData)
      }
    }

    ctx.form.validate((isValid) => {
      if (!isValid) return

      const formData = parseValues(
        ctx.form!.getValues(),
        ctx.form!.getFields(),
        departmentFormRef.value!.getFields(),
      )
      ctx.isSubmitting.value = true

      handleSubmit(formData)
        ?.then(() => {
          tableRef.value?.refresh()
          dialog.close()
          ElMessage.success(
            mode === 'update' ? t('message.updateSuccess') : t('message.saveSuccess'),
          )
        })
        .finally(() => {
          ctx.isSubmitting.value = false
        })
    })
  },
})

const initTableColumns = async () => {
  const { data } = await DeptApi.getTableField()
  columns.value = data
    .map(
      (it: any) =>
        ({
          label: it.fieldTitle,
          prop: it.fieldCode,
          formatter(row, column, cellValue) {
            if (it.fieldType === 'select' || it.fieldType === 'radio') {
              return row[it.fieldCode]?.map((item: any) => item.label).join(',')
            }
            return cellValue
          },
        }) as ZTableColumn,
    )
    .concat([{ prop: 'operation', label: '操作', align: 'center', width: 220, fixed: 'right' }])
}

const initToolbarTools = async () => {
  const { data } = await DeptApi.getTableQueryParams()
  toolbarTools.value = data
    .map((it: any) => {
      if (it.fieldType === 'input') {
        return {
          key: it.fieldCode,
          type: it.fieldType,
          label: it.fieldTitle,
          placeholder: it.fieldPlaceholder,
          width: 240,
        }
      }

      if (it.fieldType === 'radio') {
        const dictionaryCode = it.fieldValue.value
        if (!dictionaryCode) return null

        return {
          key: it.fieldCode,
          type: 'select',
          label: it.fieldTitle,
          placeholder: it.fieldPlaceholder,
          width: 240,
          options: () =>
            DictApI.getDictItemsByCode(dictionaryCode).then((res) =>
              res.data.map((it: any) => ({
                label: it.dictionaryName,
                value: it.dictionaryCode,
              })),
            ),
        }
      }
      return null
    })
    .filter(Boolean)
}

onMounted(async () => {
  await initTableColumns()
  await initToolbarTools()
  tableRef.value?.refresh()
})
</script>
