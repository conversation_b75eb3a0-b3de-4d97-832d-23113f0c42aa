<template>
  <z-form
    ref="formRef"
    :fetch-data="fetchData"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
  </z-form>
</template>

<script setup lang="ts">
import DeptApi from '@/api/dept'
import DictApI from '@/api/dict'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { reactive, ref } from 'vue'

const { onOpen } = useDialogFormContext()
const props = defineProps<{ dataId: string }>()
const formRef = ref<ZFormInstance>()

const initValue = {
  parentId: 0,
}

const form = reactive(initValue)

const fields = ref<ZFormField[]>()
const rawFields = ref<any[]>([])

const fetchData = () =>
  DeptApi.getDeptById(props.dataId).then((res) => {
    const o = (fields.value ?? []).reduce((acc, { type, name }) => {
      acc[name] = res.data[name]
      if (type === 'select' || type === 'radio') {
        const v = res.data[name]
        if (Array.isArray(v) && v.length) {
          acc[name] = v[0].value ?? v[0]
        }
      }
      return acc
    }, {} as any)

    return o
  })

const parseFields = (data: any[]) => {
  fields.value = [
    {
      name: 'parentId',
      show: false,
      type: 'input',
    },
  ]
  data.forEach((it) => {
    it.formStructureData?.fieldList.forEach((f: any) => {
      const {
        fieldValue,
        fieldRequired: required,
        fieldPlaceholder: placeholder,
        fieldTitle: label,
        fieldType: type,
        fieldCode: name,
      } = f
      const field = {
        label,
        required,
        name,
        type,
        props: {
          placeholder,
        },
      } as any

      if (required) {
        field.rules = [
          {
            required,
            message: `${label}不能为空`,
            trigger: type === 'select' || type === 'radio' ? 'change' : 'blur',
          },
        ]
      }

      if (type === 'textarea') {
        field.type = 'input'
        field.props.type = 'textarea'
      }

      if (type === 'radio' || type === 'select') {
        field.options = fieldValue?.valueList?.length
          ? fieldValue?.valueList.map((m: any) => ({ label: m, value: m }))
          : () =>
              DictApI.getDictItemsByCode(fieldValue.value).then((res) => {
                return res.data.map((it: any) => ({
                  label: it.dictionaryName,
                  value: it.dictionaryCode,
                }))
              })
      }

      fields.value?.push(field)
      rawFields.value.push(f)
    })
  })
}

defineExpose({
  getFields: () => rawFields.value,
})

onOpen(() => {
  DeptApi.getFormFields().then((res) => {
    parseFields(res.data ?? [])
    if (props.dataId) {
      formRef.value?.refetch()
    }
  })
})
</script>
