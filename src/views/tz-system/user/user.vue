<template>
  <z-page class="bg-transparent p-0">
    <div class="flex gap-4">
      <div class="shrink-0 w-[280px]">
        <z-sticky
          class="transition-all duration-300 ease-in-out bg-card w-[280px]"
          :class="{
            'left-[96px]!': isCollapse && isVertical,
            'left-[272px]!': !isCollapse && isVertical,
          }"
          ref="stickyRef"
          :margin-top="isNavbarFixed ? 74 : 10"
        >
          <template v-slot="{ isSticky }">
            <div
              class="transition-height duration-300 ease-in-out"
              :style="{
                height: isSticky
                  ? 'calc(100vh - 74px - 16px)'
                  : 'calc(100vh - 70px - 29px - 16px - 16px)',
              }"
            >
              <z-scrollbar class="ps-0">
                <div class="p-4">
                  <el-input
                    v-model="filterText"
                    class="w-60 mb-2"
                    placeholder="请输入部门名称"
                  />
                  <el-tree
                    ref="treeRef"
                    style="max-width: 600px"
                    class="filter-tree"
                    :expand-on-click-node="false"
                    :data="data"
                    :props="defaultProps"
                    default-expand-all
                    :filter-node-method="filterNode"
                    @node-click="handleNodeClick"
                  />
                </div>
              </z-scrollbar>
            </div>
          </template>
        </z-sticky>
      </div>

      <div class="flex-1 w-0">
        <div
          class="bg-card p-4"
          :style="{
            minHeight: 'calc(100vh - 70px - 29px - 16px - 16px - 32px)',
          }"
        >
          <z-table
            ref="tableRef"
            row-key="accountId"
            v-model:query="query"
            :columns="columns"
            :toolbar-rows="toolbarRows"
            :fetch-data="fetchTableData"
            show-toolbar
            selectable
            reserveSelection
          >
            <template #applicationStatus="scope">
              <component
                :is="OptionUtils.renderTag(scope.row.applicationStatus, 'status')"
              ></component>
            </template>

            <template #operation="{ row }">
              <el-popconfirm
                :title="t('message.confirmResetPassword')"
                placement="top-start"
                @confirm="onResetPassword(row)"
              >
                <template #reference>
                  <el-button
                    size="small"
                    type="warning"
                    link
                    >{{ t('common.resetPassword') }}</el-button
                  >
                </template>
              </el-popconfirm>
              <el-button
                size="small"
                type="primary"
                link
                @click="onEdit(row)"
              >
                {{ t('common.edit') }}
              </el-button>
              <el-button
                size="small"
                type="success"
                link
                @click="onAssignRoles(row)"
              >
                分配角色
              </el-button>

              <el-popconfirm
                :title="t('message.confirmDelete')"
                placement="top-start"
                @confirm="onDelete(row)"
              >
                <template #reference>
                  <el-button
                    size="small"
                    type="danger"
                    link
                    >{{ t('common.delete') }}</el-button
                  >
                </template>
              </el-popconfirm>
            </template>
          </z-table>
        </div>
      </div>
    </div>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <UserForm :model="dialog.model.value" />
    </z-dialog-form>

    <UserRoleDrawer
      v-model="roleDrawerVisible"
      :user-id="selectedUser?.accountId"
      :user-info="selectedUser"
      @closed="onRoleDrawerClosed"
      @saved="onRolesSaved"
    />
  </z-page>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { ElButton, ElMessage, type TreeInstance } from 'element-plus'
import { useDialog } from '@/composables/useDialog'
import { useTypedI18n } from '@/i18n'
import { OptionUtils } from '@/utils/option'

import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'
import type {
  ZTableInstance,
  ZTableColumn,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types'
import UserApi from '@/api/user'
import ComApi from '@/api/common'
import type { ZStickyInstance } from '@/ui/components/ZSticky/types'
import { useSettingsStore } from '@/stores/settings'
import UserForm from './UserForm.vue'
import UserRoleDrawer from './UserRoleDrawer.vue'

interface Tree {
  [key: string]: any
}
const stickyRef = ref<ZStickyInstance>()
const filterText = ref('')
const treeRef = ref<TreeInstance>()

const settingsStore = useSettingsStore()
const isCollapse = computed(() => settingsStore.settings.layout.sidebarCollapse)
const isNavbarFixed = computed(() => settingsStore.settings.layout.navbarFixed)
const isVertical = computed(() => settingsStore.settings.layout.sidebarMode === 'vertical')

const defaultProps = {
  children: 'children',
  label: 'departmentName',
}

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.departmentName.includes(value)
}

// 部门树数据
const data = ref<Tree[]>([])

// 加载部门树数据
const loadDeptTree = async () => {
  try {
    const res = await ComApi.getDeptTree()
    if (res.data) {
      data.value = res.data
    }
  } catch (error) {
    console.error('加载部门树失败:', error)
    ElMessage.error('加载部门树失败')
  }
}

// 处理树节点点击事件
const handleNodeClick = (data: Tree) => {
  console.log('选中的部门:', data)
  if (data.dataId) {
    query.value.accountDepartment = data.dataId
    tableRef.value?.refresh()
  }
}
const { t } = useTypedI18n()

const tableRef = ref<ZTableInstance | null>(null)
const query = ref<any>({})

const dialog = useDialog({
  name: '用户',
  modeMap: { addChild: '分配角色' },
  onConfirm(mode, ctx) {
    if (mode === 'create' || mode === 'addChild') {
      ctx.form?.validate((isValid, values) => {
        if (isValid) {
          ctx.isSubmitting.value = true
          UserApi.addUser(values)
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.saveSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      })
    } else if (mode === 'edit') {
      ctx.form?.validate((isValid, values) => {
        if (isValid) {
          ctx.isSubmitting.value = true
          UserApi.updateUserById({
            ...values,
          })
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.updateSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      })
    }
  },
  onCancel() {
    console.log('取消')
  },
  onClose() {
    console.log('关闭')
  },
})

const columns: ZTableColumn[] = [
  { prop: 'accountNumber', label: '登录账号' },
  { prop: 'accountName', label: '姓名' },
  { prop: 'accountDepartmentName', label: '所在部门' },
  { prop: 'accountPhone', label: '手机号' },
  {
    prop: 'accountStatus',
    label: '状态',
    formatter(row, column, cellValue) {
      return OptionUtils.renderTag(cellValue, 'status')!
    },
  },
  { prop: 'createTime', label: '创建时间', width: 180 },
  { prop: 'operation', label: '操作', align: 'center', width: 240, fixed: 'right' },
]

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          noMargin: true,
          tools: [
            {
              key: 'accountName',
              type: 'input',
              label: '姓名',
              placeholder: '请输入姓名',
              width: 200,
            },
            {
              key: 'accountPhone',
              type: 'input',
              label: '手机号',
              placeholder: '请输入手机号',
              width: 200,
            },
            {
              key: 'accountNumber',
              type: 'input',
              label: '登录账号',
              placeholder: '请输入登录账号',
              width: 200,
            },
            {
              key: 'accountStatus',
              type: 'select',
              label: '状态',
              placeholder: '请选择状态',
              options: OptionUtils.getOptions('status'),
              width: 200,
            },
            ZTableToolbarFactory.createSearch(),
            ZTableToolbarFactory.createReset(),
          ],
        },
      },
    ],
  },
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create'),
      }),
      ZTableToolbarFactory.createExport({
        onClick: (ctx) => {
          UserApi.export({
            ...query.value,
            ids: ctx.selectedRows.map((row: any) => row.accountId),
          })
        },
      }),
    ],
  },
]

const fetchTableData = async (params: ZTableParams) => {
  return UserApi.getUserPagination(params).then((res) => res.data)
}
const onDelete = (row: any) => {
  UserApi.deleteUserById(row.accountId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const onEdit = (row: any) => {
  dialog.open('edit', row)
}

const onResetPassword = (row: any) => {
  UserApi.resetPassword(row.accountId).then(() => {
    ElMessage.success(t('message.resetPasswordSuccess'))
  })
}

const roleDrawerVisible = ref(false)
const selectedUser = ref<any>(null)

const onAssignRoles = (row: any) => {
  selectedUser.value = row
  roleDrawerVisible.value = true
}

const onRoleDrawerClosed = () => {
  selectedUser.value = null
}

const onRolesSaved = () => {
  ElMessage.success('角色分配成功')
  tableRef.value?.refresh()
}

// 在组件挂载时加载部门树
onMounted(() => {
  loadDeptTree()
})

watch(
  () => settingsStore.settings.layout.sidebarCollapse,
  () => {
    setTimeout(() => {
      stickyRef.value?.update()
    }, 350)
  },
)

watch(
  () => settingsStore.settings.layout.sidebarMode,
  () => {
    setTimeout(() => {
      stickyRef.value?.update()
    }, 0)
  },
)

// 可以添加导航守卫来调试路由问题
import { onBeforeRouteUpdate } from 'vue-router'

onBeforeRouteUpdate((to, from) => {
  console.log('Route updated:', { to, from })
})

// 在组件卸载时清除自定义渲染
onUnmounted(() => {
  // 清理工作（如果需要）
})
</script>
