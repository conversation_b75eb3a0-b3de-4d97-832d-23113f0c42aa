<template>
  <z-form
    ref="formRef"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
    :fetch-data="fetchData"
  >
  </z-form>
</template>

<script setup lang="ts">
import ComApi from '@/api/common'
import DictApI from '@/api/dict'
import RoleApi from '@/api/role'
import UserApi from '@/api/user'
import { defaultPassword } from '@/config/default-settings'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { Status } from '@/utils/enums'
import { OptionUtils } from '@/utils/option'
import { isAlphanumericWithLength, isChineseWithLength, RegexValidator } from '@/utils/regex'
import { reactive, ref } from 'vue'

const { onOpen } = useDialogFormContext()
const props = defineProps<{ model: Record<string, any> | null }>()

const initValue = {
  accountPassword: defaultPassword,
  accountStatus: Status.Enabled,
}

const form = reactive(initValue)
const formRef = ref<ZFormInstance>()

const fields = ref<ZFormField[]>([
  {
    name: 'accountId',
    show: false,
    type: 'input',
  },
  {
    name: 'accountNumber',
    label: '登录账号',
    type: 'input',
    rules: [
      {
        required: true,
        message: '请输入登录账号',
        trigger: 'blur',
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!isAlphanumericWithLength(value, 6, 20)) {
            callback(new Error('请输入6-20位字母数字'))
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入登录账号',
      minlength: 6,
      maxlength: 20,
    },
  },
  {
    name: 'accountPassword',
    label: '密码',
    type: 'input',
    props: {
      type: 'password',
      placeholder: '请输入密码',
      showPassword: true,
    },
    show: ({ model }) => {
      return !model.accountId
    },
    rules: [
      {
        pattern:
          /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[`~!@#$%^&*()+=|{}':; ',.<>?~！@#￥%……&*()——+|{}【】‘；：”“’。， 、？])[\da-zA-Z`~!@#$%^&*()+=|{}': ; ',.<>?~！@#￥%……&*()——+|{}【】‘；：”“’。， 、？]{8,}$/,
        message: '密码必须包含字母、数字和特殊字符，且长度至少为8位',
        trigger: 'blur',
      },
    ],
  },
  {
    name: 'accountName',
    label: '姓名',
    type: 'input',
    rules: [
      { required: true, message: '请输入姓名', trigger: 'blur' },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!isChineseWithLength(value, 2, 8)) {
            callback(new Error('请输入2-8位中文'))
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入姓名',
    },
  },
  {
    name: 'accountDepartment',
    label: '所在部门',
    type: 'tree-select',
    rules: [{ required: true, message: '请选择所在部门', trigger: 'change' }],
    props: {
      placeholder: '请选择',
      checkStrictly: true,
    },
    formatIn(value) {
      if (typeof value !== 'undefined') {
        return String(value)
      }
    },
    treeProps: {
      value: 'dataId',
      label: 'departmentName',
      children: 'children',
    },
    treeOptions() {
      return ComApi.getDeptTree().then((res) => res.data)
    },
  },
  {
    name: 'accountDepartmentInfo',
    type: 'input',
    show: false,
    dependencies: {
      fields: ['accountDepartment'],
      handler({ dependencyValues, updateModel }) {
        updateModel([dependencyValues.accountDepartment])
      },
    },
  },

  {
    name: 'accountDepartmentLevelCode',
    type: 'input',
    show: false,
    dependencies: {
      fields: ['accountDepartment'],
      async handler({ dependencyValues, updateModel, form }) {
        if (!dependencyValues.accountDepartment) {
          updateModel('')
          return
        }

        await form.refreshFieldOptions('accountDepartment')
        const nodes = form.getFieldOptions('accountDepartment')

        const findNodeByDataId = (nodes: any[], dataId: string): any | null => {
          for (const node of nodes) {
            if (String(node.dataId) === String(dataId)) {
              return node
            }

            if (node.children && node.children.length > 0) {
              const found = findNodeByDataId(node.children, dataId)
              if (found) {
                return found
              }
            }
          }

          return null
        }
        const targetNode = findNodeByDataId(nodes, dependencyValues.accountDepartment)

        if (targetNode) {
          updateModel(targetNode.levelCode || '')
        } else {
          updateModel('')
        }
      },
    },
  },

  {
    name: 'accountPost',
    label: '职务',
    type: 'select',
    props: {
      placeholder: '请选择',
      valueKey: 'dictionaryCode',
      labelKey: 'dictionaryName',
    },
    options() {
      return DictApI.getDictItemsByCode('POST').then((res) => res.data)
    },
  },
  {
    name: 'accountPhone',
    label: '手机号码',
    type: 'input',
    rules: [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      {
        pattern: RegexValidator.getPattern('mobile'),
        message: '请输入有效的手机号码',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入手机号码',
    },
  },
  {
    name: 'accountStatus',
    label: '状态',
    type: 'radio',
    options: OptionUtils.getOptions('status'),
  },
  {
    name: 'authorityList',
    label: '角色',
    type: 'select',
    options: () => RoleApi.getUserRolesAll().then((res) => res.data),
    valueKey: 'roleId',
    labelKey: 'roleName',
    formatOut(value) {
      return value?.map((it: any) => ({
        authorityResourcesList: [it],
        authorityResourcesType: 'ROLE',
      }))
    },
    formatIn(value) {
      if (typeof value?.[0] === 'number') {
        return value
      }
      return value?.flatMap((it: any) => it.authorityResourcesList).map(Number)
    },
    rules: [{ required: true, message: '请选择角色', trigger: 'change' }],
    props: {
      placeholder: '请选择',
      multiple: true,
    },
  },
  // {
  //   name: 'accountSynchronizeUser',
  //   label: '是否移动端账号',
  //   type: 'radio',
  //   options: OptionUtils.getOptions('yesNo', 'boolean'),
  // },
  {
    name: 'remark',
    label: '备注',
    type: 'input',
    props: {
      placeholder: '请输入备注',
      type: 'textarea',
      maxlength: 120,
      autosize: { minRows: 2, maxRows: 4 },
    },
  },
])

const fetchData = () => {
  return UserApi.getUserById(props.model?.accountId).then((res) => res.data)
}

onOpen(() => {
  if (props.model?.accountId) {
    formRef.value?.refetch()
  }
})
</script>
