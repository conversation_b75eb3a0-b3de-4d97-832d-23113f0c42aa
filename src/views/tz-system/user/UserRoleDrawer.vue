<template>
  <el-drawer
    v-model="visible"
    :title="title"
    :size="size"
    :destroy-on-close="true"
    @closed="onClosed"
  >
    <div class="user-role-drawer">
      <div class="flex pb-4 gap-x-2 gap-y-1 flex-wrap">
        <el-tag
          v-for="tag in selectedRoles"
          :key="tag.roleId"
          closable
          @close="handleTagClose(tag)"
        >
          {{ tag.roleName }}
        </el-tag>
      </div>
      <z-table
        ref="tableRef"
        v-model:query="query"
        :fetch-data="fetchTableData"
        :columns="columns"
        row-key="roleId"
        selectable
        @select="handleSelect"
        @select-all="handleSelectAll"
      >
      </z-table>

      <div class="user-role-drawer__footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          >保存</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElButton } from 'element-plus'
import type { ZTableInstance, ZTableColumn, ZTableParams } from '@/ui/components/ZTable/types'
import UserApi from '@/api/user'
import RoleApi from '@/api/role'

const props = defineProps<{
  modelValue: boolean
  userId?: number
  userInfo?: {
    accountName: string
    accountNumber: string
    [key: string]: any
  }
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'closed'): void
  (e: 'saved'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const title = computed(() => `分配角色 - ${props.userInfo?.accountName || ''}`)
const size = ref('60%')
const tableRef = ref<ZTableInstance | null>(null)
const query = ref({})
const saving = ref(false)

const selectedRoles = ref<any[]>([])
const user = ref<any>({})

const columns: ZTableColumn[] = [
  { prop: 'roleName', label: '角色名称', minWidth: 120 },
  { prop: 'roleCode', label: '角色编码', minWidth: 120 },

  { prop: 'remark', label: '描述', minWidth: 200, showOverflowTooltip: true },
]

const handleTagClose = (tag: any) => {
  console.log(`handleTagClose`, tag)
  selectedRoles.value = selectedRoles.value.filter((item) => item.roleId !== tag.roleId)
  setSelectedRows()
}

const getUserRoles = async () => {
  if (!props.userId) return

  try {
    const response = await UserApi.getUserById(props.userId)
    if (response.data) {
      user.value = response.data
      RoleApi.getRolesByIds(
        (response.data.authorityList || []).flatMap((role: any) => role.authorityResourcesList),
      ).then((res) => {
        console.log(`getRolesByIds`, res.data)
        selectedRoles.value = res.data ?? []
        setSelectedRows()
      })
    }
  } catch (error) {
    console.error('获取用户角色失败:', error)
  }
}

const setSelectedRows = async () => {
  if (!tableRef.value) return

  try {
    const tableData = await tableRef.value.getTableDataAsync({
      waitForLoading: true,
      timeout: 5000,
    })

    tableData.forEach((row: any) => {
      if (selectedRoles.value.find((item) => item.roleId === row.roleId)) {
        tableRef.value?.toggleRowSelection(row, true)
      } else {
        tableRef.value?.toggleRowSelection(row, false)
      }
    })
  } catch (error) {
    console.error('设置选中状态失败:', error)
  }
}

const fetchTableData = async (params: ZTableParams) => {
  try {
    const response = await RoleApi.getRolesPage(params)

    setTimeout(() => {
      setSelectedRows()
    }, 0)

    return response.data
  } catch (error) {
    console.error('获取角色列表失败:', error)
    return { data: [], total: 0 }
  }
}

const handleSelect = (selection: any[], row: any) => {
  const isSelected = selection.find((item) => item.roleId === row.roleId)
  if (!isSelected) {
    selectedRoles.value = selectedRoles.value.filter((item) => item.roleId !== row.roleId)
  } else {
    selectedRoles.value.push(row)
  }
}

const handleSelectAll = (selection: any[]) => {
  if (!selection.length) {
    tableRef.value
      ?.getTableDataAsync({
        waitForLoading: true,
        timeout: 5000,
      })
      .then((data) => {
        selectedRoles.value = selectedRoles.value.filter(
          (item) => !data.find((row: any) => row.roleId === item.roleId),
        )
      })
  } else {
    selection.forEach((row: any) => {
      if (!selectedRoles.value.find((item) => item.roleId === row.roleId)) {
        selectedRoles.value.push(row)
      }
    })
  }
}

const handleSave = async () => {
  if (!props.userId) return

  saving.value = true
  try {
    const data = {
      ...user.value,
      authorityList: [
        {
          authorityResourcesList: selectedRoles.value.map((item: any) => item.roleId),
          authorityResourcesType: 'ROLE',
        },
      ],
    }

    await UserApi.updateUserById(data)
    emit('saved')
    visible.value = false
  } catch (error) {
    console.error('角色分配失败:', error)
  } finally {
    saving.value = false
  }
}

const onClosed = () => {
  emit('closed')
  selectedRoles.value = []
}

watch(
  () => props.userId,
  (newVal) => {
    if (newVal && visible.value) {
      getUserRoles()
      tableRef.value?.refresh(true)
    }
  },
)

onMounted(() => {
  if (props.userId && visible.value) {
    getUserRoles()
    tableRef.value?.refresh(true)
  }
})
</script>

<style lang="scss" scoped>
.user-role-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;

  &__header {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .user-info {
      display: flex;
      gap: 24px;

      &__item {
        .label {
          font-weight: bold;
          margin-right: 4px;
        }
      }
    }
  }

  &__footer {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  :deep(.el-table) {
    flex: 1;
  }
}
</style>
