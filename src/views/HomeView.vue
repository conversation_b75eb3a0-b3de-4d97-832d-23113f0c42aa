<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import type { SystemApp } from '@/interfaces'
import { cacheService } from '@/utils/cache.ts'
import { SCREEN_MENU_ID_KEY } from '@/views/Screen/utils/conts.ts'
import { CaretBottom, SwitchButton } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTypedI18n } from '@/i18n'
import { computed } from 'vue'
import c from 'lodash/template'
import { useSettingsStore } from '@/stores/settings.ts'

const { t } = useTypedI18n()
const router = useRouter()
const settingsStore = useSettingsStore()
const userStore = useUserStore()

const appTitle = import.meta.env.VITE_APP_TITLE

const boxStyle = {
  background: `url(${new URL('@/assets/imgs/home/<USER>', import.meta.url).href}) 0% 0% / 100% 100% no-repeat`
}
const wrapperStyle = {
  background: 'transparent'
}

const welcome = computed(() => {
  const template = settingsStore.settings.user.welcome
  const accountName = userStore.account?.accountName
  try {
    return c(template)({ accountName })
  } catch {
    return accountName
  }
})

// 根据下标获取应用数据
const getAppData = (index: number): SystemApp | null => {
  if (!userStore.applications?.length) return null
  return userStore.applications[index] || null
}

const handleEnter = (index: number) => {
  const app: SystemApp | null = getAppData(index)
  if (!app) return ElMessage.warning('请选择系统')
  cacheService.remove(SCREEN_MENU_ID_KEY, { strategy: 'session' })
  router.push(app.homePage)
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      t('message.confirmLogout'),
      t('common.tips'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )

    await userStore.logout()
    ElMessage.success(t('message.logoutSuccess'))
    router.replace('/login')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(t('message.logoutFailed'))
    }
  }
}
</script>

<template>
  <z-screen-container :loading="false" :box-style="boxStyle" :wrapperStyle="wrapperStyle">
    <div class="w-full h-full overflow-hidden px-30 py-10 box-border">
      <div class="flex justify-between h-[120px]">
        <div class="flex items-center">
          <div class="flex items-center">
            <img src="@/assets/logo.png" alt="logo" class="w-[78px] h-[78px] mr-[10px]" />
            <span class="c-white text-[40px] youshe-bold">{{ appTitle }}</span>
          </div>
        </div>
        <div class="flex items-center">
          <el-dropdown trigger="click" placement="bottom-end">
            <div class="flex items-center cursor-pointer">
              <img src="@/assets/imgs/layout/def-avatar.png" alt="" class="w-[40px] h-[40px] rounded-full" />
              <div class="flex flex-col mx-[15px]">
                <span class="text-lg c-white">{{ welcome }}</span>
<!--                <span class="mt-1 c-white c-op-60">部门名称</span>-->
              </div>
              <el-icon :size="20" color="#fff"><CaretBottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleLogout"><el-icon><SwitchButton /></el-icon>{{ t('common.logout') }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="flex items-center justify-center px-[20x] pb-[20px] mt-8">
        <div class="flex flex-col mr-[10px] w-[70%]">
          <div class="flex">
            <div class="flex-1 h-[404px] flex flex-col justify-center items-center mr-[10px] bg-[url(@/assets/imgs/home/<USER>" @click="handleEnter(0)">
              <h1 class="text-[30px] c-white leading-[42px] tracking-[2px] text-right">{{ getAppData(0)?.name }}</h1>
              <img src="@/assets/imgs/home/<USER>" alt="" class="w-[131px] h-[131px] mt-[30px]">
            </div>
            <div class="flex-1 h-[404px] flex flex-col justify-center items-center bg-[url(@/assets/imgs/home/<USER>" @click="handleEnter(1)">
              <h1 class="text-[30px] c-white leading-[42px] tracking-[2px] text-right">{{ getAppData(1)?.name }}</h1>
              <img src="@/assets/imgs/home/<USER>" alt="" class="w-[131px] h-[131px] mt-[30px]">
            </div>
          </div>
          <div class="flex mt-[10px]">
            <div class="flex-1 h-[404px] flex flex-col justify-center items-center mr-[10px] bg-[url(@/assets/imgs/home/<USER>" @click="handleEnter(2)">
              <h1 class="text-[30px] c-white leading-[42px] tracking-[2px] text-right">{{ getAppData(2)?.name }}</h1>
              <img src="@/assets/imgs/home/<USER>" alt="" class="w-[131px] h-[131px] mt-[30px]">
            </div>
            <div class="flex-[1.5] h-[404px] flex flex-col justify-center items-center bg-[url(@/assets/imgs/home/<USER>" @click="handleEnter(3)">
              <h1 class="text-[30px] c-white leading-[42px] tracking-[2px] text-right">{{ getAppData(3)?.name }}</h1>
              <img src="@/assets/imgs/home/<USER>" alt="" class="w-[131px] h-[131px] mt-[30px]">
            </div>
          </div>
        </div>
        <div class="flex-1 h-[820px] flex flex-col items-center justify-center bg-[url(@/assets/imgs/home/<USER>" @click="handleEnter(4)">
          <h1 class="text-[30px] c-white leading-[42px] tracking-[2px] text-right">{{ getAppData(4)?.name }}</h1>
          <img src="@/assets/imgs/home/<USER>" alt="" class="w-[131px] h-[131px] mt-[150px]">
        </div>
      </div>
    </div>
  </z-screen-container>
</template>

<style lang="scss" scoped>

</style>
