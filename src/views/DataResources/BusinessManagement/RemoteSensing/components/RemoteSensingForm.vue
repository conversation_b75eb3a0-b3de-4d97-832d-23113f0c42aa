<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
    <template #item-beforePicPath="{ validate, value, updateValue }">
      <z-upload
        :allowed-types="[FileType.IMAGE]"
        :model-value="value"
        :limit="1"
        :max-size="5"
        :show-file-list="false"
        tip="支持格式：jpg/jpeg/png ，最多上传1张，单张图片大小不超过5MB"
        @success="
            (r) => handleUploadSuccess(r, { validate, updateValue })
          "
      >
      </z-upload>
    </template>
    <template #item-endPicPath="{ validate, value, updateValue }">
      <z-upload
        :allowed-types="[FileType.IMAGE]"
        :model-value="value"
        :limit="1"
        :max-size="5"
        :show-file-list="false"
        tip="支持格式：jpg/jpeg/png ，最多上传1张，单张图片大小不超过5MB"
        @success="
            (r) => handleUploadSuccess(r, { validate, updateValue })
          "
      >
      </z-upload>
    </template>
  </z-form>
</template>

<script setup lang="ts">
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context.ts'
import type { ZFormField } from '@/ui/components/ZForm/types.ts'
import { reactive, ref } from 'vue'
import DictApI from '@/api/dict.ts'
import ComApi from '@/api/common.ts'
import { FileType } from '@/ui/components/ZUpload/constants.ts'

const props = defineProps<{ model: Record<string, any> | null }>()

const { onOpen } = useDialogFormContext()

const form = reactive({
  status: 0
})

const fields = ref<ZFormField[]>([
  {
    type: 'custom',
    name: 'id',
    show: false,
  },
  {
    type: 'custom',
    name: 'status',
    show: false,
  },
  {
    type: 'select',
    name: 'dataType',
    label: '问题类型',
    props: {
      placeholder: '请选择问题类型',
    },
    rules: [{ required: true, message: '请选择问题类型', trigger: 'change' }],
    options: () => DictApI.getDictItemsByCode('questiontypes').then((res) => res.data),
    valueKey: 'dictionaryCode',
    labelKey: 'dictionaryName',
  },
  {
    name: 'geom',
    label: 'geo位置',
    type: 'input',
    rules: [{ required: true, message: '请输入geo位置', trigger: 'blur' }],
    props: {
      type: 'textarea',
      placeholder: '请输入geo位置',
      rows: 3,
    },
  },
  {
    name: 'area',
    label: '图斑面积',
    type: 'input',
    rules: [{ required: true, message: '请输入图斑面积', trigger: 'blur' }],
    props: {
      placeholder: '请输入图斑面积',
    },
  },
  {
    type: 'select',
    name: 'regionCode',
    label: '所属行政区县',
    props: {
      placeholder: '请选择所属行政区县',
    },
    rules: [{ required: true, message: '请选择所属行政区县', trigger: 'change' }],
    options: () => ComApi.getAreaList().then((res) => res.data),
    valueKey: 'areaCode',
    labelKey: 'areaName',
  },
  {
    name: 'beforePicTime',
    label: '前时相时间',
    type: 'date',
    fieldClass: 'w-full!',
    props: {
      placeholder: '请选择前时相时间',
      format: 'YYYY-MM-DD',
      valueFormat: "YYYY-MM-DD"
    },
    rules: [{ required: true, message: '请选择前时相时间', trigger: 'change' }],
  },
  {
    type: 'custom',
    name: 'beforePicPath',
    label: '前时相影像',
    rules: [{ required: true, message: '请上传前时相影像', trigger: 'change' }],
  },
  {
    name: 'endPicTime',
    label: '后时相时间',
    type: 'date',
    fieldClass: 'w-full!',
    props: {
      placeholder: '请选择后时相时间',
      format: 'YYYY-MM-DD',
      valueFormat: "YYYY-MM-DD"
    },
    rules: [{ required: true, message: '请选择后时相时间', trigger: 'change' }],
  },
  {
    type: 'custom',
    name: 'endPicPath',
    label: '后时相影像',
    rules: [{ required: true, message: '请上传后时相影像', trigger: 'change' }],
  },
])

const handleUploadSuccess = (r: any, { validate, updateValue }: any) => {
  updateValue(r?.data?.absUrl)
  validate()
}

onOpen(() => {
  Object.assign(form, props.model ?? {})
})
</script>

<style scoped lang="scss">

</style>
