<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="80px"
  >
    <template #item-file="{ validate, updateValue }">
      <el-upload
        ref="uploadRef"
        action="#"
        :auto-upload="false"
        :limit="1"
        drag
        class="w-full"
        :on-exceed="handleExceed"
        :on-change="(file) => handleUploadSuccess(file, { validate, updateValue })"
      >
        <template #trigger>
          <el-icon class="z-upload__icon">
            <Upload />
          </el-icon>
          <div class="z-upload__text">将文件拖到此处，或 <em class="c-[#409eff]">点击上传</em></div>
        </template>
        <template #tip>
          <div class="el-upload__tip">
            支持格式：zip 文件大小不超过500MB
          </div>
        </template>
      </el-upload>
    </template>
  </z-form>
</template>

<script setup lang="ts">
import type { ZFormField } from '@/ui/components/ZForm/types.ts'
import { reactive, ref } from 'vue'
import { Upload } from '@element-plus/icons-vue'
import { genFileId, type UploadInstance, type UploadProps, type UploadRawFile } from 'element-plus'

const uploadRef = ref<UploadInstance>()

const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  uploadRef.value!.handleStart(file)
}

const form = reactive({})

const fields = ref<ZFormField[]>([
  {
    type: 'custom',
    name: 'file',
    label: '上传文件',
    rules: [{ required: true, message: '请上传上传文件', trigger: 'change' }],
  },
])

const handleUploadSuccess = (file: any, { validate, updateValue }: any) => {
  updateValue(file)
  validate()
}
</script>

<style scoped lang="scss">

</style>
