<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="60px"
  >
  </z-form>
</template>

<script setup lang="ts">
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context.ts'
import type { ZFormField } from '@/ui/components/ZForm/types.ts'
import { reactive, ref } from 'vue'

const props = defineProps<{ model: Record<string, any> | null }>()

const { onOpen } = useDialogFormContext()

const form = reactive({
  status: 1
})

const fields = ref<ZFormField[]>([
  {
    type: 'custom',
    name: 'id',
    show: false,
  },
  {
    type: 'custom',
    name: 'status',
    show: false,
  },
  {
    name: 'analysisText',
    label: '原因',
    type: 'input',
    rules: [{ required: true, message: '请输入原因', trigger: 'blur' }],
    props: {
      type: 'textarea',
      placeholder: '请输入原因',
      autosize: { minRows: 2, maxRows: 4 },
    },
  },
])

onOpen(() => {
  Object.assign(form, props.model ?? {})
})
</script>

<style scoped lang="scss">

</style>
