<template>
  <div class="timeline-player">
    <!-- 时间轴进度条 -->
    <div
      class="timeline-wrapper"
      ref="timelineRef"
      @mousedown="handleMouseDown"
      @mouseenter="isHovering = true"
      @mouseleave="isHovering = false"
    >
      <div
        class="timeline-track"
        @click="handleTimelineClick"
      >
        <div
          class="timeline-progress"
          :style="{ width: progressPercentage + '%' }"
        ></div>
        <!-- 滑块 -->
        <div
          class="slider-button"
          :class="{
            'hover': isHovering || isDragging,
            'dragging': isDragging
          }"
          :style="{ left: progressPercentage + '%' }"
          @mousedown="startDrag"
        ></div>

        <!-- 当前时间指示器 -->
        <div
          class="current-time-indicator"
          :style="{ left: progressPercentage + '%' }"
        ></div>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div class="controls">
      <el-button
        @click="togglePlay"
        :icon="isPlaying ? 'VideoPause' : 'VideoPlay'"
      >
        {{ isPlaying ? '暂停' : '播放' }}
      </el-button>
      <span class="time-display">
        {{ formatTimeDisplay(currentTime) }} / {{ formatTimeDisplay(totalDuration) }}
      </span>

      <el-select
        v-model="playbackSpeed"
        size="small"
        style="width: 100px; margin-left: 10px;"
      >
        <el-option label="0.5x" :value="0.5" />
        <el-option label="1.0x" :value="1" />
        <el-option label="1.5x" :value="1.5" />
        <el-option label="2.0x" :value="2" />
      </el-select>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { dayjs } from 'element-plus'

interface TimeMarker {
  time: Date
  position: number
}

const props = defineProps<{
  dataRange: any
}>()

// 时间刻度标记
const timeMarkers = ref<TimeMarker[]>([])

// 时间轴相关状态
const timelineRef = ref<HTMLElement | null>(null)
const isPlaying = ref(false)
const isHovering = ref(false)
const isDragging = ref(false)
const currentTime = ref<Date | number>()
const animationFrameId = ref<number | null>(null)
const playbackSpeed = ref(1) // 播放速度倍数

// 计算总时长（毫秒）
const totalDuration = computed(() => {
  if (!props.dataRange) return 0
  return dayjs(props.dataRange[1]).diff(dayjs(props.dataRange[0]))
})

// 计算进度百分比
const progressPercentage = computed(() => {
  if (!props.dataRange) return 0
  const elapsed = dayjs(currentTime.value).diff(dayjs(props.dataRange[0]))
  return (elapsed / totalDuration.value) * 100
})

// 初始化时间轴
const initTimeline = () => {
  if (!props.dataRange) return

  // 重置当前时间
  currentTime.value = props.dataRange[0]
  isPlaying.value = false
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
    animationFrameId.value = null
  }

  // 生成时间刻度
  generateTimeMarkers()
}

// 生成时间刻度
const generateTimeMarkers = () => {
  timeMarkers.value = []
  if (!props.dataRange) return

  const [start, end] = props.dataRange
  const duration = dayjs(end).diff(dayjs(start))

  // 根据总时长决定刻度间隔
  let interval: number
  let timeFormat: string

  if (duration <= 3600000) {
    // 1小时以内
    interval = 5 * 60000 // 5分钟
    timeFormat = 'HH:mm'
  } else if (duration <= 86400000) {
    // 1天以内
    interval = 30 * 60000 // 30分钟
    timeFormat = 'HH:mm'
  } else {
    interval = 3600000 // 1小时
    timeFormat = 'DD HH:mm'
  }

  // 生成刻度
  let current = dayjs(start)
  while (current.isBefore(end)) {
    const position = (current.diff(dayjs(start)) / duration) * 100
    timeMarkers.value.push({
      time: current.toDate(),
      position,
    })
    current = current.add(interval, 'millisecond')
  }

  // 确保结束时间有一个刻度
  timeMarkers.value.push({
    time: end,
    position: 100,
  })
}

// 播放/暂停切换
const togglePlay = () => {
  if (!props.dataRange) return

  isPlaying.value = !isPlaying.value

  if (isPlaying.value) {
    const startTime = dayjs(props.dataRange[0])
    const endTime = dayjs(props.dataRange[1])
    const startTimestamp = Date.now()
    const startProgress = dayjs(currentTime.value).diff(startTime)

    const animate = () => {
      const elapsedRealTime = (Date.now() - startTimestamp) * playbackSpeed.value
      const newProgress = startProgress + elapsedRealTime

      if (newProgress >= totalDuration.value) {
        currentTime.value = props.dataRange[1]
        isPlaying.value = false
        return
      }

      currentTime.value = startTime.add(newProgress, 'millisecond').toDate()
      animationFrameId.value = requestAnimationFrame(animate)
    }

    animationFrameId.value = requestAnimationFrame(animate)
  } else if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
    animationFrameId.value = null
  }
}

// 处理时间轴点击
const handleTimelineClick = (event: MouseEvent) => {
  if (!timelineRef.value || !props.dataRange || isDragging.value) return

  const rect = timelineRef.value.getBoundingClientRect()
  const clickPosition = Math.min(Math.max((event.clientX - rect.left) / rect.width, 0), 1)
  updateCurrentTime(clickPosition)

  // 如果正在播放，需要重新计算动画
  if (isPlaying.value) {
    togglePlay() // 先暂停
    togglePlay() // 再播放
  }
}

// 更新当前时间
const updateCurrentTime = (percentage: number) => {
  if (!props.dataRange) return

  const newTime = dayjs(props.dataRange[0])
    .add(totalDuration.value * percentage, 'millisecond')
    .toDate()

  currentTime.value = newTime
}

// 开始拖拽
const startDrag = (event: MouseEvent) => {
  if (!timelineRef.value || !props.dataRange) return

  event.preventDefault()
  event.stopPropagation()

  isDragging.value = true

  // 暂停播放
  const wasPlaying = isPlaying.value
  if (wasPlaying) {
    togglePlay()
  }

  const rect = timelineRef.value.getBoundingClientRect()
  const handleDrag = (moveEvent: MouseEvent) => {
    const position = Math.min(Math.max((moveEvent.clientX - rect.left) / rect.width, 0), 1)
    updateCurrentTime(position)
  }

  const handleDragEnd = () => {
    document.removeEventListener('mousemove', handleDrag)
    document.removeEventListener('mouseup', handleDragEnd)
    isDragging.value = false

    // 如果之前是播放状态，恢复播放
    if (wasPlaying) {
      togglePlay()
    }
  }

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', handleDragEnd)
}

// 处理鼠标按下事件（点击轨道时也触发）
const handleMouseDown = (event: MouseEvent) => {
  if (!timelineRef.value || !props.dataRange) return

  const rect = timelineRef.value.getBoundingClientRect()
  const clickPosition = Math.min(Math.max((event.clientX - rect.left) / rect.width, 0), 1)
  updateCurrentTime(clickPosition)

  // 暂停播放
  const wasPlaying = isPlaying.value
  if (wasPlaying) {
    togglePlay()
  }

  const handleDrag = (moveEvent: MouseEvent) => {
    const position = Math.min(Math.max((moveEvent.clientX - rect.left) / rect.width, 0), 1)
    updateCurrentTime(position)
  }

  const handleDragEnd = () => {
    document.removeEventListener('mousemove', handleDrag)
    document.removeEventListener('mouseup', handleDragEnd)
    isDragging.value = false

    // 如果之前是播放状态，恢复播放
    if (wasPlaying) {
      togglePlay()
    }
  }

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', handleDragEnd)
}

// 处理刻度点击
const handleMarkerClick = (time: Date) => {
  currentTime.value = time

  // 如果正在播放，需要重新计算动画
  if (isPlaying.value) {
    togglePlay() // 先暂停
    togglePlay() // 再播放
  }
}

function formatDuration(ms: number): string {
  const totalSeconds = Math.floor(ms / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    seconds.toString().padStart(2, '0'),
  ].join(':')
}

// 格式化时间显示
const formatTimeDisplay = (time: Date | number | undefined) => {
  if (typeof time === 'number') {
    return formatDuration(time)
  }
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 格式化刻度时间
const formatMarkerTime = (time: Date) => {
  if (!props.dataRange) return dayjs(time).format('HH:mm')

  const duration = dayjs(props.dataRange[1]).diff(dayjs(props.dataRange[0]))
  return duration <= 86400000 ? dayjs(time).format('HH:mm') : dayjs(time).format('MM-DD HH:mm')
}

watch(
  () => props.dataRange,
  (newRange) => {
    currentTime.value = newRange[0]
    initTimeline()
  },
  {
    deep: true,
    immediate: true,
  }
)

// 组件挂载时初始化
onMounted(() => {
  // initTimeline()
})

// 组件卸载时清理
onUnmounted(() => {
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
  }
})
</script>

<style scoped>
.timeline-player {
  width: 600px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: 0;
  left: 0;
}

.timeline-wrapper {
  width: 100%;
  margin-bottom: 20px;
  padding: 10px 0;
  cursor: pointer;
}

.timeline-track {
  position: relative;
  height: 10px;
  background: #ebeef5;
  border-radius: 5px;
  cursor: pointer;
}

.timeline-progress {
  position: absolute;
  height: 100%;
  background: #409eff;
  border-radius: 5px;
  transition: width 0.1s linear;
  user-select: none;
}

.time-marker {
  position: absolute;
  top: -10px;
  width: 2px;
  height: 16px;
  background: #c0c4cc;
  transform: translateX(-50%);
  cursor: pointer;
}

.time-marker:hover {
  background: #409eff;
  height: 20px;
}

.time-marker .marker-label {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

.current-time-indicator {
  position: absolute;
  top: 10px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 10px solid #409eff;
  transform: translateX(-50%);
  z-index: 2;
}

.slider-button {
  position: absolute;
  top: -8px;
  width: 16px;
  height: 16px;
  background: #fff;
  border: 2px solid #409eff;
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 3;
  cursor: grab;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s, box-shadow 0.2s;
  display: none;
}

.slider-button.hover {
  transform: translateX(-50%) scale(1.2);
}

.slider-button.dragging {
  cursor: grabbing;
  transform: translateX(-50%) scale(1.2);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.time-display {
  font-size: 14px;
  color: #606266;
}
</style>
