export interface DialogProps {
  /**
   * 对话框是否可见
   */
  visible: boolean

  /**
   * 收藏点位id
   */
  monitorDevicePosition?: string

  /**
   * 对话框标题
   */
  monitorFavoritesId?: number

  /**
   * 对话框宽度
   * @default '50%'
   */
  monitorFavoritesName?: string
}

export interface DialogEmits {
  /**
   * 更新对话框可见状态
   */
  (e: 'update:visible', value: boolean): void

  /**
   * 点击确认按钮时触发
   */
  (e: 'confirm'): void

  /**
   * 点击取消按钮时触发
   */
  (e: 'cancel'): void

  /**
   * 对话框关闭时触发
   */
  (e: 'close'): void
}
