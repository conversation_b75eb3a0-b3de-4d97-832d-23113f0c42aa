<template>
  <div
    class="outer-circle"
    ref="outerCircleRef"
  >
    <div class="degree-mark">
      <div
        v-for="angle in angles"
        :key="angle.degrees"
        class="degree-text"
        :style="getDegreeStyle(angle)"
        @mousedown.stop="handleClick(angle.degrees, 0)"
        @mouseup.stop="handleClick(angle.degrees, 1)"
        v-html="getIconTemplate(angle.degrees)"
      ></div>
    </div>

    <div class="inner-circle" @click="setCamera">
      <img
        :src="direction_reset"
        alt="icon"
        width="20"
        height="20"
      ></img>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject, type Ref } from 'vue'
import type { MonitoringGrid, ControlCommand } from '../types'
import dataResApi from '@/api/dataResources'
import direction_reset from '@/assets/imgs/screen/direction_reset.png'

const props = defineProps<{
  speed: number
}>()
const selectedGrid = inject<Ref<MonitoringGrid | null>>('SELECT_GRID')

const outerCircleRef = ref<HTMLElement | null>(null)
const radius = 80
const center = 110
const angles = Array.from({ length: 8 }, (_, i) => ({ degrees: i * 45 }))

// SVG 图标模版（可替换为图片）
const getIconTemplate = (degrees: number) => {
  const icons: Record<string, { default: string }> = import.meta.glob(
    '@/assets/imgs/screen/direction_*.png',
    { eager: true }
  );
  const path = `/src/assets/imgs/screen/direction_${degrees}.png`
  const icon = icons[path]?.default
  if (!icon) {
    console.error(`找不到对应角度的图片: ${degrees}`)
    return ''
  }
  return `<img src="${icon}" width="16" height="16" icon>`
}

// 根据角度计算样式
const getDegreeStyle = (angle: { degrees: number }) => {
  const radian = ((angle.degrees - 90) * Math.PI) / 180
  const x = center + radius * Math.cos(radian)
  const y = center + radius * Math.sin(radian)

  return {
    left: `${x}px`,
    top: `${y}px`,
    transform: `translate(-50%, -50%)`,
  }
}

// 回到预置点
const setCamera = async () => {
  controlAngle('GOTO_PRESET', 0)
}

// 控制摄像头角度
const controlAngle = async (code: ControlCommand, action: number) => {
  const data = {
    action: action,
    cameraIndexCode: JSON.parse(selectedGrid?.value?.point?.monitorDeviceInfo || '')?.indexCode,
    command: code,
    speed: props.speed
  }
  await dataResApi.controlCamera(data)
}
const handleClick = (degrees: number, action: number) => {
  console.log(degrees, '///////////////')
  switch (degrees) {
    case 0:
      controlAngle('UP', action);
      break
    case 45:
      controlAngle('RIGHT_UP', action);
      break
    case 90:
      controlAngle('RIGHT', action);
      break
    case 135:
      controlAngle('RIGHT_DOWN', action);
      break
    case 180:
      controlAngle('DOWN', action);
      break
    case 225:
      controlAngle('LEFT_DOWN', action);
      break
    case 270:
      controlAngle('LEFT', action);
      break
    case 315:
      controlAngle('LEFT_UP', action);
      break
  }
}
</script>

<style lang="scss" scoped>
.outer-circle {
  position: relative;
  width: 220px;
  height: 220px;
  border-radius: 50%;
  background-color: #8e8e8e;
  display: flex;
  justify-content: center;
  align-items: center;
}

.inner-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: #aaaaaa;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.degree-mark {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.degree-text {
  position: absolute;
  transform-origin: center center;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
