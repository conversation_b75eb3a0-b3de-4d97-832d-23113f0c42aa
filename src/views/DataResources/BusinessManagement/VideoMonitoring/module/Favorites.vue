<template>
  <el-dialog
    :title="dialogTitle"
    v-model="internalVisible"
    width="360"
    :before-close="handleBeforeClose"
    v-bind="$attrs"
    class="z-dialog"
  >
    <el-form
      :model="favoriteForm"
      label-width="auto"
    >
      <el-form-item :label="showSelectFooter ? '收藏夹' : ''">
        <el-select
          v-if="showSelectFooter"
          v-model="favoriteForm.monitorFavoritesId"
          placeholder="请选择"
        >
          <el-option
            v-for="item in favoriteData"
            :key="item.monitorFavoritesId"
            :label="item.monitorFavoritesName"
            :value="item.monitorFavoritesId"
          />
        </el-select>
        <el-input
          v-else
          v-model="favoriteForm.monitorFavoritesName"
          :maxlength="20"
          show-word-limit
          placeholder="请输入收藏夹名称"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div
        class="z-dialog__footer"
        v-if="showSelectFooter"
      >
        <el-button
          @click="handleCancel"
          class="z-dialog__button z-dialog__button--cancel"
        >
          新建收藏夹
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          class="z-dialog__button z-dialog__button--confirm"
        >
          确定
        </el-button>
      </div>
      <div
        class="z-dialog__footer"
        v-else
      >
        <el-button
          @click="handleAddCancel"
          class="z-dialog__button z-dialog__button--cancel"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleAddConfirm"
          class="z-dialog__button z-dialog__button--confirm"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import dataResApi from '@/api/dataResources'
import { ref, reactive, computed, watch } from 'vue'
import type { DialogProps, DialogEmits } from './types'

import { ElMessage } from 'element-plus'

const props = withDefaults(defineProps<DialogProps>(), {
  monitorDevicePosition: '',
})

const emit = defineEmits<DialogEmits>()

const showSelectFooter = ref<boolean>(true)

const favoriteData = ref<any[]>([])

const dialogTitle = ref<string>('选择收藏夹')

const internalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})
const favoriteForm = reactive({
  monitorFavoritesName: '',
  monitorFavoritesId: '',
})
const handleCancel = () => {
  showSelectFooter.value = false
}

const handleConfirm = async () => {
  if (!favoriteForm.monitorFavoritesId) {
    ElMessage.warning('请选择收藏夹')
    return
  }
  try {
    const res = await dataResApi.addMonitorFavorite({
      monitorFavoritesId: favoriteForm.monitorFavoritesId,
      monitorFavoritePosition: props.monitorDevicePosition,
    })
    ElMessage.success(res.message)
    emit('confirm')
  } catch (error) {
    console.log(error)
  }
}

const handleAddCancel = () => {
  showSelectFooter.value = true
}

const handleAddConfirm = async () => {
  if (!favoriteForm.monitorFavoritesName) {
    ElMessage.warning('请输入收藏夹名称')
    return
  }
  try {
    const res = await dataResApi.addMonitorFavorites({
      monitorFavoritesName: favoriteForm.monitorFavoritesName,
    })
    ElMessage.success(res.message)
    getFavoriteList()
    showSelectFooter.value = true
  } catch (error) {
    console.log(error)
  }
}

const handleBeforeClose = (done: () => void) => {
  if (showSelectFooter.value) {
    emit('close')
    done()
  } else {
    showSelectFooter.value = true
  }
}

const getFavoriteList = async () => {
  const data = await dataResApi
    .getMonitorFavoriteList({ ifShowPosition: false })
    .then((res) => res.data)
  favoriteData.value = data
}
watch(
  () => showSelectFooter.value,
  (newVal) => {
    if (newVal) {
      dialogTitle.value = '选择收藏夹'
    } else {
      dialogTitle.value = '新建收藏夹'
    }
  },
)

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      favoriteForm.monitorFavoritesId = ''
      favoriteForm.monitorFavoritesName = ''
      getFavoriteList()
    }
  },
)
</script>

<style lang="scss" scoped>
.z-dialog {
  // 对话框主体
  &__body {
    padding: 20px;
  }

  // 底部区域
  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
  }

  // 按钮基础样式
  &__button {
    min-width: 80px;

    // 取消按钮
    &--cancel {
      &:hover {
        background-color: var(--el-color-info-light-7);
      }
    }

    // 确认按钮
    &--confirm {
      &:hover {
        background-color: var(--el-color-primary-light-3);
      }
    }
  }

  // 响应式样式
  @media screen and (max-width: 768px) {
    &--mobile {
      width: 90% !important;

      .z-dialog__footer {
        flex-direction: column;
        gap: 8px;
      }

      .z-dialog__button {
        width: 100%;
      }
    }
  }
  .z-dialog__footer {
    margin: 0;
    padding: 0;
  }
}
</style>
