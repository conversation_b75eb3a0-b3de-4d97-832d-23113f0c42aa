<template>
  <div class="monitor-view">
    <!-- 显示监控画面 -->
    <video
      ref="videoElement"
      class="monitor-video"
      controls
      muted
      autoplay
    ></video>
    <!-- 错误信息展示 -->
    <p
      v-if="errorMessage"
      class="error-message"
    >
      {{ errorMessage }}
    </p>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import flvjs, { Player } from 'flv.js'

interface Props {
  previewUrl?: string
  isLive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  previewUrl: '',
  isLive: true,
})

const flvPlayer = ref<Player | null>(null)
const timer = ref(null)
const videoElement = ref<HTMLVideoElement | null>(null)
const errorMessage = ref('')

const destroyPlayer = () => {
  if (flvPlayer.value) {
    flvPlayer.value.pause()
    flvPlayer.value.unload()
    flvPlayer.value.detachMediaElement()
    flvPlayer.value.destroy()
    flvPlayer.value = null
    errorMessage.value = ''
  }
}
const init = () => {
  errorMessage.value = ''
  if (flvjs.isSupported()) {
    destroyPlayer()
    play()
  } else {
    errorMessage.value = '当前浏览器不支持 FLV.js 播放。'
  }
}
const play = () => {
  flvPlayer.value = flvjs.createPlayer(
    {
      type: 'flv',
      url: props.previewUrl,
      isLive: props.isLive, // 数据源是否为直播流
      hasAudio: false, // 数据源是否包含有音频
      hasVideo: true, // 数据源是否包含有视频
    },
    {
      enableWorker: false, // 不启用分离线程
      autoCleanupSourceBuffer: true, // 自动清除缓存
      enableStashBuffer: false, // 如果您需要实时（最小延迟）来进行实时流播放，则设置为false
      stashInitialSize: 128, // 减少首帧显示等待时长
      lazyLoad: false,
    },
  )
  flvPlayer.value.attachMediaElement(videoElement.value)
  flvPlayer.value.load()
  setTimeout(() => {
    flvPlayer.value.play()
  }, 300)

  // 监听播放错误事件
  flvPlayer.value.on(flvjs.Events.ERROR, (type: any, detail: any) => {
    errorMessage.value = '播放错误: 当前视频使用的编码格式不兼容'
  })
}
watch(
  () => props.previewUrl,
  async (newUrl) => {
    if (timer.value) {
      clearInterval(timer.value)
    }
    destroyPlayer()
    await nextTick()
    init()
  },
)
onMounted(() => {
  const dom = document.querySelector('.monitor-video')
  if (dom) {
    ;(dom as any).disablePictureInPicture = true
  }
  props.previewUrl && init()
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
  destroyPlayer()
})
</script>

<style scoped>
.monitor-view {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: #000000;
}

.monitor-video {
  width: 100%;
  height: 100%;
  background-color: #000000;
}

.error-message {
  color: red;
  font-size: 16px;
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  text-align: center;
}
</style>
