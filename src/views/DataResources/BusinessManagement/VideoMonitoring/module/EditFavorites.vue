<template>
  <el-dialog
    title="编辑收藏夹"
    v-model="internalVisible"
    width="360"
    :before-close="handleBeforeClose"
    v-bind="$attrs"
    class="z-dialog"
  >
    <el-form
      :model="favoriteForm"
      label-width="auto"
    >
      <el-form-item>
        <el-input
          v-model="favoriteForm.monitorFavoritesName"
          :maxlength="20"
          show-word-limit
          placeholder="请输入收藏夹名称"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="z-dialog__footer">
        <el-button
          @click="handleCancel"
          class="z-dialog__button z-dialog__button--cancel"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          class="z-dialog__button z-dialog__button--confirm"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import dataResApi from '@/api/dataResources'
import { reactive, computed, watch } from 'vue'
import type { DialogProps, DialogEmits } from './types'

import { ElMessage } from 'element-plus'

const props = withDefaults(defineProps<DialogProps>(), {
  monitorFavoritesName: '',
  monitorFavoritesId: 0
})

const emit = defineEmits<DialogEmits>()

const internalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})
const favoriteForm = reactive({
  monitorFavoritesName: props.monitorFavoritesName,
  monitorFavoritesId: '',
})
const handleCancel = () => {
  internalVisible.value = false
}

const handleConfirm = async () => {
  try {
    const res = await dataResApi.editMonitorFavorites({
      monitorFavoritesId: favoriteForm.monitorFavoritesId,
      monitorFavoritesName: favoriteForm.monitorFavoritesName,
    })
    ElMessage.success(res.message)
    emit('confirm')
  } catch (error) {
    console.log(error)
  }
}

const handleBeforeClose = (done: () => void) => {
  emit('close')
  done()
}

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      Object.assign(favoriteForm, props)
    }
  },
)
</script>

<style lang="scss" scoped>
.z-dialog {
  // 对话框主体
  &__body {
    padding: 20px;
  }

  // 底部区域
  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
  }

  // 按钮基础样式
  &__button {
    min-width: 80px;

    // 取消按钮
    &--cancel {
      &:hover {
        background-color: var(--el-color-info-light-7);
      }
    }

    // 确认按钮
    &--confirm {
      &:hover {
        background-color: var(--el-color-primary-light-3);
      }
    }
  }

  // 响应式样式
  @media screen and (max-width: 768px) {
    &--mobile {
      width: 90% !important;

      .z-dialog__footer {
        flex-direction: column;
        gap: 8px;
      }

      .z-dialog__button {
        width: 100%;
      }
    }
  }
  .z-dialog__footer {
    margin: 0;
    padding: 0;
  }
}
</style>
