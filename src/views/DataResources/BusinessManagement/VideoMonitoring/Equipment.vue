<template>
  <z-page>
    <z-table
      ref="tableRef"
      :auto-load="false"
      row-key="monitorDeviceId"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
      :pagination-mode="'server'"
    >
      <template #toolbar-item-synchronizer="scope">
        <el-button
          type="primary"
          plain
          @click="synchronizer"
        >
          同步设备
        </el-button>
      </template>
      <template #online="{ row }">
        <span>{{ row.online === 1 ? '在线' : '离线' }}</span>
      </template>
    </z-table>
  </z-page>
</template>

<script setup lang="ts">
import type {
  ZTableInstance,
  ZTableColumn,
  ZTableToolbarRow,
  ZTableParams,
} from '@/ui/components/ZTable/types'
import type { ComputedRef } from 'vue'
import { ElButton, ElMessage, ElLoading } from 'element-plus'
import { computed, onMounted, ref } from 'vue'

import dataResApi from '@/api/dataResources'

const tableRef = ref<ZTableInstance | null>(null)

const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'monitorDeviceCode', label: '设备编码' },
  { prop: 'monitorDeviceName', label: '设备名称' },
  { prop: 'monitorDeviceType', label: '设备类型' },
  { prop: 'monitorDevicePositionName', label: '安装位置' },
  { prop: 'online', label: '设备状态' },
  { prop: 'monitorDeviceRegionName', label: '区域名称' },
  { prop: 'monitorDeviceCoordinate', label: '经纬度' },
])

const toolbarRows: ComputedRef<ZTableToolbarRow[]> = computed(() => [
  {
    tools: [
      {
        key: 'synchronizer',
        type: 'button',
        label: '同步设备',
        buttonType: 'primary',
      },
    ],
  },
])

const fetchTableData = async (params: ZTableParams) => {
  return dataResApi.getDeviceManageList({ ...params }).then((res) => res.data)
}

// 设备同步
const synchronizer = async () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '同步中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  try {
    await performDeviceSync()
    ElMessage.success({
      message: '设备同步完成',
      duration: 2000,
    })

    tableRef.value?.refresh()
  } catch (error: any) {
    ElMessage.error({
      message: `同步失败: ${error.message || '未知错误'}`,
      duration: 3000,
    })
  } finally {
    loadingInstance.close()
  }
}

// 实际同步方法
async function performDeviceSync() {
  return dataResApi.synchronizer()
}
onMounted(async () => {
  tableRef.value?.refresh()
})
</script>
