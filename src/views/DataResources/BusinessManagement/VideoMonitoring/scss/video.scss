:deep(.el-tabs__nav) {
  width: 100%;
  .el-tabs__item {
    width: 50%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
}
/* 隐藏父节点的复选框 */
:deep(.el-tree-node__content) {
  .is-disabled {
    display: none;
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .node-name {
    display: block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .node-select-action {
    background: #E8F1FD;
  }
}
.custom-tree-node-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  padding-right: 8px;
  .node-name {
    display: flex;
    align-items: center;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.view-toggle {
  margin-left: auto;
}

.monitoring-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.point-list {
  width: 280px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e6e8eb;
  background-color: #fff;
}

.list-header {
  padding: 12px;
  border-bottom: 1px solid #e6e8eb;
}

.list-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.point-item {
  padding: 10px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  cursor: grab;
  transition: all 0.3s;
}

.point-item:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.point-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.point-name {
  flex: 1;
  margin: 0 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.favorite-icon {
  cursor: pointer;
  transition: color 0.2s;
}

.timestamp {
  font-size: 12px;
  color: #909399;
}

.grid-container {
  flex: 1;
  display: grid;
  gap: 12px;
  padding: 16px;
  overflow: auto;
  background-color: #f0f2f5;
}

.grid-container.layout-single {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
}

.grid-container.layout-quad {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

.grid-container.layout-nine {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
}

.grid-item {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.grid-item-active {
  border: 2px solid #409eff;
}

.grid-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}

.grid-title {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.grid-title span {
  margin-left: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.close-icon {
  cursor: pointer;
  color: #c0c4cc;
  transition: color 0.2s;
}

.close-icon:hover {
  color: #f56c6c;
}

.monitor-view {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  color: #fff;
  position: relative;
}

.grid-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  cursor: pointer;
  transition: all 0.3s;
}

.grid-empty:hover {
  color: #409eff;
}

.grid-empty span {
  margin-top: 8px;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .grid-container.layout-nine {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .monitoring-container {
    flex-direction: column;
  }

  .point-list {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #e6e8eb;
  }

  .grid-container {
    height: calc(100vh - 260px);
  }
}
