export interface MonitoringPoint {
  monitorDeviceCode: string
  monitorDeviceName: string
  previewUrl: string
  monitorDeviceInfo: string
  isFavorite?: boolean
  playBackUrl?: string
  havePtz: boolean
}

export interface MonitoringGrid {
  id: string
  point?: MonitoringPoint
}

export type LayoutType = 'single' | 'quad' | 'nine'

// command 动作(LEFT-左转、RIGHT-右转、UP-上转、DOWN-下转、ZOOM_IN-焦距变大、ZOOM_OUT-焦距变小、LEFT_UP-左上、LEFT_DOWN-左下、RIGHT_UP-右上、RIGHT_DOWN-右下、FOCUS_NEAR-焦点前移、FOCUS_FAR-焦点后移、IRIS_ENLARGE-光圈扩大、IRIS_REDUCE-光圈缩小、WIPER_SWITCH-接通雨刷开关、START_RECORD_TRACK-开始记录路线、STOP_RECORD_TRACK-停止记录路线、START_TRACK-开始路线、STOP_TRACK-停止路线、GOTO_PRESET-到预置点)
export type ControlCommand = 'LEFT' | 'RIGHT' | 'UP' | 'DOWN' | 'ZOOM_IN' | 'ZOOM_OUT' | 'LEFT_UP' | 'LEFT_DOWN' | 'RIGHT_UP' | 'RIGHT_DOWN' | 'FOCUS_NEAR' | 'FOCUS_FAR' | 'IRIS_ENLARGE' | 'IRIS_REDUCE' | 'WIPER_SWITCH' | 'START_RECORD_TRACK' | 'STOP_RECORD_TRACK' | 'START_TRACK' | 'STOP_TRACK' | 'GOTO_PRESET'

