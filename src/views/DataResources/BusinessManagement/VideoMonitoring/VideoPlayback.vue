<template>
  <z-page class="w-full p-0 bg-white h-[80vh] relative">
    <div class="w-full bg-white p-2 pb-[0px] rounded-md box-border">
      <div class="flex justify-between">
        <div class="form">
          <el-date-picker
            v-model="ruleForm.dataRange"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm"
            date-format="YYYY-MM-DD"
            time-format="HH:mm"
            :clearable="false"
            :show-now="false"
            :disabled-date="disabledFutureDate"
            class="!w-[300px] mr-[10px]"
          />
          <el-select
            v-model="ruleForm.monitorDeviceRegion"
            placeholder="区域选择"
            clearable
            class="w-[180px] mr-[10px]"
          >
            <el-option
              v-for="item in areaData"
              :key="item.areaCode"
              :label="item.areaName"
              :value="item.areaCode"
            />
          </el-select>
          <el-input
            v-model="ruleForm.searchName"
            style="width: 240px"
            clearable
            placeholder="请输入点位/监控名称查询"
          />
          <el-button
            type="primary"
            class="ml-[20px]"
            @click="handleSearch"
            >查询</el-button
          >
          <el-button @click="resetSearch">重置</el-button>
        </div>
        <el-button-group>
          <el-button
            :type="activeLayout === 'single' ? 'primary' : ''"
            @click="changeLayout('single')"
            :icon="FullScreen"
          >
            单画面
          </el-button>
          <el-button
            :type="activeLayout === 'quad' ? 'primary' : ''"
            @click="changeLayout('quad')"
            :icon="Menu"
          >
            四宫格
          </el-button>
          <el-button
            :type="activeLayout === 'nine' ? 'primary' : ''"
            @click="changeLayout('nine')"
            :icon="Grid"
          >
            九宫格
          </el-button>
        </el-button-group>
      </div>
    </div>
    <div class="w-full h-[calc(100%-42px)] flex">
      <div class="w-1/5 h-[100%] bg-white p-2 rounded-md">
        <el-tabs
          v-model="activeName"
          class="demo-tabs"
          @tab-change="tabChange"
        >
          <el-tab-pane
            label="全部点位"
            name="1"
          >
            <el-tree
              ref="treeRef"
              :data="allDataSource"
              node-key="monitorDeviceCode"
              default-expand-all
              :expand-on-click-node="false"
              :props="{
                children: 'monitorDeviceViews',
                label: 'nodeName',
              }"
            >
              <template #default="{ node, data }">
                <div class="custom-tree-node">
                  <span
                    class="node-name"
                    :draggable="true"
                    @dragstart="onDragStart($event, data)"
                    @dblclick="onDblclick(data)"
                    >{{ node.label }}</span
                  >
                  <template v-if="data.monitorDeviceName">
                    <img
                      v-if="checkedState(data)"
                      :src="select_action"
                      width="16px"
                      height="16px"
                      alt=""
                    />
                  </template>
                </div>
              </template>
            </el-tree>
          </el-tab-pane>
          <el-tab-pane
            label="收藏点位"
            name="2"
          >
            <el-tree
              :data="monitorFavoriteList"
              node-key="monitorFavoritesId"
              default-expand-all
              :expand-on-click-node="false"
              :props="{
                children: 'nodes',
                label: 'nodeName',
                disabled: (data) => data.monitorFavoritesName || data.monitorDevicePosition,
              }"
            >
              <template #default="{ node, data }">
                <div
                  :class="{
                    'custom-tree-node': data.monitorDevicePosition ? true : false,
                    'custom-tree-node-left': data.monitorDeviceName ? true : false,
                  }"
                >
                  <span
                    class="node-name"
                    :draggable="true"
                    @dragstart="onDragStart($event, data)"
                    @dblclick="onDblclick(data)"
                  >
                    {{ node.label }}
                  </span>
                  <template v-if="data.monitorDeviceName">
                    <img
                      v-if="checkedState(data)"
                      :src="select_action"
                      width="16px"
                      height="16px"
                      alt=""
                    />
                  </template>
                </div>
              </template>
            </el-tree>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="w-4/5 h-[100%] bg-white p-2 rounded-md">
        <!-- 右侧监控区域 -->
        <div
          class="grid-container h-[100%] box-border"
          :class="[`layout-${activeLayout}`]"
        >
          <div
            v-for="grid in visibleGrids"
            :key="grid.id"
            class="grid-item"
            :class="{ 'grid-item-active': selectedGrid?.id === grid.id }"
            @dragover.prevent
            @dragenter.prevent
            @drop="onDrop($event, grid)"
            @click="selectGrid(grid)"
          >
            <div
              v-if="grid.point"
              class="grid-content"
            >
              <div class="grid-header">
                <div class="grid-title">
                  <span>{{ grid.point.monitorDeviceName }}</span>
                </div>
                <el-icon
                  class="close-icon"
                  @click.stop="removeFromGrid(grid)"
                >
                  <Close />
                </el-icon>
              </div>
              <div class="monitor-view">
                <FlvVideo
                  :previewUrl="grid.point.playBackUrl"
                  :is-live="false"
                />
              </div>
            </div>
            <div
              v-else
              class="grid-empty"
            >
              <el-icon :size="24"><Plus /></el-icon>
              <span>请在左侧监控列表中拖拽或双击选择监控</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Favorites
      v-model:visible="internalVisible"
      :monitorDevicePosition="monitorDevicePosition"
      @confirm="handleConfirm"
    />
    <EditFavorites
      v-model:visible="editVisible"
      :monitorFavoritesName="monitorFavoritesName"
      :monitorFavoritesId="monitorFavoritesId"
      @confirm="handleEditConfirm"
    />
    <!-- <TimeLine :data-range="ruleForm.dataRange"/> -->
  </z-page>
</template>
<script setup lang="ts">
import dayjs from 'dayjs'
import ComApi from '@/api/common'
import { ref, reactive, onMounted, computed, provide } from 'vue'
import dataResApi from '@/api/dataResources'
import { FullScreen, Menu, Grid, Close } from '@element-plus/icons-vue'
import Favorites from './module/Favorites.vue'
import EditFavorites from './module/EditFavorites.vue'
import type { MonitoringPoint, MonitoringGrid, LayoutType } from './types'
import FlvVideo from './module/FlvVideo.vue'
import type { ElTree, TabPaneName } from 'element-plus'
import select_action from '@/assets/imgs/screen/select_action.png'
import TimeLine from './module/TimeLine.vue'

interface RuleForm {
  monitorDeviceRegion: string
  searchName: string
  dataRange: any
}

const ruleForm = reactive<RuleForm>({
  monitorDeviceRegion: '',
  searchName: '',
  dataRange: [getTodayStartTime(), getCurrentHourTime()],
})
const treeRef = ref<InstanceType<typeof ElTree>>()
const activeLayout = ref<LayoutType>('quad')
const areaData = ref<any[]>([])
const allDataSource = ref<any[]>([])
const monitorFavoriteList = ref<any[]>([])

const activeName = ref('1')
const internalVisible = ref<boolean>(false)
const editVisible = ref<boolean>(false)
const monitorDevicePosition = ref<string>('')
const monitorFavoritesId = ref<number>()
const monitorFavoritesName = ref<string>('')

// 对话框状态
const selectedGrid = ref<MonitoringGrid | null>(null)
provide('SELECT_GRID', selectedGrid)

const draggedPoint = ref<MonitoringPoint | null>(null)

// 监控格子数据
const allGrids = ref<MonitoringGrid[]>([
  { id: 'grid1', point: undefined },
  { id: 'grid2', point: undefined },
  { id: 'grid3', point: undefined },
  { id: 'grid4', point: undefined },
  { id: 'grid5', point: undefined },
  { id: 'grid6', point: undefined },
  { id: 'grid7', point: undefined },
  { id: 'grid8', point: undefined },
  { id: 'grid9', point: undefined },
])

// 当前可见的格子
const visibleGrids = computed(() => {
  switch (activeLayout.value) {
    case 'single':
      return allGrids.value.slice(0, 1)
    case 'quad':
      return allGrids.value.slice(0, 4)
    case 'nine':
      return allGrids.value
    default:
      return allGrids.value.slice(0, 4)
  }
})

// 是否选中设备
const checkedState = computed(() => {
  return (data: any) => {
    const grid = allGrids.value.find((g) => g.point?.monitorDeviceCode === data.monitorDeviceCode)
    return grid
  }
})

// 拖拽开始
const onDragStart = (event: DragEvent, point: MonitoringPoint) => {
  draggedPoint.value = point
  event.dataTransfer?.setData('text/plain', point.monitorDeviceCode)
}

// 从格子中移除点位
const removeFromGrid = (grid: MonitoringGrid) => {
  grid.point = undefined
}
// 拖拽放置
const onDrop = (event: DragEvent, grid: MonitoringGrid) => {
  event.preventDefault()
  if (!draggedPoint.value) return
  assignPoint(draggedPoint.value, grid, false)
  draggedPoint.value = null
}
// 选择格子（点击事件）
const selectGrid = (grid: MonitoringGrid) => {
  // if (selectedGrid.value && selectedGrid.value.id === grid.id) {
  //   selectedGrid.value = null
  // } else {
  //   selectedGrid.value = grid
  // }
  selectedGrid.value = grid
}
function getFLVDuration(videoUrl: string) {
  const video = document.createElement('video')
  video.src = videoUrl

  return new Promise((resolve) => {
    video.addEventListener('loadedmetadata', () => {
      console.log(211111)
      resolve(video.duration)
    })
  })
}
// 分配点位到格子
const assignPoint = async (point: MonitoringPoint, grid: MonitoringGrid, auto = true) => {
  // 从其他格子中移除该点位
  allGrids.value.forEach((g) => {
    if (g.point?.monitorDeviceCode === point.monitorDeviceCode) {
      g.point = undefined
    }
  })
  const data = {
    beginTime: dayjs(ruleForm.dataRange[0]).format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    endTime: dayjs(ruleForm.dataRange[1]).format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    cameraIndexCode: point.monitorDeviceCode,
    recordLocation: '1',
    // protocol: 'hls'
  }
  console.log(data, 'data')
  const res = await dataResApi.getPlayBackUrl(data)
  // 使用
  getFLVDuration(res.data).then((duration) => {
    console.log(`视频时长: ${duration}秒`)
  })

  // 分配到新格子
  grid.point = { ...point, playBackUrl: res.data }

  if (auto) {
    updateSelectedGrid(grid)
  }
}
const updateSelectedGrid = (grid: MonitoringGrid) => {
  const index = allGrids.value.findIndex((g) => g.id === grid.id)
  // 根据 activeLayout 的类型确定最大允许索引
  let maxIndex
  switch (activeLayout.value) {
    case 'single':
      maxIndex = 0
      break
    case 'quad':
      maxIndex = 3
      break
    case 'nine':
      maxIndex = 8
      break
    default:
      maxIndex = allGrids.value.length - 1 // 默认无限制
  }

  // 计算下一个索引，不超过最大索引
  const nextIndex = Math.min(index + 1, maxIndex)
  selectedGrid.value = allGrids.value[nextIndex]
}
// 切换布局
const changeLayout = (layout: LayoutType) => {
  activeLayout.value = layout
  localStorage.setItem('monitoringLayout', layout)
  selectedGrid.value = allGrids.value[0]
}
// 禁止选择未来的日期
const disabledFutureDate = (date: Date) => {
  return date > new Date()
}

// 获取当天的00:00:00
function getTodayStartTime() {
  const now = new Date()
  now.setHours(0, 0, 0, 0)
  return now
}

// 获取当前时间的整点（如14:00:00）
function getCurrentHourTime() {
  const now = new Date()
  now.setMinutes(0, 0, 0)
  return now
}
const onDblclick = (data: any) => {
  if (selectedGrid.value) {
    assignPoint(data, selectedGrid.value)
  }
}

const handleConfirm = () => {
  internalVisible.value = false
  getEquipomentData()
  getMonitorFavoriteList()
}
const handleEditConfirm = () => {
  editVisible.value = false
  getEquipomentData()
  getMonitorFavoriteList()
}

const handleSearch = () => {
  if (activeName.value === '1') {
    getEquipomentData()
  } else {
    getMonitorFavoriteList()
  }
  // allGrids.value.forEach((g) => {
  //   g.point = undefined
  // })
  // selectedGrid.value = allGrids.value[0]
}
const resetSearch = () => {
  Object.assign(ruleForm, { monitorDeviceRegion: '', searchName: '' })
  if (activeName.value === '1') {
    getEquipomentData()
  } else {
    getMonitorFavoriteList()
  }
  // allGrids.value.forEach((g) => {
  //   g.point = undefined
  // })
  // selectedGrid.value = allGrids.value[0]
}

const getAreaList = async () => {
  areaData.value = await ComApi.getAreaList().then((res) => res.data)
}

const getEquipomentData = async () => {
  try {
    const { dataRange, ...rest } = ruleForm
    const data = await dataResApi.getDeviceList({ ...rest }).then((res) => res.data)
    allDataSource.value = data
  } catch (error) {
    console.log(error)
  }
}
const getMonitorFavoriteList = async (ifShowPosition = true) => {
  try {
    const { dataRange, ...rest } = ruleForm
    const data = await dataResApi
      .getMonitorFavoriteList({ ...rest, ifShowPosition: ifShowPosition })
      .then((res) => res.data)
    monitorFavoriteList.value = data
  } catch (error) {
    console.log(error)
  }
}
const tabChange = (name: TabPaneName) => {
  if (name === '1') {
    getEquipomentData()
  } else {
    getMonitorFavoriteList()
  }
}
onMounted(() => {
  // 从本地存储加载布局
  const savedLayout = localStorage.getItem('monitoringLayout') as LayoutType
  if (savedLayout) changeLayout(savedLayout)
  Promise.all([getAreaList(), getEquipomentData(), getMonitorFavoriteList()])
  selectedGrid.value = allGrids.value[0]
})
</script>

<style lang="scss" scoped>
@import url('./scss/video.scss');
</style>
