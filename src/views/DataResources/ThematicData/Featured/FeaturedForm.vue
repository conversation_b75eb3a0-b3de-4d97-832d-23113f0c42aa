<template>
  <z-form
    ref="formRef"
    :fetch-data="fetchData"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
  </z-form>
</template>

<script setup lang="ts">
import dataResApi from '@/api/dataResources'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { reactive, ref } from 'vue'
import { moduleTypeOptions } from '@/views/Screen/utils/option'
const { onOpen } = useDialogFormContext()
const props = defineProps<{
  dataId: any,
  parentId: any,
  editType: boolean
  mode?: 'create' | 'edit' | 'addChild' | string
}>()
const formRef = ref<ZFormInstance>()

const initValue = {
  thematicParent: props.parentId || 0,
  thematicId: props.dataId || 0,
}

const form = reactive(initValue)
const fields = ref<ZFormField[]>([
  {
    name: 'thematicId',
    type: 'input',
    show: false,
  },
  {
    name: 'thematicParent',
    label: '上级专题',
    type: 'tree-select',
    props: {
      checkStrictly: true,
      placeholder: '请选择上级专题',
    },
    show: props.dataId && props.editType ? true : false,
    disabled: props.dataId ? true : false,
    valueKey: 'thematicId',
    treeProps: {
      label: 'thematicName',
      children: 'children',
    },
    rules: [
      {
        required: true,
        message: '请选择上级专题',
        trigger: 'change',
      },
    ],
    noCache: true,
    treeOptions: async (context) => {
      if (String(context.model.thematicId)) {
        const children = await dataResApi.getTableData().then((res) => res.data)
        return children
      } else {
        return []
      }
    },
  },
  {
    name: 'thematicName',
    label: '专题名称',
    type: 'input',
    rules: [
      {
        required: true,
        message: '请输入专题名称',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入专题名称',
      maxlength: 20,
      showWordLimit: true,
      clearable: true
    },
  },
  {
    name: 'thematicSort',
    label: '排序',
    type: 'number',
    rules: [
      {
        required: true,
        message: '请输入正整数',
        trigger: 'blur',
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!/^[1-9]\d*$/.test(value)) {
            callback(new Error('请输入正整数'))
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请选择',
      min: 1
    },
  },
  {
    name: 'thematicRoute',
    label: '路由地址',
    type: 'select',
    rules: [
      {
        message: '请输入路由地址',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请选择路由地址',
      clearable: true
    },
    options: moduleTypeOptions
  },
])
const rawFields = ref<any[]>([])

const fetchData = () =>
  dataResApi.editTopicManagement(props.dataId).then((res) => {
    const o = (fields.value ?? []).reduce((acc, { type, name }) => {
      if (name === 'thematicParent') {
        acc[name] = props.parentId
        return acc
      }
      acc[name] = res.data[name]
      return acc
    }, {} as any)
    return o
  })

defineExpose({
  getFields: () => rawFields.value,
  resetForms:() => {
    formRef.value?.resetFields()
    formRef.value?.clearCache()
  },
})

onOpen(() => {
  if (props.dataId && props.mode === 'edit') {
    if (props.dataId) {
      formRef.value?.refetch()
    }
  }
})
</script>
