<template>
  <z-page>
    <z-table
      ref="tableRef"
      :auto-load="false"
      row-key="thematicId"
      :use-pagination="false"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #enableStatus="{ row }">
        <el-switch
          :model-value="row.enableStatus"
          :active-value="1"
          :inactive-value="0"
          @change="() => statusChange(row, row.enableStatus)"
        />
      </template>
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          @click="onAddChild(row)"
          link
        >
          新增子级
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="onEdit(row)"
          link
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          :title="row.children && row.children.length ? '确定删除该专题及其子专题？' : '确定删除该专题？'"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
            >
              {{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <FeaturedForm
        ref="featuredFormRef"
        :data-id="dialog.model.value?.dataId"
        :mode="dialog.mode.value"
        :parent-id="dialog.model.value?.parentId"
        :edit-type="dialog.model.value?.editType"
      />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import type { ZTableInstance, ZTableColumn, ZTableToolbarRow } from '@/ui/components/ZTable/types'
import type { ComputedRef } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { useDialog } from '@/composables/useDialog'
import FeaturedForm from './FeaturedForm.vue'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'

import dataResApi from '@/api/dataResources'

const tableRef = ref<ZTableInstance | null>(null)
const featuredFormRef = ref<InstanceType<typeof FeaturedForm> | null>(null)

const { t } = useTypedI18n()
const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'thematicName', label: '专题名称' },
  { prop: 'thematicSort', label: '排序' },
  {
    prop: 'enableStatus',
    label: '状态',
  },
  { prop: 'operation', label: '操作', align: 'center', width: 240, fixed: 'right' },
])

const toolbarRows: ComputedRef<ZTableToolbarRow[]> = computed(() => [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create', { parentId: 0 , editType: false}),
      }),
    ],
  },
])

const getOptionValue = (v: string, options: any[]) => {
  return options.find((item: any) => item === v || item.value === v)
}

const parseValues = (values: Record<string, any>, fields: any[], rawFields: any[]) => {
  return fields.reduce((acc, field) => {
    if (field.type === 'radio') {
      const rawOptions = rawFields.find((it: any) => it.fieldCode === field.name).fieldValue
        .valueList
      const fetchedOptions = field.options
      acc[field.name] = [
        getOptionValue(values[field.name], rawOptions.length ? rawOptions : fetchedOptions),
      ]
    } else {
      acc[field.name] = values[field.name]
    }
    return acc
  }, {} as any)
}

const fetchTableData = async () => {
  const data = await dataResApi.getTableData().then((res) => res.data)
  const _data = formatData(data)
  return _data
}

const formatData = (data: any[]) => {
  return data.map((item) => {
    // 为当前节点添加 loading
    const newItem = { ...item, enableStatus: item.enableStatus ? 1 : 0 }

    // 递归处理子节点
    if (item.children && item.children.length > 0) {
      newItem.children = formatData(item.children)
    }

    return newItem
  })
}
const onDelete = (row: any) => {
  dataResApi.deleteDeptById(row.thematicId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const currentEditParentId = ref(0)
const onEdit = (row: any) => {
  const flag = row.thematicParent == 0 ? false : true
  currentEditParentId.value = row.thematicParent
  dialog.open('edit', { dataId: row.thematicId, parentId: !flag ? row.thematicId : row.thematicParent, editType: flag})
}

const onAddChild = (row: any) => {
  dialog.open('addChild', { dataId: row.thematicId, parentId: row.thematicId, editType: true})
}
const statusChange = (row: any, flag: boolean) => {
  if (!row.thematicId) return
  if (flag) {
    dataResApi.disbaledTopicManagement(row.thematicId).then(res => {
      tableRef.value?.refresh()
      ElMessage.success(res.message)
    })
  } else {
    dataResApi.enableTopicManagement(row.thematicId).then(res => {
      tableRef.value?.refresh()
      ElMessage.success(res.message)
    })
  }
}

const dialog = useDialog({
  name: '专题',
  modeMap: { addChild: '新增子级' },
  onConfirm(mode, ctx) {
    if (!ctx.form) return

    const handleSubmit = (formData: any) => {
      if (mode === 'create') {
        const { thematicParent, thematicId, ...rest } = formData
        return dataResApi.addTopicManagement({
          ...rest,
          enableStatus: true,
          thematicParent: 0
        })
      } else if (mode === 'edit') {
        const { thematicParent, ...rest } = formData
        return dataResApi.updateTopicManagement({
          ...rest,
          thematicParent: currentEditParentId.value
        })
      } else if (mode === 'addChild') {
        const { thematicId, ...rest } = formData
        return dataResApi.addTopicManagement({
          ...rest,
          thematicParent: thematicId,
          enableStatus: true,
        })
      }
    }

    ctx.form.validate((isValid) => {
      if (!isValid) return

      const formData = parseValues(
        ctx.form!.getValues(),
        ctx.form!.getFields(),
        featuredFormRef.value!.getFields(),
      )
      ctx.isSubmitting.value = true

      handleSubmit(formData)
        ?.then(() => {
          featuredFormRef.value?.resetForms()
          tableRef.value?.refresh()
          dialog.close()
          ElMessage.success(
            mode === 'create' ? t('message.saveSuccess') : t('message.updateSuccess'),
          )
        })
        .finally(() => {
          ctx.isSubmitting.value = false
          // featuredFormRef.value?.resetForms()
        })
    })
  },
})

onMounted(async () => {
  tableRef.value?.refresh()
})
</script>
