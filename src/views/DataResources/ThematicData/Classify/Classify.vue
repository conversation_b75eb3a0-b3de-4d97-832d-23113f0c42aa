<template>
  <z-page>
    <z-table
      ref="tableRef"
      :auto-load="false"
      row-key="thematicClassId"
      :use-pagination="false"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #thematicId="{ row }">
        <span>{{ findNameById(featuredData, row.thematicId) || '-' }}</span>
      </template>
      <template #thematicClassIcon="{ row }">
        <z-svg :name="`marker-${row.thematicClassIcon}`" width="20px" height="25px"></z-svg>
      </template>
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          @click="onAddChild(row)"
          link
        >
          新增子级
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="onEdit(row)"
          link
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          :title="row.children && row.children.length ? '确定删除该分类及其子分类吗？' : '确定删除该分类？'"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
            >
              {{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <ClassifyForm
        ref="ClassifyFormRef"
        :data-id="dialog.model.value?.dataId"
        :mode="dialog.mode.value"
        :parent-id="dialog.model.value?.parentId"
        :edit-type="dialog.model.value?.editType"
      />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import type { ZTableInstance, ZTableColumn, ZTableToolbarRow } from '@/ui/components/ZTable/types'
import type { ComputedRef } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { useDialog } from '@/composables/useDialog'
import ClassifyForm from './ClassifyForm.vue'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'
import dataResApi from '@/api/dataResources'
const tableRef = ref<ZTableInstance | null>(null)
const ClassifyFormRef = ref<InstanceType<typeof ClassifyForm> | null>(null)

const { t } = useTypedI18n()
const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'thematicClassName', label: '分类名称' },
  { prop: 'thematicClassSort', label: '排序' },
  { prop: 'thematicId', label: '关联专题' },
  { prop: 'thematicClassIcon', label: '图标' },
  { prop: 'operation', label: '操作', align: 'center', width: 240, fixed: 'right' },
])

const toolbarRows: ComputedRef<ZTableToolbarRow[]> = computed(() => [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create', { parentId: 0 , editType: true}),
      }),
    ],
  },
])

const getOptionValue = (v: string, options: any[]) => {
  return options.find((item: any) => item === v || item.value === v)
}

const parseValues = (values: Record<string, any>, fields: any[], rawFields: any[]) => {
  return fields.reduce((acc, field) => {
    if (field.type === 'select' || field.type === 'radio') {
      const rawOptions = rawFields.find((it: any) => it.fieldCode === field.name).fieldValue
        .valueList
      const fetchedOptions = field.options
      acc[field.name] = [
        getOptionValue(values[field.name], rawOptions.length ? rawOptions : fetchedOptions),
      ]
    } else {
      acc[field.name] = values[field.name]
    }
    return acc
  }, {} as any)
}

const fetchTableData = async () => {
  const data = await dataResApi.getCategoryData().then((res) => res.data)
  const _data = formatData(data)
  return _data
}

const featuredData = ref<any[]>([])
const getFeaturedData = async () => {
  const data = await dataResApi.getFeatureTree().then((res) => res.data)
  featuredData.value = data
}

/**
 * 根据 id 查找树形结构中的 name
 * @param tree 树形数据
 * @param targetId 要查找的 id
 * @returns 匹配的 name，未找到返回 undefined
 */
const findNameById = (tree: Array<any>, targetId: string): string | undefined => {
  for (const node of tree) {
    if (node.thematicId === targetId) {
      return node.thematicName; // 找到匹配项，返回 name
    }
    if (node.children && node.children.length > 0) {
      const result = findNameById(node.children, targetId); // 递归查找子节点
      if (result !== undefined) {
        return result; // 子节点找到，直接返回
      }
    }
  }
  return undefined; // 未找到
}

const formatData = (data: any[], loading = false) => {
  return data.map((item) => {
    // 为当前节点添加 loading
    const newItem = { ...item, loading }

    // 递归处理子节点
    if (item.children && item.children.length > 0) {
      newItem.children = formatData(item.children, loading)
    }

    return newItem
  })
}

const onDelete = (row: any) => {
  dataResApi.deleteCategoryDataById(row.thematicClassId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const currentEditParentId = ref(0)
const onEdit = (row: any) => {
  const flag = row.thematicClassParent == 0 ? true : false
  currentEditParentId.value = row.thematicClassParent
  dialog.open('edit', { dataId: row.thematicClassId, parentId: flag ? row.thematicClassId : row.thematicClassParent, editType: flag })
}

const onAddChild = (row: any) => {
  dialog.open('addChild', { dataId: row.thematicClassId, parentId: row.thematicClassId, editType: false})
}

const dialog = useDialog({
  name: '分类',
  modeMap: { addChild: '新增分类' },
  onConfirm(mode, ctx) {
    if (!ctx.form) return
    const handleSubmit = (formData: any) => {
      if (mode === 'create') {
        const { thematicClassId, ...rest } = formData
        return dataResApi.addCategoryManagement({
          ...rest,
        })
      } else if (mode === 'edit') {
        const { thematicClassParent, ...rest } = formData
        return dataResApi.updateCategoryManagement({
          ...rest,
          thematicClassParent: currentEditParentId.value
        })
      } else if (mode === 'addChild') {
        const { thematicClassId, ...rest } = formData
        return dataResApi.addCategoryManagement({
          ...rest,
          thematicClassParent: thematicClassId,
        })
      }
    }

    ctx.form.validate((isValid) => {
      if (!isValid) return

      const formData = parseValues(
        ctx.form!.getValues(),
        ctx.form!.getFields(),
        ClassifyFormRef.value!.getFields(),
      )
      ctx.isSubmitting.value = true

      handleSubmit(formData)
        ?.then(() => {
          ClassifyFormRef.value?.resetForms()
          tableRef.value?.refresh()
          dialog.close()
          ElMessage.success(
            mode === 'create' ? t('message.saveSuccess') : t('message.updateSuccess'),
          )
        })
        .finally(() => {
          ctx.isSubmitting.value = false
          // ClassifyFormRef.value?.resetForms()
        })
    })
  },
})

onMounted(async () => {
  getFeaturedData()
  tableRef.value?.refresh()
})
</script>
