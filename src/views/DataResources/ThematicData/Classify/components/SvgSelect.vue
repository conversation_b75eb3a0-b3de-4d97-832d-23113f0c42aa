<template>
  <el-popover
    v-model:visible="visible"
    trigger="click"
    :width="520"
    @show="visible = true"
    @hide="visible = false"
  >
    <template #reference>
      <div
        class="z-icon-select-trigger"
        :class="{ 'is-selected': !!modelValue }"
      >
        <z-svg v-if="modelValue" :name="`marker-${modelValue}`" width="20px"></z-svg>
        <span v-else>请选择图标</span>
        <el-icon class="arrow-icon">
          <arrow-down />
        </el-icon>
      </div>
    </template>

    <div class="z-icon-select">
      <div class="z-icon-select__container">
        <div
          v-for="(icons, category) in filteredIcons"
          :key="category"
          class="z-icon-select__group"
        >
          <div class="z-icon-select__group-title">{{ getCategoryName(category as IconCategory) }}</div>
          <div class="z-icon-select__grid">
            <div
              v-for="icon in icons"
              :key="icon.name"
              class="z-icon-select__item"
              :class="{ 'is-selected': modelValue === icon.name }"
              @click="handleSelect(icon.name)"
            >
              <z-svg :name="`marker-${icon.name}`" width="30px" height="45px"></z-svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'

const ICON_CATEGORIES = {
  ly: ['ly-1', 'ly-2', 'ly-3', 'ly-4'],
  tscp: ['tscp-1', 'tscp-2', 'tscp-3', 'tscp-4', 'tscp-5', 'tscp-6', 'tscp-7', 'tscp-8', 'tscp-9', 'tscp-10', 'tscp-11', 'tscp-12', 'tscp-13', 'tscp-14', 'tscp-15', 'tscp-16', 'tscp-17', 'tscp-18'],
  cyl:  ['cyl-1', 'cyl-2', 'cyl-3'],
  jk: ['jk-1', 'jk-2', 'jk-3', 'jk-4']
} as const

const getCategoryName = (category: keyof typeof ICON_CATEGORIES): string => {
  const names: Record<keyof typeof ICON_CATEGORIES, string> = {
    ly: '流域大屏点位',
    tscp: '特色产品',
    cyl: '产业链',
    jk: '监控点位'
  }
  return names[category]
}

interface IconOption {
  name: string
}

type IconCategory = keyof typeof ICON_CATEGORIES

defineOptions({ name: 'SvgSelect' })

defineProps<{
  modelValue?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}>()

const visible = ref(false)

const filteredIcons = computed(() => {
  const groups: Record<IconCategory, IconOption[]> = {} as Record<IconCategory, IconOption[]>

  Object.entries(ICON_CATEGORIES).forEach(([category, iconNames]) => {
    const filtered = iconNames
      .map((name) => ({
        name
      }))

    if (filtered.length > 0) {
      groups[category as IconCategory] = filtered
    }
  })

  return groups
})

const handleSelect = (iconName: string) => {
  emit('update:modelValue', iconName)
  emit('change', iconName)
  visible.value = false
}
</script>

<style lang="scss" scoped>
.z-icon-select-trigger {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 5px 15px;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  cursor: pointer;
  min-width: 120px;
  height: 32px;
  transition: all 0.2s;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-regular);

  &:hover {
    border-color: var(--el-color-primary);
  }

  &.is-selected {
    color: var(--el-text-color-primary);
  }

  .arrow-icon {
    margin-left: auto;
    transition: transform 0.2s;
  }

  &:hover .arrow-icon {
    transform: rotate(180deg);
  }
}

.z-icon-select {
  &__search {
    margin-bottom: 12px;
  }

  &__container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
    background-color: var(--el-bg-color);
  }

  &__group {
    &:not(:last-child) {
      border-bottom: 1px solid var(--el-border-color-lighter);
    }
  }

  &__group-title {
    padding: 8px 12px;
    font-size: 13px;
    color: var(--el-text-color-secondary);
    background-color: var(--el-fill-color-light);
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 4px;
    padding: 8px;
  }

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px 4px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }

    &.is-selected {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }

    .el-icon {
      font-size: 20px;
    }
  }

  &__item-name {
    font-size: 12px;
    text-align: center;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 4px;
  }
}
</style>
