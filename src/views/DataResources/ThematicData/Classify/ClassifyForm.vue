<template>
  <z-form
    ref="formRef"
    :fetch-data="fetchData"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
    <template #item-thematicClassIcon="{ formData, field }">
      <svg-select v-model="formData[field.name]" />
    </template>
  </z-form>
</template>

<script setup lang="ts">
import dataResApi from '@/api/dataResources'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { reactive, ref } from 'vue'
import SvgSelect from './components/SvgSelect.vue'

const { onOpen } = useDialogFormContext()
const props = defineProps<{
  dataId: any
  parentId: any
  editType: boolean
  mode?: 'create' | 'edit' | 'addChild' | string
}>()
const formRef = ref<ZFormInstance>()

const initValue = {
  thematicClassParent: props.parentId || 0,
  thematicClassId: props.dataId || 0,
}

const form = reactive(initValue)
const fields = ref<ZFormField[]>([
  {
    name: 'thematicClassId',
    type: 'input',
    show: false,
  },
  {
    name: 'thematicClassParent',
    label: '上级分类',
    type: 'tree-select',
    props: {
      checkStrictly: true,
      placeholder: '请选择上级分类',
    },
    show: props.mode === 'addChild' || (props.mode === 'edit' && !props.editType) ? true : false,
    disabled: props.dataId ? true : false,
    valueKey: 'thematicClassId',
    treeProps: {
      label: 'thematicClassName',
      children: 'children',
    },
    rules: [
      {
        required: true,
        message: '请选择上级分类',
        trigger: 'change',
      },
    ],
    noCache: false,
    treeOptions: async (context) => {
      if (String(context.model.thematicId)) {
        const children = await dataResApi.getCategoryData().then((res) => res.data)
        return children
      } else {
        return []
      }
    },
  },
  {
    name: 'thematicClassName',
    label: '分类名称',
    type: 'input',
    rules: [
      {
        required: true,
        message: '请输入分类名称',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入分类名称',
      maxlength: 20,
      showWordLimit: true,
      clearable: true
    },
  },
  {
    name: 'thematicClassSort',
    label: '排序',
    type: 'number',
    rules: [
      {
        required: true,
        message: '请输入正整数',
        trigger: 'change',
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!/^[1-9]\d*$/.test(value)) {
            callback(new Error('请输入正整数'))
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请选择',
      min: 1,
    },
  },
  {
    name: 'thematicId',
    label: '关联专题',
    type: 'tree-select',
    props: {
      checkStrictly: false,
      placeholder: '请选择关联专题',
    },
    show: props.mode !== 'addChild' && props.editType ? true : false,
    valueKey: 'thematicId',
    treeProps: {
      label: 'thematicName',
      children: 'children',
    },
    rules: [
      {
        required: true,
        message: '请选择上级专题',
        trigger: 'change',
      },
    ],
    noCache: true,
    treeOptions: async (context) => {
      if (String(context.model.thematicClassId)) {
        const children = await dataResApi.getTableData().then((res) => res.data)
        const data = setDisabledStatus(children)
        return data
      } else {
        return []
      }
    },
  },
  {
    type: 'custom',
    name: 'thematicClassIcon',
    label: '图标',
    rules: [
      {
        required: true,
        message: '请选择图标',
        trigger: 'change',
      },
    ],
  },
])

const rawFields = ref<any[]>([])
const fetchData = () =>
  dataResApi.editCategoryManagement(props.dataId).then((res) => {
    const o = (fields.value ?? []).reduce((acc, { type, name }) => {
      if (name === 'thematicClassParent') {
        acc[name] = props.parentId
        return acc
      }
      acc[name] = res.data[name]
      return acc
    }, {} as any)
    return o
  })

const setDisabledStatus = (data: any): any => {
  if (Array.isArray(data)) {
    return data.map((item) => setDisabledStatus(item))
  }

  if (typeof data === 'object' && data !== null) {
    const newItem = {
      ...data,
      disabled: !data.enableStatus,
    }

    if (data.children) {
      newItem.children = setDisabledStatus(data.children)
    }
    return newItem
  }

  return data
}
defineExpose({
  getFields: () => rawFields.value,
  resetForms: () => {
    formRef.value?.resetFields()
    formRef.value?.clearCache()
  },
})

onOpen(() => {
  if (props.dataId && props.mode === 'edit') {
    if (props.dataId) {
      formRef.value?.refetch()
    }
  }
})
</script>
