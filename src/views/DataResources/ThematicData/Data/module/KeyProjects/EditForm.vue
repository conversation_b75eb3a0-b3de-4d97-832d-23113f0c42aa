<template>
  <z-page class="pt-2">
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      label-width="140px"
    >
      <p class="p-0 m-0 text-sm font-bold title">基础信息</p>
      <el-form-item
        label="项目名称"
        prop="projectName"
        class="w-[560px]"
      >
        <el-input
          placeholder="请输入项目名称"
          :maxlength="50"
          clearable
          show-word-limit
          v-model="ruleForm.projectName"
        />
      </el-form-item>
      <el-form-item
        label="项目类型"
        prop="classList"
        class="w-[560px]"
      >
        <el-tree-select
          v-model="ruleForm.classList"
          :data="classData"
          placeholder="请选择，支持多选"
          node-key="thematicClassId"
          :props="{
            label: 'thematicClassName',
            children: 'children',
          }"
          multiple
          :render-after-expand="false"
        />
      </el-form-item>
      <el-form-item
        label="项目地点"
        prop="projectLocationName"
      >
        <el-input
          class="w-[420px] mb-10px"
          v-model="ruleForm.projectLocationName"
          placeholder="请输入位置"
        />
        <template v-if="!showMapView">
          <map-view
            :locationName="ruleForm.projectLocationName"
            :position="ruleForm.projectLocation"
            @setPosition="setPosition"
            @setRegisteredAddress="setRegisteredAddress"
          />
        </template>
        <template v-else>
          <map-view
            :locationName="ruleForm.projectLocationName"
            :position="ruleForm.projectLocation"
            @setPosition="setPosition"
            @setRegisteredAddress="setRegisteredAddress"
          />
        </template>
      </el-form-item>
      <el-form-item
        label="投资类型"
        class="w-[560px]"
      >
        <el-select
          v-model="ruleForm.investType"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="invest in investData"
            :key="invest.dictionaryCode"
            :label="invest.dictionaryName"
            :value="invest.dictionaryCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="所属产业"
        class="w-[560px]"
        clearable
      >
        <el-input
          placeholder="请输入所属产业"
          v-model="ruleForm.projectIndustry"
        />
      </el-form-item>
      <el-form-item
        label="所属行业"
        clearable
        class="w-[560px]"
      >
        <el-input
          placeholder="请输入所属行业"
          v-model="ruleForm.projectBusiness"
        />
      </el-form-item>
      <el-form-item label="产品图片">
        <z-upload
          class="w-140! avatar-uploader"
          :allowed-types="[FileType.IMAGE]"
          :model-value="ruleForm.projectImgList"
          :show-file-list="false"
          :limit="9"
          :max-size="5"
          tip="支持格式：jpg/jpeg/png ，最多上传9张，单张图片大小不超过5MB"
          @success="(r, file, newFileList) => handleUploadSuccess(r, newFileList)"
        >
        </z-upload>
      </el-form-item>
      <el-form-item label="项目简介">
        <z-editor
          :model-value="ruleForm.projectIntroduction"
          @change="(html) => handleEditorSuccess(html)"
        />
      </el-form-item>
      <!-- 项目进度 -->
      <p class="p-0 m-0 text-sm font-bold title">项目进度</p>
      <el-form-item label="里程碑">
        <el-checkbox-group
          v-model="ruleForm.projectProgress"
          @change="handleProgressChange"
        >
          <el-checkbox
            v-for="progress in projectProgressData"
            :label="progress.dictionaryName + progress.dictionaryCode"
            :value="progress.dictionaryCode"
          />
        </el-checkbox-group>
      </el-form-item>

      <p class="p-0 m-0 text-sm font-bold title">核心指标</p>
      <el-form-item
        label="投资规模"
        class="w-[560px]"
        prop="projectInvestment"
      >
        <el-input
          placeholder="请输入投资规模"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.projectInvestment"
        />
      </el-form-item>
      <el-form-item
        label="用地面积"
        class="w-[560px]"
        prop="projectArea"
      >
        <el-input
          placeholder="请输入用地面积"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.projectArea"
        />
      </el-form-item>
      <el-form-item
        label="从业人数"
        class="w-[560px]"
        prop="projectNumber"
      >
        <el-input
          placeholder="请输入从业人数"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.projectNumber"
        />
      </el-form-item>
      <el-form-item
        label="能源消耗(吨标准煤)"
        class="w-[560px]"
        prop="projectEnergy"
      >
        <el-input
          placeholder="请输入能源消耗"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.projectEnergy"
        />
      </el-form-item>
      <!-- <el-form-item>
        <el-button @click="resetForm(ruleFormRef)">取 消</el-button>
        <el-button
          type="primary"
          @click="submitForm(ruleFormRef)"
          >保 存</el-button
        >
      </el-form-item> -->
    </el-form>
    <div class="flex justify-center mt-10">
      <el-button @click="resetForm(ruleFormRef)">取消</el-button>

      <el-button
        type="primary"
        @click="submitForm(ruleFormRef)"
        >保存</el-button
      >
    </div>
  </z-page>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import ComApi from '@/api/common'
import dataResApi from '@/api/dataResources'
import DictApI from '@/api/dict'
import { ElMessage, type FormInstance, type FormRules, type CheckboxValueType } from 'element-plus'
import { FileType } from '@/ui/components/ZUpload/constants'
import MapView from './MapView.vue'
import router from '@/router'

interface RuleForm {
  projectName: string
  classList: string[]
  projectLocationName: string
  projectLocation: string
  investType: string
  projectIndustry: string
  projectBusiness: string
  projectIntroduction: string
  projectInvestment: string
  projectArea: string
  projectNumber: string
  projectEnergy: string
  projectProgress: any[]
  projectImgList: any[]
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  projectName: '',
  classList: [],
  projectLocationName: '',
  projectLocation: '',
  investType: '',
  projectIndustry: '',
  projectBusiness: '',
  projectIntroduction: '',
  projectInvestment: '',
  projectArea: '',
  projectNumber: '',
  projectEnergy: '',
  projectProgress: [],
  projectImgList: [],
})

const showMapView = ref<boolean>(false)

const rules = reactive<FormRules<RuleForm>>({
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { max: 50, message: '项目名称长度不能超过50', trigger: 'blur' },
  ],
  classList: [{ required: true, message: '请选择', trigger: 'change' }],
  projectLocationName: [{ required: true, message: '请输入项目地点', trigger: 'blur' }],
  projectInvestment: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  projectArea: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  projectNumber: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  projectEnergy: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
})

const currentId = ref<string>('')
const areaData = ref<any[]>([])
const classData = ref<any[]>([])
const investData = ref<any[]>([])
const projectProgressData = ref<any[]>([])

const getAreaList = async () => {
  areaData.value = await ComApi.getAreaList().then((res) => res.data)
}

const getProductsClassify = async () => {
  classData.value = await dataResApi.getCategoryData().then((res) => res.data)
}
const getInvestTypes = async () => {
  investData.value = await DictApI.getDictItemsByCode('investTypes').then((res) => res.data)
}

const getProjectProgress = async () => {
  projectProgressData.value = await DictApI.getDictItemsByCode('projectProgress').then(
    (res) => res.data,
  )
}

const handleEditorSuccess = (value: string) => {
  ruleForm.projectIntroduction = value
}

const setRegisteredAddress = (addrrss: string) => {
  ruleForm.projectLocationName = addrrss
}

const setPosition = (position: string) => {
  ruleForm.projectLocation = position
}

const handleUploadSuccess = (r: any, newFileList: any[]) => {
  ruleForm.projectImgList = newFileList.map((it: any) => it?.response?.data?.absUrl || it.name)
}
const handleProgressChange = (selectedValues: CheckboxValueType[]) => {
  // 如果全部取消选中
  if (selectedValues.length === 0) {
    ruleForm.projectProgress = []
    return
  }

  // 获取当前选中的最后一项
  const lastSelected = selectedValues[selectedValues.length - 1]
  const lastIndex = projectProgressData.value.findIndex(
    (item) => item.dictionaryCode === lastSelected,
  )

  // 判断是选中操作还是取消选中操作
  const isCheckAction = selectedValues.includes(lastSelected)

  if (isCheckAction) {
    // 选中操作 - 选中该项及前面所有项
    ruleForm.projectProgress = projectProgressData.value
      .slice(0, lastIndex + 1)
      .map((item) => item.dictionaryCode)
  } else {
    // 取消选中操作 - 取消该项及后面所有项
    const uncheckedItem = projectProgressData.value.find(
      (item) =>
        !selectedValues.includes(item.dictionaryCode) &&
        ruleForm.projectProgress.includes(item.dictionaryCode),
    )

    if (uncheckedItem) {
      const uncheckedIndex = projectProgressData.value.findIndex(
        (item) => item.dictionaryCode === uncheckedItem.dictionaryCode,
      )

      ruleForm.projectProgress = projectProgressData.value
        .slice(0, uncheckedIndex)
        .map((item) => item.dictionaryCode)
    }
  }
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      const { projectImgList, classList, projectProgress } = ruleForm
      const data = {
        ...ruleForm,
        classList: classList.length
          ? classList.map((classId) => {
              return {
                thematicClassId: classId,
              }
            })
          : [],
        projectProgress: projectProgress.length ? projectProgress.join(',') : '',
        projectImgList: projectImgList.length
          ? projectImgList.map((item) => {
              return {
                fileUrl: item,
                fileName: item,
              }
            })
          : [],
      }
      if (!currentId.value) {
        dataResApi.saveThematicProject(data).then((res) => {
          ElMessage.success(res.message)
          router.go(-1)
        })
      } else {
        dataResApi.editThematicProject(data).then((res) => {
          ElMessage.success(res.message)
          router.go(-1)
        })
      }
    } else {
      console.log('error submit!')
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  router.go(-1)
}
const loadData = async () => {
  const res = await dataResApi.getThematicProjectDetail(currentId.value).then((res) => res.data)
  res.classList =
    res.classList && res.classList.length
      ? res.classList.map((item: any) => item.thematicClassId)
      : []
  res.projectImgList =
    res.projectImgList && res.projectImgList.length
      ? res.projectImgList.map((item: any) => item.fileUrl)
      : []
  res.projectProgress = res.projectProgress ? res.projectProgress.split(',') : []
  Object.assign(ruleForm, { ...ruleForm, ...res })
  if (res.projectLocation) {
    showMapView.value = true
  }
}
onMounted(async () => {
  const route = useRoute()
  currentId.value = (route.query.id as string) || ''

  await Promise.all([getAreaList(), getProductsClassify(), getInvestTypes(), getProjectProgress()])

  if (currentId.value) {
    loadData()
  }
})
</script>

<style lang="scss" scoped>
.title {
  width: 100%;
  opacity: 0.72;
  line-height: 21px;
  padding-left: 10px;
  position: relative;
  margin-bottom: 10px;
  &::before {
    content: '';
    position: absolute;
    height: 14px;
    width: 4px;
    left: 0;
    top: 7px;
    margin-top: -3.5px;
    background-color: hsl(210 100% 63%);
  }
}
.input-with-select .el-input-group__prepend {
  background-color: var(--el-fill-color-blank);
}
</style>
