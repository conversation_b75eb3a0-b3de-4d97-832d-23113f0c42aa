<template>
  <z-page class="pt-2">
    <z-table
      ref="tableRef"
      row-key="projectId"
      :auto-load="false"
      v-model:query="query"
      :columns="columns"
      :toolbar-rows="toolbarRows"
      :fetch-data="fetchTableData"
      show-toolbar
      selectable
      pagination-mode="server"
      width="100%"
    >
      <template #toolbar-item-slotAdd="scope">
        <z-dynamic-route
          :route="{
            name: '新增重点项目',
            path: 'createproject',
            viewPath: '@/views/DataResources/ThematicData/Data/module/KeyProjects/EditForm.vue'
          }"
          :inherit="true"
          :persistent="true"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              plain
              :icon="Plus"
              @click="
                () =>
                  navigate()
              "
            >
              新增
            </el-button>
          </template>
        </z-dynamic-route>
      </template>
      <template #investType="{ row }">
        <span>{{ formatInvestType(row.investType) }}</span>
      </template>

      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            name: '编辑重点项目',
            path: 'editproject',
            viewPath: '@/views/DataResources/ThematicData/Data/module/KeyProjects/EditForm.vue'
          }"
          :inherit="true"
          :persistent="true"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              link
              @click="
                () =>
                  navigate({
                    query: {
                      id: row.projectId,
                    },
                  })
              "
            >
              编辑
            </el-button>
          </template>
        </z-dynamic-route>
        <el-popconfirm
          title="确定删除该数据？"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>
  </z-page>
</template>
<script setup lang="ts">
import ComApi from '@/api/common'
import DictApI from '@/api/dict'
import dataResApi from '@/api/dataResources'
import { onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { ElButton, ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type {
  ZTableColumn,
  ZTableInstance,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types.ts'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'

const { t } = useTypedI18n()

const query = ref()
const tableRef = ref<ZTableInstance>()
const investData = ref<any []>([])

const columns: ZTableColumn[] = [
  { prop: 'projectName', label: '项目名称', width: 160 },
  { prop: 'projectLocationName', label: '项目地点', width: 160},
  { prop: 'investType', label: '投资类型', width: 120},
  { prop: 'projectIndustry', label: '所属产业', width: 160},
  { prop: 'projectIndustry', label: '所属行业', width: 120},
  { prop: 'projectIndustry', label: '投资规模', width: 120},
  { prop: 'projectArea', label: '用地面积', width: 120},
  { prop: 'projectNumber', label: '就业人数', width: 100},
  { prop: 'projectEnergy', label: '能源消耗(吨标准煤)', width: 160},
  { prop: 'operation', label: '操作', align: 'center', width: 120, fixed: 'right' },
]

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        key: 'projectName',
        type: 'input',
        label: '项目名称',
        placeholder: '请输入项目名称',
        width: 240,
      },
      {
        key: 'thematicClassId',
        type: 'tree-select',
        label: '项目类型',
        placeholder: '请选择分类',
        valueKey: 'thematicClassId',
        selectProps: {
          label: 'thematicClassName',
          value: 'thematicClassId',
          children: 'children',
        },
        options: () => dataResApi.getCategoryData().then((res) => res.data),
        width: 240,
      },
      ZTableToolbarFactory.createSearch(), ZTableToolbarFactory.createReset()
    ],
  },
  {
    tools: [
      {
        key: 'slotAdd',
        type: 'button',
        label: '添加',
        buttonType: 'primary',
      },
      ZTableToolbarFactory.createDelete({
        disabled: () => {
          const selectSelection = tableRef.value?.getSelection()
          if (selectSelection && selectSelection.length > 0) {
            return false
          } else {
            return true
          }
        },
        onClick: () => {
          const selectSelection = tableRef.value?.getSelection()
          if (selectSelection && selectSelection.length > 0) {
            const result = selectSelection.reduce((str, item, index) => {
              return str + (index > 0 ? ', ' : '') + item.projectId
            }, '')
            dataResApi.deleteThematicProject(result).then(() => {
              tableRef.value?.refresh()
              ElMessage.success(t('message.deleteSuccess'))
            })
          }
        },
      }),
    ],
  },
]

const formatInvestType = (code: string): string => {
  const option = investData.value.find(invest => invest.dictionaryCode === code)
  if (option) {
    return option.dictionaryName
  }
  return '-'
}

const fetchTableData = async (params: ZTableParams) => {
  return dataResApi.getThematicProjectData({ ...params }).then((res) => res.data)
}

const onDelete = (row: any) => {
  dataResApi.deleteThematicProject(row.projectId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const getInvestTypes = async () => {
  investData.value = await DictApI.getDictItemsByCode('investTypes').then(res => res.data)
}
onMounted(async () => {
  await getInvestTypes()
  tableRef.value?.refresh()
})
</script>
