<template>
  <div class="w-full h-[400px] relative">
    <TMapView
      ref="mapRef"
      :options="mapOptions"
      @ready="onMapReady"
    />
    <div class="map-controls">
      <el-button-group class="mark-controls">
        <el-button
          @click="toggleMarkTool"
          :type="markToolActive ? 'primary' : ''"
          :icon="Location"
        >
          {{ markToolActive ? '停止标点' : '开始标点' }}
        </el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { Location } from '@element-plus/icons-vue'
import type { TOverlayLayer } from '@/map/core/layer/TOverlayLayer'
import { TMapView, TMap, TSearchType, TPointMarkerTool } from '@/map'
import type { TLayerType, TNativeMap } from '@/map'
const props = defineProps<{
  position: any[]
}>()
const emit = defineEmits(['setPosition', 'removePoint'])

// 添加自定义图层的引用
const customLayer = ref<TOverlayLayer | null>(null)

const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const markTool = ref<TPointMarkerTool | null>(null)
const markToolActive = ref(false)
const pointPosition = ref<any[]>([])
const pointAddress = ref<string>('')
const mapOptions = {
  center: {
    lng: 107.237743,
    lat: 34.363184,
  },
  zoom: 8,
  minZoom: 3,
  maxZoom: 18,
  search: {
    show: true,
    type: TSearchType.VIEWPORT,
    placeholder: '搜索地点或地址',
    timeout: 15000,
    maxResults: 20,
  },
}
// 添加点
const addPoint = async (options: any) => {
  if (!mapRef.value) return
  const map = mapRef.value?.getMap()

  if (!map) return

  if (!customLayer.value) {
    customLayer.value = map.createOverlayLayer({ id: 'regression-layer' })
  }
  const point = [options.position[0], options.position[1]]
  customLayer.value.createSvgOverlay({
    userData: JSON.stringify(point),
    iconName: 'marker-ly-2',
    position: [options.position[0], options.position[1]],
    label: options.address,
    labelOffset: [10, 0],
    labelRemoveable: true,
    removeWithLabel: true,
    iconStyle: {
      color: 'red'
    },
    events: {
      labelremove: (e, overlay: any) => {
        emit('removePoint', overlay.options.position)
      },
    },
  })
  // map.setCenter(
  //   {
  //     lng: options.position[0],
  //     lat: options.position[1],
  //   },
  //   15,
  // )
}

// 在操作标注工具时更新状态
const toggleMarkTool = () => {
  if (markTool.value) {
    const result = markTool.value.toggle()
    markToolActive.value = result
    if (result) {
      // removePointLayer()
      ElMessage.info('请点击地图添加标注')
    }
  }
}

// 当前底图类型
const currentLayer = ref<TLayerType>('vec')

// 获取地图实例
const nativeMap = ref<TNativeMap>()

const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')
  currentLayer.value = 'img'
  nativeMap.value = map.getNativeMap()

  markTool.value = map.createPointMarkerTool({
    iconName: 'marker-ly-2',
    removeable: true,
    enableGeocoding: true,
    autoSetAddress: true,
    updateAddressOnDrag: true,
    geocodeDebounceTime: 0,
    iconStyle: {
      color: 'red'
    },
    onMark: (position) => {
      pointPosition.value = position
    },
    onComplete: (markers) => {
      emit('setPosition', {
        address: pointAddress.value,
        position: pointPosition.value,
      })
      markToolActive.value = false
    },
    onGeocodeComplete(marker, address, detail) {
      pointAddress.value = address
    },
    onRemove(marker: any) {
      emit('removePoint', marker.options.position)
    },
  })
  if (props.position) {
    props.position.map((option) => {
      addPoint(option)
    })
  }
}
</script>
<style lang="scss" scoped>
.map-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
