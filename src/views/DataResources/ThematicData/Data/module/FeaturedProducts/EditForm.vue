<template>
  <z-page class="pt-2">
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      label-width="120px"
    >
      <p class="p-0 m-0 text-sm font-bold title">基础信息</p>
      <el-form-item
        label="产品名称"
        prop="productName"
        style="width: 560px"
      >
        <el-input
          placeholder="请输入产品名称"
          :maxlength="50"
          clearable
          show-word-limit
          v-model="ruleForm.productName"
        />
      </el-form-item>
      <el-form-item
        label="产品分类"
        prop="classList"
        style="width: 560px"
      >
        <el-tree-select
          v-model="ruleForm.classList"
          :data="productsClassifyData"
          node-key="thematicClassId"
          :props="{
            label: 'thematicClassName',
            children: 'children',
          }"
          multiple
          :render-after-expand="false"
          style="width: 560px"
        />
      </el-form-item>
      <el-form-item
        label="所属区域"
        prop="regionCode"
        style="width: 560px"
      >
        <el-select
          v-model="ruleForm.regionCode"
          placeholder="请选择"
        >
          <el-option
            v-for="area in areaData"
            :key="area.areaId"
            :label="area.areaName"
            :value="area.areaCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="重点园区"
        prop="productLocation"
      >
        <template v-if="!showMapView">
          <map-view
            :position="ruleForm.productLocation"
            @setPosition="setPosition"
            @removePoint="removePoint"
          />
        </template>
        <template v-else>
          <map-view
            :position="ruleForm.productLocation"
            @setPosition="setPosition"
            @removePoint="removePoint"
          />
        </template>
      </el-form-item>
      <el-form-item
        label="是否主页展示"
        style="width: 560px"
      >
        <el-radio-group v-model="ruleForm.hasShow">
          <el-radio value="0">是</el-radio>
          <el-radio label="1">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="产品简介">
        <z-editor
          :model-value="ruleForm.productIntroduction"
          @change="(html) => handleEditorSuccess(html)"
        />
      </el-form-item>
      <el-form-item label="产品图片">
        <z-upload
          class="w-140! avatar-uploader"
          :allowed-types="[FileType.IMAGE]"
          :model-value="ruleForm.imgs"
          :show-file-list="false"
          :limit="9"
          :max-size="5"
          tip="支持格式：jpg/jpeg/png ，最多上传9张，单张图片大小不超过5MB"
          @success="(r, file, newFileList) => handleUploadSuccess(r, newFileList)"
        >
        </z-upload>
      </el-form-item>
      <el-form-item
        label="关联企业"
        style="width: 560px"
      >
        <el-select
          v-model="ruleForm.companyList"
          placeholder="请选择"
          multiple
        >
          <el-option
            v-for="company in companyData"
            :key="company.companyId"
            :label="company.companyName"
            :value="company.companyId"
          />
        </el-select>
      </el-form-item>
      <p class="p-0 m-0 text-sm font-bold title">核心指标</p>
      <el-form-item label="产量">
        <production-input-list
          v-model="ruleForm.productOutputList"
          :unitOptions="systemUnitData"
          placeholder="请输入产量"
          class="w-[560px]"
        />
      </el-form-item>

      <el-form-item label="产值">
        <production-input-list
          v-model="ruleForm.productValueList"
          :unitOptions="systemUnitData"
          placeholder="请输入产值"
          class="w-[560px]"
        />
      </el-form-item>

      <el-form-item label="种植/养殖规模">
        <production-input-list
          v-model="ruleForm.productScaleList"
          :unitOptions="systemUnitData"
          placeholder="请输入种植/养殖规模"
          class="w-[560px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="resetForm(ruleFormRef)">取 消</el-button>
        <el-button
          type="primary"
          @click="submitForm(ruleFormRef)"
          >保 存</el-button
        >
      </el-form-item>
    </el-form>
  </z-page>
</template>
<script lang="ts" setup>
import { isEqual } from 'lodash'
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import ComApi from '@/api/common'
import DictApI from '@/api/dict'
import dataResApi from '@/api/dataResources'
import { FileType } from '@/ui/components/ZUpload/constants'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import MapView from './MapView.vue'
import ProductionInputList from './ProductsList.vue'
import router from '@/router'

interface ProductionItem {
  dataYear: string
  dataValue: string
  dataUnit: string
}

interface RuleForm {
  productName: string
  classList: string[]
  regionCode: string
  productLocation: any[]
  imgs: any[]
  companyList: any[]
  productOutputList: ProductionItem[]
  productValueList: ProductionItem[]
  productScaleList: ProductionItem[]
  hasShow: '0' | '1'
  productIntroduction: string
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  productName: '',
  regionCode: '',
  classList: [],
  productLocation: [],
  imgs: [],
  companyList: [],
  productOutputList: [createDefaultProductionItem()],
  productValueList: [createDefaultProductionItem()],
  productScaleList: [createDefaultProductionItem()],
  hasShow: '1',
  productIntroduction: '',
})
const showMapView = ref<boolean>(false)

const rules = reactive<FormRules<RuleForm>>({
  productName: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { max: 50, message: '产品名称长度不能超过50', trigger: 'blur' },
  ],
  regionCode: [{ required: true, message: '请选择所属区域', trigger: 'change' }],
  classList: [{ required: true, message: '请选择产品分类', trigger: 'change' }],
  productLocation: [{ required: true, message: '请添加重点园区', trigger: 'blur' }],
})

const companyData = ref<any[]>([])
const currentId = ref<string>('')
const productsClassifyData = ref<any[]>([])
const systemUnitData = ref<any[]>([])
const areaData = ref<any[]>([])
const productImg = ref<any[]>([])
function createDefaultProductionItem(): ProductionItem {
  return {
    dataYear: '',
    dataValue: '',
    dataUnit: '',
  }
}

const getProductsClassify = async () => {
  productsClassifyData.value = await dataResApi.getCategoryData().then((res) => res.data)
}

const getSysUnit = async () => {
  systemUnitData.value = await DictApI.getDictItemsByCode('systemUnit').then((res) => res.data)
}

const getAreaList = async () => {
  areaData.value = await ComApi.getAreaList().then((res) => res.data)
}

const getCompanyData = async () => {
  companyData.value = await dataResApi.getThematicCompanyAllList().then((res) => res.data.data)
}
const handleEditorSuccess = (value: string) => {
  ruleForm.productIntroduction = value
}

const handleUploadSuccess = (r: any, newFileList: any[]) => {
  ruleForm.imgs = newFileList.map((it: any) => it?.response?.data?.absUrl || it.name)
  productImg.value.push({
    fileUrl: r.data?.absUrl,
    fileName: r.data?.fileName,
  })
}

const loadData = async () => {
  const res = await dataResApi.getThematicProductDetail(currentId.value).then((res) => res.data)
  const processedData = {
    ...res,
    classList: res.classList?.map((item: any) => item.thematicClassId) ?? [],
    imgs: res.productImgList?.map((item: any) => item.fileUrl) ?? [],
    companyList: res.companyList?.map((item: any) => item.companyId) ?? [],
    productLocation: res.productLocation ? JSON.parse(res.productLocation) : [],
    productOutputList: res.productOutputList || [createDefaultProductionItem()],
    productValueList: res.productValueList || [createDefaultProductionItem()],
    productScaleList: res.productScaleList || [createDefaultProductionItem()],
  }
  productImg.value = res.productImgList || []
  Object.assign(ruleForm, { ...ruleForm, ...processedData })
  showMapView.value = !!res.productLocation;
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      console.log('submit!', ruleForm)
      const { companyList, classList, imgs, productLocation } = ruleForm
      const data = {
        ...ruleForm,
        productLocation:
          productLocation && productLocation.length ? JSON.stringify(productLocation) : [],
        productImgList: imgs.length ? productImg.value : [],
        classList: classList.map((id) => {
          return {
            thematicClassId: id,
          }
        }),
        companyList:
          companyList && companyList.length
            ? companyList.map((company) => {
                return {
                  companyId: company,
                }
              })
            : [],
      }
      if (!currentId.value) {
        dataResApi.saveThematicProduct(data).then((res) => {
          ElMessage.success(res.message)
          router.go(-1)
        })
      } else {
        dataResApi.editThematicProduct(data).then((res) => {
          ElMessage.success(res.message)
          router.go(-1)
        })
      }
    } else {
      console.log('error submit!')
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  router.go(-1)
}

const setPosition = (position: any) => {
  ruleForm.productLocation.push(position)
}

const removePoint = (position: any) => {
  ruleForm.productLocation = ruleForm.productLocation.filter(
    (location: any) => !isEqual(location.position, position), // 保留不匹配的项
  )
}
onMounted(async () => {
  const route = useRoute()
  currentId.value = (route.query.id as string) || ''
  await Promise.all([getProductsClassify(), getAreaList(), getSysUnit(), getCompanyData()])

  if (currentId.value) {
    loadData()
  }
})
</script>

<style lang="scss" scoped>
.title {
  width: 100%;
  opacity: 0.72;
  line-height: 21px;
  padding-left: 10px;
  position: relative;
  margin-bottom: 10px;
  &::before {
    content: '';
    position: absolute;
    height: 14px;
    width: 4px;
    left: 0;
    top: 7px;
    margin-top: -3.5px;
    background-color: hsl(210 100% 63%);
  }
}
.input-with-select .el-input-group__prepend {
  background-color: var(--el-fill-color-blank);
}
</style>
