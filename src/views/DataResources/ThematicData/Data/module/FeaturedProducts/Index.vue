<template>
  <z-page class="pt-2">
    <z-table
      ref="tableRef"
      row-key="productId"
      :auto-load="false"
      v-model:query="query"
      :columns="columns"
      :toolbar-rows="toolbarRows"
      :fetch-data="fetchTableData"
      show-toolbar
      selectable
      pagination-mode="server"
    >
      <template #toolbar-item-slotAdd="scope">
        <z-dynamic-route
          :route="{
            name: '新增特色产品',
            path: 'createproducts',
            viewPath: '@/views/DataResources/ThematicData/Data/module/FeaturedProducts/EditForm.vue'
          }"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              plain
              :icon="Plus"
              @click="
                () =>
                  navigate()
              "
            >
              新增
            </el-button>
          </template>
        </z-dynamic-route>
      </template>
      <template #regionCode="{ row }">
        <span>{{ formatRegionName(row.regionCode) }}</span>
      </template>
      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            name: '编辑特色产品',
            path: 'editproducts',
            viewPath: '@/views/DataResources/ThematicData/Data/module/FeaturedProducts/EditForm.vue'
          }"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              link
              @click="
                () =>
                  navigate({
                    query: {
                      id: row.productId,
                    },
                  })
              "
            >
              编辑
            </el-button>
          </template>
        </z-dynamic-route>
        <el-popconfirm
          title="确定删除该数据？"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>
  </z-page>
</template>
<script setup lang="ts">
import dataResApi from '@/api/dataResources'
import { onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { ElButton, ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useCommonStroe } from '@/stores/common'
import type {
  ZTableColumn,
  ZTableInstance,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types.ts'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'

const tableRef = ref<ZTableInstance>()
const { areaList } = useCommonStroe()
const query = ref()
const { t } = useTypedI18n()

const columns: ZTableColumn[] = [
  { prop: 'productName', label: '产品名称', width: 180 },
  { prop: 'regionCode', label: '所属区域' },
  { prop: 'className', label: '产品分类' },
  { prop: 'operation', label: '操作', align: 'center', width: 120, fixed: 'right' },
]

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        key: 'productName',
        type: 'input',
        label: '产品名称',
        placeholder: '请输入',
        width: 240,
      },
      {
        key: 'regionCode',
        type: 'select',
        label: '所属区域',
        placeholder: '请选择',
        options: async () =>
          areaList.map((it: any) => ({
            label: it.areaName,
            value: it.areaCode,
          })),
        width: 210,
      },
      {
        key: 'thematicClassId',
        type: 'tree-select',
        label: '产品分类',
        placeholder: '请选择',
        valueKey: 'thematicClassId',
        selectProps: {
          label: 'thematicClassName',
          value: 'thematicClassId',
          children: 'children',
        },
        options: () => dataResApi.getCategoryData().then((res) => res.data),
        width: 210,
      },
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          contentAlign: 'space-between',
          tools: [ZTableToolbarFactory.createSearch(), ZTableToolbarFactory.createReset()],
        },
      },
    ],
  },
  {
    tools: [
      {
        key: 'slotAdd',
        type: 'button',
        label: '添加',
        buttonType: 'primary',
      },
      ZTableToolbarFactory.createDelete({
        disabled: () => {
          const selectSelection = tableRef.value?.getSelection()
          if (selectSelection && selectSelection.length > 0) {
            return false
          } else {
            return true
          }
        },
        onClick: () => {
          const selectSelection = tableRef.value?.getSelection()
          if (selectSelection && selectSelection.length > 0) {
            const result = selectSelection.reduce((str, item, index) => {
              return str + (index > 0 ? ', ' : '') + item.productId
            }, '')
            dataResApi.deleteThematicProduct(result).then(() => {
              tableRef.value?.refresh()
              ElMessage.success(t('message.deleteSuccess'))
            })
          }
        },
      }),
    ],
  },
]

const fetchTableData = async (params: ZTableParams) => {
  return dataResApi.getThematicProductData({ ...params }).then((res) => res.data)
}


const formatRegionName = (code: string) => {
  const option = areaList.find((area) => area.areaCode === code)
  if (option) {
    return option.areaName
  }
  return '-'
}
const onDelete = (row: any) => {
  dataResApi.deleteThematicProduct(row.productId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

onMounted(async () => {
  tableRef.value?.refresh()
  tableRef.value
})
</script>
