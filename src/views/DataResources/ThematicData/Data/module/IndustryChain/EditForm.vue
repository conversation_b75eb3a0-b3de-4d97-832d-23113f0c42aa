<template>
  <z-page class="pt-2">
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      label-width="120px"
    >
      <p class="p-0 m-0 text-sm font-bold title">基础信息</p>
      <el-form-item
        label="产业链"
        prop="industryName"
        class="w-[560px]"
      >
        <el-input
          placeholder="请输入产业链"
          :maxlength="50"
          clearable
          show-word-limit
          v-model="ruleForm.industryName"
        />
      </el-form-item>
      <el-form-item
        label="产业链分类"
        prop="nameType"
        class="w-[560px]"
        ><el-tree-select
          v-model="ruleForm.thematicClassIds"
          :data="productsClassifyData"
          node-key="thematicClassId"
          :props="{
            label: 'thematicClassName',
            children: 'children',
          }"
          multiple
          :render-after-expand="false"
          style="width: 560px"
        />
      </el-form-item>
      <el-form-item
        label="重点区域分布"
        prop="regionCode"
        class="w-[560px]"
      >
        <el-select
          v-model="ruleForm.regionCode"
          multiple
          placeholder="请选择"
        >
          <el-option
            v-for="area in areaData"
            :key="area.areaId"
            :label="area.areaName"
            :value="area.areaCode"
          />
        </el-select>
      </el-form-item>
      <!-- 产业链图谱 -->
      <p class="p-0 m-0 text-sm font-bold title">产业链图谱</p>
      <el-form-item label="产业链图谱">
        <z-upload
          class="w-140! avatar-uploader"
          :allowed-types="[FileType.IMAGE]"
          :model-value="ruleForm.pictureList"
          :show-file-list="false"
          :limit="1"
          :max-size="1"
          tip="支持格式：jpg/jpeg/png ，最多上传1张，单张图片大小不超过5MB"
          @success="(r, file, newFileList) => handleUploadSuccess(r, newFileList)"
        >
        </z-upload>
      </el-form-item>
      <!-- 分布情况 -->
      <p class="p-0 m-0 text-sm font-bold title">分布情况</p>
      <el-form-item
        label="产品分布"
        class="w-[840px]"
      >
        <el-button
          type="primary"
          @click="addEstate"
          >添加产业</el-button
        >
        <z-table
          class="mt-[5px]"
          v-if="ruleForm.operationList && ruleForm.operationList.length"
          ref="tableRef"
          :auto-load="false"
          row-key="chartId"
          show-toolbar
          :use-pagination="false"
          :columns="columns"
          :data="ruleForm.operationList"
        >
          <template #regionCode="{ row }">
            <span>
              {{ formatAreaName(row.regionCode) }}
            </span>
          </template>
          <template #operation="{ row }">
            <el-button
              size="small"
              type="primary"
              @click="onEdit(row)"
              link
            >
              {{ t('common.edit') }}
            </el-button>
            <el-popconfirm
              title="确定删除该产业信息吗？"
              placement="top-start"
              @confirm="onDelete(row)"
            >
              <template #reference>
                <el-button
                  size="small"
                  type="danger"
                  link
                >
                  {{ t('common.delete') }}
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </z-table>
      </el-form-item>
      <el-form-item
        label="核心企业"
        class="w-[560px]"
      >
        <el-select
          v-model="ruleForm.companyIds"
          multiple
          placeholder="请选择"
        >
          <el-option
            v-for="options in thematicCompanyAllLis"
            :key="options.companyId"
            :label="options.companyName"
            :value="options.companyId"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="重点项目"
        class="w-[560px]"
      >
        <el-select
          v-model="ruleForm.projectIds"
          multiple
          placeholder="请选择"
        >
          <el-option
            v-for="options in thematicProjectAllData"
            :key="options.projectId"
            :label="options.projectName"
            :value="options.projectId"
          />
        </el-select>
      </el-form-item>
      <!-- 核心指标 -->
      <p class="p-0 m-0 text-sm font-bold title">核心指标</p>
      <el-form-item
        label="目标总产值"
        class="w-[560px]"
        prop="targetOutput"
      >
        <el-input
          v-model="ruleForm.targetOutput"
          placeholder="请输入目标总产值"
          class="flex-1"
          :maxlength="100"
          clearable
          show-word-limit
        >
        </el-input>
      </el-form-item>
      <el-form-item
        label="总产值"
        class="w-[560px]"
        prop="outputSum"
      >
        <el-input
          v-model="ruleForm.outputSum"
          placeholder="请输入总产值"
          class="flex-1"
          :maxlength="100"
          clearable
          show-word-limit
        >
        </el-input>
      </el-form-item>
      <el-form-item
        label="税收贡献"
        class="w-[560px]"
        prop="industryTaxation"
      >
        <el-input
          placeholder="请输入税收贡献"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.industryTaxation"
        />
      </el-form-item>
      <el-form-item
        label="专利数量"
        class="w-[560px]"
        prop="patentSum"
      >
        <el-input
          placeholder="请输入专利数量"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.patentSum"
        />
      </el-form-item>
      <el-form-item
        label="重点项目数"
        class="w-[560px]"
        prop="projectSum"
      >
        <el-input
          placeholder="请输入重点项目数"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.projectSum"
        />
      </el-form-item>
      <el-form-item
        label="规上企业数"
        class="w-[560px]"
        prop="scaleCompanySum"
      >
        <el-input
          placeholder="请输入规上企业数"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.scaleCompanySum"
        />
      </el-form-item>
      <el-form-item
        label="民企数"
        class="w-[560px]"
        prop="privateCompanySum"
      >
        <el-input
          placeholder="请输入民企数"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.privateCompanySum"
        />
      </el-form-item>
      <el-form-item
        label="就业人数"
        class="w-[560px]"
        prop="employmentNum"
      >
        <el-input
          placeholder="请输入就业人数"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.employmentNum"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="resetForm(ruleFormRef)">取 消</el-button>
        <el-button
          type="primary"
          @click="submitForm(ruleFormRef)"
          >保 存</el-button
        >
      </el-form-item>
    </el-form>
    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <EstateForm
        ref="estateFormRef"
        :model="dialog.model.value"
      />
    </z-dialog-form>
  </z-page>
</template>
<script lang="ts" setup>
import ComApi from '@/api/common'
import router from '@/router'
import { useTypedI18n } from '@/i18n'
import { useRoute } from 'vue-router'
import dataResApi from '@/api/dataResources'
import { ref, reactive, onMounted } from 'vue'
import { useDialog } from '@/composables/useDialog'
import { FileType } from '@/ui/components/ZUpload/constants'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import EstateForm from './EstateForm.vue'
import type { ZTableColumn } from '@/ui/components/ZTable/types'

interface RuleForm {
  industryName: string
  thematicClassIds: string[]
  operationList: string[]
  regionCode: string[]
  pictureList: any[]
  projectIds: any[]
  companyIds: any[]
  targetOutput: string
  outputSum: string
  patentSum: string
  projectSum: string
  scaleCompanySum: string
  industryTaxation: string
  privateCompanySum: string
  employmentNum: string
}

const { t } = useTypedI18n()
const currentEditIndex = ref<number>(0)
const ruleFormRef = ref<FormInstance>()
const estateFormRef = ref<InstanceType<typeof EstateForm> | null>(null)

const ruleForm = reactive<RuleForm>({
  industryName: '',
  thematicClassIds: [],
  operationList: [],
  regionCode: [],
  pictureList: [],
  projectIds: [],
  companyIds: [],
  targetOutput: '',
  outputSum: '',
  projectSum: '',
  employmentNum: '',
  patentSum: '',
  scaleCompanySum: '',
  industryTaxation: '',
  privateCompanySum: '',
})

const rules = reactive<FormRules<RuleForm>>({
  industryName: [
    { required: true, message: '请输入产业链', trigger: 'blur' },
    { max: 50, message: '产业链名称长度不能超过50', trigger: 'blur' },
  ],
  thematicClassIds: [{ required: true, message: '请选择产业链类型', trigger: 'blur' }],
  regionCode: [{ required: true, message: '请选择所属区域', trigger: 'change' }],
  targetOutput: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  outputSum: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  industryTaxation: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  patentSum: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  projectSum: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  scaleCompanySum: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  privateCompanySum: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  employmentNum: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
})

const columns = ref<ZTableColumn[]>([
  { prop: 'industryInformation', label: '产业名称', width: 120 },
  {
    prop: 'regionCode',
    label: '产业分布',
    width: 240,
  },
  {
    prop: 'industryDescription',
    label: '描述',
    width: 240,
  },
  { prop: 'operation', label: '操作', align: 'center', width: 120, fixed: 'right' },
])

const currentId = ref<string>('')
const areaData = ref<any[]>([])
const productsClassifyData = ref<any[]>([])
const thematicProjectAllData = ref<any[]>([])
const thematicCompanyAllLis = ref<any[]>([])

const parseValues = (values: Record<string, any>, fields: any[], rawFields: any[]) => {
  return fields.reduce((acc, field) => {
    acc[field.name] = values[field.name]
    return acc
  }, {} as any)
}
const dialog = useDialog({
  name: '产业信息',
  onConfirm(mode, ctx) {
    if (!ctx.form) return
    const handleSubmit = (formData: any) => {
      if (mode === 'create') {
        const index = ruleForm.operationList && ruleForm.operationList.length ? ruleForm.operationList.length - 1 : 0
        ruleForm.operationList.push({
          ...formData,
          index: index
        })
        return new Promise((resolve) => {
          resolve(true)
        })
      } else if (mode === 'edit') {
        const i = ruleForm.operationList.findIndex((operation: any) => operation.index == currentEditIndex.value)
        ruleForm.operationList[i] = {
          ...formData,
          index: currentEditIndex.value
        }
        return new Promise((resolve) => {
          resolve(true)
        })
      }
    }

    ctx.form.validate((isValid) => {
      if (!isValid) return

      const formData = parseValues(
        ctx.form!.getValues(),
        ctx.form!.getFields(),
        estateFormRef.value!.getFields(),
      )
      ctx.isSubmitting.value = true

      handleSubmit(formData)
        ?.then(() => {
          estateFormRef.value?.resetForms()
          dialog.close()
        })
        .finally(() => {
          ctx.isSubmitting.value = false
        })
    })
  },
})

const getAreaList = async () => {
  areaData.value = await ComApi.getAreaList().then((res) => res.data)
}

const getProductsClassify = async () => {
  productsClassifyData.value = await dataResApi.getCategoryData().then((res) => res.data)
}

const getThematicProjectAll = async () => {
  thematicProjectAllData.value = await dataResApi.getThematicProjectAll().then((res) => res.data)
}

const getThematicCompanyAllList = async () => {
  thematicCompanyAllLis.value = await dataResApi
    .getThematicCompanyAllList()
    .then((res) => res.data.data)
}

const handleUploadSuccess = (r: any, newFileList: any[]) => {
  ruleForm.pictureList = newFileList.map((it: any) => it?.response?.data?.absUrl || it.name)
}

const addEstate = () => {
  dialog.open('create')
}

const onDelete = (row: any) => {
  ruleForm.operationList = ruleForm.operationList.filter((operation: any) => operation.index != row.index)
}

const onEdit = (row: any) => {
  currentEditIndex.value = row.index
  dialog.open('edit', row)
}
const loadData = async () => {
  const res = await dataResApi.getThematicIndustryDetail(currentId.value).then((res) => res.data)
  const industryImg = res.industryImg ? JSON.parse(res.industryImg) : []
  res.pictureList = industryImg.length ? industryImg.map((item: any) => item.pictureUrl) : []
  res.regionCode = res.regionCode ? res.regionCode.split(',') : []
  res.operationList = res.operationList && res.operationList.length ? res.operationList.map((item: any, index: number) => {
    return {
      ...item,
      regionCode: JSON.parse(item.regionCode),
      index: index
    }
  }) : []
  Object.assign(ruleForm, { ...ruleForm, ...res })
}
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      const { pictureList, regionCode, operationList } = ruleForm
      const data = {
        ...ruleForm,
        regionCode: regionCode.length ? regionCode.join(',') : '',
        operationList: operationList.length ? operationList.map((item: any) => {
          return {
            ...item,
            regionCode: JSON.stringify(item.regionCode)
          }
        }) : [],
        pictureList: pictureList.length
          ? pictureList.map((item) => {
              return {
                pictureName: item,
                pictureUrl: item,
              }
            })
          : [],
      }
      if (!currentId.value) {
        dataResApi.saveThematicIndustry(data).then((res) => {
          ElMessage.success(res.message)
          router.go(-1)
        })
      } else {
        dataResApi.editThematicIndustry(data).then((res) => {
          ElMessage.success(res.message)
          router.go(-1)
        })
      }
    } else {
      console.log('error submit!')
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  router.go(-1)
}

const formatAreaName = (codes: any[]) => {
  if (!codes.length) return ''
  const matchedAreaNames = areaData.value
    .filter((item) => codes.includes(item.areaCode))
    .map((item) => item.areaName)
  return matchedAreaNames.join(',')
}

onMounted(async () => {
  const route = useRoute()
  currentId.value = (route.query.id as string) || ''

  await Promise.all([
    getAreaList(),
    getProductsClassify(),
    getThematicProjectAll(),
    getThematicCompanyAllList(),
  ])

  if (currentId.value) {
    loadData()
  }
})
</script>

<style lang="scss" scoped>
.title {
  width: 100%;
  opacity: 0.72;
  line-height: 21px;
  padding-left: 10px;
  position: relative;
  margin-bottom: 10px;
  &::before {
    content: '';
    position: absolute;
    height: 14px;
    width: 4px;
    left: 0;
    top: 7px;
    margin-top: -3.5px;
    background-color: hsl(210 100% 63%);
  }
}
.input-with-select .el-input-group__prepend {
  background-color: var(--el-fill-color-blank);
}
</style>
