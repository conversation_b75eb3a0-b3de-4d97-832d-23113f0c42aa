<template>
  <z-page class="pt-2">
    <z-table
      ref="tableRef"
      row-key="industryId"
      :auto-load="false"
      v-model:query="query"
      :columns="columns"
      :toolbar-rows="toolbarRows"
      :fetch-data="fetchTableData"
      show-toolbar
      selectable
      pagination-mode="server"
    >
      <template #toolbar-item-slotAdd="scope">
        <z-dynamic-route
          :route="{
            name: '新增产业链',
            path: 'createindustry',
            viewPath: '@/views/DataResources/ThematicData/Data/module/IndustryChain/EditForm.vue'
          }"
          :inherit="true"
          :persistent="true"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              plain
              :icon="Plus"
              @click="
                () =>
                  navigate()
              "
            >
              新增
            </el-button>
          </template>
        </z-dynamic-route>
      </template>
      <template #regionCode="{ row }">
        <span>{{ formatAreaName(row.regionCode) }}</span>
      </template>

      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            name: '编辑产业链',
            path: 'editindustry',
            viewPath: '@/views/DataResources/ThematicData/Data/module/IndustryChain/EditForm.vue'
          }"
          :inherit="true"
          :persistent="true"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              link
              @click="
                () =>
                  navigate({
                    query: {
                      id: row.industryId,
                    },
                  })
              "
            >
              编辑
            </el-button>
          </template>
        </z-dynamic-route>
        <el-popconfirm
          title="确定删除该数据？"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>
  </z-page>
</template>
<script setup lang="ts">
import ComApi from '@/api/common'
import dataResApi from '@/api/dataResources'
import { onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { ElButton, ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type {
  ZTableColumn,
  ZTableInstance,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types.ts'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'

const tableRef = ref<ZTableInstance>()
const query = ref()
const { t } = useTypedI18n()
const areaData = ref<any []>([])
const columns: ZTableColumn[] = [
  { prop: 'industryName', label: '产业链', width: 240 },
  { prop: 'regionCode', label: '重点区域分布', width: 240},
  { prop: 'targetOutput', label: '目标总产值', width: 120},
  { prop: 'outputSum', label: '总产值'},
  { prop: 'patentSum', label: '专利数量'},
  { prop: 'scaleCompanySum', label: '规上企业数'},
  { prop: 'privateCompanySum', label: '民企数'},
  { prop: 'employmentNum', label: '就业人数'},
  { prop: 'operation', label: '操作', align: 'center', width: 120, fixed: 'right' },
]

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        key: 'industryName',
        type: 'input',
        label: '产业链',
        placeholder: '请输入产业链',
        width: 240,
      },
      ZTableToolbarFactory.createSearch(), ZTableToolbarFactory.createReset()
    ],
  },
  {
    tools: [
      {
        key: 'slotAdd',
        type: 'button',
        label: '添加',
        buttonType: 'primary',
      },
      ZTableToolbarFactory.createDelete({
        disabled: () => {
          const selectSelection = tableRef.value?.getSelection()
          if (selectSelection && selectSelection.length > 0) {
            return false
          } else {
            return true
          }
        },
        onClick: () => {
          const selectSelection = tableRef.value?.getSelection()
          if (selectSelection && selectSelection.length > 0) {
            const result = selectSelection.map(item => item.industryId)
            dataResApi.batchDeleteThematicIndustry(result).then(() => {
              tableRef.value?.refresh()
              ElMessage.success(t('message.deleteSuccess'))
            })
          }
        },
      }),
    ],
  },
]

const fetchTableData = async (params: ZTableParams) => {
  return dataResApi.getThematicIndustryData({ ...params }).then((res) => res.data)
}

const onDelete = (row: any) => {
  dataResApi.deleteThematicIndustry(row.industryId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}
const getAreaList = async () => {
  areaData.value = await ComApi.getAreaList().then((res) => res.data)
}

const formatAreaName = (codes: string) => {
  if (!codes) return ''
  const codeList = codes.split(',')
  const matchedAreaNames = areaData.value
    .filter((item) => codeList.includes(item.areaCode))
    .map((item) => item.areaName)
  return matchedAreaNames.join(',')
}
onMounted(() => {
  getAreaList()
  tableRef.value?.refresh()
})
</script>
