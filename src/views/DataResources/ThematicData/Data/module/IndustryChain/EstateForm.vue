<template>
  <z-form
    ref="formRef"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
  </z-form>
</template>

<script setup lang="ts">
import ComApi from '@/api/common'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { reactive, ref } from 'vue'

const formRef = ref<ZFormInstance>()

const form = reactive({})
const fields = ref<ZFormField[]>([
  {
    name: 'industryInformation',
    label: '产业名称',
    type: 'input',
    rules: [
      {
        required: true,
        message: '请输入产业名称',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入产业名称',
      maxlength: 50,
      showWordLimit: true,
      clearable: true
    },
  },
  {
    name: 'regionCode',
    type: 'select',
    label: '产业分布',
    rules: [
      {
        required: true,
        message: '请选择产业分布',
        trigger: 'change',
      },
    ],
    props: {
      checkStrictly: true,
      multiple: true,
      placeholder: '请选择区域，支持多选',
    },
    options: () =>
      ComApi.getAreaList().then((res) =>
        res.data.map((it: any) => ({
          label: it.areaName,
          value: it.areaCode,
        })),
      ),
  },
  {
    name: 'industryDescription',
    label: '描述',
    type: 'input',

    rules: [
      {
        message: '请输入',
        trigger: 'blur',
      },
    ],
    props: {
      type: 'textarea',
      maxlength: 300,
      showWordLimit: true,
    },
  },
])

const rawFields = ref<any[]>([])

const setDisabledStatus = (data: any): any => {
  if (Array.isArray(data)) {
    return data.map((item) => setDisabledStatus(item))
  }

  if (typeof data === 'object' && data !== null) {
    const newItem = {
      ...data,
      disabled: !data.enableStatus,
    }

    if (data.children) {
      newItem.children = setDisabledStatus(data.children)
    }
    return newItem
  }

  return data
}
defineExpose({
  getFields: () => rawFields.value,
  resetForms: () => {
    formRef.value?.resetFields()
    formRef.value?.clearCache()
  },
})
</script>
