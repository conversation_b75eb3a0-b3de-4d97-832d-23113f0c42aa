<template>
  <z-page class="pt-2">
    <z-table
      ref="tableRef"
      row-key="companyId"
      :auto-load="false"
      v-model:query="query"
      :columns="columns"
      :toolbar-rows="toolbarRows"
      :fetch-data="fetchTableData"
      show-toolbar
      selectable
      pagination-mode="server"
    >

      <template #toolbar-item-slotAdd="scope">
        <z-dynamic-route
          :route="{
            name: '新增企业',
            path: 'createenterprise',
            viewPath: '@/views/DataResources/ThematicData/Data/module/Enterprise/EditForm.vue'
          }"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              plain
              :icon="Plus"
              @click="
                () =>
                  navigate()
              "
            >
              新增
            </el-button>
          </template>
        </z-dynamic-route>
      </template>
      <template #regionCode="{ row }">
        <span>{{ formatRegionName(row.regionCode) }}</span>
      </template>
      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            name: '编辑企业',
            path: 'editedterprise',
            viewPath: '@/views/DataResources/ThematicData/Data/module/Enterprise/EditForm.vue'
          }"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              link
              @click="
                () =>
                  navigate({
                    query: {
                      id: row.companyId,
                    },
                  })
              "
            >
              编辑
            </el-button>
          </template>
        </z-dynamic-route>
        <el-popconfirm
          title="确定删除该数据？"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>
  </z-page>
</template>
<script setup lang="ts">
import ComApi from '@/api/common'
import dataResApi from '@/api/dataResources'
import { onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { ElButton, ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useCommonStroe } from '@/stores/common'
import type {
  ZTableColumn,
  ZTableInstance,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types.ts'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'

const { areaList } = useCommonStroe()
const tableRef = ref<ZTableInstance>()
const query = ref()
const { t } = useTypedI18n()

const columns: ZTableColumn[] = [
  { prop: 'companyName', label: '企业名称', width: 180 },
  { prop: 'regionCode', label: '行政区划' },
  { prop: 'companyAddress', label: '注册地址' },
  { prop: 'companyType', label: '企业性质' },
  { prop: 'companyCapital', label: '注册资本' },
  { prop: 'operation', label: '操作', align: 'center', width: 120, fixed: 'right' },
]

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        key: 'companyName',
        type: 'input',
        label: '企业名称',
        placeholder: '请输入名称',
        width: 240,
      },
      {
        key: 'regionCode',
        type: 'select',
        label: '行政区划',
        placeholder: '请选择',
        options: async () =>
          areaList.map((it: any) => ({
            label: it.areaName,
            value: it.areaCode,
          })),
        width: 210,
      },
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          contentAlign: 'space-between',
          tools: [ZTableToolbarFactory.createSearch(), ZTableToolbarFactory.createReset()],
        },
      },
    ],
  },
  {
    tools: [
      {
        key: 'slotAdd',
        type: 'button',
        label: '添加',
        buttonType: 'primary',
      },
      ZTableToolbarFactory.createDelete({
        disabled: () => {
          const selectSelection = tableRef.value?.getSelection()
          if (selectSelection && selectSelection.length > 0) {
            return false
          } else {
            return true
          }
        },
        onClick: () => {
          const selectSelection = tableRef.value?.getSelection()
          if (selectSelection && selectSelection.length > 0) {
            const result = selectSelection.map(item => item.companyId)
            dataResApi.deleteThematicCompany(result).then(() => {
              tableRef.value?.refresh()
              ElMessage.success(t('message.deleteSuccess'))
            })
          }
        },
      }),
    ],
  },
]

const formatRegionName = (code: string) => {
  const option = areaList.find((area) => area.areaCode === code)
  if (option) {
    return option.areaName
  }
  return '-'
}
const fetchTableData = async (params: ZTableParams) => {
  return dataResApi.getThematicCompanyData({ ...params }).then((res) => res.data)
}

const onDelete = (row: any) => {
  dataResApi.deleteThematicCompany([row.companyId]).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

onMounted(async () => {
  tableRef.value?.refresh()
})
</script>
