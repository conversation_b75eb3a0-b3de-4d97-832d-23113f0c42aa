<template>
  <div class="w-full[560px]">
    <ul class="m-0 p-0 list-none">
      <li
        v-for="(item, index) in modelValue"
        :key="index"
        class="flex items-center mb-3 gap-2"
      >
        <el-date-picker
          v-model="item.year"
          type="year"
          placeholder="请选择年份"
          style="width: 120px"
          @change="onChangeData($event, item)"
        />

        <el-input
          v-model="item.operatingRevenue"
          :placeholder="placeholder"
          :maxlength="100"
          show-word-limit
          clearable
          class="flex-1"
        >
          <template #append>
            <el-select
              v-model="item.unit"
              filterable
              placeholder="单位"
              class="w-[72px]"
            >
              <el-option
                v-for="unit in props.unitOptions"
                :key="unit.dictionaryCode"
                :label="unit.dictionaryName"
                :value="unit.dictionaryCode"
              />
            </el-select>
          </template>
        </el-input>

        <el-icon
          class="text-base text-[#E6555B] cursor-pointer"
          @click="deleteItem(index)"
        >
          <Delete />
        </el-icon>
      </li>
    </ul>

    <el-button
      type="primary"
      @click="addItem"
      class="mt-2"
    >
      添加
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { Delete } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import type { PropType } from 'vue'

interface ProductionItem {
  year: string
  operatingRevenue: string
  unit: string
}
interface UnitItem {
  dictionaryCode: string
  dictionaryId: number
  dictionaryName: string
  [key: string]: any
}

const props = defineProps({
  modelValue: {
    type: Array as PropType<ProductionItem[]>,
    required: true,
  },
  placeholder: {
    type: String,
    default: '请输入',
  },
  unitOptions: {
    type: Array as PropType<UnitItem[]>,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue'])

const addItem = () => {
  const newList = [...props.modelValue, { year: '', operatingRevenue: '', unit: '' }]
  emit('update:modelValue', newList)
}

const deleteItem = (index: number) => {
  const newList = [...props.modelValue]
  newList.splice(index, 1)
  emit('update:modelValue', newList)
}

const onChangeData = (data: any, row: any) => {
  row.year = dayjs(data).format('YYYY')
}
</script>
