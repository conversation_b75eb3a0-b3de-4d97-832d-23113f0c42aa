<template>
  <z-page class="pt-2">
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      label-width="120px"
    >
      <p class="p-0 m-0 text-sm font-bold title">基础信息</p>
      <el-form-item
        label="企业名称"
        prop="companyName"
        class="w-[560px]"
      >
        <el-input
          placeholder="请输入企业名称"
          :maxlength="50"
          clearable
          show-word-limit
          v-model="ruleForm.companyName"
        />
      </el-form-item>
      <el-form-item
        label="行政区划"
        prop="regionCode"
        class="w-[560px]"
      >
        <el-select
          v-model="ruleForm.regionCode"
          placeholder="请选择"
        >
          <el-option
            v-for="area in areaData"
            :key="area.areaId"
            :label="area.areaName"
            :value="area.areaCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="注册地址"
        prop="companyAddress"
      >
        <el-input
          class="w-[440px] mb-10px"
          v-model="ruleForm.companyAddress"
          placeholder="请输入位置"
        />
        <template v-if="!showMapView">
          <map-view
            :locationName="ruleForm.companyAddress"
            :position="ruleForm.latitudeLongitude"
            @setPosition="setPosition"
            @setRegisteredAddress="setRegisteredAddress"
          />
        </template>
        <template v-else>
          <map-view
            :locationName="ruleForm.companyAddress"
            :position="ruleForm.latitudeLongitude"
            @setPosition="setPosition"
            @setRegisteredAddress="setRegisteredAddress"
          />
        </template>
      </el-form-item>
      <el-form-item
        label="是否主页展示"
        class="w-[560px]"
      >
        <el-radio-group v-model="ruleForm.hasShow">
          <el-radio value="0">是</el-radio>
          <el-radio value="1">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="成立日期"
        class="w-[560px]"
      >
        <el-date-picker
          v-model="ruleForm.companyDate"
          type="date"
          placeholder="请选择日期"
          style="width: 560px"
        />
      </el-form-item>
      <el-form-item
        label="法定代表人"
        class="w-[560px]"
      >
        <el-input
          placeholder="请输入法定代表人"
          v-model="ruleForm.companyLegal"
        />
      </el-form-item>
      <el-form-item
        label="注册资本"
        class="w-[560px]"
      >
        <el-input
          v-model="ruleForm.companyCapital"
          placeholder="请输入注册资本"
          class="flex-1"
        >
          <template #append>
            <el-select
              placeholder="单位"
              class="w-[72px]"
              v-model="ruleForm.capitalUnit"
            >
              <el-option
                v-for="unit in systemUnitData"
                :key="unit.dictionaryCode"
                :label="unit.dictionaryName"
                :value="unit.dictionaryCode"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        label="企业性质"
        class="w-[560px]"
      >
        <el-select
          v-model="ruleForm.companyType"
          placeholder="请选择企业性质"
        >
          <el-option
            v-for="unit in companyTypesData"
            :key="unit.dictionaryCode"
            :label="unit.dictionaryName"
            :value="unit.dictionaryCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="员工总数"
        class="w-[560px]"
      >
        <el-input
          placeholder="请输入员工总数，单位:人"
          v-model="ruleForm.companyEmployee"
        />
      </el-form-item>
      <el-form-item
        label="总资产"
        class="w-[560px]"
      >
        <el-input
          v-model="ruleForm.companyAsset"
          placeholder="请输入总资产"
          class="flex-1"
        >
          <template #append>
            <el-select
              placeholder="单位"
              class="w-[72px]"
              v-model="ruleForm.assetUnit"
            >
              <el-option
                v-for="unit in systemUnitData"
                :key="unit.dictionaryCode"
                :label="unit.dictionaryName"
                :value="unit.dictionaryCode"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="企业简介">
        <z-editor
          :model-value="ruleForm.companyIntroduction"
          @change="(html) => handleEditorSuccess(html)"
        />
      </el-form-item>
      <el-form-item label="图片">
        <z-upload
          class="w-140! avatar-uploader"
          :allowed-types="[FileType.IMAGE]"
          :model-value="ruleForm.companyImg"
          :show-file-list="false"
          :limit="9"
          :max-size="5"
          tip="支持格式：jpg/jpeg/png ，最多上传9张，单张图片大小不超过5MB"
          @success="(r, file, newFileList) => handleUploadSuccess(r, newFileList)"
        >
        </z-upload>
      </el-form-item>
      <p class="p-0 m-0 text-sm font-bold title">核心指标</p>
      <el-form-item label="营业收入">
        <production-input-list
          v-model="ruleForm.operatingRevenueList"
          :unit-options="systemUnitData"
          placeholder="请输入营业收入"
          class="w-[560px]"
        />
      </el-form-item>

      <el-form-item label="净利润">
        <production-input-list
          v-model="ruleForm.netProfit"
          :unit-options="systemUnitData"
          placeholder="请输入净利润"
          class="w-[560px]"
        />
      </el-form-item>

      <el-form-item
        label="市占率"
        class="w-[560px]"
      >
        <el-input
          placeholder="请输入市占率"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.marketShare"
        />
      </el-form-item>
      <el-form-item
        label="专利数量"
        class="w-[560px]"
      >
        <el-input
          placeholder="请输入专利数量"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.patentNum"
        />
      </el-form-item>
      <el-form-item
        label="带动就业人数"
        class="w-[560px]"
      >
        <el-input
          placeholder="请输入带动就业人数"
          :maxlength="100"
          clearable
          show-word-limit
          v-model="ruleForm.employmentNum"
        />
      </el-form-item>
      <!-- <el-form-item>
        <el-button @click="resetForm(ruleFormRef)">取 消</el-button>
        <el-button
          type="primary"
          @click="submitForm(ruleFormRef)"
          >保 存</el-button
        >
      </el-form-item> -->
    </el-form>
    <div class="flex justify-center mt-10">
      <el-button @click="resetForm(ruleFormRef)">取消</el-button>

      <el-button
        type="primary"
        @click="submitForm(ruleFormRef)"
        >保存</el-button
      >
    </div>
  </z-page>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import ComApi from '@/api/common'
import DictApI from '@/api/dict'
import dataResApi from '@/api/dataResources'
import { FileType } from '@/ui/components/ZUpload/constants'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import MapView from './MapView.vue'
import ProductionInputList from './ProductsList.vue'
import router from '@/router'
import dayjs from 'dayjs'
interface ProductionItem {
  year: string
  operatingRevenue: string
  unit: string
}

interface RuleForm {
  companyName: string
  regionCode: string
  companyAddress: string
  latitudeLongitude: string
  hasShow: '0' | '1'
  companyDate: string
  companyLegal: string
  companyCapital: string
  capitalUnit: string
  companyType: string
  companyEmployee: string
  companyAsset: string
  assetUnit: string
  companyIntroduction: string
  companyImg: any[]
  operatingRevenueList: ProductionItem[]
  netProfit: ProductionItem[]
  marketShare: string
  patentNum: string
  employmentNum: string
}

const createDefaultProductionItem = (): ProductionItem => {
  return {
    year: '',
    operatingRevenue: '',
    unit: '',
  }
}

const ruleFormRef = ref<FormInstance>()

const ruleForm = reactive<RuleForm>({
  companyName: '',
  regionCode: '',
  companyAddress: '',
  latitudeLongitude: '',
  hasShow: '1',
  companyDate: '',
  companyLegal: '',
  companyCapital: '',
  capitalUnit: '',
  companyType: '',
  companyEmployee: '',
  companyAsset: '',
  assetUnit: '',
  companyIntroduction: '',
  companyImg: [],
  operatingRevenueList: [createDefaultProductionItem()],
  netProfit: [createDefaultProductionItem()],
  marketShare: '',
  patentNum: '',
  employmentNum: '',
})

const currentId = ref<string>('')
const areaData = ref<any[]>([])
const systemUnitData = ref<any[]>([])
const companyTypesData = ref<any[]>([])
const showMapView = ref<boolean>(false)

const rules = reactive<FormRules<RuleForm>>({
  companyName: [
    { required: true, message: '请输入企业名称', trigger: 'blur' },
    { max: 50, message: '企业名称长度不能超过50', trigger: 'blur' },
  ],
  companyAddress: [{ required: true, message: '请输入位置', trigger: 'blur' }],
  regionCode: [{ required: true, message: '请选择所属区域', trigger: 'change' }],
  marketShare: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  patentNum: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
  employmentNum: [{ max: 100, message: '长度不能超过100', trigger: 'blur' }],
})

const handleEditorSuccess = (value: string) => {
  ruleForm.companyIntroduction = value
}

const handleUploadSuccess = (r: any, newFileList: any[]) => {
  ruleForm.companyImg = newFileList.map((it: any) => it?.response?.data?.absUrl || it.name)
}

const setRegisteredAddress = (address: string) => {
  ruleForm.companyAddress = address
}

const setPosition = (position: string) => {
  ruleForm.latitudeLongitude = position
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      const { companyImg, companyDate } = ruleForm
      const data = {
        ...ruleForm,
        companyImg: '',
        companyDate: companyDate ? dayjs(companyDate).format('YYYY-MM-DD') : '',
        pictureList: companyImg.length
          ? companyImg.map((item) => {
              return {
                pictureName: item,
                pictureUrl: item,
              }
            })
          : [],
      }
      if (!currentId.value) {
        dataResApi.saveThematicCompany(data).then((res) => {
          ElMessage.success(res.message)
          router.go(-1)
        })
      } else {
        dataResApi.editThematicCompany(data).then((res) => {
          ElMessage.success(res.message)
          router.go(-1)
        })
      }
    } else {
      console.log('error submit!')
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  router.go(-1)
}
const loadData = async () => {
  const res = await dataResApi.getThematicCompanyDetail(currentId.value).then((res) => res.data)
  const processedData = {
    ...res,
    companyImg: res.pictureList?.map((item: any) => item.pictureUrl) ?? [],
    operatingRevenueList: res.operatingRevenueList || [createDefaultProductionItem()],
    netProfit: res.netProfit || [createDefaultProductionItem()],
  }
  showMapView.value = !!res.latitudeLongitude
  Object.assign(ruleForm, { ...ruleForm, ...processedData })
}

const getSysUnit = async () => {
  systemUnitData.value = await DictApI.getDictItemsByCode('systemUnit').then((res) => res.data)
}
const getCompanyTypes = async () => {
  companyTypesData.value = await DictApI.getDictItemsByCode('companyTypes').then((res) => res.data)
}

const getAreaList = async () => {
  areaData.value = await ComApi.getAreaList().then((res) => res.data)
}
onMounted(async () => {
  const route = useRoute()
  currentId.value = (route.query.id as string) || ''
  await Promise.all([getAreaList(), getSysUnit(), getCompanyTypes()])
  if (currentId.value) {
    loadData()
  }
})
</script>

<style lang="scss" scoped>
.noUlli {
  margin: 0;
  padding: 0;
  list-style: none;
}
.title {
  width: 100%;
  opacity: 0.72;
  line-height: 21px;
  padding-left: 10px;
  position: relative;
  margin-bottom: 10px;
  &::before {
    content: '';
    position: absolute;
    height: 14px;
    width: 4px;
    left: 0;
    top: 7px;
    margin-top: -3.5px;
    background-color: hsl(210 100% 63%);
  }
}
.input-with-select .el-input-group__prepend {
  background-color: var(--el-fill-color-blank);
}
</style>
