<template>
  <z-form
    ref="formRef"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
    :show-footer="false"
  >
    <!-- 下载模版 -->
    <template #item-downloadTemplate="{ validate, value, updateValue }">
      <el-button
        type="primary"
        :icon="Download"
        @click="download"
        >下载模版</el-button
      >
    </template>
    <!-- 上传文件 -->
    <template #item-importFile="{ validate, value, updateValue }">
      <z-upload
        class="w-140!"
        :show-file-info="true"
        :allowed-types="[FileType.OTHER]"
        tip="支持格式：xlsx、xls"
        :file-type-map="{
          other: ['xlsx', 'xls']
        }"
        :limit="1"
        :show-preview="false"
        @success="
          (r, file, newFileList) => handleUploadSuccess(r, { validate, updateValue }, newFileList, file)
        "
      >
      </z-upload>
    </template>
  </z-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import dataResApi from '@/api/dataResources'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { Download } from '@element-plus/icons-vue'
import { FileType } from '@/ui/components/ZUpload/constants'

const { onOpen } = useDialogFormContext()

const formRef = ref<ZFormInstance>()

const form = reactive({})
const fields = ref<ZFormField[]>([
  {
    name: 'downloadTemplate',
    label: '下载模版',
    type: 'custom',
  },
  {
    name: 'importFile',
    label: '上传文件',
    type: 'custom',
  },
])
const rawFields = ref<any[]>([])

const handleUploadSuccess = (r: any, { validate, updateValue }: any, newFileList: any[], file: any) => {
  updateValue(file)
  validate()
}
const download = async () => {
  try {
    const res = await dataResApi.downloadTemplateFile('XLSX')
  } catch (error) {}
}
defineExpose({
  getFields: () => rawFields.value,
  resetForms: () => {
    formRef.value?.resetFields()
    formRef.value?.clearCache()
  },
})
</script>
