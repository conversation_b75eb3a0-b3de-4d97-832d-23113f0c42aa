<template>
  <div class="w-full h-[400px] relative">
    <TMapView
      ref="mapRef"
      :options="mapOptions"
      @ready="onMapReady"
    />
    <div class="map-controls">
      <el-button-group class="mark-controls">
        <template v-if="props.locationType === '1'">
          <div class="flex gap-2">
            <el-input
              v-model="point"
              style="width: 380px"
              :class="{ 'invalid-input': !isValid }"
              placeholder="请输入经纬度（如：107.237743,34.363184）"
            >
              <template #append>
                <el-button type="primary" @click="handleConfirm"> 确 认 </el-button>
              </template>
            </el-input>
            <el-button
              @click="toggleMarkTool"
              :type="markToolActive ? 'primary' : ''"
              :icon="Location"
            >
              {{ markToolActive ? '停止标点' : '开始标点' }}
            </el-button>
          </div>
        </template>
        <el-button
          v-else-if="props.locationType === '2'"
          @click="togglePolylineTool"
          :type="polylineToolActive ? 'primary' : ''"
          :icon="Connection"
        >
          {{ polylineToolActive ? '停止绘制线' : '绘制线' }}
        </el-button>
        <el-button
          v-else
          @click="togglePolygonTool"
          :type="polygonToolActive ? 'primary' : ''"
          :icon="Edit"
        >
          {{ polygonToolActive ? '停止绘制面' : '绘制面' }}
        </el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { LocationType } from '@/utils/enums'
import { Location, Edit, Connection } from '@element-plus/icons-vue'
import type { TOverlayLayer } from '@/map/core/layer/TOverlayLayer'
import { TMapView, TMap, TSearchType, TPolygonTool, TPolylineTool } from '@/map'
import type { TLayerType, TNativeMap, TPointMarkerTool } from '@/map'

const emit = defineEmits(['setName', 'setPosition', 'setPoint'])

const props = defineProps<{
  locationType: LocationType
  position?: string
  locationName?: string
}>()

// 添加自定义图层的引用
const customLayer = ref<TOverlayLayer | null>(null)

const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const markToolActive = ref(false)
const markersCount = ref(0)
const isdrew = ref(false)

const markTool = ref<TPointMarkerTool | null>(null)
const polygonTool = ref<TPolygonTool | null>(null)
const polylineTool = ref<TPolylineTool | null>(null)
const polygonToolActive = ref(false)
const polylineToolActive = ref(false)
const point = ref('')
const isValid = ref(true)

const mapOptions = {
  center: {
    lng: 107.237743,
    lat: 34.363184,
  },
  zoom: 8,
  minZoom: 3,
  maxZoom: 18,
  search: {
    show: true,
    type: TSearchType.VIEWPORT,
    placeholder: '搜索地点或地址',
    timeout: 15000,
    maxResults: 20,
  },
}

const validateCoordinates = (input: string) => {
  // 正则匹配：经度（-180 到 180），纬度（-90 到 90），用逗号分隔
  const regex = /^[-+]?(0?|[1-9]\d?|1[0-7]\d|180)(\.\d+)?,\s*[-+]?([0-8]?\d|90)(\.\d+)?$/;
  return regex.test(input);
};

const handleConfirm = () => {
  isValid.value = validateCoordinates(point.value);
  if (isValid.value) {
    removePointLayer()
    const [lng, lat] = point.value.split(',').map(Number);
    markTool.value?.addMarkerAt([lng, lat])
    isdrew.value = true
  } else {
    ElMessage.error('请输入正确的经纬度格式！');
  }
};
// 处理线的坐标集合
const convertCoordinates = (coordinateArray: any[]): any[] => {
  return coordinateArray.map((point) => [point.lng, point.lat])
}

// 删除点
const removePointLayer = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  if (customLayer.value) {
    map.removeOverlayLayer('regression-layer')
    customLayer.value = null
  } else {
    if (isdrew.value) {
      isdrew.value = false
      markTool.value?.clear()
    }
  }
}
// 删除线
const removePolylineLayer = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  if (customLayer.value) {
    map.removeOverlayLayer('regression-layer')
    customLayer.value = null
  } else {
    if (isdrew.value) {
      isdrew.value = false
      polylineTool.value?.clear()
    }
  }
}
// 删除面
const removePolygonLayer = () => {
  const map = mapRef.value?.getMap()
  if (!map) return
  if (customLayer.value) {
    map.removeOverlayLayer('regression-layer')
    customLayer.value = null
  } else {
    if (isdrew.value) {
      isdrew.value = false
      polygonTool.value?.clear()
    }
  }
}
// 在操作标注工具时更新状态
const toggleMarkTool = () => {
  if (markTool.value) {
    const result = markTool.value.toggle()
    markToolActive.value = result
    if (result) {
      removePointLayer()
      ElMessage.info('请点击地图添加标注')
    }
  }
}

// 多边形工具控制
const togglePolygonTool = () => {
  if (!polygonTool.value) return
  if (polygonToolActive.value) {
    polygonTool.value.stop()
  } else {
    polygonTool.value.start()
  }
  removePolygonLayer()
  polygonToolActive.value = !polygonToolActive.value
}

// 折线工具控制
const togglePolylineTool = () => {
  if (!polylineTool.value) return
  if (polylineToolActive.value) {
    polylineTool.value.stop()
  } else {
    polylineTool.value.start()
  }
  removePolylineLayer()
  polylineToolActive.value = !polylineToolActive.value
}

// 添加点
const addPoint = async (position: any[]) => {
  if (!mapRef.value) return
  const map = mapRef.value?.getMap()

  if (!map) return

  if (!customLayer.value) {
    customLayer.value = map.createOverlayLayer({ id: 'regression-layer' })
  }

  customLayer.value.createSvgOverlay({
    userData: 'point',
    iconName: 'marker-ly-2',
    position: [position[0], position[1]],
    label: props.locationName,
    iconStyle: {
      color: 'red',
    },
  })
  map.setCenter(
    {
      lng: position[0],
      lat: position[1],
    },
    15,
  )
}

const addPolyline = (position: any[]) => {
  if (!mapRef.value) return

  const map = mapRef.value?.getMap()
  if (!map) return

  if (!customLayer.value) {
    customLayer.value = map.createOverlayLayer({ id: 'regression-layer' })
  }
  customLayer.value.createPolyline({
    userData: 'polyline',
    path: position,
    color: '#ff4d4f',
    weight: 4,
    opacity: 1,
    lineStyle: 'solid',
    label: props.locationName,
  })
}

const addPolygon = (position: any[]) => {
  if (!mapRef.value) return

  const map = mapRef.value?.getMap()
  if (!map) return

  if (!customLayer.value) {
    customLayer.value = map.createOverlayLayer({ id: 'regression-layer' })
  }
  customLayer.value.createPolygon({
    userData: 'polygon',
    path: position,
    color: '#ff4d4f',
    weight: 4,
    opacity: 1,
    lineStyle: 'solid',
    label: props.locationName,
  })
}
// 当前底图类型
const currentLayer = ref<TLayerType>('vec')

// 获取地图实例
const nativeMap = ref<TNativeMap>()

const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')
  currentLayer.value = 'img'
  nativeMap.value = map.getNativeMap()

  // 在地图准备好后初始化标注工具
  markTool.value = map.createPointMarkerTool({
    iconName: 'marker-ly-2',
    enableGeocoding: true,
    autoSetAddress: true,
    updateAddressOnDrag: true,
    iconStyle: {
      color: 'red',
    },
    onMark: (position) => {
      // emit('setPosition', position)
      isdrew.value = true
      markersCount.value = markTool.value?.getMarkers().length ?? 0
    },
    onComplete: (markers) => {
      markToolActive.value = false
      markersCount.value = markers.length
    },
    onGeocodeComplete(marker: any, address, detail) {
      emit('setPoint', {
        position: marker.options.position,
        name: address
      })
      // emit('setName', address)
    },
  })

  // 初始化多边形工具
  polygonTool.value = map.createPolygonTool({
    color: '#1890ff',
    weight: 2,
    opacity: 1,
    fillColor: '#1890ff',
    fillOpacity: 0.3,
    lineStyle: 'solid',
    showArea: true,
    onComplete(polygons) {
      polygonToolActive.value = false
      const position = polygons.map((polygon) => {
        return polygon
          .getLngLats()
          .flat()
          .map((point) => [point.lng, point.lat])
      })
      isdrew.value = true
      emit('setPosition', ...position)
    },
  })

  // 初始化折线工具
  polylineTool.value = map.createPolylineTool({
    color: '#ff4d4f',
    weight: 3,
    opacity: 1,
    lineStyle: 'solid',
    showDistance: true,
    onComplete(polylines) {
      polylineToolActive.value = false
      const position = polylines.map((polyline) => polyline.getLngLats())
      const lines = convertCoordinates(position[0])
      isdrew.value = true
      emit('setPosition', lines)
    },
  })
  // 回显点
  if (props.locationType === '1' && props.position) {
    const position = JSON.parse(props.position)
    const point = [position[0], position[1]]
    addPoint(point)
  }
  // 回显线
  if (props.locationType === '2' && props.position) {
    const position = JSON.parse(props.position)
    addPolyline(position)
  }
  // 回显面
  if (props.locationType === '3' && props.position) {
    const position = JSON.parse(props.position)
    addPolygon(position)
  }
}

const type = computed(() => props.locationType)

watch(
  type,
  (value, pre) => {
    if (mapRef.value?.getMap()) {
      if (pre === '1') {
        removePointLayer()
      }
      if (pre === '2') {
        removePolylineLayer()
      }
      if (pre === '3') {
        removePolygonLayer()
      }
      polygonToolActive.value = false
      polylineToolActive.value = false
      markToolActive.value = false
    }
  },
  { immediate: false },
)
</script>
<style lang="scss" scoped>
.map-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
