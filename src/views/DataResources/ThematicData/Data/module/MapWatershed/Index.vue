<template>
  <z-page class="pt-2">
    <z-table
      ref="tableRef"
      row-key="thematicFeatureDataId"
      :auto-load="false"
      v-model:query="query"
      :columns="columns"
      :toolbar-rows="toolbarRows"
      :fetch-data="fetchTableData"
      show-toolbar
      selectable
      pagination-mode="server"
    >
      <template #toolbar-item-slotAdd="scope">
        <z-dynamic-route
          :route="{
            name: '新增一张图',
            path: 'createview',
            viewPath: '@/views/DataResources/ThematicData/Data/module/MapWatershed/EditData.vue'
          }"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              plain
              :icon="Plus"
              @click="
                () =>
                  navigate()
              "
            >
              新增
            </el-button>
          </template>
        </z-dynamic-route>
      </template>
      <template #thematicClassId="{ row }">
        <span>{{ formatThematicClassName(row.dataClassManageLists) }}</span>
      </template>

      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            name: '编辑一张图',
            path: 'editview',
            viewPath: '@/views/DataResources/ThematicData/Data/module/MapWatershed/EditData.vue'
          }"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              link
              @click="
                () =>
                  navigate({
                    query: {
                      id: row.thematicFeatureDataId,
                    },
                  })
              "
            >
              编辑
            </el-button>
          </template>
        </z-dynamic-route>
        <el-popconfirm
          :title="t('message.confirmDelete')"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      title="导入数据"
      :show-footer="true"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <ImportForm
        ref="importFormRef"
        :data-id="dialog.model.value?.dataId"
        :mode="dialog.mode.value"
        :parent-id="dialog.model.value?.parentId"
      />
    </z-dialog-form>
  </z-page>
</template>
<script setup lang="ts">
import ComApi from '@/api/common'
import dataResApi from '@/api/dataResources'
import { onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { useDialog } from '@/composables/useDialog'
import { ElButton, ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type {
  ZTableColumn,
  ZTableInstance,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types.ts'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'
import ImportForm from './ImportForm.vue'

const tableRef = ref<ZTableInstance>()
const importFormRef = ref<InstanceType<typeof ImportForm> | null>(null)
const query = ref()
const { t } = useTypedI18n()

const columns: ZTableColumn[] = [
  { prop: 'thematicFeatureDataName', label: '名称', width: 180 },
  { prop: 'areaName', label: '所属区域' },
  { prop: 'thematicClassId', label: '所属分类' },
  { prop: 'locationName', label: '地址位置' },
  { prop: 'operation', label: '操作', align: 'center', width: 120, fixed: 'right' },
]

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        key: 'thematicFeatureDataName',
        type: 'input',
        label: '名称',
        placeholder: '请输入名称',
        width: 210,
      },
      {
        key: 'areaCode',
        type: 'select',
        label: '所属区域',
        placeholder: '请选择区域',
        options: () =>
          ComApi.getAreaList().then((res) =>
            res.data.map((it: any) => ({
              label: it.areaName,
              value: it.areaCode,
            })),
          ),
        width: 210,
      },
      {
        key: 'thematicClassId',
        type: 'tree-select',
        label: '所属分类',
        placeholder: '请选择分类',
        valueKey: 'thematicClassId',
        selectProps: {
          label: 'thematicClassName',
          value: 'thematicClassId',
          children: 'children',
        },
        options: () => dataResApi.getCategoryData().then((res) => res.data),
        width: 210,
      },
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          contentAlign: 'space-between',
          tools: [ZTableToolbarFactory.createSearch(), ZTableToolbarFactory.createReset()],
        },
      },
    ],
  },
  {
    tools: [
      {
        key: 'slotAdd',
        type: 'button',
        label: '添加',
        buttonType: 'primary',
      },
      ZTableToolbarFactory.createImport({
        onClick: () => dialog.open('create'),
      }),
      ZTableToolbarFactory.createExport({
        onClick: async ({ query, selectedRows }) => {
          try {
            const selectSelection = selectedRows || []
            let data: {
              areaCode: string
              thematicClassId: string
              thematicFeatureDataName: string
              thematicFeatureDataIds?: any[]
            } = {
              areaCode: query?.areaCode || '',
              thematicClassId: query?.thematicClassId || '',
              thematicFeatureDataName: query?.thematicFeatureDataName || '',
            }
            if (selectSelection && selectSelection.length) {
              data.thematicFeatureDataIds = selectSelection.map((i) => i.thematicFeatureDataId)
            }
            await dataResApi.exportThematicData(data)
          } catch (error) {
            ElMessage.error('导出失败')
          }
        },
      }),
      ZTableToolbarFactory.createDelete({
        disabled: () => {
          const selectSelection = tableRef.value?.getSelection()
          if (selectSelection && selectSelection.length > 0) {
            return false
          } else {
            return true
          }
        },
        onClick: () => {
          const selectSelection = tableRef.value?.getSelection()
          if (selectSelection && selectSelection.length > 0) {
            const result = selectSelection.reduce((str, item, index) => {
              return str + (index > 0 ? ', ' : '') + item.thematicFeatureDataId
            }, '')
            dataResApi.deleteFeatureDataById(result).then(() => {
              tableRef.value?.refresh()
              ElMessage.success(t('message.deleteSuccess'))
            })
          }
        },
      }),
    ],
  },
]

const getOptionValue = (v: string, options: any[]) => {
  return options.find((item: any) => item === v || item.value === v)
}

const parseValues = (values: Record<string, any>, fields: any[], rawFields: any[]) => {
  return fields.reduce((acc, field) => {
    if (field.type === 'select' || field.type === 'radio') {
      const rawOptions = rawFields.find((it: any) => it.fieldCode === field.name).fieldValue
        .valueList
      const fetchedOptions = field.options
      acc[field.name] = [
        getOptionValue(values[field.name], rawOptions.length ? rawOptions : fetchedOptions),
      ]
    } else {
      acc[field.name] = values[field.name]
    }
    return acc
  }, {} as any)
}

const dialog = useDialog({
  name: '导入数据',
  onConfirm(mode, ctx) {
    if (!ctx.form) return

    const handleSubmit = async (formData: any) => {
      // 保存的逻辑
      const fileData = new FormData()
      fileData.append('file', formData.importFile.raw)
      return dataResApi.importTemplateData(fileData)
    }

    ctx.form.validate((isValid) => {
      if (!isValid) return

      const formData = parseValues(
        ctx.form!.getValues(),
        ctx.form!.getFields(),
        importFormRef.value!.getFields(),
      )
      ctx.isSubmitting.value = true

      handleSubmit(formData)
        ?.then(() => {
          tableRef.value?.refresh()
          dialog.close()
          ElMessage.success(
            mode === 'create' ? t('message.saveSuccess') : t('message.updateSuccess'),
          )
        })
        .finally(() => {
          ctx.isSubmitting.value = false
          importFormRef.value?.resetForms()
        })
    })
  },
})
const formatThematicClassName = (data: any[]): string => {
  if (!data || !data.length) return '-'
  const result = data.reduce((str, item, index) => {
    return str + (index > 0 ? ', ' : '') + item.thematicClassName
  }, '')
  return result
}

const fetchTableData = async (params: ZTableParams) => {
  return dataResApi.getThematicFeatureData({ ...params }).then((res) => res.data)
}

const onDelete = (row: any) => {
  dataResApi.deleteFeatureDataById(row.thematicFeatureDataId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

onMounted(() => {
  tableRef.value?.refresh()
})
</script>
