<template>
  <z-page
    :stretch="true"
    class="w-full h-full flex p-0 bg-[#F0F2F5] gap-2"
  >
    <div class="w-[180px]! bg-white p-2 rounded-md">
      <el-tree
        style="width: 180px !important"
        node-key="value"
        :current-node-key="currentClassify"
        :data="classifyData"
        :props="{
          children: 'children',
          label: 'label',
        }"
        :expand-on-click-node="false"
        :highlight-current="true"
        default-expand-all
        @node-click="handleNodeClick"
      />
    </div>
    <div
      class="bg-white p-2 rounded-md"
      style="width: calc(100% - 200px)"
    >
      <Suspense>
        <template #default>
          <component
            :is="currentComponent"
            :key="currentClassify"
          />
        </template>
        <template #fallback>
          <div class="loading">组件加载中...</div>
        </template>
      </Suspense>
    </div>
  </z-page>
</template>

<script lang="ts" setup>
import type { Component } from 'vue'
import { ref, defineAsyncComponent, onMounted } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { useCommonStroe } from '@/stores/common'

type ComponentTypes =
  | 'MapWatershed'
  | 'Enterprise'
  | 'FeaturedProducts'
  | 'IndustryChain'
  | 'KeyProjects'

interface Tree {
  label: string
  value: ComponentTypes
}

const { getAreaList } = useCommonStroe()

const currentClassify = ref<ComponentTypes>('MapWatershed')
const currentComponent = ref()

const classifyData = ref<Tree[]>([
  {
    label: '流域一张图',
    value: 'MapWatershed',
  },
  {
    label: '特色产品',
    value: 'FeaturedProducts',
  },
  {
    label: '企业',
    value: 'Enterprise',
  },
  {
    label: '产业链',
    value: 'IndustryChain',
  },
  {
    label: '重点项目',
    value: 'KeyProjects',
  },
])

// 预先加载所有可能的组件
const componentModules: Record<string, () => Promise<Component>> = import.meta.glob(
  './module/**/Index.vue',
)

const loadComponent = (name: ComponentTypes) => {
  const path = `./module/${name}/Index.vue`
  if (componentModules[path]) {
    currentComponent.value = defineAsyncComponent(componentModules[path])
    currentClassify.value = name
  } else {
    console.error(`Component not found: ${path}`)
    // 可以设置一个默认组件或错误组件
  }
}

const handleNodeClick = (row: Tree) => {
  if (row.value !== currentClassify.value) {
    loadComponent(row.value)
  }
}
// 初始化加载默认组件
loadComponent(currentClassify.value)

onMounted(() => {
  console.log(1)
  getAreaList()
})
onBeforeRouteLeave((to, from) => {
  console.log(to, '----------to')
  console.log(from, '----------from')
  const isFromChildRoute = from.matched.some((record) =>
    to.matched.some((parentRecord) => parentRecord.path === record.path),
  )

  if (isFromChildRoute) {
    console.log('离开当前路由，跳转来自子路由')
  }
})
</script>

<style scoped>
.loading {
  padding: 20px;
  text-align: center;
  color: #666;
}
</style>
