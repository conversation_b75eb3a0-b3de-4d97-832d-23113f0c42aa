<template>
  <z-form
    ref="formRef"
    v-model:model="form"
    :fields="fields"
    :validate-on-rule-change="false"
    label-width="120px"
    :fetch-data="fetchData"
  >
  </z-form>
</template>

<script setup lang="ts">
import dataResApi from '@/api/dataResources'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { reactive, ref } from 'vue'
import { chartTypeOptions } from '@/views/Screen/utils/option'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context.ts'
const props = defineProps<{
  dataId: any
  mode?: 'create' | 'edit' | string
}>()
const formRef = ref<ZFormInstance>()

const initValue = {
  chartId: props.dataId || 0,
  baseList: [],
}

const form = reactive(initValue)
const fields = ref<ZFormField[]>([
  {
    name: 'chartId',
    type: 'input',
    show: false,
  },
  {
    name: 'thematicId',
    label: '关联专题',
    type: 'tree-select',
    props: {
      checkStrictly: false,
      placeholder: '请选择关联专题',
    },
    valueKey: 'thematicId',
    treeProps: {
      label: 'thematicName',
      children: 'children',
    },
    rules: [
      {
        required: true,
        message: '请选择上级专题',
        trigger: 'change',
      },
    ],
    noCache: true,
    treeOptions: async (context) => {
      const children = await dataResApi.getFeatureTree().then((res) => res.data)
      return children
    },
  },
  {
    name: 'classId',
    label: '指标分类',
    type: 'tree-select',
    props: {
      clearable: true,
      checkStrictly: false,
      placeholder: '请选择指标分类',
    },
    valueKey: 'classId',
    treeProps: {
      label: 'className',
      children: 'children',
    },
    rules: [
      {
        required: false,
        message: '请选择指标分类',
        trigger: 'change',
      },
    ],

    treeOptions: async (context) => {
      const children = await dataResApi.getNormClassifyData().then((res) => res.data)
      return children
    },
  },
  {
    name: 'baseList',
    label: '选择指标',
    type: 'checkbox',
    show: (context) => {
      if (context.model.classId) {
        return true
      }
      return false
    },
    rules: [
      {
        required: true,
        message: '请选择指标',
        trigger: ['change', 'blur'],
      },
    ],
    options: async (context) => {
      if (context.model.classId) {
        const children = await dataResApi
          .getNormLibraryDataAll({ classId: context.model.classId })
          .then((res) =>
            res.data.map((item: any) => {
              return {
                label: item.indexName,
                value: item.baseId,
              }
            }),
          )
        return children
      } else {
        return []
      }
    },
    noCache: true,
    dependencies: {
      fields: ['classId'],
      async handler({ dependencyValues, updateModel, form, reason }) {
        if (!dependencyValues.classId) return
        formRef.value?.refetchOptions('baseList')
        if (reason.type === 'VALUE_CHANGE') {
          if (isRest.value) {
            updateModel([])
          }
          isRest.value = true
          setTimeout(() => {
            form.clearValidate('baseList')
          }, 0)
        }
      },
    },
  },
  {
    name: 'chartType',
    label: '图表类型',
    type: 'select',
    rules: [
      {
        required: true,
        message: '请选择图表类型',
        trigger: 'change',
      },
    ],
    options: chartTypeOptions,
  },
  {
    name: 'chartSort',
    label: '排序',
    type: 'number',
    rules: [
      {
        required: true,
        message: '请输入正整数',
        trigger: 'change',
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!/^[1-9]\d*$/.test(value)) {
            callback(new Error('请输入正整数'))
          } else {
            callback()
          }
        },
        trigger: 'change',
      },
    ],
    props: {
      placeholder: '请选择',
      min: 1,
    },
  },
])

const rawFields = ref<any[]>([])
const fetchData = () =>
  dataResApi.getIndexChartDetail(props.dataId).then((res) => {
    const o = (fields.value ?? []).reduce((acc, { type, name }) => {
      if (name === 'baseList') {
        acc[name] = res.data[name].map((item: any) => item.baseId)
        return acc
      }
      acc[name] = res.data[name]
      return acc
    }, {} as any)
    console.log(o, 'o')
    return o
  })

defineExpose({
  getFields: () => rawFields.value,
  resetForms: () => {
    formRef.value?.resetFields()
    formRef.value?.clearCache()
  },
})
const isRest = ref(false)

const { onOpen } = useDialogFormContext()
onOpen(() => {
  if (props.dataId && props.mode === 'edit') {
    formRef.value?.refetch()
  }
})
</script>
