<template>
  <z-page>
    <z-table
      ref="tableRef"
      :auto-load="false"
      row-key="chartId"
      pagination-mode="server"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #indexName="{ row }">
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="row.indexName"
          placement="top-start"
        >
          <p class="w-full whitespace-nowrap overflow-hidden text-ellipsis">{{ row.indexName }}</p>
        </el-tooltip>
      </template>
      <template #chartType="{ row }">
        <span>
          {{ formatChartType(row.chartType) }}
        </span>
      </template>
      <template #chartStatus="{ row }">
        <el-switch
          :model-value="row.chartStatus"
          active-value="0"
          inactive-value="1"
          @change="() => statusChange(row, row.chartStatus)"
        />
      </template>
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          @click="onEdit(row)"
          link
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          title="确定删除该图表组吗？"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
            >
              {{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <ChartGroupForm
        ref="normFormRef"
        :data-id="dialog.model.value?.dataId"
        :mode="dialog.mode.value"
      />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import type {
  ZTableInstance,
  ZTableColumn,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types'
import type { ComputedRef } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { useDialog } from '@/composables/useDialog'
import ChartGroupForm from './ChartGroupForm.vue'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'
import dataResApi from '@/api/dataResources'
import { chartTypeOptions } from '@/views/Screen/utils/option'
const tableRef = ref<ZTableInstance | null>(null)
const normFormRef = ref<InstanceType<typeof ChartGroupForm> | null>(null)

const { t } = useTypedI18n()
const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'thematicName', label: '关联专题' },
  {
    prop: 'className',
    label: '指标分类',
  },
  {
    prop: 'indexName',
    label: '选择指标',
    width: 320,
  },
  {
    prop: 'chartType',
    label: '图表类型',
  },
  { prop: 'chartSort', label: '排序' },
  {
    prop: 'chartStatus',
    label: '状态',
  },
  { prop: 'operation', label: '操作', align: 'center', width: 120, fixed: 'right' },
])

const toolbarRows: ComputedRef<ZTableToolbarRow[]> = computed(() => [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create'),
      }),
    ],
  },
])

const getOptionValue = (v: string, options: any[]) => {
  return options.find((item: any) => item === v || item.value === v)
}

const statusChange = (row: any, flag: string) => {
  if (!row.chartId) return
  const action = flag == '1' ? false : true
  if (action) {
    dataResApi.disbaledIndexChart(row.chartId).then((res) => {
      tableRef.value?.refresh()
      ElMessage.success(res.message)
    })
  } else {
    dataResApi.enableIndexChart(row.chartId).then((res) => {
      tableRef.value?.refresh()
      ElMessage.success(res.message)
    })
  }
}

const parseValues = (values: Record<string, any>, fields: any[], rawFields: any[]) => {
  return fields.reduce((acc, field) => {
    if (field.type === 'radio') {
      const rawOptions = rawFields.find((it: any) => it.fieldCode === field.name).fieldValue
        .valueList
      const fetchedOptions = field.options
      acc[field.name] = [
        getOptionValue(values[field.name], rawOptions.length ? rawOptions : fetchedOptions),
      ]
    } else {
      acc[field.name] = values[field.name]
    }
    return acc
  }, {} as any)
}

const formatChartType = (value: string) => {
  const optoion = chartTypeOptions.find((option: any) => option.value === value)
  if (optoion) {
    return optoion.label
  }
  return value
}

const fetchTableData = async (params: ZTableParams) => {
  console.log(params, '///////')
  return dataResApi.getChartGroup({ ...params }).then((res) => res.data)
}

const featuredData = ref<any[]>([])
const getFeaturedData = async () => {
  const data = await dataResApi.getNormClassifyData().then((res) => res.data)
  featuredData.value = data
}

const onDelete = (row: any) => {
  dataResApi.deleteIndexChart(row.chartId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const onEdit = (row: any) => {
  dialog.open('edit', { dataId: row.chartId })
}

const dialog = useDialog({
  name: '图表',
  modeMap: { addChild: '新增图表' },
  onConfirm(mode, ctx) {
    if (!ctx.form) return
    const handleSubmit = (formData: any) => {
      const { chartId, baseList, ...rest } = formData
      const list = baseList.map((base: any) => {
        return {
          baseId: base,
        }
      })
      if (mode === 'create') {
        return dataResApi.saveIndexChart({
          ...rest,
          baseList: list,
        })
      } else if (mode === 'edit') {
        return dataResApi.updateIndexChart({
          ...rest,
          baseList: list,
          chartId,
        })
      }
    }

    ctx.form.validate((isValid) => {
      if (!isValid) return
      const values = ctx.form!.getValues()
      if (values.baseList && !values.baseList.length) {
        // ctx.form?.refetchOptions('baseList')
        // console.log(ctx.form?.validateField('baseList'), '/////')
        return
      }

      const formData = parseValues(values, ctx.form!.getFields(), normFormRef.value!.getFields())
      ctx.isSubmitting.value = true

      handleSubmit(formData)
        ?.then(() => {
          normFormRef.value?.resetForms()
          tableRef.value?.refresh()
          dialog.close()
          ElMessage.success(
            mode === 'create' ? t('message.saveSuccess') : t('message.updateSuccess'),
          )
        })
        .finally(() => {
          ctx.isSubmitting.value = false
        })
    })
  },
})

onMounted(async () => {
  getFeaturedData()
  tableRef.value?.refresh()
})
</script>
