<template>
  <z-form
    ref="formRef"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
    <!-- 关联分类 -->
    <template #item-classIds="{ formData, field }">
      <el-tree-select
        v-model="formData[field.name]"
        :data="classData"
        node-key="classId"
        :props="{
          label: 'className',
          children: 'children',
        }"
        multiple
        :render-after-expand="false"
        style="width: 560px"
      />
    </template>
  </z-form>
</template>

<script setup lang="ts">
import dataResApi from '@/api/dataResources'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { reactive, ref } from 'vue'
import DictApI from '@/api/dict'

const formRef = ref<ZFormInstance>()

const initValue = {
  baseId: '',
  indexName: '',
  indexUnit: '',
  classIds: [],
  indexSort: 0,
}

const form = reactive(initValue)
const fields = ref<ZFormField[]>([
  {
    name: 'baseId',
    type: 'input',
    show: false,
  },
  {
    name: 'indexName',
    label: '指标名称',
    type: 'input',
    rules: [
      {
        required: true,
        message: '请输入指标名称',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入指标名称',
      maxlength: 30,
      showWordLimit: true,
      clearable: true
    },
  },
  {
    name: 'indexUnit',
    label: '单位',
    type: 'select',
    props: {
      placeholder: '请选择单位',
    },
    rules: [
      {
        required: true,
        message: '请选择单位',
        trigger: 'change',
      },
    ],
    valueKey: 'dictionaryCode',
    labelKey: 'dictionaryName',
    options: () => DictApI.getDictItemsByCode('systemUnit').then((res) => res.data),
  },
  {
    name: 'classIds',
    label: '关联分类',
    type: 'custom',
    rules: [
      {
        required: true,
        message: '请选择指标分类',
        trigger: 'change',
      },
    ],
  },
  {
    name: 'indexSort',
    label: '排序',
    type: 'number',
    rules: [
      {
        required: true,
        message: '请输入正整数',
        trigger: 'blur',
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!/^[1-9]\d*$/.test(value)) {
            callback(new Error('请输入正整数'))
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请选择',
      min: 1,
    },
  },
])
const classData = ref([])
const getData = async () => {
  classData.value = await dataResApi.getNormClassifyData({ classStatus: 0 }).then((res) => res.data)
}

const rawFields = ref<any[]>([])

defineExpose({
  getFields: () => rawFields.value,
  resetForms: () => {
    formRef.value?.resetFields()
    formRef.value?.clearCache()
  },
})
getData()
</script>
