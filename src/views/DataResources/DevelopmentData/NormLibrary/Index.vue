<template>
  <z-page>
    <z-table
      ref="tableRef"
      :auto-load="false"
      row-key="baseId"
      pagination-mode="server"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #classNames="{ row }">
        <span>{{ formatClassName(row.classNames) }}</span>
      </template>
      <template #indexUnit="{ row }">
        <span>{{ formatIndexUnit(row.indexUnit) }}</span>
      </template>
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          @click="onEdit(row)"
          link
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          title="确认删除该指标？"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
            >
              {{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <NormForm
        ref="normFormRef"
        :model="dialog.model.value"
        :mode="dialog.mode.value"
      />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import type {
  ZTableInstance,
  ZTableColumn,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types'
import type { ComputedRef } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { useDialog } from '@/composables/useDialog'
import NormForm from './NormForm.vue'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'
import dataResApi from '@/api/dataResources'
import DictApI from '@/api/dict'
const tableRef = ref<ZTableInstance | null>(null)
const normFormRef = ref<InstanceType<typeof NormForm> | null>(null)

const { t } = useTypedI18n()
const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'indexName', label: '指标名称' },
  { prop: 'indexUnit', label: '单位' },
  { prop: 'classNames', label: '指标分类' },
  { prop: 'indexSort', label: '排序' },
  { prop: 'operation', label: '操作', align: 'center', width: 160, fixed: 'right' },
])

const toolbarRows: ComputedRef<ZTableToolbarRow[]> = computed(() => [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create'),
      }),
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          tools: [
            {
              key: 'indexName',
              type: 'input',
              label: '指标名称',
              placeholder: '请输入指标名称',
            },
            {
              key: 'classId',
              type: 'tree-select',
              label: '所属分类',
              placeholder: '请选择分类',
              selectProps: {
                label: 'className',
                value: 'classId',
                children: 'children',
              },
              options: () => dataResApi.getNormClassifyData({ classStatus: 0 }).then((res) => res.data),
              width: 240,
            },
            ZTableToolbarFactory.createSearch(),
            ZTableToolbarFactory.createReset(),
          ],
        },
      },
    ],
  },
])

const getOptionValue = (v: string, options: any[]) => {
  return options.find((item: any) => item === v || item.value === v)
}

const parseValues = (values: Record<string, any>, fields: any[], rawFields: any[]) => {
  return fields.reduce((acc, field) => {
    if (field.type === 'radio') {
      const rawOptions = rawFields.find((it: any) => it.fieldCode === field.name).fieldValue
        .valueList
      const fetchedOptions = field.options
      acc[field.name] = [
        getOptionValue(values[field.name], rawOptions.length ? rawOptions : fetchedOptions),
      ]
    } else {
      acc[field.name] = values[field.name]
    }
    return acc
  }, {} as any)
}

const fetchTableData = async (params: ZTableParams) => {
  return dataResApi.gitNormLibraryData({ ...params }).then((res) => res.data)
}

const featuredData = ref<any[]>([])
const getFeaturedData = async () => {
  const data = await dataResApi.getNormClassifyData().then((res) => res.data)
  featuredData.value = data
}

const indexUnitsData = ref<any[]>([])
const getIndexUnitsData = async () => {
  indexUnitsData.value = await DictApI.getDictItemsByCode('systemUnit').then(res => res.data)
}
const onDelete = (row: any) => {
  dataResApi.deleteNormLibrary(row.baseId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const onEdit = (row: any) => {
  dialog.open('edit', row)
}

const formatClassName = (list: Array<string> | null): string => {
  if (list && list.length) {
    return list.join(',')
  }
  return '-'
}

const formatIndexUnit = (code: string): string => {
  const option = indexUnitsData.value.find((unit: any) => unit.dictionaryCode === code)
  if (option) {
    return option.dictionaryName
  }
  return '-'
}

const dialog = useDialog({
  name: '指标',
  modeMap: { addChild: '新增指标' },
  onConfirm(mode, ctx) {
    if (!ctx.form) return
    const handleSubmit = (formData: any) => {
      if (mode === 'create') {
        const { baseId, ...rest } = formData
        return dataResApi.saveNormLibrary({
          ...rest,
        })
      } else if (mode === 'edit') {
        return dataResApi.updaNormLibrary(formData)
      } else if (mode === 'addChild') {
        return dataResApi.saveNormLibrary({
          ...formData,
        })
      }
    }

    ctx.form.validate((isValid) => {
      if (!isValid) return

      const formData = parseValues(
        ctx.form!.getValues(),
        ctx.form!.getFields(),
        normFormRef.value!.getFields(),
      )
      ctx.isSubmitting.value = true

      handleSubmit(formData)
        ?.then(() => {
          normFormRef.value?.resetForms()
          tableRef.value?.refresh()
          dialog.close()
          ElMessage.success(
            mode === 'create' ? t('message.saveSuccess') : t('message.updateSuccess'),
          )
        })
        .finally(() => {
          ctx.isSubmitting.value = false
        })
    })
  },
})

onMounted(async () => {
  getIndexUnitsData()
  getFeaturedData()
  tableRef.value?.refresh()
})
</script>
