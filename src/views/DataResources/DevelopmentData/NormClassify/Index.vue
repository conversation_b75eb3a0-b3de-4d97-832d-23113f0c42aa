<template>
  <z-page>
    <z-table
      ref="tableRef"
      :auto-load="false"
      row-key="classId"
      :use-pagination="false"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #classStatus="{ row }">
        <el-switch
          :model-value="row.classStatus"
          active-value="0"
          inactive-value="1"
          @change="() => statusChange(row, row.classStatus)"
        />
      </template>
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          @click="onAddChild(row)"
          link
        >
          新增子级
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="onEdit(row)"
          link
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          :title="row.children && row.children.length ? '确定删除该分类及其子分类？' : '确定删除该分类？'"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
            >
              {{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <NormClassifyForm
        ref="featuredFormRef"
        :data-id="dialog.model.value?.dataId"
        :mode="dialog.mode.value"
        :edit-type="dialog.model.value?.editType"
        :parent-id="dialog.model.value?.parentId"
      />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import type { ZTableInstance, ZTableColumn, ZTableToolbarRow } from '@/ui/components/ZTable/types'
import type { ComputedRef } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { useDialog } from '@/composables/useDialog'
import NormClassifyForm from './NormClassifyForm.vue'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'

import dataResApi from '@/api/dataResources'

const tableRef = ref<ZTableInstance | null>(null)
const featuredFormRef = ref<InstanceType<typeof NormClassifyForm> | null>(null)

const { t } = useTypedI18n()
const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'className', label: '分类名称' },
  { prop: 'classSort', label: '排序' },
  {
    prop: 'classStatus',
    label: '状态',
  },
  { prop: 'operation', label: '操作', align: 'center', width: 240, fixed: 'right' },
])

const toolbarRows: ComputedRef<ZTableToolbarRow[]> = computed(() => [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create', { parentId: 0, editType: false}),
      }),
    ],
  },
])

const getOptionValue = (v: string, options: any[]) => {
  return options.find((item: any) => item === v || item.value === v)
}

const parseValues = (values: Record<string, any>, fields: any[], rawFields: any[]) => {
  return fields.reduce((acc, field) => {
    if (field.type === 'select' || field.type === 'radio') {
      const rawOptions = rawFields.find((it: any) => it.fieldCode === field.name).fieldValue
        .valueList
      const fetchedOptions = field.options
      acc[field.name] = [
        getOptionValue(values[field.name], rawOptions.length ? rawOptions : fetchedOptions),
      ]
    } else {
      acc[field.name] = values[field.name]
    }
    return acc
  }, {} as any)
}

const fetchTableData = async () => {
  return dataResApi.getNormClassifyData().then((res) => res.data)
}

const onDelete = (row: any) => {
  dataResApi.deleteNormClassify(row.classId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}
const currentEditParentId = ref(0)
const onEdit = (row: any) => {
  const flag = row.parentId == 0 ? false : true
  currentEditParentId.value = row.parentId
  dialog.open('edit', { dataId: row.classId, parentId: !flag ? row.classId : row.parentId, editType: flag})
}

const onAddChild = (row: any) => {
  dialog.open('addChild', { dataId: row.classId, parentId: row.classId, editType: true})
}
const statusChange = (row: any, flag: string) => {
  if (!row.classId) return
  const action = flag == '1' ? false : true
  if (action) {
    dataResApi.disableNormClassify(row.classId).then((res) => {
      tableRef.value?.refresh()
      ElMessage.success(res.message)
    })
  } else {
    dataResApi.enableNormClassify(row.classId).then((res) => {
      tableRef.value?.refresh()
      ElMessage.success(res.message)
    })
  }
}

const dialog = useDialog({
  name: '分类',
  modeMap: { addChild: '新增子级' },
  onConfirm(mode, ctx) {
    if (!ctx.form) return

    const handleSubmit = (formData: any) => {
      if (mode === 'create') {
        const { classId, ...rest } = formData
        return dataResApi.addNormClassify({
          ...rest,
          classStatus: '1',
        })
      } else if (mode === 'edit') {
        const { parentId, ...rest } = formData
        return dataResApi.updateNormClassify({
          ...rest,
          parentId: currentEditParentId.value
        })
      } else if (mode === 'addChild') {
        const { classId, ...rest } = formData
        return dataResApi.addNormClassify({
          ...rest,
          parentId: classId,
          classStatus: '1',
        })
      }
    }

    ctx.form.validate((isValid) => {
      if (!isValid) return

      const formData = parseValues(
        ctx.form!.getValues(),
        ctx.form!.getFields(),
        featuredFormRef.value!.getFields(),
      )
      ctx.isSubmitting.value = true

      handleSubmit(formData)
        ?.then(() => {
          tableRef.value?.refresh()
          featuredFormRef.value?.resetForms()
          dialog.close()
          ElMessage.success(
            mode === 'create' ? t('message.saveSuccess') : t('message.updateSuccess'),
          )
        })
        .finally(() => {
          ctx.isSubmitting.value = false
        })
    })
  },
})

onMounted(async () => {
  tableRef.value?.refresh()
})
</script>
