<template>
  <z-form
    ref="formRef"
    :fetch-data="fetchData"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
  </z-form>
</template>

<script setup lang="ts">
import dataResApi from '@/api/dataResources'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { reactive, ref } from 'vue'

const { onOpen } = useDialogFormContext()
const props = defineProps<{
  dataId: any,
  parentId: any,
  editType: boolean
  mode?: 'create' | 'edit' | 'addChild' | string
}>()
const formRef = ref<ZFormInstance>()

const initValue = {
  parentId: props.parentId || 0,
  classId: props.dataId || 0,
  classStatus: 1
}

const form = reactive(initValue)
const fields = ref<ZFormField[]>([
  {
    name: 'classId',
    type: 'input',
    show: false,
  },
  {
    name: 'classStatus',
    type: 'input',
    show: false,
  },
  {
    name: 'parentId',
    label: '上级分类',
    type: 'tree-select',
    props: {
      checkStrictly: true,
      placeholder: '请选择上级分类',
    },

    show: props.dataId && props.editType ? true : false,
    disabled: props.dataId ? true : false,
    valueKey: 'classId',
    treeProps: {
      label: 'className',
      children: 'children',
    },
    rules: [
      {
        required: true,
        message: '请选择上级分类',
        trigger: 'change',
      },
    ],
    noCache: false,
    treeOptions: async (context) => {
      if (String(context.model.classId) != '0') {
        console.log(context.model.classId, 'ontext.model.classId')
        const children = await dataResApi.getNormClassifyData().then((res) => res.data)
        return children
      } else {
        return []
      }
    },
  },
  {
    name: 'className',
    label: '分类名称',
    type: 'input',
    rules: [
      {
        required: true,
        message: '请输入分类名称',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入分类名称',
      maxlength: 20,
      showWordLimit: true,
      clearable: true
    },
  },
  {
    name: 'classSort',
    label: '排序',
    type: 'number',
    rules: [
      {
        required: true,
        message: '请输入正整数',
        trigger: 'blur',
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!/^[1-9]\d*$/.test(value)) {
            callback(new Error('请输入正整数'))
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请选择',
      min: 1
    },
  },
])
const rawFields = ref<any[]>([])

const fetchData = () =>
  dataResApi.getNormClassifyDetaile(props.dataId).then((res) => {
    const o = (fields.value ?? []).reduce((acc, { type, name }) => {
      if (name === 'parentId') {
        acc[name] = props.parentId
        return acc
      }
      acc[name] = res.data[name]
      return acc
    }, {} as any)
    return o
  })

defineExpose({
  getFields: () => rawFields.value,
  resetForms:() => {
    formRef.value?.resetFields()
    formRef.value?.clearCache()
  },
})

onOpen(() => {
  if (props.dataId && props.mode === 'edit') {
    if (props.dataId) {
      formRef.value?.refetch()
    }
  }
})
</script>
