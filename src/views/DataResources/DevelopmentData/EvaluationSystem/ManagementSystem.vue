<template>
  <z-page>
    <z-table
      ref="tableRef"
      :auto-load="false"
      row-key="evaluateId"
      :use-pagination="false"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #evaluateStatus="{ row }">
        <el-switch
          :model-value="row.evaluateStatus"
          active-value="0"
          inactive-value="1"
          @change="() => statusChange(row)"
        />
      </template>
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          @click="onAddChild(row)"
          v-if="row.evaluateLevel - 1 != Number(currentLevel)"
          link
        >
          新增子级
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="onEdit(row)"
          link
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          :title="
            row.children && row.children.length
              ? '确定删除该指标体系及其子指标体系吗？'
              : '确定删除该指标体系吗？'
          "
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
            >
              {{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <ManagementSystemForm
        ref="managementFormRef"
        :data-id="dialog.model.value?.dataId"
        :mode="dialog.mode.value"
        :edit-type="dialog.model.value?.editType"
        :parent-id="dialog.model.value?.parentId"
        :currentId="currentId"
        :lastLevel="dialog.model.value?.lastLevel"
      />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import type {
  ZTableInstance,
  ZTableColumn,
  ZTableToolbarRow,
  ZTableParams,
} from '@/ui/components/ZTable/types'
import type { ComputedRef } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import { useDialog } from '@/composables/useDialog'
import ManagementSystemForm from './ManagementSystemForm.vue'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'

import dataResApi from '@/api/dataResources'

const tableRef = ref<ZTableInstance | null>(null)
const managementFormRef = ref<InstanceType<typeof ManagementSystemForm> | null>(null)

const currentId = ref<string>('')
const currentLevel = ref<string>('')

const { t } = useTypedI18n()
const query = ref()

const columns = ref<ZTableColumn[]>([
  { prop: 'evaluateName', label: '名称' },
  { prop: 'evaluateWeight', label: '权重' },
  { prop: 'evaluateStatus', label: '状态' },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
])

const toolbarRows: ComputedRef<ZTableToolbarRow[]> = computed(() => [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create', { parentId: 1, editType: false }),
      }),
    ],
  },
])

const parseValues = (values: Record<string, any>, fields: any[], rawFields: any[]) => {
  return fields.reduce((acc, field) => {
    acc[field.name] = values[field.name]
    return acc
  }, {} as any)
}

const fetchTableData = async () => {
  return dataResApi.getEvaluateSystemTree({ evaluateId: currentId.value }).then((res) => res.data)
}

const onDelete = (row: any) => {
  dataResApi.deleteEvaluate(row.evaluateId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}
const currentEditParentId = ref(0)
const onEdit = (row: any) => {
  const flag = row.evaluateLevel == 2 ? true : false
  const lastLevel = row.evaluateLevel - 1 == Number(currentLevel.value) ? true : false
  currentEditParentId.value = row.parentId
  dialog.open('edit', {
    dataId: row.evaluateId,
    parentId: flag ? row.evaluateId : row.parentId,
    editType: !flag,
    lastLevel
  })
}

const onAddChild = (row: any) => {
  const lastLevel = row.evaluateLevel == Number(currentLevel.value) ? true : false
  dialog.open('addChild', {
    dataId: row.evaluateId,
    parentId: row.evaluateId,
    editType: true,
    lastLevel: lastLevel,
  })
}
const statusChange = (row: any) => {
  if (!row.evaluateId || !row.evaluateStatus) return
  dataResApi.changeEvaluateSystemStatus(row.evaluateId).then((res) => {
    tableRef.value?.refresh()
    ElMessage.success(res.message)
  })
}

const dialog = useDialog({
  name: '指标体系',
  modeMap: { addChild: '新增子级' },
  onConfirm(mode, ctx) {
    if (!ctx.form) return

    const handleSubmit = (formData: any) => {
      if (mode === 'create') {
        const { evaluateId, ...rest } = formData
        return dataResApi.addEvaluateSystem({
          ...rest,
          parentId: currentId.value,
        })
      } else if (mode === 'edit') {
        const { parentId, ...rest } = formData
        return dataResApi.editEvaluateSystem({
          ...rest,
          parentId: currentEditParentId.value,
        })
      } else if (mode === 'addChild') {
        const { evaluateId, ...rest } = formData
        return dataResApi.addEvaluateSystem({
          ...rest,
          parentId: evaluateId,
        })
      }
    }

    ctx.form.validate((isValid) => {
      if (!isValid) return

      const formData = parseValues(
        ctx.form!.getValues(),
        ctx.form!.getFields(),
        managementFormRef.value!.getFields(),
      )
      ctx.isSubmitting.value = true

      handleSubmit(formData)
        ?.then(() => {
          tableRef.value?.refresh()
          managementFormRef.value?.resetForms()
          dialog.close()
          ElMessage.success(
            mode === 'create' ? t('message.saveSuccess') : t('message.updateSuccess'),
          )
        })
        .finally(() => {
          ctx.isSubmitting.value = false
        })
    })
  },
})

onMounted(async () => {
  const route = useRoute()
  currentId.value = (route.query.id as string) || ''
  currentLevel.value = (route.query.level as string) || ''
  if (currentId.value) {
    tableRef.value?.refresh()
  }
})
</script>
