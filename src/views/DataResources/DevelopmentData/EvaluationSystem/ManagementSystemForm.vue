<template>
  <z-form
    ref="formRef"
    :fetch-data="fetchData"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
  </z-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import DictApI from '@/api/dict'
import HighApi from '@/api/highQuality'
import dataResApi from '@/api/dataResources'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'

const { onOpen } = useDialogFormContext()
const props = defineProps<{
  dataId: any
  parentId: any
  editType: boolean
  currentId: string
  lastLevel: boolean
  mode?: 'create' | 'edit' | 'addChild' | string
}>()
const formRef = ref<ZFormInstance>()

const initValue = {
  parentId: props.parentId || 1,
  evaluateId: props.dataId || 0,
}

const form = reactive(initValue)
const fields = ref<ZFormField[]>([
  {
    name: 'evaluateId',
    type: 'input',
    show: false,
  },
  {
    name: 'parentId',
    label: '上级指标',
    type: 'tree-select',
    props: {
      checkStrictly: true,
      placeholder: '请选择上级指标',
    },

    show: props.dataId && props.editType ? true : false,
    disabled: props.dataId ? true : false,
    valueKey: 'evaluateId',
    treeProps: {
      label: 'evaluateName',
      children: 'children',
    },
    rules: [
      {
        required: true,
        message: '请选择上级指标',
        trigger: 'change',
      },
    ],
    noCache: false,
    treeOptions: async (context) => {
      if (String(context.model.evaluateId) != '0') {
        const children = await dataResApi
          .getEvaluateSystemTree({ evaluateId: props.currentId })
          .then((res) => res.data)
        return children
      } else {
        return []
      }
    },
  },
  {
    name: 'evaluateName',
    label: '名称',
    type: 'input',
    rules: [
      {
        required: true,
        message: '请输入指标体系名称',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入指标体系名称',
      maxlength: 50,
      showWordLimit: true,
      clearable: true,
    },
  },
  {
    name: 'evaluateWeight',
    label: '权重',
    type: 'number',
    show: !props.editType ? true : false,
    rules: [
      {
        required: true,
        message: '同级指标权重之和必须=100%',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入',
      min: 1,
      max: 100,
      precision: 2,
    },
  },
  {
    name: 'indexId',
    label: '选取指标',
    type: 'select',
    show: props.dataId && props.lastLevel && props.editType ? true : false,
    rules: [
      {
        required: props.lastLevel ? true : false,
        trigger: 'change',
        message: '请选择指标',
      },
    ],
    props: {
      placeholder: '请选择指标',
      clearable: true,
    },
    valueKey: 'baseId',
    labelKey: 'indexName',
    options: async () => {
      const data = await dataResApi.getNormLibraryDataAll().then((res) => res.data)
      return data
    },
  },
  {
    name: 'indexProperty',
    label: '指标属性',
    type: 'input',
    show: props.dataId && props.lastLevel && props.editType ? true : false,
    props: {
      placeholder: '请输入指标属性',
      clearable: true,
    },
  },
  {
    name: 'evaluateTarget',
    label: '目标值',
    type: 'input',
    show: props.dataId && props.lastLevel && props.editType ? true : false,
    props: {
      placeholder: '请输入目标值',
      clearable: true,
    },
  },
  {
    name: 'targetRule',
    label: '目标值规则',
    type: 'select',
    show: props.dataId && props.lastLevel && props.editType ? true : false,
    props: {
      placeholder: '请选择目标值规则',
      clearable: true,
    },
    valueKey: 'dictionaryCode',
    labelKey: 'dictionaryName',
    options: async () => {
      const data = await DictApI.getDictItemsByCode('targetValueRule').then((res) => res.data)
      return data
    },
  },
])
const rawFields = ref<any[]>([])

const fetchData = () =>
  dataResApi.getEvaluateSystemInfo(props.dataId).then((res) => {
    const o = (fields.value ?? []).reduce((acc, { type, name }) => {
      if (name === 'parentId') {
        acc[name] = props.parentId
        return acc
      }
      acc[name] = res.data[name]
      return acc
    }, {} as any)
    return o
  })

defineExpose({
  getFields: () => rawFields.value,
  resetForms: () => {
    formRef.value?.resetFields()
    formRef.value?.clearCache()
  },
})
// 递归构建树结构
const buildTree = (data: any[]): any[] => {
  return (
    data?.map((item) => {
      // 创建当前节点
      return {
        ...item,
        id: item.classId,
        label: item.className,
        isLeaf: false,
        // 如果有children且不为空，则递归处理
        children:
          item.children && item.children.length > 0
            ? buildTree(item.children)
            : (item.indexList || []).map((it: any) => {
                return {
                  ...it,
                  id: `${it.baseId}-${item.classId}`,
                  label: it.indexName,
                  isLeaf: true, // 标记为叶子节点
                }
              }),
      }
    }) || []
  )
}
onOpen(() => {
  console.log(props, '//////////')
  if (props.dataId && props.mode === 'edit') {
    formRef.value?.refetch()
  }
})
</script>
