<template>
  <z-page>
    <z-table
      ref="tableRef"
      :auto-load="false"
      row-key="evaluateId"
      pagination-mode="server"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #evaluateStatus="{ row }">
        <el-switch
          :model-value="row.evaluateStatus"
          active-value="0"
          inactive-value="1"
          @change="() => statusChange(row)"
        />
      </template>
      <template #indicatorHierarchy="{ row }">
        <span>{{ row.indicatorHierarchy ? row.indicatorHierarchy == '2' ? '二级' : '三级' : '' }}</span>
      </template>
      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            name: '管理指标评价体系',
            path: 'normEvaluationSystem',
            viewPath: '@/views/DataResources/DevelopmentData/EvaluationSystem/ManagementSystem.vue',
          }"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              link
              @click="
                () =>
                  navigate({
                    query: {
                      id: row.evaluateId,
                      level: row.indicatorHierarchy,
                    },
                  })
              "
            >
              管理
            </el-button>
          </template>
        </z-dynamic-route>
        <el-button
          size="small"
          type="primary"
          @click="onEdit(row)"
          link
        >
          {{ t('common.edit') }}
        </el-button>
        <el-popconfirm
          title="确认删除该指标评价体系吗？"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
            >
              {{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <EvaluationForm
        ref="EvaluationFormRef"
        :model="dialog.model.value"
        :mode="dialog.mode.value"
      />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import type {
  ZTableInstance,
  ZTableColumn,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types'

import type { ComputedRef } from 'vue'
import { computed, onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import EvaluationForm from './EvaluationForm.vue'
import { useDialog } from '@/composables/useDialog'
import { ElButton, ElMessage } from 'element-plus'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories'
import dataResApi from '@/api/dataResources'


const tableRef = ref<ZTableInstance | null>(null)
const EvaluationFormRef = ref<InstanceType<typeof EvaluationForm> | null>(null)


const { t } = useTypedI18n()
const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'evaluateName', label: '名称' },
  { prop: 'indicatorHierarchy', label: '层级' },
  { prop: 'evaluateStatus', label: '状态' },
  { prop: 'operation', label: '操作', align: 'center', width: 160, fixed: 'right' },
])

const toolbarRows: ComputedRef<ZTableToolbarRow[]> = computed(() => [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => dialog.open('create'),
      }),
    ],
  },
])
const parseValues = (values: Record<string, any>, fields: any[], rawFields: any[]) => {
  return fields.reduce((acc, field) => {
    acc[field.name] = values[field.name]
    return acc
  }, {} as any)
}

const fetchTableData = async (params: ZTableParams) => {
  return dataResApi.getEvaluateSystem({ ...params }).then((res) => res.data)
}

const onDelete = (row: any) => {
  dataResApi.deleteEvaluateSystem(row.evaluateId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const onEdit = (row: any) => {
  dialog.open('edit', row)
}

const dialog = useDialog({
  name: '指标体系',
  onConfirm(mode, ctx) {
    if (!ctx.form) return
    const handleSubmit = (formData: any) => {
      if (mode === 'create') {
        const { evaluateId, ...rest } = formData
        return dataResApi.addEvaluateSystem({
          ...rest,
          parentId: 0,
        })
      } else if (mode === 'edit') {
        return dataResApi.editEvaluateSystem(formData)
      }
    }

    ctx.form.validate((isValid) => {
      if (!isValid) return

      const formData = parseValues(
        ctx.form!.getValues(),
        ctx.form!.getFields(),
        EvaluationFormRef.value!.getFields(),
      )
      ctx.isSubmitting.value = true

      handleSubmit(formData)
        ?.then(() => {
          EvaluationFormRef.value?.resetForms()
          tableRef.value?.refresh()
          dialog.close()
          ElMessage.success(
            mode === 'create' ? t('message.saveSuccess') : t('message.updateSuccess'),
          )
        })
        .finally(() => {
          ctx.isSubmitting.value = false
        })
    })
  },
})

const statusChange = (row: any) => {
  if (!row.evaluateId || !row.evaluateStatus) return
  dataResApi.changeEvaluateSystemStatus(row.evaluateId).then((res) => {
    tableRef.value?.refresh()
    ElMessage.success(res.message)
  })
}

onMounted(async () => {
  tableRef.value?.refresh()
})
</script>
