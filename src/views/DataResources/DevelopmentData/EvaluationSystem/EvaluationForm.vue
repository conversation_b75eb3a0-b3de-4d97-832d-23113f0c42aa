<template>
  <z-form
    ref="formRef"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
  </z-form>
</template>

<script setup lang="ts">
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { reactive, ref } from 'vue'
const formRef = ref<ZFormInstance>()

const initValue = {
  evaluateId: '',
  evaluateName: '',
  evaluateIntroduction: '',
  indicatorHierarchy: '',
}

const form = reactive(initValue)
const normLevel = [
  {
    label: '二级',
    value: '2',
  },
  {
    label: '三级',
    value: '3',
  },
]
const fields = ref<ZFormField[]>([
  {
    name: 'evaluateId',
    type: 'input',
    show: false,
  },
  {
    name: 'evaluateName',
    label: '名称',
    type: 'input',
    rules: [
      {
        required: true,
        message: '请输入指标体系名称',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入指标体系名称',
      maxlength: 50,
      showWordLimit: true,
      clearable: true,
    },
  },
  {
    name: 'indicatorHierarchy',
    label: '指标层级',
    type: 'select',
    rules: [
      {
        required: true,
        message: '请选择指标层级',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请选择指标层级',
      clearable: true,
    },
    options: normLevel
  },
  {
    name: 'evaluateIntroduction',
    label: '简介',
    type: 'input',
    props: {
      placeholder: '请输入简介说明',
      maxlength: 300,
      showWordLimit: true,
      clearable: true,
      type: 'textarea',
    },
  },
])

const rawFields = ref<any[]>([])

defineExpose({
  getFields: () => rawFields.value,
  resetForms: () => {
    formRef.value?.resetFields()
    formRef.value?.clearCache()
  },
})
</script>
