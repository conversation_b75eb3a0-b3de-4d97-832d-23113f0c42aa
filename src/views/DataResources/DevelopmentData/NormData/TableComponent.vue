<template>
  <table>
    <thead>
      <tr>
        <th class="diagonal-header fixed-width">
          <span class="diagonal-top">指标</span>
          <span class="diagonal-bottom">区域</span>
        </th>
        <th
          v-for="(area, index) in areaData"
          :key="index"
          class="bg-[#F2F2F2] area-header"
        >
          <span>{{ area.areaName }}</span>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr
        v-if="shouldRender"
        v-for="(item, itemIndex) in data"
        :key="itemIndex"
      >
        <td
          class="fixed-width p-0 m-0"
          style="width: 110px"
        >
          {{ item.indexName }}（{{ item.indexUnit }}）
        </td>
        <template v-for="area in areaData">
          <template v-for="region in getDataByCode(area, item.regionList)">
            <td>
              <InputCell v-model="region.dataValue" />
            </td>
          </template>
        </template>
      </tr>
    </tbody>
  </table>
</template>

<script lang="ts" setup>
import { ref, onMounted, toRefs } from 'vue'
import ComApi from '@/api/common'
import InputCell from './InputCell.vue'
import { BJ_AREA_CODE } from '@/config/default-settings.ts'
interface Region {
  regionCode: string
  dataValue: string
}

interface DataItem {
  dataYear: string
  baseId: number
  indexName: string
  indexUnit: string
  regionList: Region[]
}

interface TableProps {
  headers: string[]
  data: DataItem[]
}

const props = defineProps<TableProps>()
const { headers, data } = toRefs(props)
const shouldRender = ref(false)

const areaData = ref<any[]>([])

const getDataByCode = (
  row: any,
  list: any[],
): {
  regionCode: string
  dataValue: string
}[] => {
  const option = list.find((item) => item.regionCode === row.areaCode)
  if (option) {
    return [option]
  }
  list.push({
    regionCode: row.areaCode,
    dataValue: '',
  })
  return []
}

onMounted(async () => {
  const data = await ComApi.getAreaList().then((res) => res.data)
  areaData.value = [{ areaName: '宝鸡市', areaCode: BJ_AREA_CODE, regionList: [] }, ...data]
})

defineExpose({
  close: () => (shouldRender.value = false),
  show: () => (shouldRender.value = true),
})
</script>

<style scoped>
table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  border: 1px solid #ccc;
  padding: 8px 2px;
  text-align: center;
  color: rgb(96, 98, 102);
  font-size: 14px;
  font-weight: 400;
  box-sizing: border-box;
}
.diagonal-header {
  position: relative;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.fixed-width {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
}
.area-header {
  width: 100px !important;
}
.diagonal-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: -10px;
  width: 124px;
  height: 100%;
  border-bottom: 1px solid #e0d6d6;
  transform: rotate(16deg);
  transform-origin: top right;
}

.diagonal-top {
  position: absolute;
  top: 15px;
  left: 20px;
}

.diagonal-bottom {
  position: absolute;
  bottom: 15px;
  right: 20px;
}
</style>
