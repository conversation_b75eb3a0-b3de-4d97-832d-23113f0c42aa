<template>
  <z-form
    ref="formRef"
    :fetch-data="fetchData"
    v-model:model="form"
    :fields="fields"
    label-width="120px"
  >
  </z-form>
</template>

<script setup lang="ts">
import dataResApi from '@/api/dataResources'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { reactive, ref } from 'vue'

const { onOpen } = useDialogFormContext()
const props = defineProps<{
  dataId: any
  parentId: any
  mode?: 'create' | 'edit' | 'addChild' | string
}>()
const formRef = ref<ZFormInstance>()

const initValue = {
  thematicClassParent: props.parentId || 0,
  thematicClassId: props.dataId || 0,
}

const form = reactive(initValue)
const fields = ref<ZFormField[]>([
  {
    name: 'thematicClassParent',
    type: 'input',
    show: false,
  },
  {
    name: 'thematicClassName',
    label: '指标名称',
    type: 'input',
    rules: [
      {
        required: true,
        message: '请输入指标名称',
        trigger: 'blur',
      },
      { max: 30, message: '不能超过20个字符', trigger: 'blur' },
    ],
    props: {
      placeholder: '请输入指标名称',
    },
  },
  {
    name: 'unit',
    label: '单位',
    type: 'select',
    props: {
      placeholder: '请选择单位',
    },
    rules: [
      {
        required: true,
        message: '请选择单位',
        trigger: 'change',
      },
    ],
    options: [
      {
        label: '万 m³',
        value: '1'
      }, {
        label: '%',
        value: '2'
      }, {
        label: '亿元',
        value: '3'
      }, {
        label: '元',
        value: '4'
      },

    ]
  },
  {
    name: 'thematicId',
    label: '关联分类',
    type: 'tree-select',
    props: {
      checkStrictly: false,
      placeholder: '请选择关联分类',
    },
    show: props.mode !== 'addChild' ? true : false,
    valueKey: 'thematicId',
    treeProps: {
      label: 'thematicName',
      children: 'children',
    },
    rules: [
      {
        required: true,
        message: '请选择上级专题',
        trigger: 'change',
      },
    ],
    noCache: false,
    treeOptions: async (context) => {
      if (String(context.model.thematicClassId)) {
        const children = await dataResApi.getFeatureTree().then((res) => res.data)
        return children
      } else {
        return []
      }
    },
  },
  {
    name: 'thematicClassSort',
    label: '排序',
    type: 'number',
    rules: [
      {
        required: true,
        message: '请输入正整数',
        trigger: 'blur',
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!/^[1-9]\d*$/.test(value)) {
            callback(new Error('请输入正整数'))
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请选择',
      min: 1,
    },
  },
])

const rawFields = ref<any[]>([])
const fetchData = () =>
  dataResApi.editCategoryManagement(props.dataId).then((res) => {
    const o = (fields.value ?? []).reduce((acc, { type, name }) => {
      acc[name] = res.data[name]
      return acc
    }, {} as any)
    return o
  })

defineExpose({
  getFields: () => rawFields.value,
  resetForms: () => {
    formRef.value?.resetFields()
    formRef.value?.clearCache()
  },
})

onOpen(() => {
  if (props.dataId && props.mode === 'edit') {
    if (props.dataId) {
      formRef.value?.refetch()
    }
  }
})
</script>
