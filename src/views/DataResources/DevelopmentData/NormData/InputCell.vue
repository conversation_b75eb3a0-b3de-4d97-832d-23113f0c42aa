<template>
  <el-input
    class="input-cell"
    v-model="displayValue"
    @keydown="handleKeyDown"
    @blur="handleBlur"
  />
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue'

export default defineComponent({
  name: 'NumericInput',
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const displayValue = ref(props.modelValue.toString())

    // 处理输入值
    const processInput = (value: string): string => {
      let val = value

      // 1. 过滤非数字和小数点
      let filtered = val.replace(/[^\d.]/g, '')

      // 2. 处理多个小数点的情况
      const pointArr = filtered.split('.')
      if (pointArr.length > 2) {
        filtered = `${pointArr[0]}.${pointArr.slice(1).join('')}`
      }

      // 3. 禁止小数点开头
      if (filtered.startsWith('.')) {
        filtered = filtered.substring(1)
      }

      // 4. 限制长度不超过20
      if (filtered.length > 20) {
        filtered = filtered.slice(0, 20)
      }

      return filtered
    }

    // 处理键盘事件
    const handleKeyDown = (e: KeyboardEvent) => {
      // 允许: 数字、小数点、退格、删除、Tab、左右箭头
      const allowedKeys = [
        8,   // 退格
        9,   // Tab
        37,  // 左箭头
        39,  // 右箭头
        46,  // 删除
        48, 49, 50, 51, 52, 53, 54, 55, 56, 57,  // 数字键 0-9
        96, 97, 98, 99, 100, 101, 102, 103, 104, 105, // 小键盘数字
        110, // 小键盘小数点
        190  // 小数点
      ]

      // 禁止直接输入小数点作为第一个字符
      const isDotKey = e.keyCode === 190 || e.keyCode === 110
      const isEmptyInput = (e.target as HTMLInputElement).value === ''

      if (!allowedKeys.includes(e.keyCode) || (isDotKey && isEmptyInput)) {
        e.preventDefault()
      }
    }

    // 处理失去焦点事件
    const handleBlur = () => {
      const processed = processInput(displayValue.value)
      displayValue.value = processed
      emit('update:modelValue', processed === '' ? null : processed)
    }

    // 计算属性处理双向绑定
    const valueProxy = computed({
      get: () => displayValue.value,
      set: (value: string) => {
        const processed = processInput(value)
        displayValue.value = processed
        emit('update:modelValue', processed === '' ? null : processed)
      }
    })

    return {
      displayValue: valueProxy,
      handleKeyDown,
      handleBlur
    }
  }
})
</script>

<style scoped>
.input-cell {
  border: none;
  background: transparent;
  width: 100%;
  outline: none;
  --el-input-border-color: transparent;
  --el-input-hover-border-color: transparent;
}

.input-cell:focus {
  border: 1px solid #007bff;
}
</style>
