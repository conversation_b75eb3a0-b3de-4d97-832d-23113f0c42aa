<template>
  <z-page class="flex p-0 bg-[#F0F2F5] gap-2">
    <div class="w-1/5 bg-white p-2 rounded-md">
      <el-tree
        style="max-width: 600px"
        node-key="classId"
        :current-node-key="currentNormClassify"
        :data="normClassifyData"
        :props="defaultProps"
        :expand-on-click-node="false"
        :highlight-current="true"
        default-expand-all
        @node-click="handleNodeClick"
      />
    </div>
    <div class="w-4/5 bg-white p-2 rounded-md">
      <div class="flex justify-between mb-[10px]">
        <div class="form">
          <span>年份</span>
          <el-date-picker
            class="w-[240px] ml-[10px]"
            v-model="currentDate"
            type="year"
            placeholder="请选择年份"
            @change="onChangeDate"
          />
        </div>
        <el-button
          type="primary"
          :disabled="tableData.length ? false : true"
          @click="saveData"
          >保 存</el-button
        >
      </div>
      <TableComponent
        ref="tableComponentRef"
        :headers="headers"
        :data="tableData"
      />
    </div>
  </z-page>
</template>
<script setup lang="ts">
import dayjs from 'dayjs'
import { ref, onMounted } from 'vue'
import dataResApi from '@/api/dataResources'
import TableComponent from './TableComponent.vue'
import { ElMessage } from 'element-plus'
interface Tree {
  classId: any
  parentId: any
  classSort: any
  classStatus: any
  className: string
  children?: Tree[] | null
  [key: string]: unknown
}
// 定义表格数据接口
interface Region {
  regionCode: string
  dataValue: string
}

interface DataItem {
  dataYear: string
  baseId: number
  indexName: string
  indexUnit: string
  regionList: Region[]
}

const defaultProps = {
  children: 'children',
  label: 'className',
}

const tableComponentRef = ref<InstanceType<typeof TableComponent> | null>(null)
const headers = ref<string[]>([])
const tableData = ref<DataItem[]>([])

const getLastYear = () => {
  const date = new Date()
  date.setFullYear(date.getFullYear() - 1)
  return date
}

const currentDate = ref(getLastYear())
const currentNormClassify = ref('')
const normClassifyData = ref<Tree[]>([])
/**
 * 获取树形结构中第一个节点最末级节点
 * @param {Array} treeData 树形数据
 * @returns {Object|null} 最末级节点对象，如果没有则返回null
 */
const findFirstLeafNode = (treeData: any): any => {
  if (!Array.isArray(treeData)) return null
  if (treeData.length === 0) return null

  // 获取第一个节点
  const firstNode = treeData[0]

  // 如何第一个接点的children为[]，则返回当前节点
  if (!firstNode.children || firstNode.children == 0) {
    return firstNode
  }
  // 如果第一个节点没有子节点，则返回null（因为题目要求的是第一个子节点的末节点）
  if (!firstNode.children || firstNode.children.length === 0) return null

  // 获取第一个子节点
  const firstChild = firstNode.children[0]

  // 递归查找该子节点的最末级节点
  return findDeepestLeaf(firstChild)
}

/**
 * 递归查找节点的最末级子节点
 * @param {Object} node 当前节点
 * @returns {Object} 最末级节点
 */
const findDeepestLeaf = (node: any): any => {
  if (!node.children || node.children.length === 0) {
    return node
  }
  // 继续查找第一个子节点的最末级
  return findDeepestLeaf(node.children[0])
}

const handleNodeClick = (row: Tree) => {
  tableComponentRef.value?.close()
  currentNormClassify.value = row.classId
  getData(row.classId)
}

const onChangeDate = (date: Date) => {
  tableComponentRef.value?.close()
  getData(currentNormClassify.value)
}
const getData = (id: any) => {
  const data = {
    classId: id,
    dataYear: dayjs(currentDate.value).format('YYYY'),
  }
  dataResApi.getNormData(data).then((res) => {
    if (res.data && res.data.length) {
      tableComponentRef.value?.show()
    }
    tableData.value = res.data
  })
}
const saveData = async () => {
  try {
    const year = dayjs(currentDate.value).format('YYYY')
    const data = tableData.value.map((item) => {
      return {
        ...item,
        regionList: item.regionList.filter((region) => region.dataValue),
      }
    })
    const res = await dataResApi.saveNormData(data, year)
    ElMessage.success(res.message)
  } catch (error) {
    console.log(error)
  }
}

const getNormClassifyData = async () => {
  try {
    const { data } = await dataResApi.getNormClassifyData({ classStatus: 0 })
    if (data && data.length) {
      normClassifyData.value = data
      const firstChild = findFirstLeafNode(data)
      console.log(firstChild, '///////')
      currentNormClassify.value = firstChild.classId
      getData(currentNormClassify.value)
    }
  } catch (error) {
    console.log(error)
  }
}
onMounted(() => {
  getNormClassifyData()
})
</script>
