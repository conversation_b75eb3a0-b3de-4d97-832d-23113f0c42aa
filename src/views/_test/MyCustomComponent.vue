<template>
  <div class="custom-overlay">
    <h3>{{ title }}</h3>
    <p>{{ description }}</p>
    <button @click.self="(e) => $emit('click', e)">点击</button>
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount } from 'vue'

defineProps<{
  title: string
  description: string
}>()

defineEmits<{
  click: []
  mouseenter: []
  mouseleave: []
}>()

onBeforeUnmount(() => {
  console.log('component unmounted')
})
</script>

