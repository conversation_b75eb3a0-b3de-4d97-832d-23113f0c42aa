<template>
  <div class="entropy-topsis-analysis">
    <div class="header">
      <h1>熵权TOPSIS多准则决策分析</h1>
      <p>基于熵权法和TOPSIS方法的综合评价分析</p>
    </div>

    <div class="content">
      <!-- 数据输入区域 -->
      <el-card
        class="data-input-card"
        shadow="hover"
      >
        <template #header>
          <div class="card-header">
            <span>📊 决策矩阵数据</span>
            <el-button
              type="primary"
              @click="runAnalysis"
              :loading="analyzing"
            >
              {{ analyzing ? '分析中...' : '开始分析' }}
            </el-button>
          </div>
        </template>

        <div class="data-section">
          <div class="table-container">
            <el-table
              :data="decisionMatrix"
              style="width: 100%"
            >
              <el-table-column
                prop="scheme"
                label="方案"
                width="100"
                fixed="left"
              />
              <el-table-column
                v-for="(criterion, index) in criteriaNames"
                :key="index"
                :prop="`criterion${index}`"
                :label="criterion"
                width="120"
              >
                <template #header>
                  <div class="criterion-header">
                    <span>{{ criterion }}</span>
                    <el-tag
                      :type="criteriaTypes[index] === 1 ? 'success' : 'warning'"
                      size="small"
                    >
                      {{ criteriaTypes[index] === 1 ? '效益型' : '成本型' }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <!-- 分析结果区域 -->
      <div
        v-if="analysisResult"
        class="results-section"
      >
        <!-- 权重结果 -->
        <el-card
          class="result-card"
          shadow="hover"
        >
          <template #header>
            <span>⚖️ 指标权重分析</span>
          </template>
          <div
            class="weights-chart"
            ref="weightsChartRef"
          ></div>
          <el-table :data="weightsTableData">
            <el-table-column
              prop="criterion"
              label="指标"
            />
            <el-table-column
              prop="weight"
              label="权重"
            />
            <el-table-column
              prop="type"
              label="类型"
            />
          </el-table>
        </el-card>

        <!-- TOPSIS结果 -->
        <el-card
          class="result-card"
          shadow="hover"
        >
          <template #header>
            <span>🎯 TOPSIS分析结果</span>
          </template>
          <div
            class="ranking-chart"
            ref="rankingChartRef"
          ></div>
          <el-table :data="rankingTableData">
            <el-table-column
              prop="rank"
              label="排名"
              width="80"
            />
            <el-table-column
              prop="scheme"
              label="方案"
            />
            <el-table-column
              prop="closeness"
              label="贴近度"
            />
            <el-table-column
              prop="status"
              label="评价"
            />
          </el-table>
        </el-card>

        <!-- 3D可视化 -->
        <el-card
          class="result-card full-width"
          shadow="hover"
        >
          <template #header>
            <span>📈 3D散点图可视化</span>
          </template>
          <div
            class="chart-3d"
            ref="chart3dRef"
          ></div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ElCard, ElTable, ElTableColumn, ElButton, ElTag, ElMessage } from 'element-plus'
import * as echarts from 'echarts'
// import 'echarts-gl'

// 响应式数据
const analyzing = ref(false)
const analysisResult = ref<any>(null)
const weightsChartRef = ref<HTMLElement>()
const rankingChartRef = ref<HTMLElement>()
const chart3dRef = ref<HTMLElement>()

// 模拟决策矩阵数据
const criteriaNames = ['经济效益', '环境影响', '技术水平', '社会效益', '风险程度']
const criteriaTypes = [1, 1, 1, 1, -1]

const decisionMatrix = ref([
  {
    scheme: '方案A',
    criterion0: 85,
    criterion1: 78,
    criterion2: 92,
    criterion3: 88,
    criterion4: 15,
  },
  {
    scheme: '方案B',
    criterion0: 92,
    criterion1: 85,
    criterion2: 78,
    criterion3: 82,
    criterion4: 22,
  },
  {
    scheme: '方案C',
    criterion0: 78,
    criterion1: 92,
    criterion2: 85,
    criterion3: 90,
    criterion4: 18,
  },
  {
    scheme: '方案D',
    criterion0: 88,
    criterion1: 82,
    criterion2: 88,
    criterion3: 85,
    criterion4: 12,
  },
  {
    scheme: '方案E',
    criterion0: 82,
    criterion1: 88,
    criterion2: 82,
    criterion3: 92,
    criterion4: 25,
  },
])

const weightsTableData = ref<any[]>([])
const rankingTableData = ref<any[]>([])

// 熵权TOPSIS算法实现
function entropyTopsis(matrix: number[][], types: number[]) {
  const m = matrix.length // 方案数
  const n = matrix[0].length // 指标数

  // 1. 标准化矩阵
  const normMatrix = matrix.map(() => Array(n).fill(0))
  for (let j = 0; j < n; j++) {
    const sumSquare = Math.sqrt(matrix.reduce((sum, row) => sum + row[j] ** 2, 0))
    for (let i = 0; i < m; i++) {
      normMatrix[i][j] = matrix[i][j] / sumSquare
    }
  }


  // 2. 熵值法计算权重
  const pMatrix = normMatrix.map(() => Array(n).fill(0))
  const entropy = Array(n).fill(0)
  const weights = Array(n).fill(0)

  for (let j = 0; j < n; j++) {
    const sumNorm = normMatrix.reduce((sum, row) => sum + row[j], 0)
    for (let i = 0; i < m; i++) {
      pMatrix[i][j] = sumNorm === 0 ? 0 : normMatrix[i][j] / sumNorm
    }
    entropy[j] =
      -pMatrix.reduce((sum, row) => {
        const p = row[j]
        return sum + (p === 0 ? 0 : p * Math.log(p))
      }, 0) / Math.log(m)
  }

  // 计算权重 - 修复权重计算公式
  const totalDifference = entropy.reduce((sum, e) => sum + (1 - e), 0)
  for (let j = 0; j < n; j++) {
    weights[j] = (1 - entropy[j]) / totalDifference
  }

  const weightedMatrix = normMatrix.map((row) => row.map((val, j) => val * weights[j]))

  const idealPos = Array(n).fill(0)
  const idealNeg = Array(n).fill(0)
  for (let j = 0; j < n; j++) {
    const col = weightedMatrix.map((row) => row[j])
    idealPos[j] = types[j] === 1 ? Math.max(...col) : Math.min(...col)
    idealNeg[j] = types[j] === 1 ? Math.min(...col) : Math.max(...col)
  }

  const sPos = weightedMatrix.map((row) =>
    Math.sqrt(row.reduce((sum, val, j) => sum + (val - idealPos[j]) ** 2, 0)),
  )
  const sNeg = weightedMatrix.map((row) =>
    Math.sqrt(row.reduce((sum, val, j) => sum + (val - idealNeg[j]) ** 2, 0)),
  )

  const closeness = sPos.map((sp, i) => sNeg[i] / (sp + sNeg[i]))

  const ranked = closeness
    .map((c, i) => ({ scheme: `方案${String.fromCharCode(65 + i)}`, closeness: c, index: i }))
    .sort((a, b) => b.closeness - a.closeness)

  return { weights, closeness: ranked, rawCloseness: closeness, idealPos, idealNeg }
}



function prepareMatrixData() {
  return decisionMatrix.value.map((row) => [
    row.criterion0,
    row.criterion1,
    row.criterion2,
    row.criterion3,
    row.criterion4,
  ])
}

async function runAnalysis() {
  analyzing.value = true

  try {
    const matrix = prepareMatrixData()
    const result = entropyTopsis(matrix, criteriaTypes)

    analysisResult.value = result

    // 准备权重表格数据
    weightsTableData.value = result.weights.map((weight, index) => ({
      criterion: criteriaNames[index],
      weight: weight.toFixed(4),
      type: criteriaTypes[index] === 1 ? '效益型' : '成本型',
    }))

    // 准备排名表格数据
    rankingTableData.value = result.closeness.map((item, index) => ({
      rank: index + 1,
      scheme: item.scheme,
      closeness: item.closeness.toFixed(4),
      status: index === 0 ? '最优' : index === result.closeness.length - 1 ? '最差' : '良好',
    }))

    ElMessage.success('分析完成！')

    await nextTick()
    renderCharts()
  } catch {
    ElMessage.error('分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}

function renderCharts() {
  if (!analysisResult.value) return

  renderWeightsChart()

  renderRankingChart()

  render3DChart()
}

function renderWeightsChart() {
  if (!weightsChartRef.value) return

  const chart = echarts.init(weightsChartRef.value)
  const option = {
    title: {
      text: '指标权重分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    xAxis: {
      type: 'category',
      data: criteriaNames,
      axisLabel: {
        rotate: 45,
      },
    },
    yAxis: {
      type: 'value',
      name: '权重',
    },
    series: [
      {
        data: analysisResult.value.weights.map((weight: number, index: number) => ({
          value: weight,
          itemStyle: {
            color: criteriaTypes[index] === 1 ? '#67C23A' : '#F56C6C',
          },
        })),
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)',
        },
      },
    ],
  }
  chart.setOption(option)
}

function renderRankingChart() {
  if (!rankingChartRef.value) return

  const chart = echarts.init(rankingChartRef.value)
  const option = {
    title: {
      text: 'TOPSIS贴近度排名',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    xAxis: {
      type: 'value',
      name: '贴近度',
    },
    yAxis: {
      type: 'category',
      data: analysisResult.value.closeness.map((item: any) => item.scheme).reverse(),
    },
    series: [
      {
        data: analysisResult.value.closeness.map((item: any) => item.closeness).reverse(),
        type: 'bar',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' },
          ]),
        },
      },
    ],
  }
  chart.setOption(option)
}

function render3DChart() {
  if (!chart3dRef.value) return

  const chart = echarts.init(chart3dRef.value)
  const matrix = prepareMatrixData()

  // 解决降维问题：使用PCA或选择主要指标组合
  // 方案1：使用加权综合得分的三个维度
  // X轴：效益型指标加权平均 (经济效益、技术水平、社会效益)
  // Y轴：环境影响指标
  // Z轴：TOPSIS贴近度
  const data3D = matrix.map((row, index) => {
    const weights = analysisResult.value.weights

    // 计算效益型指标加权平均 (指标0,2,3)
    const benefitIndices = [0, 2, 3] // 经济效益、技术水平、社会效益
    const benefitWeights = benefitIndices.map((i) => weights[i])
    const totalBenefitWeight = benefitWeights.reduce((sum, w) => sum + w, 0)
    const benefitScore =
      benefitIndices.reduce((sum, i) => sum + row[i] * weights[i], 0) / totalBenefitWeight

    // 环境影响指标 (指标1)
    const environmentScore = row[1]

    // 风险程度指标 (指标4，成本型，需要转换)
    const riskScore = 100 - row[4] // 转换为效益型显示

    // TOPSIS贴近度
    const topsisScore = analysisResult.value.rawCloseness[index] * 100

    return {
      name: `方案${String.fromCharCode(65 + index)}`,
      value: [benefitScore, environmentScore, topsisScore],
      itemStyle: {
        color: `hsl(${index * 60}, 70%, 60%)`,
      },
      // 保存原始数据用于tooltip显示
      originalData: row,
      closeness: analysisResult.value.rawCloseness[index],
      riskScore: riskScore,
    }
  })

  const option = {
    title: {
      text: '熵权TOPSIS 3D分析结果（智能降维）',
      left: 'center',
      textStyle: {
        fontSize: 16,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params: any) {
        const data = params.data
        const originalData = data.originalData
        return `
          <div style="padding: 12px; max-width: 320px;">
            <strong style="color: ${data.itemStyle.color}; font-size: 14px;">${data.name}</strong><br/>
            <hr style="margin: 8px 0; border: none; border-top: 1px solid #eee;"/>
            <div style="display: grid; grid-template-columns: 1fr auto; gap: 8px; font-size: 12px;">
              ${criteriaNames
                .map(
                  (name, i) =>
                    `<span>${name}:</span><span style="text-align: right; font-weight: bold;">${originalData[i]}</span>`,
                )
                .join('')}
            </div>
            <hr style="margin: 8px 0; border: none; border-top: 1px solid #eee;"/>
            <div style="font-weight: bold; color: ${data.itemStyle.color};">
              TOPSIS贴近度: ${data.closeness.toFixed(4)} (${data.value[2].toFixed(1)}%)
            </div>
            <div style="font-size: 11px; color: #666; margin-top: 6px; line-height: 1.3;">
              <strong>3D坐标说明：</strong><br/>
              X轴: 效益型指标综合得分 (${data.value[0].toFixed(1)})<br/>
              Y轴: 环境影响得分 (${data.value[1].toFixed(1)})<br/>
              Z轴: TOPSIS贴近度 (${data.value[2].toFixed(1)}%)
            </div>
          </div>
        `
      },
    },
    legend: {
      data: data3D.map((item) => item.name),
      top: 50,
      textStyle: {
        fontSize: 12,
      },
    },
    xAxis3D: {
      name: '效益型指标综合得分',
      nameTextStyle: {
        fontSize: 12,
        color: '#666',
      },
      type: 'value',
      min: 'dataMin',
      max: 'dataMax',
      axisLabel: {
        fontSize: 10,
      },
    },
    yAxis3D: {
      name: '环境影响得分',
      nameTextStyle: {
        fontSize: 12,
        color: '#666',
      },
      type: 'value',
      min: 'dataMin',
      max: 'dataMax',
      axisLabel: {
        fontSize: 10,
      },
    },
    zAxis3D: {
      name: 'TOPSIS贴近度 (%)',
      nameTextStyle: {
        fontSize: 12,
        color: '#666',
      },
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        fontSize: 10,
        formatter: '{value}%',
      },
    },
    grid3D: {
      boxWidth: 200,
      boxHeight: 200,
      boxDepth: 200,
      viewControl: {
        projection: 'perspective',
        autoRotate: true,
        autoRotateSpeed: 3,
        rotateSensitivity: 1,
        zoomSensitivity: 1,
        panSensitivity: 1,
        alpha: 25,
        beta: 40,
        distance: 300,
      },
      light: {
        main: {
          intensity: 1.2,
          shadow: true,
          shadowQuality: 'medium',
        },
        ambient: {
          intensity: 0.4,
        },
      },
      environment: '#f8f9fa',
    },
    series: [
      {
        type: 'scatter3D',
        data: data3D,
        symbolSize: function (data: any) {
          // 根据TOPSIS贴近度调整点的大小
          return 15 + data[2] * 0.25
        },
        emphasis: {
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 3,
          },
        },
        animationDuration: 2000,
        animationEasing: 'cubicOut',
      },
    ],
  }
  chart.setOption(option)

  // 添加图表点击事件
  chart.on('click', function (params: any) {
    if (params.componentType === 'series') {
      const data = params.data
      console.log('点击了方案:', data.name, '详细数据:', data.originalData)
      // 可以在这里添加更多交互功能，比如显示详细分析
    }
  })
}

onMounted(() => {
  // 页面加载时自动执行一次分析
  runAnalysis()
})
</script>

<style scoped lang="scss">
.entropy-topsis-analysis {
  min-height: 100vh;
  background: linear-gradient(135deg, #000000 0%, #764ba2 100%);
  padding: 20px;

  .header {
    text-align: center;
    color: white;
    margin-bottom: 30px;

    h1 {
      font-size: 2.5rem;
      font-weight: 300;
      margin: 0 0 10px 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    p {
      font-size: 1.2rem;
      opacity: 0.9;
      margin: 0;
    }
  }

  .content {
    max-width: 1400px;
    margin: 0 auto;
  }

  .data-input-card {
    margin-bottom: 30px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .data-section {
      .table-container {
        overflow-x: auto;

        .criterion-header {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
        }
      }
    }
  }

  .results-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;

    .result-card {
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);

      &.full-width {
        grid-column: 1 / -1;
      }

      :deep(.el-card__header) {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
      }

      .weights-chart,
      .ranking-chart {
        height: 300px;
        margin-bottom: 20px;
      }

      .chart-3d {
        height: 600px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .results-section {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 768px) {
    padding: 10px;

    .header {
      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .data-input-card {
      .card-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-table) {
  .el-table__header {
    th {
      background: #f8f9fa;
      color: #333;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background: #f5f7fa;
    }
  }
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;

  &.el-button--primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #3d8bfe 0%, #0dcaf0 100%);
    }
  }
}

:deep(.el-tag) {
  border-radius: 12px;
  font-size: 11px;

  &.el-tag--success {
    background: #f0f9ff;
    color: #059669;
    border-color: #a7f3d0;
  }

  &.el-tag--warning {
    background: #fffbeb;
    color: #d97706;
    border-color: #fde68a;
  }
}
</style>
