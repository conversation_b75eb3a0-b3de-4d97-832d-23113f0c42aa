<template>
  <svg
    :width="width"
    :height="height"
    :viewBox="viewBox"
    ref="svgRef"
  >
    <defs>
      <filter
        id="glow"
        x="-100%"
        y="-100%"
        width="300%"
        height="300%"
      >
        <feGaussianBlur
          in="SourceGraphic"
          stdDeviation="6"
          result="blur1"
        />
        <feGaussianBlur
          in="SourceGraphic"
          stdDeviation="2"
          result="blur2"
        />
        <feMerge>
          <feMergeNode in="blur1" />
          <feMergeNode in="blur2" />
          <feMergeNode in="SourceGraphic" />
        </feMerge>
      </filter>

      <linearGradient id="dotGradient">
        <stop
          offset="0%"
          stop-color="#3D9DFF"
          stop-opacity="0.3"
        />
        <stop
          offset="40%"
          stop-color="#3D9DFF"
          stop-opacity="0.3"
        />
        <stop
          offset="50%"
          stop-color="#3D9DFF"
          stop-opacity="1"
        />
        <stop
          offset="60%"
          stop-color="#3D9DFF"
          stop-opacity="0.3"
        />
        <stop
          offset="100%"
          stop-color="#3D9DFF"
          stop-opacity="0.3"
        />
      </linearGradient>
    </defs>

    <path
      :d="pathData"
      fill="#164485f2"
      stroke="#2D65AF"
      stroke-width="2"
      stroke-linecap="butt"
      stroke-linejoin="miter"
    />

    <rect
      v-for="(segment, index) in chamferSegments"
      :key="'chamfer-' + index"
      :x="segment.x"
      :y="segment.y"
      :width="segment.width"
      :height="segment.thickness"
      :transform="segment.transform"
      fill="#3D9DFF"
    />

    <path
      :d="glowPathData"
      fill="none"
      stroke="#3D9DFF"
      stroke-width="5"
      stroke-linecap="round"
      stroke-linejoin="round"
      opacity="0.2"
    />

    <path
      v-if="animated"
      :d="glowPathData"
      fill="none"
      stroke="url(#dotGradient)"
      stroke-width="8"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="moving-dot"
      filter="url(#glow)"
      ref="glowPath"
      :style="pathStyle"
    />

    <path
      v-if="animated"
      :d="glowPathData"
      fill="none"
      stroke="url(#dotGradient)"
      stroke-width="8"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="moving-dot-delayed"
      filter="url(#glow)"
      :style="pathStyle"
    />
  </svg>
</template>

<script lang="ts">
import { defineComponent, computed, ref, onMounted } from 'vue'

export default defineComponent({
  name: 'ChamferedRect',
  props: {
    width: {
      type: Number,
      default: 400,
    },
    height: {
      type: Number,
      default: 200,
    },
    chamfer: {
      type: Number,
      default: 10,
      validator: (value: number) => value >= 0,
    },
    offset: {
      type: Number,
      default: 0,
      validator: (value: number) => value >= -5 && value <= 5,
    },
    thickness: {
      type: Number,
      default: 4,
      validator: (value: number) => value > 0 && value <= 10,
    },
    direction: {
      type: String,
      default: 'outward',
      validator: (value: string) => ['outward', 'inward'].includes(value),
    },
    angleOffset: {
      type: Number,
      default: 0,
      validator: (value: number) => value >= -180 && value <= 180,
    },
    gapStart: {
      type: Number,
      default: 100,
    },
    gapWidth: {
      type: Number,
      default: 210,
    },
    animated: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const svgRef = ref<SVGSVGElement | null>(null)
    const glowPath = ref<SVGPathElement | null>(null)
    const totalPathLength = ref(0)

    onMounted(() => {
      if (glowPath.value) {
        totalPathLength.value = glowPath.value?.getTotalLength() || 0
      }
    })

    const viewBox = computed(() => {
      const p = 4
      return `-${p} -${p} ${props.width + 2 * p} ${props.height + 2 * p}`
    })

    const pathData = computed(() => {
      const { chamfer, width, height, gapStart, gapWidth } = props
      const c = Math.min(chamfer, Math.min(width, height) / 2)

      const safeGapStart = Math.max(c, Math.min(gapStart, width - c - gapWidth))
      const safeGapWidth = Math.min(gapWidth, width - 2 * c - safeGapStart)
      const gapEnd = safeGapStart + safeGapWidth

      return `
        M ${c},0
        L ${safeGapStart},0
        M ${gapEnd},0
        L ${width - c},0
        L ${width},${c}
        L ${width},${height - c}
        L ${width - c},${height}
        L ${c},${height}
        L ${0},${height - c}
        L ${0},${c}
        L ${c},0
        L ${safeGapStart},0
        M ${gapEnd},0
        L ${width - c},0
      `
    })

    const chamferSegments = computed(() => {
      const { chamfer, width, height, offset, thickness, direction, angleOffset } = props
      const c = Math.min(chamfer, Math.min(width, height) / 2)
      const diagonalLength = Math.sqrt(2) * c
      const offsetSign = direction === 'outward' ? 1 : -1

      // 法线方向的单位向量（归一化）
      const normal = (dx: number, dy: number) => {
        const len = Math.sqrt(dx * dx + dy * dy)
        return len ? [-dy / len, dx / len] : [0, 0]
      }

      // 计算斜边角度（度）
      const getAngle = (dx: number, dy: number) => {
        return (Math.atan2(dy, dx) * 180) / Math.PI // 范围：-180 到 180 度
      }

      // 原始斜边坐标和角度
      const segments = [
        {
          start: [width - c - diagonalLength + 1, 0],
          end: [width - c, 0],
          angle: 0,
          n: normal(c, c),
        },
        {
          start: [width - c, 0],
          end: [width, c],
          angle: getAngle(c, c), // 45 度
        },
        {
          start: [width, c - 1],
          end: [width, 2 * c],
          angle: getAngle(0, c),
          n: normal(c, c),
        },

        {
          start: [width, height - c - diagonalLength + 1],
          end: [width, height - c],
          angle: getAngle(0, c),
          n: normal(-c, c),
        },
        // 右下角：(width, height-c) -> (width-c, height)
        // 斜率 -1，角度 -45 度（左下方向）
        {
          start: [width, height - c],
          end: [width - c, height],
          angle: getAngle(-c, c), // -45 度
        },
        {
          start: [width - c - diagonalLength + 1, height],
          end: [width - c, height],
          angle: getAngle(c, 0),
          n: normal(-c, c),
        },
        {
          start: [c - 1, height],
          end: [diagonalLength + c, height],
          angle: getAngle(c, 0),
          n: normal(-c, -c),
        },
        // 左下角：(c, height) -> (0, height-c)
        // 斜率 1，角度 45 度（左上方向，atan2 调整）
        {
          start: [c, height],
          end: [0, height - c],
          angle: getAngle(-c, -c), // 45 度
        },
        {
          start: [0, height - c - diagonalLength + 1],
          end: [0, height - c],
          angle: getAngle(0, c),
          n: normal(-c, -c),
        },
        {
          start: [0, c - 1.5],
          end: [0, 2 * c],
          angle: getAngle(0, c),
          n: normal(c, -c),
        },
        // 左上角：(0, c) -> (c, 0)
        // 斜率 -1，角度 -45 度（右上方向）
        {
          start: [0, c],
          end: [c, 0],
          angle: getAngle(c, -c), // -45 度
        },
        {
          start: [c - 1, 0],
          end: [c + diagonalLength, 0],
          angle: getAngle(c, 0),
          n: normal(c, -c),
        },
      ]

      // 计算rect的定位和变换
      return segments.map((segment) => {
        const dx = segment.end[0] - segment.start[0]
        const dy = segment.end[1] - segment.start[1]
        const [nx, ny] = segment.n || normal(dx, dy)
        const x = segment.start[0] + nx * offset * offsetSign
        const y = segment.start[1] + ny * offset * offsetSign - thickness / 2
        const adjustedAngle = segment.angle + angleOffset
        return {
          x,
          y,
          width: diagonalLength,
          thickness,
          transform: `rotate(${adjustedAngle}, ${x}, ${y + thickness / 2})`,
        }
      })
    })

    const glowPathData = computed(() => {
      const { chamfer, width, height } = props
      const c = Math.min(chamfer, Math.min(width, height) / 2)

      return `
        M ${c},0
        L ${width - c},0
        L ${width},${c}
        L ${width},${height - c}
        L ${width - c},${height}
        L ${c},${height}
        L 0,${height - c}
        L 0,${c}
        Z
      `
    })

    const pathStyle = computed(() => ({
      '--path-length': `${totalPathLength.value}px`,
      '--animation-duration': '8s',
    }))

    return {
      viewBox,
      pathData,
      glowPathData,
      chamferSegments,
      svgRef,
      glowPath,
      totalPathLength,
      pathStyle,
    }
  },
})
</script>

<style scoped>
svg {
  display: block;
}

.moving-dot {
  stroke-dasharray: 30 var(--path-length, 2000);
  animation: dot-flow var(--animation-duration, 8s) linear infinite;
}

.moving-dot-delayed {
  stroke-dasharray: 30 var(--path-length, 2000);
  animation: dot-flow var(--animation-duration, 8s) linear infinite;
  animation-delay: calc(var(--animation-duration, 8s) / -2);
}

@keyframes dot-flow {
  from {
    stroke-dashoffset: var(--path-length, 2000);
  }
  to {
    stroke-dashoffset: 0;
  }
}
</style>
