<template>
  <el-descriptions size="large" :column="1" label-width="90px">
    <el-descriptions-item label="办结申请原因" label-align="right" label-class-name="text-right">{{ dataInfo.completeReason }}</el-descriptions-item>
    <el-descriptions-item label="图片" label-align="right" label-class-name="text-right">
      <el-image v-for="(item, index) in completeImgArr" :key="index" :src="item" :preview-src-list="completeImgArr" :initial-index="index" fit="cover" class="w-[160px] h-[160px]" />
    </el-descriptions-item>
    <el-descriptions-item label="附件" label-align="right" label-class-name="text-right">
      <el-link v-for="(item, index) in completeFileArr" :key="index" :src="item" :icon="Files" type="primary">{{ item }}</el-link>
    </el-descriptions-item>
  </el-descriptions>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="105px"
  >
  </z-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import type { ZFormField } from '@/ui/components/ZForm/types.ts'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import SupervisionApi from '@/api/supervision.ts'
import { Files } from '@element-plus/icons-vue'

const props = defineProps<{
  model: Record<string, any> | null
}>()

const form = reactive({
  completeId: '',
  approveResult: '',
  approveMessage: ''
})

const fields = ref<ZFormField[]>([
  {
    type: 'input',
    name: 'completeId',
    show: false
  },
  {
    type: 'radio',
    name: 'approveResult',
    label: '审批批示',
    options: [
      { label: '同意', value: '0' },
      { label: '不同意', value: '1' }
    ],
    rules: [
      { required: true, message: '请选择审批批示', trigger: 'change' }
    ]
  },
  {
    type: 'input',
    name: 'approveMessage',
    label: '审批意见',
    props: {
      placeholder: '请输入审批意见',
      maxlength: 1000,
      showWordLimit: true,
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 }
    }
  }
])

const dataInfo = ref<any>({})
const completeFileArr = computed(() => dataInfo.value.completeFile?.split(',').map((v:any) => import.meta.env.VITE_STATIC_ASSETS_URL + v) || [])
const completeImgArr = computed(() => dataInfo.value.completeImg?.split(',').map((v:any) => import.meta.env.VITE_STATIC_ASSETS_URL + v) || [])
const { onOpen } = useDialogFormContext()
onOpen(() => {
  form.completeId = props?.model?.sonId
  SupervisionApi.getCompletedDetail(props?.model?.sonId).then(res => {
    dataInfo.value = res.data
  })
})
</script>

<style scoped lang="scss">

</style>
