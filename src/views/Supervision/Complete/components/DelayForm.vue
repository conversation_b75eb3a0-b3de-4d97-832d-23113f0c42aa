<template>
  <el-descriptions size="large" :column="1" label-width="80px">
    <el-descriptions-item label="原完成时限" label-align="right" label-class-name="text-right">{{ dataInfo.oldDate }}</el-descriptions-item>
    <el-descriptions-item label="延期日期" label-align="right" label-class-name="text-right">{{ dataInfo.newDate }}</el-descriptions-item>
    <el-descriptions-item label="延期原因" label-align="right" label-class-name="text-right">{{ dataInfo.periodReason }}</el-descriptions-item>
  </el-descriptions>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="95px"
  >
  </z-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { ZFormField } from '@/ui/components/ZForm/types.ts'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context'
import SupervisionApi from '@/api/supervision.ts'

const props = defineProps<{
  model: Record<string, any> | null
}>()

const form = reactive({
  periodId: '',
  approveResult: '',
  approveMessage: ''
})

const fields = ref<ZFormField[]>([
  {
    type: 'input',
    name: 'periodId',
    show: false
  },
  {
    type: 'radio',
    name: 'approveResult',
    label: '审批批示',
    options: [
      { label: '同意', value: '0' },
      { label: '不同意', value: '1' }
    ],
    rules: [
      { required: true, message: '请选择审批批示', trigger: 'change' }
    ]
  },
  {
    type: 'input',
    name: 'approveMessage',
    label: '审批意见',
    props: {
      placeholder: '请输入审批意见',
      maxlength: 1000,
      showWordLimit: true,
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 }
    }
  }
])

const dataInfo = ref<any>({})
const { onOpen } = useDialogFormContext()
onOpen(() => {
  form.periodId = props?.model?.sonId
  SupervisionApi.getDelayDetail(props?.model?.sonId).then(res => {
    dataInfodataInfo.value = res.data
  })
})
</script>

<style scoped lang="scss">

</style>
