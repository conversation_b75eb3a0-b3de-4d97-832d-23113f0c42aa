<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="80px"
  >
    <template #item-urgeFile="{ validate, value, updateValue }">
      <z-upload
        :model-value="value"
        :allowed-types="[FileType.OTHER]"
        :file-type-map="{
          other: ['doc', 'docx', 'xlsx', 'xls', 'pdf'],
        }"
        :limit="5"
        :max-size="5"
        tip="支持格式：doc、docx、xlsx、xls、pdf文件，最多上传5个附件，每个附件大小超过5M"
        @success="(r, file, newFileList) => handleUploadSuccess(r, { validate, updateValue }, newFileList)">
      </z-upload>
    </template>
  </z-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { ZFormField } from '@/ui/components/ZForm/types.ts'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context.ts'
import { FileType } from '@/ui/components/ZUpload/constants.ts'

const props = defineProps<{
  model: Record<string, any> | null
}>()

const form = reactive({
  superviseId: '',
  urgeContent: '',
  urgeFile: []
})

const fields = ref<ZFormField[]>([
  {
    type: 'custom',
    name: 'superviseId',
    show: false
  },
  {
    type: 'input',
    name: 'urgeContent',
    label: '催办内容',
    props: {
      placeholder: '请输入催办内容',
      maxlength: 1000,
      showWordLimit: true,
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 }
    },
    rules: [
      { required: true, message: '请输入催办内容', trigger: 'blur' }
    ]
  },
  {
    type: 'custom',
    name: 'urgeFile',
    label: '附件',
  },
])

const handleUploadSuccess = (r: any, { validate, updateValue }: any, newFileList: any[]) => {
  updateValue(newFileList.map((it: any) => it?.response?.data?.absUrl || it.name))
  validate()
}

const { onOpen } = useDialogFormContext()
onOpen(() => {
  form.superviseId = props?.model?.recordId
})
</script>

<style scoped lang="scss">

</style>
