<template>
  <z-page class="pt-2">
    <el-tabs
      v-model="tabsActive"
      @tab-click="tabsClick"
    >
      <el-tab-pane
        v-for="v in tabsRows"
        :key="v.value"
        :label="`${v.label}(${v.total})`"
        :name="v.value"
      ></el-tab-pane>
    </el-tabs>
    <z-table
      row-key="recordId"
      ref="tableRef"
      v-model:query="query"
      :columns="columns"
      :fetch-data="fetchTableData"
      paginationMode="server"
      show-toolbar
      :toolbar-rows="toolbarRows"
    >
      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            path: '/CheckDetails',
            viewPath: '@/views/Supervision/CheckList/CheckDetails.vue',
            meta: {
              title: '查看督办清单'
            }
          }"
        >
          <template #default="{ navigate }">
            <el-button
              size="small"
              type="primary"
              link
              @click="() => navigate({
                query: {
                  id: row.recordId
                }
              })"
            >{{ t('common.view') }}
            </el-button>
          </template>
        </z-dynamic-route>
        <el-button
          v-if="['1', '4'].includes(tabsActive)"
          size="small"
          type="primary"
          link
          @click="onOpenDialog('taskExpedite', row)"
        >任务催办
        </el-button>
        <template v-if="tabsActive !== '4'">
          <el-button
            v-if="row.hasPeriod === '1'"
            size="small"
            type="primary"
            link
            @click="onOpenDialog('delay', row)"
          >延期处理
          </el-button>
          <el-button
            v-if="row.hasComplete === '1'"
            size="small"
            type="primary"
            link
            @click="onOpenDialog('completion', row)"
          >核查处理
          </el-button>
        </template>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <component :is="dialogFormComponents" :model="dialog.model.value" :mode="dialog.mode.value" />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import { ref, h, computed } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import type { ZTableColumn, ZTableParams, ZTableToolbarRow } from '@/ui/components/ZTable/types.ts'
import DictApI from '@/api/dict.ts'
import SupervisionApi, { DictCodes } from '@/api/supervision.ts'
import { useTypedI18n } from '@/i18n'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'
import { useDialog } from '@/composables/useDialog.ts'
import TaskExpediteForm from './components/TaskExpediteForm.vue'
import CompletionForm from './components/CompletionForm.vue'
import DelayForm from './components/DelayForm.vue'

import { ElTag } from 'element-plus'

const { t } = useTypedI18n()

const tableRef = ref()

const tabsActive = ref('1')
const tabsRows = ref([
  { value: '1', total: 0, label: '全部', key: 'allSum' },
  { value: '2', total: 0, label: '延期申请', key: 'extendDateSum' },
  { value: '3', total: 0, label: '核查申请', key: 'completeSum' },
  { value: '4', total: 0, label: '催办任务', key: 'urgeSum' }
])
const tabsClick = () => {
  tableRef.value?.reset()
}

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        key: 'questionType',
        type: 'select',
        label: '问题类型',
        placeholder: '请输入问题类型',
        width: 240,
        options: () => DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
          .then(res => res.data.map((it: any) => ({
            label: it.dictionaryName,
            value: it.dictionaryCode
          }))),
      },
      { key: 'projectName', type: 'input', label: '项目名称', placeholder: '请输入项目名称', width: 240 },
      ZTableToolbarFactory.createSearch(),
      ZTableToolbarFactory.createReset()
    ],
  },
]

const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'questionNo', label: '问题编号' },
  {
    prop: 'questionType',
    label: '问题类型',
    asyncFormatter: {
      cacheKey: 'anyQuestionTypeValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.questionType)?.dictionaryName,
    },
  },
  { prop: 'projectName', label: '项目名称' },
  { prop: 'startDate', label: '任务开始日期', },
  {
    prop: 'endDate',
    label: '预计完成日期',
    formatter: (row) => {
      return h('div',
        { class: 'flex items-center'},
        [
          h('span', { class: 'mr-1' }, row.endDate),
          row.hasComplete !== '2' && row.residueDate <= 3
            ? h(ElTag,
              {
                size: 'small',
                type: row.residueDate <= 0 ? 'danger' : 'warning',
                effect: 'dark'
              }, () => row.residueDate <= 0 ? '超期' : `${row.residueDate}天`)
            : null
        ])
    }
  },
  {
    prop: 'reportPeriod',
    label: '整改上报周期',
    asyncFormatter: {
      cacheKey: 'anyReportPeriodValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode(DictCodes.PeriodCycle)
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.reportPeriod)?.dictionaryName,
    },
  },
  { prop: 'dutyDeptName', label: '责任部门' },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
])
const fetchTableData = async (params: ZTableParams) => {
  return SupervisionApi.getInspectList({ ...params, queryType: tabsActive.value }).then(
    (res) => res.data,
  )
}

const FormComponents = {
  taskExpedite: TaskExpediteForm,
  completion: CompletionForm,
  delay: DelayForm
}
const dialogFormComponents = computed(() => {
  return FormComponents[dialog.mode.value as keyof typeof FormComponents]
})
const dialog = useDialog({
  name: '',
  modeMap: {
    taskExpedite: '任务催办',
    delay: '延期处理',
    completion: '核查处理'
  },
  onConfirm(mode, ctx) {
    ctx.form?.validate((isValid, values) => {
      if (isValid) {
        if(mode === 'taskExpedite') {
          ctx.isSubmitting.value = true
          SupervisionApi.addTaskUrge({
            ...values,
            urgeFile: values.urgeFile?.toString(),
          })
            .then(() => {
              getStatistics()
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.operationSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }

        if(mode === 'completion') {
          SupervisionApi.approveCompleted(values)
            .then(() => {
              getStatistics()
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.operationSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }

        if(mode === 'delay') {
          SupervisionApi.approveDelay(values)
            .then(() => {
              getStatistics()
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.operationSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      }
    })
  }
})
const onOpenDialog = (type: string, row: any) => {
  dialog.open(type, row)
}

const getStatistics = () => {
  SupervisionApi.getInspectStatistics().then((res) => {
    tabsRows.value.forEach((item) => {
      item.total = res.data[item.key] || 0
    })
  })
}
getStatistics()
</script>

<style scoped lang="scss">

</style>
