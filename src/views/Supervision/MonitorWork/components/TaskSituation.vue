<template>
  <div class="bg-white py-[14px] px-[25px] mt-4 border-rd-md gap-4">
    <div v-if="isCom" class="border-b border-gray-200">
      <div class="flex items-center justify-between">
        <span class="font-size-4 text-base color-[#333333] font-medium">任务情况</span>
        <z-dynamic-route
          :route="{
              path: '/TaskList',
              viewPath: '@/views/Supervision/MonitorWork/TaskList.vue',
              meta: {
                title: '任务情况'
              }
            }"
        >
          <template #default="{ navigate }">
            <span class="cursor-pointer text-xs color-[#A1ADBC] hover:text-blue-500" @click="navigate()">查看更多 ></span>
          </template>
        </z-dynamic-route>
      </div>
    </div>
    <el-tabs
      v-model="tabActive"
      @tab-click="tabsClick"
    >
      <el-tab-pane
        v-for="v in tabsRows"
        :key="v.value"
        :label="`${v.label}(${v.total})`"
        :name="v.value"
      ></el-tab-pane>
    </el-tabs>
    <z-table
      row-key="recordId"
      ref="tableRef"
      :columns="columns"
      :fetch-data="fetchTableData"
      paginationMode="server"
      :use-pagination="!isCom"
    >
      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            path: '/CheckDetails',
            viewPath: '@/views/Supervision/CheckList/CheckDetails.vue',
            meta: {
              title: '查看督办清单'
            }
          }"
        >
          <template #default="{ navigate }">
            <el-button
              size="small"
              type="primary"
              link
              @click="navigate({
                query: {
                  id: row.recordId
                }
              })"
            >查看
            </el-button>
          </template>
        </z-dynamic-route>
        <el-button
          v-if="tabActive === '2'"
          size="small"
          type="primary"
          link
          @click="onOpenDialog(row)"
        >任务反馈
        </el-button>
      </template>
    </z-table>
    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <TaskFeedbackForm :model="dialog.model.value" :mode="dialog.mode.value" />
    </z-dialog-form>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { ZTableColumn, ZTableParams } from '@/ui/components/ZTable/types.ts'
import SupervisionApi, { DictCodes } from '@/api/supervision.ts'
import DictApI from '@/api/dict.ts'
import { ElButton, ElMessage } from 'element-plus'
import { useDialog } from '@/composables/useDialog.ts'
import { useTypedI18n } from '@/i18n'
import TaskFeedbackForm from '../../FeedBack/components/TaskFeedbackForm.vue'

const { t } = useTypedI18n()

interface Props {
  type: 'component' | 'route'
}
const props = defineProps<Props>()

const isCom = computed(() => props.type === 'component')

const tableRef = ref()
const tabActive = ref('1')
const tabsRows = ref([
  { value: '1', total: 0, label: '领导重点关注', key: 'followSum' },
  { value: '2', total: 0, label: '待反馈任务', key: 'feedbackSum' },
  { value: '3', total: 0, label: '催办任务', key: 'urgeSum' },
])
const tabsClick = () => {
  tableRef.value?.reset()
}

const columns = ref<ZTableColumn[]>([
  { prop: 'questionNo', label: '问题编号' },
  {
    prop: 'questionType',
    label: '问题类型',
    asyncFormatter: {
      cacheKey: 'anyQuestionTypeValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.questionType)?.dictionaryName,
    },
  },
  { prop: 'projectName', label: '项目名称' },
  { prop: 'startDate', label: '任务开始日期' },
  { prop: 'endDate', label: '预计完成日期' },
  {
    prop: 'reportPeriod',
    label: '整改上报周期',
    asyncFormatter: {
      cacheKey: 'anyReportPeriodValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode(DictCodes.PeriodCycle)
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.reportPeriod)?.dictionaryName,
    },
  },
  { prop: 'dutyDeptName', label: '责任部门' },
  { prop: 'superviseDeptName', label: '督办部门' },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
])
const fetchTableData = async (params: ZTableParams) => {
  return SupervisionApi.getSuperviseList({ ...params, queryType: tabActive.value }).then(res => res.data)
}

const dialog = useDialog({
  name: '',
  modeMap: {
    taskFeedback: '任务反馈'
  },
  onConfirm(mode, ctx) {
    ctx.form?.validate((isValid, values) => {
      if (isValid) {
        ctx.isSubmitting.value = true
        SupervisionApi.addFeedback({
          ...values,
          feedbackImg: values.feedbackImg?.toString(),
          feedbackFile: values.feedbackFile?.toString(),
        })
          .then(() => {
            getStatistics()
            tableRef.value?.refresh()
            dialog.close()
            ElMessage.success(t('message.operationSuccess'))
          })
          .finally(() => {
            ctx.isSubmitting.value = false
          })
      }
    })
  }
})
const onOpenDialog = (row: any) => {
  dialog.open('taskFeedback', { superviseId: row.recordId, oldDate: row.endDate })
}

const getStatistics = () => {
  SupervisionApi.getSuperviseStatistics().then(res => {
    tabsRows.value.forEach(e => {
      e.total = res.data[e.key] || 0
    })
  })
}
getStatistics()
</script>

<style scoped lang="scss"></style>
