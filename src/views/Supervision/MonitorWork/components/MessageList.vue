<template>
  <div class="bg-white py-[14px] px-[25px] border-rd-sm" :class="{ 'w-[45%]': isCom }">
    <div v-if="isCom" class="border-b border-gray-200">
      <div class="flex items-center justify-between">
        <span class="font-size-4 text-base color-[#333333] font-medium">消息通知</span>
        <z-dynamic-route
          :route="{
              path: '/NewsList',
              viewPath: '@/views/Supervision/MonitorWork/NewsList.vue',
              meta: {
                title: '消息'
              }
            }"
        >
          <template #default="{ navigate }">
            <span class="cursor-pointer text-xs color-[#A1ADBC] hover:text-blue-500" @click="navigate()">查看更多 ></span>
          </template>
        </z-dynamic-route>
      </div>
    </div>
    <el-tabs
      v-model="tabsActive"
      @tab-click="tabsClick"
    >
      <el-tab-pane
        v-for="v in tabsRows"
        :key="v.value"
        :label="`${v.label}(${v.total})`"
        :name="v.value"
      ></el-tab-pane>
    </el-tabs>
    <ul v-loading="loading" class="list-none p-0 m-0 gap-4">
      <li v-for="v in dataList" :key="v.messageId" class="flex w-full mb-[10px] bg-[#F9F9FA] h-[50px] border-rd-sm items-center">
        <div class="relative">
          <img class="w-[50px] h-[50px]" src="../../../../assets/imgs/message.png" alt="">
          <div v-if="v.messageStatus === '0'" class="w-[8px] h-[8px] rounded bg-[#E81010] absolute right-[-2px] top-[-2px]"></div>
        </div>
        <div class="w-[calc(100%-50px)] flex px-4 items-center justify-between box-border">
          <p class="w-[calc(100%-36px)] box-border font-size-[14px] text-base p-0 m-0 color-[#333333]">
            <span class="block font-size-[12px] color-[#AAAAAA] line-height-[12px]">{{ v.createTime }}</span>
            <span class="block truncate max-w-100%">{{ v.messageContent }}</span>
          </p>
          <z-dynamic-route
            :route="{
              path: '/CheckDetails',
              viewPath: '@/views/Supervision/CheckList/CheckDetails.vue',
              meta: {
                title: '查看督办清单'
              }
            }"
          >
            <template #default="{ navigate }">
              <span class="w-[36px] cursor-pointer text-xs text-blue-500" @click="goPage(v, navigate)">详情 ></span>
            </template>
          </z-dynamic-route>
        </div>
      </li>
    </ul>
    <el-empty v-if="!dataList?.length && !loading" />
    <div v-if="!isCom" class="flex justify-end">
      <el-pagination
        v-model:page-size="innerPageSize"
        v-model:current-page="currentPage"
        layout="total, prev, pager, next"
        :total="total"
        background
        @current-change="getList"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import SupervisionApi from '@/api/supervision.ts'

interface Props {
  type: 'component' | 'route',
  pageSize?: number
}
const props = withDefaults(defineProps<Props>(), {
  pageSize: 9
})

const isCom = computed(() => props.type === 'component')

const tabsActive = ref('3')
const tabsRows = ref([
  { value: '3', total: 0, label: '全部消息', key: 'allSum' },
  { value: '0', total: 0, label: '任务消息', key: 'taskSum' },
  { value: '1', total: 0, label: '审批消息', key: 'approveSum' },
  { value: '2', total: 0, label: '催办消息', key: 'urgeSum' },
])
const tabsClick = () => {
  getList()
}

const loading = ref(false)
const currentPage = ref(1)
const innerPageSize = ref(props.pageSize)
const total = ref(0)
const dataList = ref<any[]>()
const getList = () => {
  loading.value = true
  SupervisionApi.getMessageList({
    pageNum: currentPage.value,
    pageSize: innerPageSize.value,
    messageType: tabsActive.value !== '3' ? tabsActive.value : undefined
  }).then(res => {
    dataList.value = res.data?.data || []
    total.value = res.data?.total || 0
  }).finally(() => {
    loading.value = false
  })
}
const getStatistics = () => {
  SupervisionApi.getMessageStatistics().then(res => {
    tabsRows.value = tabsRows.value.map(e => {
      return {
        ...e,
        total: res.data[e.key] || 0
      }
    })
  })
}
getStatistics()
getList()

const goPage = async (v: any, navigate: (route: any) => void) => {
  if (v.messageStatus === '0') {
    await SupervisionApi.updateMessageRead(v.messageId).then().catch()
  }
  navigate({ query: { id: v.superviseId }})
}
</script>

<style scoped lang="scss"></style>
