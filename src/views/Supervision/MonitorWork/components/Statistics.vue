<template>
  <div class="w-[55%]">
    <div class="flex gap-[10px]">
      <div v-for="(v, i) in sumList" :key="i" class="w-[18%] h-[52px] bg-white py-[14px] px-[5px] border-rd-md flex items-center justify-center">
        <img class="w-[40px] h-[40px]" :src="v.icon" alt="">
        <div class="ml-3">
          <span class="block font-size-[12px] mt-[5px] color-[#666666] line-height-[12px] pr-[10px]">{{ v.text }}</span>
          <span class="block font-size-[26px] alibaba-semiBold">{{ v.num }}</span>
        </div>
      </div>
    </div>
    <div class="bg-white py-[14px] px-[25px] border-rd-md mt-4">
      <div class="flex items-center justify-between">
        <span class="font-size-4 text-base color-[#333333] font-medium">公告信息</span>
        <z-dynamic-route
          :route="{
            path: '/NoticeList',
            viewPath: '@/views/Supervision/Notice/NoticeList.vue',
            meta: {
              title: '公告列表'
            }
          }"
        >
          <template #default="{ navigate }">
            <span
              class="cursor-pointer text-xs color-[#A1ADBC] hover:text-blue-500"
              @click="() => navigate()"
            >
              更多 >
            </span>
          </template>
        </z-dynamic-route>
      </div>
    </div>
    <div class="max-h-[320px] bg-white overflow-y-auto hidden-scrollbar">
      <ul class="list-none p-0 m-0 gap-4">
        <li v-for="v in noticeList" :key="v.noticeId" class="flex w-full h-[75px] mb-[10px] border-rd-md box-border px-[23px] setBorderB">
          <img class="w-[40px] h-[40px] mr-4" src="@/assets/imgs/system.png" alt="">
          <div class="w-[calc(100%-56px)] box-border">
            <p class="w-100% box-border font-size-[14px] text-base p-0 m-0 color-[#333333]">
              <span class="block font-size-[14px] mt-[5px] line-height-[20px]">系统公告</span>
              <span class="block font-size-[12px] color-[#AAAAAA] line-height-[12px]">{{ v.releaseTime }}</span>
            </p>
            <p class="w-100% box-border font-size-[14px] text-base p-0 m-0 color-[#333333] flex items-center justify-between">
              <span class="block truncate w-[calc(100%-36px)]">{{ v.noticeTitle }}</span>
              <z-dynamic-route
                :route="{
                  path: 'NoticeDetails',
                  viewPath: '@/views/Supervision/Notice/NoticeDetails.vue',
                  meta: {
                    title: '公告详情'
                  }
                }"
              >
                <template #default="{ navigate }">
                  <span
                    class="block w-[36px] cursor-pointer text-xs text-blue-500"
                      @click="
                      () =>
                        navigate({
                          query: {
                            id: v.noticeId,
                          },
                        })
                    ">
                    详情 >
                  </span>
                </template>
              </z-dynamic-route>
            </p>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import allIcon from '@/assets/imgs/allicon.png'
import SupervisionApi from '@/api/supervision.ts'

const sumList = ref([
  { text: '任务数', key: 'taskSum', num: 0, icon: allIcon },
  { text: '进行中', key: 'proceedSum', num: 0, icon: allIcon },
  { text: '已办结', key: 'completeSum', num: 0, icon: allIcon },
  { text: '超期数', key: 'exceedDateSum', num: 0, icon: allIcon },
  { text: '完成进度', key: 'completeRate', num: 0, icon: allIcon },
])
const noticeList = ref<any[]>([])
const getData = () => {
  SupervisionApi.getWorkbenchStatistics().then(res => {
    sumList.value = sumList.value.map((e, i) => {
      return {
        ...e,
        num: i === 4 ? Math.floor((res.data[e.key] * 100)) + '%' : res.data[e.key]
      }
    })
  })
  SupervisionApi.getNoticeList({
    pageSize: 10
  }).then(res => {
    noticeList.value = res.data?.data || []
  })
}
getData()
</script>

<style scoped lang="scss">
.setBorderB {
  border-bottom: 1px solid #ebeef5;
}
.hidden-scrollbar {
  overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}
.hidden-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome、Safari 和 Opera */
}
</style>
