<template>
  <div class="w-full">
    <div class="flex gap-4">
      <!-- 统计 -->
      <statistics />
      <!-- 消息通知 -->
      <message-list type="component" :page-size="6" />
    </div>
    <!-- 任务情况 -->
    <task-situation type="component" />
  </div>
</template>

<script setup lang="ts">
import Statistics from './components/Statistics.vue';
import MessageList from './components/MessageList.vue'
import TaskSituation from './components/TaskSituation.vue';

</script>

<style scoped lang="scss"></style>
