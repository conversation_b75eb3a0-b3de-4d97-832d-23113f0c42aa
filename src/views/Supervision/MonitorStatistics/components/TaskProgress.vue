<template>
  <div class="flex-1 ml-6">
    <h1 class="text-base c-[#2D333B] font-normal">任务问题类型进度统计</h1>
    <div class="border border-solid border-[#dcdfe6] h-[316px]">
      <div class="flex border-b border-b-solid border-b-[#CFD6DF] h-[38px]">
        <div
          v-for="v in tabsRows"
          :key="v.dictionaryCode"
          class="min-w-[100px] flex justify-center items-center cursor-pointer border-r border-r-solid border-r-[#CFD6DF] hover:bg-[#396EF9] hover:text-white transition duration-200 ease-in-out"
          :class="{ 'bg-[#396EF9] text-white': tabsActive === v.dictionaryCode }"
          @click="tabsClick(v)"
        >
          <span class="text-sm">{{ v.dictionaryName }}</span>
        </div>
      </div>
      <div class="flex">
        <div
          ref="chartRef1"
          class="flex-1 h-[278px]"
        ></div>
        <div
          ref="chartRef2"
          class="flex-1 h-[278px]"
        ></div>
        <div
          ref="chartRef3"
          class="flex-1 h-[278px]"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import DictApI from '@/api/dict.ts'
import SupervisionApi, { DictCodes } from '@/api/supervision.ts'
import * as echarts from 'echarts'
import { round } from 'lodash'

type EChartsOption = echarts.EChartsOption

const tabsActive = ref('all')
const tabsRows = ref<any[]>([])
const tabsClick = (v: any) => {
  tabsActive.value = v.dictionaryCode
  getChartData()
}

const chartRef1 = ref()
const chartRef2 = ref()
const chartRef3 = ref()
const chartCom = computed(() => {
  return [
    {
      ref: chartRef1,
      color: '#396EF9',
      name: '任务办结率',
      value: dataInfo.value?.completeRate,
    },
    {
      ref: chartRef2,
      color: '#6f3ada',
      name: '超期率',
      value: dataInfo.value?.exceedDateRate,
    },
    {
      ref: chartRef3,
      color: '#2fbbda',
      name: '整改质量合格率',
      value: dataInfo.value?.standardRate,
    }
  ]
})

const initChart = () => {
  chartCom.value.forEach((e) => {
    const chart = echarts.init(e.ref.value)
    const option: EChartsOption = {
      title: {
        text: `${round(e.value * 100)}%`,
        top: '41%',
        textAlign: 'center',
        left: '49%',
        textStyle: {
          fontSize: 22,
        },
        subtext: e.name,
      },
      color: [e.color, '#E8E8E8'],
      series: [
        {
          type: 'pie',
          radius: ['50%', '66%'],
          avoidLabelOverlap: true,
          label: {
            show: false,
          },
          emphasis: {
            disabled: false,
            scale: true,
            scaleSize: 12,
          },
          silent: true,
          data: [
            {
              value: round(e.value * 100),
            },
            {
              value: 100 - round(e.value * 100),
            }
          ]
        }
      ]
    }
    chart.setOption(option)
    chart.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: 0,
    })
  })
}

const dataInfo = ref<any>()
const getChartData = async () => {
  const { data } = await SupervisionApi.getSuperviseProgressStatistics({
    questionType: tabsActive.value !== 'all' ? tabsActive.value : undefined,
  })
  dataInfo.value = data
  initChart()
}
const getTabsRows = async () => {
  const { data } = await DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
  tabsRows.value = [{ dictionaryCode: 'all', dictionaryName: '全部' }, ...data]
  await getChartData()
}

onMounted(() => {
  getTabsRows()
})
</script>

<style scoped lang="scss"></style>
