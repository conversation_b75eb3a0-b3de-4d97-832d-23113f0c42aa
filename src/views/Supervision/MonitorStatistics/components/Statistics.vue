<template>
  <div class="flex justify-start">
    <div v-for="(v, i) in cardRows" :key="v.key" class="flex items-center w-1/4">
      <el-divider v-if="i !== 0" direction="vertical" class="h-[40px] mr-10" />
      <img src="@/assets/imgs/allicon.png" alt="" class="w-[40px] h-[40px]">
      <div class="flex flex-col ml-5">
        <span class="text-[13px] c-[#666]">{{ v.label }}</span>
        <span class="text-[24px] c-[#2D333B] mt-2 alibaba-semiBold">{{ v.total }} 个</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SupervisionApi from '@/api/supervision.ts'
import { ref } from 'vue'

const cardRows = ref([
  { total: 0, label: '任务总数', key: 'taskSum' },
  { total: 0, label: '紧急任务数', key: 'exigenceSum' },
  { total: 0, label: '重要任务数', key: 'importantSum' },
  { total: 0, label: '催办任务数', key: 'urgeSum' }
])
const getStatistics = () => {
  SupervisionApi.getSuperviseTotalStatistics().then((res) => {
    cardRows.value.forEach((item) => {
      item.total = res.data[item.key] || 0
    })
  })
}
getStatistics()
</script>

<style scoped lang="scss">

</style>
