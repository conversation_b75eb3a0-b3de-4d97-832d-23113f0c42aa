<template>
  <div>
    <h1 class="text-base c-[#2D333B] font-normal">超期任务列表</h1>
    <z-table
      row-key="recordId"
      ref="tableRef"
      :columns="columns"
      :fetch-data="fetchTableData"
      paginationMode="server"
      :use-pagination="false"
    >
      <template #operation="{ row }">

        <z-dynamic-route
          :route="{
            path: '/CheckDetails',
            viewPath: '@/views/Supervision/CheckList/CheckDetails.vue',
            meta: {
              title: '查看督办清单'
            }
          }"
        >
          <template #default="{ navigate }">
            <el-button
              size="small"
              type="primary"
              link
              @click="navigate({
                query: {
                  id: row.recordId
                }
              })"
            >查看
            </el-button>
          </template>
        </z-dynamic-route>
      </template>
    </z-table>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ZTableColumn } from '@/ui/components/ZTable/types.ts'
import SupervisionApi from '@/api/supervision.ts'

const columns = ref<ZTableColumn[]>([
  { prop: 'projectName', label: '标题' },
  { prop: 'endDate', label: '任务期限' },
  {
    prop: '任务状态',
    label: '任务状态',
    formatter: () => '超期'
  },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
])

const fetchTableData = async () => {
  return SupervisionApi.getOverdueList().then(res => res.data)
}
</script>

<style scoped lang="scss"></style>
