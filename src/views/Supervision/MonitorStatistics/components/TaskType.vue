<template>
  <div class="w-[45%]">
    <h1 class="text-base c-[#2D333B] font-normal">任务问题类型统计</h1>
    <div class="border border-solid border-[#dcdfe6] h-[316px] flex">
      <div ref="chartRef" class="h-[316px] w-1/2"></div>
      <div class="flex-1 flex flex-col justify-center items-center">
        <div v-for="(v, i) in dataList" :key="v.dictionaryCode" class="px-4 py-1 flex items-center rounded-1 mb-2 cursor-pointer" :class="tabIndex === i ? 'bg-[#EEF5FF]' : 'bg-[#F9F9FA]'" @click="tabClick(i)">
          <span class="w-[10px] h-[10px] rounded-full mr-2" :style="{ backgroundColor: v.color }"></span>
          <span class="text-sm">{{ v.taskName }} {{ v.taskCount }}个 占比{{ v.percent() }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import * as echarts from 'echarts';
import DictApI from '@/api/dict.ts'
import SupervisionApi, { DictCodes } from '@/api/supervision.ts'

type EChartsOption = echarts.EChartsOption

const COLORS = ['#396EF9', '#AF3ADA', '#FA9A44', '#14BA87', '#6CB8FF']

const chartRef = ref()

let chart: any = null
const tabIndex = ref(0)
const initChart = () => {
  chart = echarts.init(chartRef.value)
  const seriesData = dataList.value.map((e: any) => {
    return {
      value: e.taskCount,
      name: e.taskName
    }
  })
  const option: EChartsOption = {
    title: {
      text: '任务类型',
      top: '46%',
      textAlign: "center",
      left: "49%",
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    color: COLORS,
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: false
          },
          scale: true,
          scaleSize: 15
        },
        data: seriesData
      }
    ]
  }
  chart.setOption(option)
  tabActive()
}

const tabClick = (i: number) => {
  tabIndex.value = i
  tabActive()
}

const tabActive = () => {
  const length = dataList.value.length - 1
  // 提示框
  // chart.dispatchAction({
  //   type: "hideTip",
  //   seriesIndex: 0,
  //   dataIndex: tabIndex.value,
  // });
  // chart.dispatchAction({
  //   type: "showTip",
  //   seriesIndex: 0,
  //   dataIndex: tabIndex.value,
  // });
  // 取消高亮指定的数据图形
  chart.dispatchAction({
    type: "downplay",
    seriesIndex: 0,
    dataIndex: tabIndex.value == 0 ? length : tabIndex.value - 1,
  });
  chart.dispatchAction({
    type: "highlight",
    seriesIndex: 0,
    dataIndex: tabIndex.value,
  })
}

const dataList = ref<any[]>([])
const getTabsRows = async () => {
  try {
    const { data: dict } = await DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
    const { data: list } = await SupervisionApi.getSuperviseTypeStatistics()
    dataList.value = dict.map((e: any, i: number) => {
      const taskCount = list.find((d: any) => d.taskType === e.dictionaryCode)?.taskCount || 0
      return {
        color: COLORS[i],
        taskCount,
        taskName: e.dictionaryName,
        percent: () => {
          const sum = list.reduce((acc: number, cur: any) => acc + cur.taskCount, 0)
          return sum && (taskCount / sum * 100).toFixed(2) || '0.00'
        }
      }
    })
    initChart()
  } catch (error) {
    console.log(error)
  }
}


onMounted(() => {
  getTabsRows()
})
</script>

<style scoped lang="scss">

</style>
