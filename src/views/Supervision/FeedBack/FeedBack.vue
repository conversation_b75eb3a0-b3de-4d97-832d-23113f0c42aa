<template>
  <z-page class="pt-2">
    <el-tabs
      v-model="tabsActive"
      @tab-click="tabsClick"
    >
      <el-tab-pane
        v-for="v in tabsRows"
        :key="v.value"
        :label="`${v.label}(${v.total})`"
        :name="v.value"
      ></el-tab-pane>
    </el-tabs>
    <z-table
      row-key="recordId"
      ref="tableRef"
      v-model:query="query"
      :columns="columns"
      :fetch-data="fetchTableData"
      paginationMode="server"
      show-toolbar
      :toolbar-rows="toolbarRows"
    >
      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            path: '/CheckDetails',
            viewPath: '@/views/Supervision/CheckList/CheckDetails.vue',
            meta: {
              title: '查看督办清单'
            }
          }"
        >
          <template #default="{ navigate }">
            <el-button
              size="small"
              type="primary"
              link
              @click="() => navigate({
                query: {
                  id: row.recordId
                }
              })"
            >{{ t('common.view') }}
            </el-button>
          </template>
        </z-dynamic-route>
        <el-button
          v-if="row.hasComplete !== '2'"
          size="small"
          type="primary"
          link
          @click="onOpenDialog('taskFeedback', row)"
        >任务反馈
        </el-button>
        <el-button
          v-if="['0', '3'].includes(row.hasComplete) && row.hasPeriod !== '1'"
          size="small"
          type="primary"
          link
          @click="onOpenDialog('inspectApply', row)"
        >核查申请
        </el-button>
        <el-button
          v-if="row.hasPeriod !== '1' && !['1', '2'].includes(row.hasComplete)"
          size="small"
          type="primary"
          link
          @click="onOpenDialog('delayApply', row)"
        >延期申请
        </el-button>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <component :is="dialogFormComponents" :model="dialog.model.value" :mode="dialog.mode.value" />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import { ref, h, computed } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import type { ZTableColumn, ZTableParams, ZTableToolbarRow } from '@/ui/components/ZTable/types.ts'
import DictApI from '@/api/dict.ts'
import SupervisionApi, { DictCodes } from '@/api/supervision.ts'
import { useTypedI18n } from '@/i18n'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'
import { useDialog } from '@/composables/useDialog.ts'
import TaskFeedbackForm from './components/TaskFeedbackForm.vue'
import InspectApplyForm from './components/InspectApplyForm.vue'
import DelayApplyForm from './components/DelayApplyForm.vue'
import { ElTag } from 'element-plus'

const { t } = useTypedI18n()

const tableRef = ref()

const tabsActive = ref('1')
const tabsRows = ref([
  { value: '1', total: 0, label: '全部', key: 'allSum' },
  { value: '0', total: 0, label: '进行中', key: 'proceedSum' },
  { value: '3', total: 0, label: '核查不通过', key: 'rejectSum' },
  { value: '2', total: 0, label: '已办结', key: 'completeSum' }
])
const tabsClick = () => {
  tableRef.value?.reset()
}

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        key: 'questionType',
        type: 'select',
        label: '问题类型',
        placeholder: '请输入问题类型',
        width: 240,
        options: () => DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
          .then(res => res.data.map((it: any) => ({
            label: it.dictionaryName,
            value: it.dictionaryCode
          }))),
      },
      { key: 'projectName', type: 'input', label: '项目名称', placeholder: '请输入项目名称', width: 240 },
      ZTableToolbarFactory.createSearch(),
      ZTableToolbarFactory.createReset()
    ],
  },
]

const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'questionNo', label: '问题编号' },
  {
    prop: 'questionType',
    label: '问题类型',
    asyncFormatter: {
      cacheKey: 'anyQuestionTypeValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.questionType)?.dictionaryName,
    },
  },
  { prop: 'projectName', label: '项目名称' },
  { prop: 'startDate', label: '任务开始日期' },
  {
    prop: 'endDate',
    label: '预计完成日期',
    formatter: (row) => {
      return h('div',
        { class: 'flex items-center'},
        [
          h('span', { class: 'mr-1' }, row.endDate),
          (row.residueDate <= 3 && row.hasComplete !== '2')
            ? h(ElTag,
              {
                size: 'small',
                type: row.residueDate <= 0 ? 'danger' : 'warning',
                effect: 'dark'
              }, () => row.residueDate <= 0 ? '超期' : `${row.residueDate}天`)
            : null
      ])
    }
  },
  {
    prop: 'reportPeriod',
    label: '整改上报周期',
    asyncFormatter: {
      cacheKey: 'anyReportPeriodValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode(DictCodes.PeriodCycle)
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.reportPeriod)?.dictionaryName,
    },
  },
  { prop: 'superviseDeptName', label: '督办部门' },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
])
const fetchTableData = async (params: ZTableParams) => {
  return SupervisionApi.getFeedbackDeployList({ ...params, hasComplete: tabsActive.value !== '1' ? tabsActive.value : undefined }).then(
    (res) => res.data,
  )
}

const dialog = useDialog({
  name: '',
  modeMap: {
    taskFeedback: '任务反馈',
    inspectApply: '核查申请',
    delayApply: '延期申请'
  },
  onConfirm(mode, ctx) {
    ctx.form?.validate((isValid, values) => {
      if (isValid) {
        if(mode === 'taskFeedback') {
          ctx.isSubmitting.value = true
          SupervisionApi.addFeedback({
            ...values,
            feedbackImg: values.feedbackImg?.toString(),
            feedbackFile: values.feedbackFile?.toString(),
          })
            .then(() => {
              getStatistics()
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.operationSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }

        if(mode === 'inspectApply') {
          SupervisionApi.addTaskCompleted({
            ...values,
            completeImg: values.completeImg?.toString(),
            completeFile: values.completeFile?.toString(),
          })
            .then(() => {
              getStatistics()
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.operationSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }

        if(mode === 'delayApply') {
          SupervisionApi.addTaskDelay(values)
            .then(() => {
              getStatistics()
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.operationSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }

      }
    })
  }
})
const FormComponents = {
  taskFeedback: TaskFeedbackForm,
  inspectApply: InspectApplyForm,
  delayApply: DelayApplyForm
}
const dialogFormComponents = computed(() => {
  return FormComponents[dialog.mode.value as keyof typeof FormComponents]
})
const onOpenDialog = (type: string, row: any) => {
  dialog.open(type,{ superviseId: row.recordId, oldDate: row.endDate })
}

const getStatistics = () => {
  SupervisionApi.getFeedbackDeployStatistics().then((res) => {
    tabsRows.value.forEach((item) => {
      item.total = res.data[item.key] || 0
    })
  })
}
getStatistics()
</script>

<style scoped lang="scss">

</style>
