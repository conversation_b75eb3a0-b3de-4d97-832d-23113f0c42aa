<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="100px"
  >
  </z-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { ZFormField } from '@/ui/components/ZForm/types.ts'

const form = reactive({
  superviseId: '',
  oldDate: '',
  newDate: '',
  periodReason: ''
})

const fields = ref<ZFormField[]>([
  {
    type: 'input',
    name: 'superviseId',
    show: false
  },
  {
    type: 'date',
    name: 'oldDate',
    label: '原完成时限',
    fieldClass: 'w-full!',
    disabled: true,
    props: {
      format: 'YYYY-MM-DD'
    }
  },
  {
    type: 'date',
    name: 'newDate',
    label: '延期日期',
    fieldClass: 'w-full!',
    props: {
      placeholder: '请选择延期日期',
      format: 'YYYY-MM-DD',
      valueFormat: "YYYY-MM-DD"
    },
    rules: [
      {
        required: true,
        message: '请选择延期日期',
        trigger: 'blur'
      }
    ]
  },
  {
    type: 'input',
    name: 'periodReason',
    label: '延期原因',
    props: {
      placeholder: '请输入延期原因',
      maxlength: 1000,
      showWordLimit: true,
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 }
    },
    rules: [
      {
        required: true,
        message: '请输入延期原因',
        trigger: 'blur'
      }
    ]
  }
])
</script>

<style scoped lang="scss">

</style>
