<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="80px"
  >
    <template #item-completeImg="{ validate, value, updateValue }">
      <z-upload
        :allowed-types="[FileType.IMAGE]"
        :model-value="value"
        :limit="9"
        :max-size="5"
        tip="支持格式：jpg/jpeg/png ，最多上传9张，单张图片大小不超过5MB"
        @success="(r, file, newFileList) => handleUploadSuccess(r, { validate, updateValue }, newFileList)">
      </z-upload>
    </template>
    <template #item-completeFile="{ validate, value, updateValue }">
      <z-upload
        :model-value="value"
        :allowed-types="[FileType.OTHER]"
        :file-type-map="{
          other: ['doc', 'docx', 'xlsx', 'xls', 'pdf'],
        }"
        :limit="5"
        :max-size="5"
        tip="支持格式：doc、docx、xlsx、xls、pdf文件，最多上传5个附件，每个附件大小超过5M"
        @success="(r, file, newFileList) => handleUploadSuccess(r, { validate, updateValue }, newFileList)">
      </z-upload>
    </template>
  </z-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { ZFormField } from '@/ui/components/ZForm/types.ts'
import { FileType } from '@/ui/components/ZUpload/constants.ts'

const form = reactive({
  superviseId: '',
  completeReason: '',
  completeImg: '',
  completeFile: ''
})

const fields = ref<ZFormField[]>([
  {
    type: 'input',
    name: 'superviseId',
    show: false
  },
  {
    type: 'input',
    name: 'completeReason',
    label: '申请原因',
    props: {
      placeholder: '请输入申请原因',
      maxlength: 1000,
      showWordLimit: true,
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 }
    },
    rules: [
      { required: true, message: '请输入申请原因', trigger: 'blur' }
    ]
  },
  {
    type: 'custom',
    name: 'completeImg',
    label: '图片',
  },
  {
    type: 'custom',
    name: 'completeFile',
    label: '附件',
  },
])

const handleUploadSuccess = (r: any, { validate, updateValue }: any, newFileList: any[]) => {
  updateValue(newFileList.map((it: any) => it?.response?.data?.absUrl || it.name))
  validate()
}
</script>

<style scoped lang="scss">

</style>
