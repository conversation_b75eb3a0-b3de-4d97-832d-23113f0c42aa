<template>
  <z-page class="pt-2">
    <el-tabs
      v-model="tabsActive"
      @tab-click="tabsClick"
    >
      <el-tab-pane
        v-for="v in tabsRows"
        :key="v.value"
        :label="v.label"
        :name="v.value"
      ></el-tab-pane>
    </el-tabs>
    <z-table
      row-key="clueId"
      ref="tableRef"
      v-model:query="query"
      :columns="columns"
      :fetch-data="fetchTableData"
      paginationMode="server"
    >
      <template #beforePicPath="{ row }">
        <el-image :src="ASSETS_URL + row.beforePicPath" preview-teleported :preview-src-list="[ASSETS_URL + row.beforePicPath]" class="w-[80px] h-[80px]" />
      </template>
      <template #endPicPath="{ row }">
        <el-image :src="ASSETS_URL + row.endPicPath" preview-teleported :preview-src-list="[ASSETS_URL + row.endPicPath]" class="w-[80px] h-[80px]" />
      </template>
      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            path: '/CheckForm',
            viewPath: '@/views/Supervision/CheckList/CheckForm.vue',
            meta: {
              title: '创建督办清单'
            }
          }"
        >
          <template #default="{ navigate }">
            <el-button
              size="small"
              type="primary"
              link
              @click="() => navigate({
                query: {
                  clueId: row.clueId
                }
              })"
            >
              创建督查清单
            </el-button>
          </template>
        </z-dynamic-route>
      </template>
    </z-table>
  </z-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ZTableColumn, ZTableParams } from '@/ui/components/ZTable/types.ts'
import DictApI from '@/api/dict.ts'
import ComApi from '@/api/common.ts'
import SupervisionApi, { DictCodes } from '@/api/supervision.ts'

const ASSETS_URL = import.meta.env.VITE_STATIC_ASSETS_URL

const tableRef = ref()
const tabsActive = ref('2')
const tabsRows = ref([
  { value: '1', label: '视频监控' },
  { value: '2', label: '遥感监测' },
  { value: '3', label: '无人机查询' }
])
const tabsClick = () => {
  // tableRef.value?.reset()
}

const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'clueNumber', label: '线索编号' },
  {
    prop: 'questionType',
    label: '问题类型',
    asyncFormatter: {
      cacheKey: 'anyQuestionTypeValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.questionType)?.dictionaryName,
    },
  },
  { prop: 'questionContent', label: '存在问题', showOverflowTooltip: true },
  { prop: 'questionLocationName', label: '位置' },
  { prop: 'area', label: '图斑面积' },
  { prop: 'beforePicPath', label: '前时相影像' },
  { prop: 'endPicPath', label: '后时相影像' },
  {
    prop: 'questionArea',
    label: '所属行政区县',
    asyncFormatter: {
      cacheKey: 'questionAreaValue',
      async fetch() {
        return await ComApi.getAreaList().then(res => res.data)
      },
      render: (value) => (row) => value.find((it: any) => it.areaCode === row.questionArea)?.areaName,
    },
  },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
])

const fetchTableData = async (params: ZTableParams) => {
  return SupervisionApi.getClueList({ ...params, clueSource: tabsActive.value }).then(
    (res) => res.data,
  )
}
</script>

<style scoped lang="scss"></style>
