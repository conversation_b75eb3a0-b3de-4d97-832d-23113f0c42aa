<template>
  <z-form
    v-model:model="form"
    :fields="fields"
    label-width="100px"
  >
  </z-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { ZFormField } from '@/ui/components/ZForm/types.ts'
import { useDialogFormContext } from '@/ui/components/ZDialogForm/context.ts'

const props = defineProps<{
  model: Record<string, any> | null
}>()

const form = reactive({
  superviseId: '',
  indicateContent: ''
})

const fields = ref<ZFormField[]>([
  {
    type: 'input',
    name: 'superviseId',
    show: false
  },
  {
    type: 'input',
    name: 'indicateContent',
    label: '批示意见',
    props: {
      placeholder: '请输入批示意见',
      maxlength: 1000,
      showWordLimit: true,
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 }
    },
    rules: [
      {
        required: true,
        message: '请输入批示意见',
        trigger: 'blur'
      }
    ]
  }
])

const { onOpen } = useDialogFormContext()
onOpen(() => {
  form.superviseId = props?.model?.recordId
})
</script>

<style scoped lang="scss">

</style>
