<template>
 <z-page class="flex flex-col">
   <span class="text-[24px] ma-auto">{{ dataInfo.noticeTitle }}</span>
   <span class="text-[#666] text-sm mt-2">部门：{{ dataInfo.departmentNames }}</span>
   <div class="w-full mt-5" v-html="dataInfo.noticeContent"></div>
 </z-page>
</template>

<script setup lang="ts">
import SupervisionApi from '@/api/supervision.ts'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLayout } from '@/composables/useLayout.ts'

const route = useRoute()
const router = useRouter()
const layout = useLayout()
layout.onBack(() => {
  router.back()
})

const dataInfo = ref<any>({})
const getData = () => {
  SupervisionApi.getNoticeDetail(route.query.id as string).then(res => {
    dataInfo.value = res.data
  })
}
getData()
</script>

<style scoped lang="scss">

</style>
