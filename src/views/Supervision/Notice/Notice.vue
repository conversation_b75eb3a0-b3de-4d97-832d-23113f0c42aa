<template>
  <z-page>
    <z-table
      ref="tableRef"
      :auto-load="false"
      row-key="noticeId"
      pagination-mode="server"
      v-model:query="query"
      :toolbar-rows="toolbarRows"
      show-toolbar
      :fetch-data="fetchTableData"
      :columns="columns"
    >
      <template #toolbar-item-slotAdd="scope">
        <z-dynamic-route
          :route="{
            name: '发布公告',
            path: 'addnotice',
            viewPath: '@/views/Supervision/Notice/NoticeForm.vue',
          }"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              plain
              @click="() => navigate()"
            >
              发布公告
            </el-button>
          </template>
        </z-dynamic-route>
      </template>
      <!-- <template #noticeContent="{ row }">
        <SingleLineHtml :html="row.noticeContent" :max-length="80" />
      </template> -->
      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            name: '修改公告',
            path: 'editnotice',
            viewPath: '@/views/Supervision/Notice/NoticeForm.vue',
          }"
        >
          <template #default="{ navigate, route }">
            <el-button
              type="primary"
              link
              @click="() => navigate({
                query: {
                  id: row.noticeId,
                },
              })"
            >
              编辑
            </el-button>
          </template>
        </z-dynamic-route>
        <el-popconfirm
          title="确认删除该公告？"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
            >
              {{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>
  </z-page>
</template>

<script setup lang="ts">
import type {
  ZTableInstance,
  ZTableColumn,
  ZTableParams,
  ZTableToolbarRow,
} from '@/ui/components/ZTable/types'
import type { ComputedRef } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useTypedI18n } from '@/i18n'
import SupervisionApi from '@/api/supervision'
import SingleLineHtml from './SingleLineHtml.vue'
const tableRef = ref<ZTableInstance | null>(null)

const { t } = useTypedI18n()
const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'noticeTitle', label: '公告标题' },
  // { prop: 'noticeContent', label: '公告内容' },
  { prop: 'departmentNames', label: '下发部门' },
  { prop: 'publisher', label: '发布人' },
  { prop: 'releaseTime', label: '发布时间' },
  { prop: 'operation', label: '操作', align: 'center', width: 160, fixed: 'right' },
])

const toolbarRows: ComputedRef<ZTableToolbarRow[]> = computed(() => [
  {
    tools: [
      {
        key: 'slotAdd',
        type: 'button',
        label: '发布公告',
        buttonType: 'primary',
      },
    ],
  },
])

const fetchTableData = async (params: ZTableParams) => {
  return SupervisionApi.getNoticeList({ ...params }).then((res) => res.data)
}

const onDelete = (row: any) => {
  SupervisionApi.deleteNotice(row.noticeId).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

onMounted(async () => {
  tableRef.value?.refresh()
})
</script>
