<template>
  <z-page>
    <z-dynamic-route
      v-for="v in noticeList"
      :key="v.noticeId"
      :route="{
        path: 'NoticeDetails',
        viewPath: '@/views/Supervision/Notice/NoticeDetails.vue',
        meta: {
          title: '公告详情'
        }
      }"
    >
      <template #default="{ navigate }">
        <div class="flex items-center py-[12px] border-b-1 border-b-dashed border-#CFD6DF cursor-pointer" @click="navigate({query: { id: v.noticeId }})">
          <img class="w-[40px] h-[40px] mr-4" src="@/assets/imgs/system.png" alt="">
          <div class="flex justify-between flex-1">
            <span>{{ v.noticeTitle }}</span>
            <span class="c-#666 text-sm">{{ v.releaseTime }}</span>
          </div>
        </div>
      </template>
    </z-dynamic-route>
    <div class="flex justify-end mt-3">
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 30]"
        background
        layout="prev, pager, next,total"
        @change="handleChange"
      />
    </div>
  </z-page>
</template>

<script setup lang="ts">
import SupervisionApi from '@/api/supervision.ts'
import { ref } from 'vue'
import { useLayout } from '@/composables/useLayout'
import { useRouter } from 'vue-router'

const router = useRouter()

const layout = useLayout()
layout.onBack(() => {
  router.back()
})

const pageNum = ref<number>(1)
const pageSize = ref<number>(10)
const total = ref<number>(0)
const noticeList = ref<any[]>([])
const getList = () => {
  SupervisionApi.getNoticeList({
    pageNum: pageNum.value,
    pageSize: pageSize.value
  }).then(res => {
    noticeList.value = res.data?.data || []
    total.value = res.data?.total || 0
  })
}

const handleChange = (num: number, size: number) => {
  pageNum.value = num
  pageSize.value = size
  getList()
}

getList()
</script>

<style scoped lang="scss">

</style>
