<template>
  <z-page class="pt-2">
    <z-form
      ref="formRef"
      v-model:model="form"
      :fields="fields"
      :validate-on-rule-change="false"
      label-width="140px"
      :fetch-data="fetchData"
      :showFooter="true"
      @submit="handleSubmit"
      @reset="reset"
    >
      <!-- 简介 -->
      <template #item-noticeContent="{ validate, value, updateValue }">
        <z-editor
          :model-value="value"
          @change="(html) => handleEditorSuccess(html, { validate, updateValue })"
        />
      </template>
    </z-form>
  </z-page>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import ComApi from '@/api/common'
import dataResApi from '@/api/dataResources'
import SupervisionApi from '@/api/supervision'
import { ElMessage } from 'element-plus'
import router from '@/router'

interface ThematicClass {
  thematicClassId: number
  thematicClassName: string
  children?: ThematicClass[]
  parent?: ThematicClass
  thematicDataId?: string
  thematicClassLevelCode?: string
  [key: string]: unknown
}

const formRef = ref<ZFormInstance>()

const form = reactive({
  noticeTitle: '',
  noticeContent: '',
  departmentIds: [],
})

const fields = ref<ZFormField[]>([
  {
    name: 'noticeTitle',
    label: '公告名称',
    type: 'input',
    fieldClass: 'w-[560px]',
    rules: [
      {
        required: true,
        message: '请输入公告名称',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入公告名称',
      maxlength: 50,
      showWordLimit: true,
      clearable: true,
    },
  },
  {
    name: 'noticeContent',
    label: '公告内容',
    type: 'custom',
    fieldClass: 'w-[560px]',
    rules: [
      {
        required: true,
      },
    ],
  },
  {
    type: 'tree-select',
    name: 'departmentIds',
    label: '下发部门',
    fieldClass: 'w-100',
    props: {
      placeholder: '请选择下发部门',
      checkStrictly: true,
      clearable: true,
      multiple: true
    },
    rules: [{ required: true, message: '请选择下发部门', trigger: 'change' }],
    treeOptions: () => ComApi.getDeptTree().then((res) => res.data),
    treeProps: {
      label: 'departmentName',
      children: 'children',
    },
    valueKey: 'dataId',
  },
])
const handleEditorSuccess = (r: any, { validate, updateValue }: any) => {
  updateValue(r)
  validate()
}
const currentId = ref('')
const fetchData = async () => {
  const formData = await SupervisionApi.getNoticeDetail(currentId.value).then(res => res.data)
  formData.departmentIds = formData.departmentIds && formData.departmentIds.map((id:number) => String(id))
  return formData
}
const reset = () => {}
const handleSubmit = async () => {
  try {
    // 验证表单
    const isValid = await formRef.value?.validate()
    if (!isValid) return

    // 获取表单数据
    const formValues = formRef.value?.getValues()
    if (!formValues) return

    const submitData = {
      ...formValues,
    }
    // 根据ID决定是更新还是新增
    const apiMethod = currentId.value
      ? SupervisionApi.editNotice(currentId.value, submitData)
      : SupervisionApi.addNotice(submitData)

    const res = await apiMethod
    ElMessage.success(res.message)
    router.go(-1)
  } catch (error) {
    ElMessage.error('提交失败，请稍后重试')
  }
}

onMounted(async () => {
  const route = useRoute()
  currentId.value = (route.query.id as string) || ''
  if (currentId.value) {
    formRef.value?.refetch()
  }
})
</script>
