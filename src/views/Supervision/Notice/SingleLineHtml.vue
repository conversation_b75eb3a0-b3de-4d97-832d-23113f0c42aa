<!-- SingleLineHtml.vue -->
<template>
  <div
    ref="htmlContainer"
    class="single-line"
    v-html="processedContent"
  ></div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, nextTick } from 'vue';

export default defineComponent({
  name: 'SingleLineHtml',
  props: {
    html: {
      type: String,
      required: true,
    },
    maxLength: {
      type: Number,
      default: 100,
    },
  },
  setup(props) {
    const htmlContainer = ref<HTMLElement | null>(null);
    const processedContent = ref<string>(props.html);

    const getFirstLine = (html: string): string => {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      const text = tempDiv.textContent || tempDiv.innerText || '';
      return text.split('\n')[0].trim();
    };

    const processContent = () => {
      const firstLine = getFirstLine(props.html);
      processedContent.value = firstLine.length > props.maxLength
        ? firstLine.substring(0, props.maxLength) + '...'
        : firstLine;
    };

    onMounted(() => nextTick(processContent));

    return { htmlContainer, processedContent };
  },
});
</script>

<style scoped>
.single-line {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
</style>
