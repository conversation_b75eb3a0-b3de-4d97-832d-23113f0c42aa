<template>
  <z-page class="pt-2">
    <el-tabs
      v-model="tabsActive"
      @tab-click="tabsClick"
    >
      <el-tab-pane
        v-for="v in tabsRows"
        :key="v.value"
        :label="`${v.label}(${v.total})`"
        :name="v.value"
      ></el-tab-pane>
    </el-tabs>
    <z-table
      row-key="recordId"
      ref="tableRef"
      v-model:query="query"
      :columns="columns"
      :fetch-data="fetchTableData"
      paginationMode="server"
      show-toolbar
      :selectable="tabsActive === '2'"
      :toolbarRows="toolbarRows"
      @selection-change="(selection: any) => tableIds = selection.map((it: any) => it.recordId )"
    >
      <template #toolbar-item-add>
        <z-dynamic-route
          :route="{
            path: '/CheckForm',
            viewPath: '@/views/Supervision/CheckList/CheckForm.vue',
            meta: {
              title: '新建督办清单'
            }
          }"
        >
          <template #default="{ navigate }">
            <el-button
              type="primary"
              :icon="Plus"
              plain
              @click="() => navigate()"
            >
              {{ t('common.add') }}
            </el-button>
          </template>
        </z-dynamic-route>
      </template>
      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            path: '/CheckDetails',
            viewPath: '@/views/Supervision/CheckList/CheckDetails.vue',
            meta: {
              title: '查看督办清单'
            }
          }"
        >
          <template #default="{ navigate }">
            <el-button
              size="small"
              type="primary"
              link
              @click="() => navigate({
                query: {
                  id: row.recordId
                }
              })"
            >{{ t('common.view') }}
            </el-button>
          </template>
        </z-dynamic-route>
        <z-dynamic-route
          :route="{
            path: '/CheckForm',
            viewPath: '@/views/Supervision/CheckList/CheckForm.vue',
            meta: {
              title: '修改督办清单'
            }
          }"
        >
          <template #default="{ navigate }">
            <el-button
              v-if="['2', '5'].includes(tabsActive)"
              size="small"
              type="primary"
              @click="() => navigate({
                query: {
                  id: row.recordId
                }
              })"
              link
            >{{ t('common.edit') }}
            </el-button>
          </template>
        </z-dynamic-route>
        <el-button
          v-if="['3'].includes(tabsActive)"
          size="small"
          type="warning"
          link
          @click="onApproval(row)"
        >审批
        </el-button>
        <el-popconfirm
          :title="t('message.confirmDelete')"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              v-if="['2'].includes(tabsActive)"
              size="small"
              type="danger"
              link
              >{{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>
    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @close="dialog.handleClose"
    >
      <ApproveForm
        :model="dialog.model.value"
      />
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ZTableColumn, ZTableParams, ZTableToolbarRow } from '@/ui/components/ZTable/types.ts'
import SupervisionApi, { DictCodes } from '@/api/supervision.ts'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import { useTypedI18n } from '@/i18n'
import DictApI from '@/api/dict.ts'
import { useDialog } from '@/composables/useDialog.ts'
import ApproveForm from './ApproveForm.vue'
import { Plus } from '@element-plus/icons-vue'

const { t } = useTypedI18n()

const dialog = useDialog({
  name: '审批',
  modeMap: {
    Approve: '审批'
  },
  onConfirm(mode, ctx) {
    ctx.form?.validate((isValid, values) => {
      if (isValid) {
        ctx.isSubmitting.value = true
        SupervisionApi.approveCheck(values)
          .then(() => {
            getStatistics()
            tableRef.value?.refresh()
            dialog.close()
            ElMessage.success(t('message.operationSuccess'))
          })
          .finally(() => {
            ctx.isSubmitting.value = false
          })
      }
    })
  },
})

const tableRef = ref()

const tabsActive = ref('1')
const tabsRows = ref([
  { value: '1', total: 0, label: '我发起的', key: 'mySum' },
  { value: '2', total: 0, label: '草稿', key: 'draftSum' },
  { value: '3', total: 0, label: '待审批', key: 'waitSum' },
  { value: '4', total: 0, label: '审批通过', key: 'passSum' },
  { value: '5', total: 0, label: '审批不通过', key: 'rejectSum' },
])

const tabsClick = () => {
  tableRef.value?.reset()
}

const tableIds = ref([])
const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      {
        type: 'button',
        key: 'add',
      },
      ZTableToolbarFactory.createDelete({
        show: () => tabsActive.value === '2',
        disabled: () => !tableIds.value.length,
        onClick: () => {
          ElMessageBox.confirm(
            t('message.confirmDelete'),
            t('common.tips'),
            {
              confirmButtonText: t('common.confirm'),
              cancelButtonText: t('common.cancel'),
              type: 'warning'
            }
          ).then(() => {
            SupervisionApi.deleteCheck(tableIds.value).then(() => {
              tableIds.value = []
              getStatistics()
              tableRef.value?.refresh()
              ElMessage.success(t('message.deleteSuccess'))
            })
          })
        }
      }),
      {
        key: 'searchGroup',
        type: 'group',
        group: {
          class: 'ml-auto',
          noMargin: true,
          tools: [
            {
              key: 'questionType',
              type: 'select',
              label: '问题类型',
              placeholder: '请输入问题类型',
              width: 240,
              options: () => DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
                .then(res => res.data.map((it: any) => ({
                  label: it.dictionaryName,
                  value: it.dictionaryCode
                })))
            },
            { key: 'projectName', type: 'input', label: '项目名称', placeholder: '请输入项目名称', width: 240 },
            ZTableToolbarFactory.createSearch(),
            ZTableToolbarFactory.createReset()
          ]
        }
      }
    ],
  },
]

const query = ref()
const columns = ref<ZTableColumn[]>([
  { prop: 'questionNo', label: '问题编号' },
  {
    prop: 'questionType',
    label: '问题类型',
    asyncFormatter: {
      cacheKey: 'anyQuestionTypeValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.questionType)?.dictionaryName,
    },
  },
  { prop: 'projectName', label: '项目名称' },
  { prop: 'startDate', label: '任务开始日期' },
  { prop: 'endDate', label: '预计完成日期' },
  {
    prop: 'reportPeriod',
    label: '整改上报周期',
    asyncFormatter: {
      cacheKey: 'anyReportPeriodValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode(DictCodes.PeriodCycle)
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.reportPeriod)?.dictionaryName,
    },
  },
  { prop: 'dutyDeptName', label: '责任部门' },
  { prop: 'superviseDeptName', label: '督办部门' },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
])

const fetchTableData = async (params: ZTableParams) => {
  return SupervisionApi.getCheckList({ ...params, queryType: tabsActive.value }).then(
    (res) => res.data,
  )
}

const onApproval = (row: any) => {
  dialog.open('Approve', { superviseId: row.recordId })
}

const onDelete = (row: any) => {
  SupervisionApi.deleteCheck(row.recordId).then(() => {
    getStatistics()
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}

const getStatistics = () => {
  SupervisionApi.getCheckStatistics().then((res) => {
    tabsRows.value.forEach((item) => {
      item.total = res.data[item.key] || 0
    })
  })
}
getStatistics()
</script>

<style scoped lang="scss"></style>
