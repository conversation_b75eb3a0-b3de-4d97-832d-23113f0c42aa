<template>
  <div
    v-loading="loading"
    class="min-h-80"
  >
    <el-timeline>
      <el-timeline-item
        v-for="v in dataList"
        :key="v.recordId"
        hollow
        :color="`#396EF9`"
        size="large"
        :timestamp="v.approveTime"
        placement="top"
      >
        <div
          class="rounded p-4 flex flex-col w-fit"
          :class="v.approveResult === '0' ? 'green' : 'red'"
        >
          <div class="flex items-center">
            <span class="c-[#333333] text-base alibaba-semiBold">{{ v.approveAccountName }}</span>
            <span class="ml-3 rounded-[30px] px-3 py-1 c-white text-xs status">{{
                v.approveResult === '0' ? '审批通过' : '审批不通过'
              }}</span>
          </div>
          <span
            v-if="v.approveMessage"
            class="mt-2 text-sm c-[#666666]"
          >审批描述：{{ v.approveMessage }}</span
          >
        </div>
      </el-timeline-item>
    </el-timeline>
    <el-empty v-if="!dataList.length && !loading" />
  </div>
</template>

<script setup lang="ts">
import SupervisionApi from '@/api/supervision.ts'
import { useRoute } from 'vue-router'
import { ref } from 'vue'

const route = useRoute()

const loading = ref(false)
const dataList = ref<any>([])

const getData = async () => {
  loading.value = true
  SupervisionApi.getApproveRecord({ superviseId: route.query.id })
    .then((res) => {
      dataList.value = res.data || []
    })
    .finally(() => {
      loading.value = false
    })
}

getData()
</script>

<style scoped lang="scss">
.red {
  background-color: #fff1ee;
  .status {
    background: linear-gradient(77deg, #f22e35 0%, #ff9164 100%);
  }
}
.green {
  background-color: #ecf9f3;
  .status {
    background: linear-gradient(90deg, #00ac95 0%, #61de85 100%);
  }
}
</style>
