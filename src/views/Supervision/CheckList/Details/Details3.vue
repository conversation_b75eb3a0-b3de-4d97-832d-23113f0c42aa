<template>
  <div
    v-loading="loading"
    class="min-h-80"
  >
    <el-timeline>
      <el-timeline-item
        v-for="v in dataList"
        :key="v.feedbackId"
        hollow
        :color="`#396EF9`"
        size="large"
        :timestamp="v.createTime"
        placement="top"
      >
        <div class="rounded p-4 flex flex-col w-fit bg-[#EEF5FF]">
          <span class="c-[#333333] text-base alibaba-semiBold">{{ v.createUserName }}</span>
          <span class="mt-2 text-sm c-[#666666]">{{ v.feedbackContent }}</span>
          <div v-if="v.feedbackImg?.length" class="flex mt-4">
            <el-image v-for="(item, index) in v.feedbackImg" :key="index" :src="item" :preview-src-list="v.feedbackImg" class="w-[100px] h-[100px] mr-3 rounded" />
          </div>
          <div v-if="v.feedbackFile?.length" class="flex flex-col mt-4 w-fit">
            <el-link v-for="(item, index) in v.feedbackFile" :key="index" :src="item" type="primary" class="mb-1 justify-start">附件：{{ item }}</el-link>
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
    <el-empty v-if="!dataList.length && !loading" />
  </div>
</template>

<script setup lang="ts">
import SupervisionApi from '@/api/supervision.ts'
import { useRoute } from 'vue-router'
import { ref } from 'vue'

const route = useRoute()

const loading = ref(false)
const dataList = ref<any>([])

const getData = async () => {
  loading.value = true
  SupervisionApi.getFeedbackRecord({ superviseId: route.query.id })
    .then((res) => {
      dataList.value = (res.data || []).map((item: any) => {
        return {
          ...item,
          feedbackImg: item.feedbackImg && item.feedbackImg?.split(',')?.map((it: any) => import.meta.env.VITE_STATIC_ASSETS_URL + it) || [],
          feedbackFile: item.feedbackFile && item.feedbackFile?.split(',')?.map((it: any) => import.meta.env.VITE_STATIC_ASSETS_URL + it) || []
        }
      })
    })
    .finally(() => {
      loading.value = false
    })
}

getData()
</script>

<style scoped lang="scss">

</style>
