<template>
  <div
    v-loading="loading"
    class="min-h-80"
  >
    <el-timeline>
      <el-timeline-item
        v-for="v in dataList"
        :key="v.urgeId"
        hollow
        :color="`#396EF9`"
        size="large"
        :timestamp="v.createTime"
        placement="top"
      >
        <div class="rounded p-4 flex flex-col w-fit bg-[#EEF5FF]">
          <span class="c-[#333333] text-base alibaba-semiBold">{{ v.createUserName }}</span>
          <span class="mt-2 text-sm c-[#666666]">{{ v.urgeContent }}</span>
          <div class="flex flex-col mt-4 w-fit">
            <el-link v-for="(item, index) in v.urgeFile" :key="index" :src="item" type="primary" class="mb-1 justify-start">附件：{{ item }}</el-link>
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
    <el-empty v-if="!dataList.length && !loading" />
  </div>
</template>

<script setup lang="ts">
import SupervisionApi from '@/api/supervision.ts'
import { useRoute } from 'vue-router'
import { ref } from 'vue'

const route = useRoute()

const loading = ref(false)
const dataList = ref<any>([])

const getData = async () => {
  loading.value = true
  SupervisionApi.getUrgeRecord({ superviseId: route.query.id })
    .then((res) => {
      dataList.value = (res.data || []).map((item: any) => {
        return {
          ...item,
          urgeFile: item.urgeFile && item.urgeFile?.split(',')?.map((it: any) => import.meta.env.VITE_STATIC_ASSETS_URL + it) || []
        }
      })
    })
    .finally(() => {
      loading.value = false
    })
}

getData()
</script>

<style scoped lang="scss">

</style>
