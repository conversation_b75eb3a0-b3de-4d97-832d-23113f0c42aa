<template>
  <el-descriptions :column="1" size="large">
    <el-descriptions-item label="问题编号">{{ form.questionNo }}</el-descriptions-item>
    <el-descriptions-item label="项目名称">{{ form.projectName }}</el-descriptions-item>
    <el-descriptions-item label="问题类型">{{ questionTypeLabel }}</el-descriptions-item>
    <el-descriptions-item label="紧急程度">{{ exigenceLevelLabel }}</el-descriptions-item>
    <el-descriptions-item label="存在问题">{{ form.questionContent }}</el-descriptions-item>
    <el-descriptions-item label="整改目标">{{ form.completeTarget }}</el-descriptions-item>
    <el-descriptions-item>
      <div class="flex">
        <span class="c-[#303133]">问题图片</span>
        <div class="ml-[16px]">
          <el-image v-for="(item, index) in questionImgArr" :key="index" :src="item" :preview-src-list="questionImgArr" class="w-[100px] h-[100px] mr-3 rounded" />
        </div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item label="附件">
      <el-link v-for="(item, index) in questionFileArr" :key="index" :src="item" :icon="Files" type="primary">{{ item }}</el-link>
    </el-descriptions-item>
    <el-descriptions-item label="位置" style="display: flex" class-name="nihaoya">
      <div class="w-[600px] h-[300px] relative">
        <TMapView
          ref="mapRef"
          :options="mapOptions"
          @ready="onMapReady"
        />
      </div>
    </el-descriptions-item>
    <el-descriptions-item label="所属行政区县">{{ formatAreaName(form.questionArea) }}</el-descriptions-item>
    <el-descriptions-item label="任务开始日期">{{ form.startDate }}</el-descriptions-item>
    <el-descriptions-item label="预计完成日期">{{ form.endDate }}</el-descriptions-item>
    <el-descriptions-item label="整改上报周期">{{ reportPeriodLabel }}</el-descriptions-item>
    <el-descriptions-item label="责任部门">{{ form.dutyDeptName }}</el-descriptions-item>
    <el-descriptions-item label="督办部门">{{ form.superviseDeptName }}</el-descriptions-item>
    <el-descriptions-item>
      <div class="flex">
        <span class="c-[#303133]">审批人</span>
        <el-timeline v-if="form.flowList?.length" >
          <el-timeline-item
            v-for="v in form.flowList"
            :key="v.id"
            :timestamp="v.approveAccountName"
            hollow
            :color="`#396EF9`"
            size="large"
          >
            {{ `${NumberChineseConverter.toChinese(v.approveLevel)}级审批人`}}
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-descriptions-item>
  </el-descriptions>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import SupervisionApi, { DictCodes } from '@/api/supervision'
import { useRoute } from 'vue-router'
import ComApi from '@/api/common.ts'
import DictApI from '@/api/dict.ts'
import { Files } from '@element-plus/icons-vue'
import { NumberChineseConverter } from '@/utils/option.ts'
import { TAnchorPosition, TMap, TMapView, TSvgOverlay } from '@/map'

const route = useRoute()

const areaData = ref<any []>([])

const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const mapOptions = {
  center: {
    lng: 107.237743,
    lat: 34.363184,
  },
  zoom: 8.5,
  minZoom: 3,
  maxZoom: 18,
  search: {
    show: false
  }
}

const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')

  getInfo()
}

const form = reactive<any>({})
const getInfo = async () => {
  const { data } = await SupervisionApi.getCheckById(route.query.id as string)
  Object.assign(form, data)
  if(data.questionLocation) {
    const map = mapRef.value?.getMap()
    if (!map) return
    const point = data.questionLocation?.split(',')
    const circleSvg = new TSvgOverlay({
      position: point,
      iconName: `marker-loc`,
      size: [35, 50],
      anchor: TAnchorPosition.BOTTOM_CENTER,
      iconStyle: {
        fill: 'red',
      }
    })
    map.addOverlay(circleSvg)
    map.setCenter(
      {
        lng: point[0],
        lat: point[1],
      },
      15,
    )
  }

}

const dicts = reactive({
  [DictCodes.QuestionTypes]: [],
  [DictCodes.Urgency]: [],
  [DictCodes.PeriodCycle]: [],
})

const questionTypeLabel = computed(() => dicts[DictCodes.QuestionTypes].find((v: any) => v.dictionaryCode === form.questionType)?.dictionaryName)
const exigenceLevelLabel = computed(() => dicts[DictCodes.Urgency].find((v: any) => v.dictionaryCode === form.exigenceLevel)?.dictionaryName)
const reportPeriodLabel = computed(() => dicts[DictCodes.PeriodCycle].find((v: any) => v.dictionaryCode === form.reportPeriod)?.dictionaryName)
const questionImgArr = computed(() => form.questionImg?.split(',').map((v:any) => import.meta.env.VITE_STATIC_ASSETS_URL + v) || [])
const questionFileArr = computed(() => form.questionFile?.split(',').map((v:any) => import.meta.env.VITE_STATIC_ASSETS_URL + v) || [])

const getDicts = async () => {
  const { data: QuestionTypes } = await DictApI.getDictItemsByCode(DictCodes.QuestionTypes)
  const { data: Urgency } = await DictApI.getDictItemsByCode(DictCodes.Urgency)
  const { data: PeriodCycle } = await DictApI.getDictItemsByCode(DictCodes.PeriodCycle)
  Object.assign(dicts, {
    [DictCodes.QuestionTypes]: QuestionTypes,
    [DictCodes.Urgency]: Urgency,
    [DictCodes.PeriodCycle]: PeriodCycle,
  })
}
const formatAreaName = (code: string) => {
  const item = areaData.value.find((v: any) => v.areaCode === code)
  return item?.areaName
}

const getAreaList = async () => {
  areaData.value = await ComApi.getAreaList().then((res) => res.data)
}

getDicts()
getAreaList()
</script>

<style scoped lang="scss">
:deep(.el-descriptions__cell){
  display: flex;
}
</style>
