<template>
  <z-page class="pt-2">
    <el-tabs
      v-model="tabsActive"
      class="demo-tabs"
    >
      <el-tab-pane
        v-for="v in tabsRows"
        :key="v.value"
        :label="v.label"
        :name="v.value"
      >
        <component :is="v.component"></component>
      </el-tab-pane>
    </el-tabs>
  </z-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Details1 from './Details/Details1.vue'
import Details2 from './Details/Details2.vue'
import Details3 from './Details/Details3.vue'
import Details4 from './Details/Details4.vue'
import Details5 from './Details/Details5.vue'
import Details6 from './Details/Details6.vue'
import Details7 from './Details/Details7.vue'
import { useLayout } from '@/composables/useLayout.ts'
import { useRouter } from 'vue-router'

const router = useRouter()

const layout = useLayout()
layout.onBack(() => {
  router.back()
})

const tabsActive = ref('1')
const tabsRows = [
  { value: '1', label: '任务信息', component: Details1 },
  { value: '2', label: '审批日志', component: Details2 },
  { value: '3', label: '任务反馈', component: Details3 },
  { value: '4', label: '领导批示', component: Details4 },
  { value: '5', label: '催办记录', component: Details5 },
  { value: '6', label: '延期记录', component: Details6 },
  { value: '7', label: '核查记录', component: Details7 },
]

</script>

<style scoped lang="scss">

</style>
