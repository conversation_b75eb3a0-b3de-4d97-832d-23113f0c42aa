<template>
  <z-form
    v-model:model="form"
    :fields="fields"
  >
  </z-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { ZFormField } from '@/ui/components/ZForm/types.ts'

const form = reactive({
  superviseId: '',
  approveResult: '',
  approveMessage: ''
})

const fields = ref<ZFormField[]>([
  {
    name: 'superviseId',
    show: false
  },
  {
    type: 'radio',
    name: 'approveResult',
    label: '审批批示',
    rules: {
      required: true,
      message: '请选择审批批示',
      trigger: 'change'
    },
    options: [
      { label: '同意', value: '0' },
      { label: '不同意', value: '1' }
    ]
  },
  {
    type: 'input',
    name: 'approveMessage',
    label: '审批意见',
    props: {
      placeholder: '请输入审批意见',
      maxlength: 300,
      showWordLimit: true,
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 }
    }
  }
])
</script>

<style scoped lang="scss">

</style>
