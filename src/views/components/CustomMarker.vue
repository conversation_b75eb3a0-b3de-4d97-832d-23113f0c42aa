<template>
  <div class="custom-marker" @click="handleClick">
    <h3 class="title">{{ title }}</h3>
    <p class="description">{{ description }}</p>
    <div class="actions">
      <button @click.stop="handleEdit">编辑</button>
      <button @click.stop="handleDelete">删除</button>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  title?: string;
  description?: string;
}>();

const emit = defineEmits<{
  (e: 'click', data: { title: string | undefined }): void
  (e: 'edit'): void
  (e: 'delete'): void
}>();

const handleClick = () => {
  emit('click', { title: props.title });
};

const handleEdit = () => {
  emit('edit');
};

const handleDelete = () => {
  emit('delete');
};
</script>

<style scoped>
.custom-marker {
  padding: 8px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.title {
  font-size: 16px;
  font-weight: bold;
  margin: 0 0 4px 0;
}

.description {
  font-size: 14px;
  margin: 0;
  color: #666;
}

.actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.actions button {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background: #f5f5f5;
  cursor: pointer;
}

.actions button:hover {
  background: #e5e5e5;
}
</style>
