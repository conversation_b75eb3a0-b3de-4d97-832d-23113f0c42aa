<template>
  <div class="not-found">
    <div class="not-found__content">
      <el-result
        icon="warning"
        title="404"
        sub-title="抱歉，您访问的页面不存在"
      >
        <template #extra>
          <div class="not-found__actions">
            <el-button type="primary" @click="goBack">
              <el-icon><Back /></el-icon>
              返回上一页
            </el-button>
            <el-button @click="goHome">
              <el-icon><House /></el-icon>
              返回首页
            </el-button>
          </div>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Back, House } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.back()
}

const goHome = () => {
  router.push('/')
}
</script>

<style lang="scss" scoped>
.not-found {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--el-bg-color);

  &__content {
    width: 100%;
    max-width: 600px;
    padding: 2rem;
  }

  &__actions {
    display: flex;
    gap: 1rem;
    justify-content: center;

    @media (max-width: 480px) {
      flex-direction: column;
      width: 100%;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>