<template>
  <div class="view-load-error">
    <div class="view-load-error__content">
      <el-result
        icon="error"
        title="视图加载失败"
        :sub-title="subTitle"
      >
        <template #extra>
          <div class="view-load-error__actions">
            <el-button type="primary" @click="retry" v-if="onRetry">
              <el-icon><RefreshRight /></el-icon>
              重试
            </el-button>
            <el-button @click="goBack">
              <el-icon><Back /></el-icon>
              返回上一页
            </el-button>
          </div>
          <div v-if="error" class="view-load-error__details">
            <el-collapse>
              <el-collapse-item title="错误详情">
                <pre class="view-load-error__message">{{ error }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { RefreshRight, Back } from '@element-plus/icons-vue'

const props = defineProps<{
  viewPath?: string
  error?: Error | string
  onRetry?: () => void
}>()

const router = useRouter()

const subTitle = computed(() => {
  if (props.viewPath) {
    return `无法加载视图: ${props.viewPath}`
  }
  return '加载视图时发生错误'
})

const retry = () => {
  props.onRetry?.()
}

const goBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.view-load-error {
  display: flex;
  align-items: center;
  justify-content: center;

  &__content {
    width: 100%;
    max-width: 600px;
    padding: 2rem;
  }

  &__actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1.5rem;

    @media (max-width: 480px) {
      flex-direction: column;
      width: 100%;

      .el-button {
        width: 100%;
      }
    }
  }

  &__details {
    margin-top: 1.5rem;
    text-align: left;

    .el-collapse {
      --el-border-color-light: var(--el-border-color);
      border: 1px solid var(--el-border-color-light);
      border-radius: 4px;
    }
  }

  &__message {
    margin: 0;
    padding: 1rem;
    background-color: var(--el-fill-color-light);
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    word-break: break-all;
    color: var(--el-text-color-regular);
  }
}
</style>
