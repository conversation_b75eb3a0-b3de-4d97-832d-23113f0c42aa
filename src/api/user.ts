import req from '@/utils/req'

const UserApi = {
  getUserPagination(params: any) {
    return req({
      url: '/systemAccount/manageList',
      method: 'get',
      params,
    })
  },

  getUserById(accountId: number) {
    return req({
      url: `/systemAccount/${accountId}/editInfo`,
      method: 'get',
    })
  },

  addUser(data: any) {
    return req({
      url: `/systemAccount`,
      method: 'post',
      data,
    })
  },

  updateUserById(data: any) {
    return req({
      url: `/systemAccount/${data.accountId}`,
      method: 'put',
      data,
    })
  },

  deleteUserById(accountId: any) {
    return req({
      url: `/systemAccount/${accountId}`,
      method: 'delete',
    })
  },

  resetPassword(accountId: any) {
    return req({
      url: `/systemAccount/${accountId}/resetPassword`,
      method: 'put',
    })
  },

  changePassword(data: { oldPassword: string; newPassword: string }) {
    return req({
      url: '/systemAccount/personal/password',
      method: 'put',
      data,
    })
  },

  export(params: any) {
    return req.download(
      `/systemAccount/exportExcel`,
      {
        ...params,
        excelType: 'xls',
        isComprehensive: true,
      },
      { method: 'post' },
    )
  },

  getUserAllList() {
    return req({
      url: '/systemAccount/accountList',
      method: 'get',
    })
  },
}

export default UserApi
