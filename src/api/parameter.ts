import req from "@/utils/req";

const ParameterApi = {

  getParameterById(id: number) {
    return req({
      url: `/systemConfig/${id}`,
      method: 'get'
    })
  },

  getParameterList(params: any) {
    return req({
      url: '/systemConfig/manageList',
      method: 'get',
      params
    })
  },

  addParameter(data: any) {
    return req({
      url: '/systemConfig/save',
      method: 'post',
      data
    })
  },

  updateParameterById(data: any) {
    return req({
      url: `/systemConfig/update/${data.configId}`,
      method: 'put',
      data
    })
  },


  deleteParameterById(id: number) {
    return req({
      url: `/systemConfig/delete/${id}`,
      method: 'delete'
    })
  },


  getParameterAll(params: any) {
    return req({
      url: '/systemConfig/allList',
      method: 'get',
      params
    })
  }
}

export default ParameterApi
