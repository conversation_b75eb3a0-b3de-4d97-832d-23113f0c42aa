import req from "@/utils/req";


const RemoteApi = {
    // 疑似列表
    getSuspectedList(params:any) {
      return req({
        url: '/thematicRemoteData/suspectedlist',
        method: 'get',
        params
      })
    },
    nearestAnalysis(wkt:string,limit:number) {
      return req({
        url: `/thematicRemoteData/nearest/${limit}`,
        method: 'get',
        params: {wkt}
      })
    },
    bufferAnalysis(data:any) {
      return req({
        url: `/thematicRemoteData/buffer`,
        method: 'post',
       data
      })
    }
}

export default RemoteApi
