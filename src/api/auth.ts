import req from "@/utils/req";

const AuthApi = {
  getVerifyCode(uniqueIdentifier: string) {
    return req({
      url: '/verifyCode',
      method: 'get',
      params: { uniqueIdentifier }
    })
  },

  login(data: any) {
    return req({
      url: '/login',
      method: 'post',
      data: data,
      headers: {
        "Content-Type": 'application/x-www-form-urlencoded'
      }
    })
  },

  logout() {
    return req({
      url: '/logout',
      method: 'put',
    })
  },

  getAuthorizationInfo() {
    return req({
      url: '/systemAccount/personal/permissions'
    })
  },

  getPermissionTree(params?: any) {
    return req({
      url: '/systemApplication/personal/personalTree',
      method: 'get',
      params
    })
  }
}

export default AuthApi
