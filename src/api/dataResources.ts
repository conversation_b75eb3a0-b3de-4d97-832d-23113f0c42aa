import req from '@/utils/req'
import { get } from 'ol/proj'

const dataResApi = {
  /**
   * 专题管理
   */
  getTableData() {
    return req({
      url: `/thematicFeature/manage/treeList`,
      method: 'get',
    })
  },
  getFeatureTree() {
    return req({
      url: `/thematicFeature/treeList`,
      method: 'get',
    })
  },
  //
  addTopicManagement(data: any) {
    return req({
      url: `/thematicFeature/saveInfo`,
      method: 'POST',
      data: data,
    })
  },

  updateTopicManagement(data: any) {
    return req({
      url: `/thematicFeature/updateInfo`,
      method: 'PUT',
      data: data,
    })
  },
  editTopicManagement(id: any) {
    return req({
      url: `thematicFeature/editInfo/${id}`,
      method: 'get',
    })
  },

  deleteDeptById(id: number) {
    return req({
      url: `/thematicFeature/delete/${id}`,
      method: 'DELETE',
    })
  },
  disbaledTopicManagement(id: number) {
    return req({
      url: `/thematicFeature/disable/${id}`,
      method: 'PUT',
    })
  },
  enableTopicManagement(id: number) {
    return req({
      url: `/thematicFeature/enable/${id}`,
      method: 'PUT',
    })
  },

  /**
   * 分类管理
   */
  getCategoryData() {
    return req({
      url: `/thematicFeatureClass/manage/treeList`,
      method: 'get',
    })
  },

  addCategoryManagement(data: any) {
    return req({
      url: `/thematicFeatureClass/saveInfo`,
      method: 'POST',
      data: data,
    })
  },

  updateCategoryManagement(data: any) {
    return req({
      url: `/thematicFeatureClass/updateInfo`,
      method: 'PUT',
      data: data,
    })
  },
  editCategoryManagement(id: any) {
    return req({
      url: `/thematicFeatureClass/editInfo/${id}`,
      method: 'get',
    })
  },
  deleteCategoryDataById(id: number) {
    return req({
      url: `/thematicFeatureClass/deleteInfo/${id}`,
      method: 'DELETE',
    })
  },

  /**
   * 专题数据管理
   */
  getThematicFeatureData(data: any) {
    return req({
      url: `/thematicFeatureData/manageList`,
      method: 'get',
      params: data,
    })
  },
  deleteFeatureDataById(id: number) {
    return req({
      url: `/thematicFeatureData/deleteInfo/${id}`,
      method: 'DELETE',
    })
  },
  getThematicFeatureDataDetaile(id: string) {
    return req({
      url: `/thematicFeatureData/editInfo/${id}`,
      method: 'get',
    })
  },
  addThematicFeatureData(data: any) {
    return req({
      url: `/thematicFeatureData/saveInfo`,
      method: 'POST',
      data: data,
    })
  },
  updateThematicFeatureData(data: any) {
    return req({
      url: `/thematicFeatureData/updateInfo`,
      method: 'PUT',
      data: data,
    })
  },
  downloadTemplateFile(type: 'XLSX' | string, filename?: string) {
    return req.download(
      '/thematicFeatureData/import/template',
      { excelType: type },
      {
        method: 'POST',
        filename: filename,
      },
    )
  },
  importTemplateData(formData: any) {
    return req({
      url: '/thematicFeatureData/import/excel',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      method: 'POST',
      data: formData,
    })
  },
  exportThematicData(data: any, filename?: string) {
    return req.download('/thematicFeatureData/exportExcel', data, {
      method: 'POST',
      filename: filename,
    })
  },
  /**
   * 特色产品
   */
  getThematicProductData(params: any) {
    return req({
      url: `/thematicProductData/manageList`,
      method: 'GET',
      params,
    })
  },
  saveThematicProduct(data: any) {
    return req({
      url: `/thematicProductData/save`,
      method: 'POST',
      data,
    })
  },
  getThematicProductDetail(productId: string) {
    return req({
      url: `/thematicProductData/${productId}/editInfo`,
      method: 'GET',
    })
  },
  editThematicProduct(data: any) {
    return req({
      url: `/thematicProductData/update/${data.productId}`,
      method: 'PUT',
      data: data,
    })
  },
  deleteThematicProduct(productIds: string) {
    return req({
      url: `/thematicProductData/delete/${productIds}`,
      method: 'DELETE',
    })
  },
  /**
   * 企业
   */
  getThematicCompanyData(params: any) {
    return req({
      url: `/thematicCompanyData/manageList`,
      method: 'GET',
      params,
    })
  },
  getThematicCompanyAllList() {
    return req({
      url: `/thematicCompanyData/allList`,
      method: 'GET',
    })
  },
  saveThematicCompany(data: any) {
    return req({
      url: `/thematicCompanyData/saveInfo`,
      method: 'POST',
      data,
    })
  },
  getThematicCompanyDetail(companyId: string) {
    return req({
      url: `/thematicCompanyData/${companyId}/editInfo`,
      method: 'GET',
    })
  },
  editThematicCompany(data: any) {
    return req({
      url: `/thematicCompanyData/${data.companyId}`,
      method: 'PUT',
      data: data,
    })
  },
  deleteThematicCompany(productIds: string[]) {
    return req({
      url: `/thematicCompanyData/batchDelete`,
      method: 'DELETE',
      data: productIds,
    })
  },
  /**
   * 产业链
   */
  getThematicIndustryData(params: any) {
    console.log(params, '/')
    return req({
      url: `/thematicIndustryData/manageList`,
      method: 'GET',
      params,
    })
  },
  saveThematicIndustry(data: any) {
    return req({
      url: `/thematicIndustryData`,
      method: 'POST',
      data,
    })
  },
  getThematicIndustryDetail(industryId: string) {
    return req({
      url: `/thematicIndustryData/${industryId}/editInfo`,
      method: 'GET',
    })
  },
  editThematicIndustry(data: any) {
    console.log(data)
    return req({
      url: `/thematicIndustryData/${data.industryId}`,
      method: 'PUT',
      data: data,
    })
  },
  deleteThematicIndustry(industryIds: string) {
    return req({
      url: `/thematicIndustryData/${industryIds}`,
      method: 'DELETE',
    })
  },
  batchDeleteThematicIndustry(industryIds: any[]) {
    return req({
      url: `/thematicIndustryData/batchDelete`,
      method: 'DELETE',
      data: industryIds,
    })
  },
  /**
   * 重点项目
   */
  getThematicProjectData(params: any) {
    return req({
      url: `/thematicProjectData/manageList`,
      method: 'GET',
      params,
    })
  },
  getThematicProjectAll() {
    return req({
      url: `/thematicProjectData/list`,
      method: 'GET',
    })
  },
  saveThematicProject(data: any) {
    return req({
      url: `/thematicProjectData/save`,
      method: 'POST',
      data,
    })
  },
  getThematicProjectDetail(projectId: string) {
    return req({
      url: `/thematicProjectData/${projectId}/editInfo`,
      method: 'GET',
    })
  },
  editThematicProject(data: any) {
    return req({
      url: `/thematicProjectData/update/${data.projectId}`,
      method: 'PUT',
      data: data,
    })
  },
  deleteThematicProject(projectIds: string) {
    return req({
      url: `/thematicProjectData/delete/${projectIds}`,
      method: 'DELETE',
    })
  },
  /**
   * 指标分类管理
   */
  getNormClassifyData(params?: any) {
    return req({
      url: `/indexClass/treeList`,
      method: 'get',
      params: params,
    })
  },
  addNormClassify(data: any) {
    return req({
      url: '/indexClass/saveInfo',
      method: 'POST',
      data: data,
    })
  },
  getNormClassifyDetaile(id: number) {
    return req({
      url: `/indexClass/editInfo/${id}`,
      method: 'POST',
      data: {
        classId: id,
      },
    })
  },
  updateNormClassify(data: any) {
    return req({
      url: `/indexClass/updateInfo`,
      method: 'PUT',
      data: data,
    })
  },
  deleteNormClassify(id: number) {
    return req({
      url: `/indexClass/delete/${id}`,
      method: 'DELETE',
    })
  },
  disableNormClassify(id: number) {
    return req({
      url: `/indexClass/disable/${id}`,
      method: 'PUT',
    })
  },
  enableNormClassify(id: number) {
    return req({
      url: `/indexClass/enable/${id}`,
      method: 'PUT',
    })
  },

  /**
   * 指标数据
   */

  getNormData(params: any) {
    return req({
      url: '/indexData/list',
      method: 'GET',
      params: params,
    })
  },
  saveNormData(data: any, year: string) {
    return req({
      url: `/indexData/save/${year}`,
      method: 'POST',
      data: data,
    })
  },
  /**
   * 指标库管理
   */
  gitNormLibraryData(params?: any) {
    return req({
      url: '/indexBase/indexBaseList',
      method: 'GET',
      params: params,
    })
  },
  getNormLibraryDataAll(params?: any) {
    return req({
      url: `/indexBase/list`,
      method: 'GET',
      params,
    })
  },
  saveNormLibrary(data: any) {
    return req({
      url: `/indexBase/saveInfo`,
      method: 'POST',
      data: data,
    })
  },
  deleteNormLibrary(baseId: number) {
    return req({
      url: `/indexBase/delete/${baseId}`,
      method: 'POST',
    })
  },
  updaNormLibrary(data: any) {
    return req({
      url: '/indexBase/updateInfo',
      method: 'PUT',
      data: data,
    })
  },
  /**
   * 图表组管理
   */
  //
  getChartGroup(params: any) {
    return req({
      url: '/indexChart/manageList',
      method: 'GET',
      params: params,
    })
  },
  disbaledIndexChart(id: number) {
    return req({
      url: `/indexChart/disable/${id}`,
      method: 'PUT',
    })
  },
  enableIndexChart(id: number) {
    return req({
      url: `/indexChart/enable/${id}`,
      method: 'PUT',
    })
  },
  saveIndexChart(data: any) {
    return req({
      url: `/indexChart/save`,
      method: 'POST',
      data: data,
    })
  },
  updateIndexChart(data: any) {
    return req({
      url: `/indexChart/update/${data.chartId}`,
      method: 'PUT',
      data: data,
    })
  },
  getIndexChartDetail(chartId: number) {
    return req({
      url: `/indexChart/${chartId}/editInfo`,
      method: 'GET',
    })
  },
  deleteIndexChart(chartId: number) {
    return req({
      url: `/indexChart/delete/${chartId}`,
      method: 'DELETE',
    })
  },
  /**
   * 视频监控数据-设备管理
   */
  getDeviceManageList(params?: any) {
    return req({
      url: `/monitorDevice/manageList`,
      method: 'GET',
      params,
    })
  },
  getDeviceManageAll() {
    return req({
      url: `/monitorDevice/list4ThematicFeatureData`,
      method: 'GET',
    })
  },
  synchronizer() {
    return req({
      url: `/monitorDevice/sync`,
      method: 'GET',
    })
  },
  /**
   * 视频监控数据-视频资源
   */
  getDeviceList(params?: any) {
    return req({
      url: `/monitorDevice/list`,
      method: 'GET',
      params,
    })
  },
  getMonitorFavoriteList(params: any) {
    return req({
      url: `/monitorFavorites/list`,
      method: 'GET',
      params,
    })
  },
  addMonitorFavorite(data: any) {
    return req({
      url: `/monitorFavorite`,
      method: 'POST',
      data,
    })
  },
  removeMonitorFavorite(monitorFavoriteId: number) {
    return req({
      url: `/monitorFavorite/${monitorFavoriteId}`,
      method: 'DELETE',
    })
  },
  removeMonitorFavorites(monitorFavoritesId: number) {
    return req({
      url: `/monitorFavorites/${monitorFavoritesId}`,
      method: 'DELETE',
    })
  },
  addMonitorFavorites(data: any) {
    return req({
      url: `/monitorFavorites`,
      method: 'POST',
      data,
    })
  },
  editMonitorFavorites(data: any) {
    return req({
      url: `/monitorFavorites/${data.monitorFavoritesId}`,
      method: 'PUT',
      data,
    })
  },
  /**
   * action 动作 0-开始 1-停止
   * cameraIndexCode 监控点唯一标识
   * command 动作(LEFT-左转、RIGHT-右转、UP-上转、DOWN-下转、ZOOM_IN-焦距变大、ZOOM_OUT-焦距变小、LEFT_UP-左上、LEFT_DOWN-左下、RIGHT_UP-右上、RIGHT_DOWN-右下、FOCUS_NEAR-焦点前移、FOCUS_FAR-焦点后移、IRIS_ENLARGE-光圈扩大、IRIS_REDUCE-光圈缩小、WIPER_SWITCH-接通雨刷开关、START_RECORD_TRACK-开始记录路线、STOP_RECORD_TRACK-停止记录路线、START_TRACK-开始路线、STOP_TRACK-停止路线、GOTO_PRESET-到预置点)
   * presetIndex 预置点编号，整数，通常在300以内
   * presetName 预置点名称
   * speed 云台速度，取值范围为1-100，默认50
   * @param data
   * @returns
   */
  controlCamera(data: any) {
    return req({
      url: `/video/controlling`,
      method: 'POST',
      data,
    })
  },
  getPlayBackUrl(data: any) {
    return req({
      url: `/video/backUrl`,
      method: 'POST',
      data,
    })
  },
  /**
   * 指标评估体系
   */

  getEvaluateSystem(params: any) {
    return req({
      url: `/indexEvaluate/findList`,
      method: 'GET',
      params,
    })
  },
  addEvaluateSystem(data: any) {
    return req({
      url: `/indexEvaluate`,
      method: 'POST',
      data,
    })
  },
  editEvaluateSystem(data: any) {
    return req({
      url: `/indexEvaluate/${data.evaluateId}`,
      method: 'PUT',
      data,
    })
  },
  deleteEvaluateSystem(evaluateId: number) {
    return req({
      url: `/indexEvaluate/deteleIndex`,
      method: 'DELETE',
      params: {
        evaluateId
      },
    })
  },
  deleteEvaluate(evaluateId: number) {
    return req({
      url: `/indexEvaluate/${evaluateId}`,
      method: 'DELETE',
      params: {
        evaluateId
      },
    })
  },
  changeEvaluateSystemStatus(evaluateId: number) {
    return req({
      url: `/indexEvaluate/statusModification/${evaluateId}`,
      method: 'PUT',
    })
  },

  getEvaluateSystemTree(params: any) {
    return req({
      url: `/indexEvaluate/treeList`,
      method: 'GET',
      params
    })
  },
  getEvaluateSystemInfo(evaluateId: string) {
    return req({
      url: `/indexEvaluate/${evaluateId}/editInfo`,
      method: 'GET',
    })
  },

  /**
   * 遥感监测
   */
  getRemoteDataList(params: any) {
    return req({
      url: `/thematicRemoteData/manageList`,
      method: 'GET',
      params,
    })
  },
  saveRemoteData(data: any) {
    return req({
      url: `/thematicRemoteData/save`,
      method: 'POST',
      data: data,
    })
  },
  updateRemoteData(data: any) {
    return req({
      url: `/thematicRemoteData/update/${data.id}`,
      method: 'PUT',
      data: data,
    })
  },
  // 转为线索
  updateSuspected(data: any) {
    return req({
      url: `/thematicRemoteData/update-suspected/${data.id}`,
      method: 'PUT',
      data
    })
  },
  deleteRemoteData(id: any) {
    return req({
      url: `/thematicRemoteData/delete/${id}`,
      method: 'DELETE',
    })
  },
  importRemoteData(data: any) {
    return req({
      url: `/thematicRemoteData/import-remote-datas`,
      method: 'POST',
      data: data,
    })
  },
}

export default dataResApi
