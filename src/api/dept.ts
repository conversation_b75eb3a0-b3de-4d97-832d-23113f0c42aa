import req from "@/utils/req";

const DeptApi = {
  getTableField() {
    return req({
      url: `/systemForm/department/render/field/table`,
      method: 'get'
    })
  },

  getTableQueryParams() {
    return req({
      url: `/systemForm/department/render/field/tableQuery`,
      method: 'get'
    })
  },

  getDeptListFromForm(data: any) {
    return req({
      url: `/systemFormData/department/manageTree`,
      method: 'POST',
      data: data
    })
  },

  getDeptById(id: string) {
    return req({
      url: `systemFormData/department/${id}/editInfo`,
      method: 'get',
    })
  },

  addDept(data: any) {
    return req({
      url: `systemFormData/department`,
      method: 'post',
      data
    })
  },

  updateDeptById(id: number, data: any) {
    return req({
      url: `systemFormData/department/${id}`,
      method: 'put',
      data
    })
  },

  deleteDeptById(id: number) {
    return req({
      url: `systemFormData/department/${id}/forTree`,
      method: 'DELETE',
    })
  },

  getFormFields() {
    return req({
      url: `/systemForm/department/render`,
      method: 'get'
    })
  }
}

export default DeptApi
