import req from '@/utils/req'

const RoleApi = {
  getRolesPage(params: any = {}) {
    return req({
      url: '/systemRole/manageList',
      method: 'get',
      params,
    })
  },

  getUserRolesAll(params: any = {}) {
    return req({
      url: '/systemRole/authorizeList',
      method: 'get',
      params,
    })
  },

  getRoleById(roleId: number, silent?: boolean) {
    return req({
      url: `/systemRole/${roleId}/editInfo`,
      method: 'get',
      silent,
    })
  },

  addRole(data: any) {
    return req({
      url: '/systemRole',
      method: 'post',
      data,
    })
  },

  updateRole(data: any) {
    return req({
      url: `/systemRole/${data.roleId}`,
      method: 'put',
      data,
    })
  },

  deleteRoleByIds(roleIds: number[]) {
    return req({
      url: `/systemRole/${roleIds.join(',')}`,
      method: 'delete',
    })
  },

  getRoleAssignedUsers(roleId: number, params: any = {}) {
    return req({
      url: `/systemRole/${roleId}/accounts`,
      method: 'get',
      params,
    })
  },

  getRoleAuth(roleId: number) {
    return req({
      url: `/systemRole/${roleId}/authorize`,
      method: 'get',
    })
  },

  getRolesByIds(roleIds: number[]) {
    return req({
      url: `/systemRole/getRoleListByRoleIds`,
      method: 'get',
      params: {
        ids: roleIds.join(','),
      },
    })
  },

  updateRoleAuth(roleId: number, data: any) {
    return req({
      url: `/systemRole/${roleId}/authorize`,
      method: 'put',
      data,
    })
  },
}

export default RoleApi
