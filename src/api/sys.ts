import req from "@/utils/req";

const SysApi = {
  getApplicationAll(params: any) {
    return req({
      url: '/systemApplication/manageTree',
      method: 'get',
      params
    })
  },

  getApplicationMenus(params: any) {
    return req({
      url: '/systemFunction/manageTree',
      method: 'get',
      params
    })
  },

  async getApplicationTree(params?: any) {
    return req({
      url: '/systemApplication/selectTree',
      method: 'get',
      params
    })
  },

  addApplication(data: any) {
    return req({
      url: '/systemApplication',
      method: 'post',
      data: data
    })
  },

  updateApplication(data: any) {
    return req({
      url: `/systemApplication/${data.applicationId}`,
      method: 'put',
      data: data
    })
  },

  deleteApplication(id: number) {
    return req({
      url: `/systemApplication/${id}`,
      method: 'delete'
    })
  },

  getFunctionById(functionId: number) {
    return req({
      url: `/systemFunction/${functionId}/editInfo`,
      method: 'get',
      params: {}
    })
  },

  addFunction(data: any) {
    return req({
      url: '/systemFunction',
      method: 'post',
      data: data
    })
  },

  deleteFunctionById(functionId: number) {
    return req({
      url: `/systemFunction/${functionId}`,
      method: 'delete',
    })
  },

  updateFunctionById(data: any) {
    return req({
      url: `/systemFunction/${data.functionId}`,
      method: 'put',
      data: data
    })
  }
}

export default SysApi
