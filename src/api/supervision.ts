import req from '@/utils/req'

export const DictCodes = {
  QuestionTypes: 'questiontypes',
  Urgency: 'urgency',
  PeriodCycle: 'periodcycle'
}

const SupervisionApi = {
  // 获取线索列表
  getClueList(params: any) {
    return req({
      url: '/superviseClue/manageList',
      method: 'get',
      params,
    })
  },
  // 获取线索详情
  getClueDetail(id: number | string) {
    return req({
      url: `/superviseClue/${id}/editInfo`,
      method: 'get',
    })
  },
  // 获取清单列表
  getCheckList(params: any) {
    return req({
      url: '/superviseRecord/manageList',
      method: 'get',
      params,
    })
  },
  // 根据id查询清单
  getCheckById(recordId: number | string) {
    return req({
      url: `/superviseRecord/${recordId}/editInfo`,
      method: 'get',
    })
  },
  // 列表统计
  getCheckStatistics() {
    return req({
      url: '/superviseRecord/statisticsInventory',
      method: 'get'
    })
  },
  // 新增清单
  addCheck(data: any) {
    return req({
      url: '/superviseRecord/save',
      method: 'post',
      data,
    })
  },
  // 更新清单
  updateCheck(recordId: number | string, data: any) {
    return req({
      url: `/superviseRecord/update/${recordId}`,
      method: 'put',
      data,
    })
  },
  // 删除清单
  deleteCheck(recordIds: number[]) {
    return req({
      url: `/superviseRecord/delete/${recordIds}`,
      method: 'delete',
    })
  },
  // 审批操作
  approveCheck(data: any) {
    return req({
      url: `/superviseRecord/approve`,
      method: 'post',
      data
    })
  },
  // 审批记录
  getApproveRecord(params: any) {
    return req({
      url: `/superviseApproveRecord/approveList`,
      method: 'get',
      params
    })
  },
  // 反馈记录
  getFeedbackRecord(params: any) {
    return req({
      url: `/superviseFeedback/manageList`,
      method: 'get',
      params
    })
  },
  // 任务批示记录
  getTaskIndicateRecord(params: any) {
    return req({
      url: `/superviseIndicate/manageList`,
      method: 'get',
      params
    })
  },
  // 催办记录
  getUrgeRecord(params: any) {
    return req({
      url: `/superviseUrge/manageList`,
      method: 'get',
      params
    })
  },
  // 延期记录
  getDelayRecord(params: any) {
    return req({
      url: `/supervisePeriod/manageList`,
      method: 'get',
      params
    })
  },
  // 办结记录
  getCompletedRecord(params: any) {
    return req({
      url: `/superviseComplete/manageList`,
      method: 'get',
      params
    })
  },
  // 反馈落实列表
  getFeedbackDeployList(params: any) {
    return req({
      url: `/superviseRecord/implementList`,
      method: 'get',
      params
    })
  },
  // 反馈落实列表统计
  getFeedbackDeployStatistics() {
    return req({
      url: `/superviseRecord/statisticsImplement`,
      method: 'get'
    })
  },
  // 任务反馈新增
  addFeedback(data: any) {
    return req({
      url: `/superviseFeedback/save`,
      method: 'post',
      data
    })
  },
  // 任务办结新增
  addTaskCompleted(data: any) {
    return req({
      url: `/superviseComplete/save`,
      method: 'post',
      data
    })
  },
  // 任务延期新增
  addTaskDelay(data: any) {
    return req({
      url: `/supervisePeriod/save`,
      method: 'post',
      data
    })
  },
  // 核查督办列表
  getInspectList(params: any) {
    return req({
      url: `/superviseRecord/inspectList`,
      method: 'get',
      params
    })
  },
  // 核查督办列表统计
  getInspectStatistics() {
    return req({
      url: `/superviseRecord/statisticsInspect`,
      method: 'get'
    })
  },
  // 任务催办新增
  addTaskUrge(data: any) {
    return req({
      url: `/superviseUrge/save`,
      method: 'post',
      data
    })
  },
  // 延期审批
  approveDelay(data: any) {
    return req({
      url: `/supervisePeriod/approve`,
      method: 'post',
      data
    })
  },
  // 办结审批
  approveCompleted(data: any) {
    return req({
      url: `/superviseComplete/approve`,
      method: 'post',
      data
    })
  },
  // 延期详情
  getDelayDetail(id: any) {
    return req({
      url: `/supervisePeriod/${id}/editInfo`,
      method: 'get'
    })
  },
  // 办结详情
  getCompletedDetail(id: any) {
    return req({
      url: `/superviseComplete/${id}/editInfo`,
      method: 'get'
    })
  },
  // 核查台账列表
  getInspectTicketList(params: any) {
    return req({
      url: `/superviseRecord/standingList`,
      method: 'get',
      params
    })
  },
  // 核查台账列表统计
  getInspectTicketStatistics() {
    return req({
      url: `/superviseRecord/statisticsStanding`,
      method: 'get'
    })
  },
  // 任务批示新增
  addTaskIndicate(data: any) {
    return req({
      url: `/superviseIndicate/save`,
      method: 'post',
      data
    })
  },
  // 更新重点关注
  updateFollow(data: any) {
    return req({
      url: `/superviseRecord/follow`,
      method: 'post',
      data
    })
  },
  // 工作台统计
  getWorkbenchStatistics() {
    return req({
      url: `/superviseWork/statisticsIndex`,
      method: 'get'
    })
  },
  // 消息列表
  getMessageList(params: any) {
    return req({
      url: `/superviseMessage/manageList`,
      method: 'get',
      params
    })
  },
  // 消息统计
  getMessageStatistics() {
    return req({
      url: `/superviseMessage/statisticsMessage`,
      method: 'get',
    })
  },
  // 更新消息已读
  updateMessageRead(id: any) {
    return req({
      url: `/superviseMessage/read/${id}`,
      method: 'put'
    })
  },
  // 任务列表
  getSuperviseList(params: any) {
    return req({
      url: `/superviseWork/manageList`,
      method: 'get',
      params
    })
  },
  // 任务列表统计
  getSuperviseStatistics() {
    return req({
      url: `/superviseWork/statisticsTask`,
      method: 'get'
    })
  },
  // 任务总数统计
  getSuperviseTotalStatistics() {
    return req({
      url: `/superviseStatistics/exigence`,
      method: 'get'
    })
  },
  // 任务类型统计
  getSuperviseTypeStatistics() {
    return req({
      url: `/superviseStatistics/taskType`,
      method: 'get'
    })
  },
  // 任务进度统计
  getSuperviseProgressStatistics(params: any) {
    return req({
      url: `/superviseStatistics/progress`,
      method: 'get',
      params
    })
  },
  // 超期任务列表
  getOverdueList() {
    return req({
      url: `/superviseStatistics/exceedDate`,
      method: 'get'
    })
  },
  // 获取公告列表
  getNoticeList(params: any) {
    return req({
      url: `/sysNotice/manageList`,
      method: 'get',
      params
    })
  },
   // 获取公告详情
  getNoticeDetail(noticeId: string) {
    return req({
      url: `/sysNotice/${noticeId}/editInfo`,
      method: 'get',
    })
  },
  // 新增公告
  addNotice(data: any) {
    return req({
      url: `/sysNotice`,
      method: 'post',
      data
    })
  },
  // 修改公告
  editNotice(noticeId: string, data: any) {
    return req({
      url: `/sysNotice/${noticeId}`,
      method: 'put',
      data
    })
  },
  // 删除公告
  deleteNotice(noticeId: number) {
    return req({
      url: `/sysNotice/${noticeId}`,
      method: 'delete',
    })
  },
}

export default SupervisionApi
