import req from '@/utils/req'

const HighApi = {
  // 指标概览分类树
  getIndexOverviewCategoryTree() {
    return req({
      url: '/indexOverview/classTree',
      method: 'get',
    })
  },
  // 指标概览数据
  getIndexOverviewData(params: any) {
    return req({
      url: '/indexOverview/dataList',
      method: 'get',
      params,
    })
  },
  // 指标数据导出
  getIndexOverviewExport(data: any) {
    return req.download('/indexOverview/exportList', data, {
      method: 'post',
    })
  },
  // 导出报告
  getIndexOverviewReport(data: any) {
    return req.download('/indexOverview/exportWord', data, {
      method: 'post',
    })
  },
  // 指标分析列表
  getIndexAnalysisList() {
    return req({
      url: '/indexAnalysis/changeQuery',
      method: 'get'
    })
  },
  // 指标分析查询
  getIndexAnalysisData(params: any) {
    return req({
      url: '/indexAnalysis/analysisQuery',
      method: 'get',
      params,
    })
  },
  // 指标分析评价结果查询
  getIndexAnalysisEvaluation(params: any) {
    return req({
      url: '/indexAnalysis/evaluateResults',
      method: 'get',
      params,
    })
  },
}

export default HighApi
