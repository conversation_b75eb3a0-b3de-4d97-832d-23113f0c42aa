import req from "@/utils/req";

const DictApI = {
  getDictionaryTree(params: any) {
    return req({
      url: '/dictionary/manage/treeList',
      method: 'get',
      params
    })
  },

  addDictionary(data: any) {
    return req({
      url: '/dictionary',
      method: 'post',
      data: data
    })
  },

  updateDictionary(data: any) {
    return req({
      url: `/dictionary/${data.dictionaryId}`,
      method: 'put',
      data: data
    })
  },

  deleteDictionary(id: number) {
    return req({
      url: `/dictionary/${id}`,
      method: 'delete'
    })
  },

  getDictItemsByCode(code: string) {
    return req({
      url: `/dictionary/${code}/selectList`,
      method: 'get'
    })
  }
}

export default DictApI
