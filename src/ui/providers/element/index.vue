<script setup lang="ts">
import { computed } from 'vue'
import { useLang } from '@/composables/useLang'
import zhCN from 'element-plus/es/locale/lang/zh-cn.mjs'
import en from 'element-plus/es/locale/lang/en.mjs'

const { locale } = useLang()

const elementLocale = computed(() => {
  return locale.value === 'zh' ? zhCN : en
})
</script>

<template>
  <ElConfigProvider :locale="elementLocale" :button="{ autoInsertSpace: true }">
    <slot />
  </ElConfigProvider>
</template>
