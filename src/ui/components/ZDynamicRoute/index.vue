<script setup lang="ts">
import { onMounted, onUnmounted, watch, ref } from 'vue'
import { useRouter, useRoute, type RouteRecordSingleView } from 'vue-router'
import { createLogger } from '@/utils/logger'
import { DYNAMIC_ROUTES_KEY } from '@/utils/conts'
import { getStorage, setStorage } from '@/utils/storage'
import type { InheritOptions, Props } from './types'
import { loadView } from '@/utils/viewLoader'

const logger = createLogger('ZDynamicRoute')

const props = withDefaults(defineProps<Props>(), {
  inherit: true,
  removeOnUnmount: false,
  replace: false,
  persistent: true,
  activeParent: true,
})

const router = useRouter()
const currentRoute = useRoute()

/**
 * 从组件定义中提取视图路径
 */
const extractComponentPath = (viewPath: string): string | undefined => {
  if (!viewPath) return undefined

  return viewPath
    .replace(/^@\/views\//, '')
    .replace(/^src\/views\//, '')
    .replace(/^views\//, '')
    .replace(/\.vue$/, '')
    .replace(/^\/+/, '')
}

/**
 * 保存动态路由到存储
 */
const saveDynamicRoute = (
  route: Omit<RouteRecordSingleView, 'component'> & { component: string },
) => {
  if (!props.persistent) return

  try {
    const savedRoutes = getStorage<RouteRecordSingleView[]>(DYNAMIC_ROUTES_KEY, [])!

    const componentPath = extractComponentPath(route.component)
    if (!componentPath) {
      logger.error('Failed to extract component path')
      return
    }

    const routeConfig = {
      ...route,
      component: componentPath,
    }

    const filteredRoutes = savedRoutes.filter((r) => r.name !== route.name)
    filteredRoutes.push(routeConfig as unknown as RouteRecordSingleView)

    setStorage(DYNAMIC_ROUTES_KEY, filteredRoutes)
    logger.log(`Route "${String(route.name)}" saved successfully`)
  } catch (error) {
    logger.error('Failed to save dynamic route:', error)
  }
}

const removeSavedRoute = (routeName: string) => {
  if (!props.persistent) return

  try {
    const savedRoutes = getStorage<RouteRecordSingleView[]>(DYNAMIC_ROUTES_KEY, [])!
    const filteredRoutes = savedRoutes.filter((r: any) => r.name !== routeName)
    setStorage(DYNAMIC_ROUTES_KEY, filteredRoutes)
  } catch (error) {
    logger.error('Failed to remove saved route:', error)
  }
}

const routeState = ref({
  name: '',
  path: '',
  fullPath: '',
})

const getInheritConfig = (): InheritOptions => {
  if (typeof props.inherit === 'boolean') {
    return {
      path: props.inherit,
      layout: props.inherit,
      application: props.inherit,
      meta: props.inherit,
      activeParent: props.activeParent,
    }
  }
  return {
    path: true,
    layout: true,
    application: true,
    meta: true,
    activeParent: props.activeParent,
    ...props.inherit,
  }
}

/**
 * 构建路由配置
 */
const buildRouteConfig = (): RouteRecordSingleView => {
  const route = { ...props.route }
  const inheritConfig = getInheritConfig()

  const parent = currentRoute.matched[currentRoute.matched.length - 1]

  if (inheritConfig.path && parent) {
    route.path = route.path.startsWith('/') ? route.path.slice(1) : route.path
    route.path = `${parent.path}/${route.path}`
  } else if (!route.path.startsWith('/')) {
    route.path = `/${route.path}`
  }

  if (!route.name) {
    const parentPath = parent ? parent.path : ''
    const baseKey = `${route.path}_${parentPath}_${JSON.stringify(route.meta || {})}}`
    route.name = `dynamic_${baseKey
      .split('')
      .reduce((hash, char) => (hash << 5) - hash + char.charCodeAt(0), 0)
      .toString(36)}`
  }

  if (parent) {
    route.meta = {
      ...(inheritConfig.layout && { layout: parent.meta.layout || 'default' }),
      ...(inheritConfig.application && { application: parent.meta.application }),
      ...(inheritConfig.meta && parent.meta),
      ...route.meta,
      index: String(route.meta?.index || route.path),
      ...(inheritConfig.activeParent && {
        activeMenu: String(parent.meta?.index || parent.path),
      }),
      isDynamicRoute: true,
    }
  } else {
    route.meta = {
      ...route.meta,
      index: String(route.meta?.index || route.path),
      isDynamicRoute: true,
    }
  }

  // 更新路由状态
  routeState.value = {
    name: String(route.name),
    path: route.path,
    fullPath: route.path,
  }

  return route as unknown as RouteRecordSingleView
}

const navigate = (options?: {
  params?: Record<string, string>
  query?: Record<string, string>
}) => {
  router.push({
    name: routeState.value.name,
    params: options?.params,
    query: options?.query,
  })
}

const addRoute = () => {
  const route = buildRouteConfig()
  const routeName = String(route.name)

  const exists = router.hasRoute(routeName)
  if (exists && !props.replace) {
    logger.warn(`Route "${routeName}" already exists`)
    return
  }

  try {
    if (exists && props.replace) {
      router.removeRoute(routeName)
      removeSavedRoute(routeName)
    }

    const runtimeRoute = {
      ...route,
      component: loadView(extractComponentPath(props.route.viewPath) || props.route.viewPath),
    }

    router.addRoute(runtimeRoute)

    if (props.persistent) {
      const storageRoute = {
        ...route,
        component: props.route.viewPath,
      }
      saveDynamicRoute(storageRoute)
    }

    logger.log(`Route "${routeName}" added successfully`)
  } catch (error) {
    logger.error('Failed to add route:', error)
    throw error
  }
}

const removeRoute = () => {
  const routeName = routeState.value.name
  if (router.hasRoute(routeName)) {
    router.removeRoute(routeName)
    removeSavedRoute(routeName)

    logger.log(`Route "${routeName}" removed`)
  }
}

watch(
  () => props.route,
  () => {
    removeRoute()
    addRoute()
  },
  { deep: true },
)

onMounted(() => {
  addRoute()
})

onUnmounted(() => {
  if (props.removeOnUnmount) {
    removeRoute()
  }
})

defineExpose({
  add: addRoute,
  remove: removeRoute,
  navigate,
})
</script>

<template>
  <slot
    :route="routeState"
    :navigate="navigate"
    :inherited="!!props.inherit"
  />
</template>
