export interface InheritOptions {
  path?: boolean
  layout?: boolean
  application?: boolean
  meta?: boolean
  activeParent?: boolean
}

export interface Props {
  route: {
    name?: string
    path: string
    viewPath: string
    meta?: Record<string, any>
  }
  inherit?: boolean | InheritOptions
  removeOnUnmount?: boolean
  replace?: boolean
  persistent?: boolean
  activeParent?: boolean
}

export interface ZDynamicRouteSlotProps {
  route: {
    name: string
    path: string
    fullPath: string
  }
  navigate: (options?: { params?: Record<string, string>; query?: Record<string, string> }) => void
  inherited: boolean
}

export interface ZDynamicRouteSlots {
  default: (props: ZDynamicRouteSlotProps) => any
}
