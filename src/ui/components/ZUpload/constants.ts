export enum ViewType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  PDF = 'iframe',
  FILE = 'file'
}

export enum FileType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  PDF = 'pdf',
  OTHER = 'other'
}

export const FILE_PREVIEW_MAP: Record<FileType, ViewType> = {
  [FileType.IMAGE]: ViewType.IMAGE,
  [FileType.VIDEO]: ViewType.VIDEO,
  [FileType.AUDIO]: ViewType.AUDIO,
  [FileType.PDF]: ViewType.PDF,
  [FileType.OTHER]: ViewType.FILE
}

export const MIME_TYPE_MAP: Record<FileType, string[]> = {
  [FileType.IMAGE]: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  [FileType.VIDEO]: ['video/mp4', 'video/webm', 'video/ogg'],
  [FileType.AUDIO]: ['audio/mpeg', 'audio/ogg', 'audio/wav'],
  [FileType.PDF]: ['application/pdf'],
  [FileType.OTHER]: []
}

export const FILE_EXTENSION_MAP: Record<FileType, string[]> = {
  [FileType.IMAGE]: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  [FileType.VIDEO]: ['mp4', 'webm', 'ogg'],
  [FileType.AUDIO]: ['mp3', 'ogg', 'wav'],
  [FileType.PDF]: ['pdf'],
  [FileType.OTHER]: []
}

export const DEFAULT_PREVIEW_CONFIG = {
  width: 120,
  height: 120,
  quality: 0.8
}

export const DEFAULT_ALLOWED_TYPES: FileType[] = [
  FileType.IMAGE,
  FileType.VIDEO,
  FileType.AUDIO,
  FileType.PDF,
]
export const DEFAULT_UPLOAD_LIMIT = 5
export const DEFAULT_MAX_SIZE = 10

export const MESSAGES = {
  UPLOAD_SUCCESS: '上传成功',
  UPLOAD_ERROR: '上传失败',
  FILE_TYPE_ERROR: '文件类型不支持',
  FILE_SIZE_ERROR: (maxSize: number) => `文件大小不能超过 ${maxSize}MB`,
  FILE_COUNT_ERROR: (limit: number) => `最多只能上传 ${limit} 个文件`,
  UPLOAD_FAILED: '上传失败',
  FILE_SIZE_EXCEED: (maxSize: number) => `文件大小不能超过 ${maxSize}MB`,
  EXCEED_LIMIT: (limit: number) => `最多只能上传 ${limit} 个文件`,
  SERVER_ERROR: (code: number) => `服务器错误(${code})`
}
