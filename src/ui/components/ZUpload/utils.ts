import { getAssetUrl } from '@/utils/utils'
import type { UploadFile } from 'element-plus'
import { DEFAULT_PREVIEW_CONFIG, FILE_EXTENSION_MAP, FILE_PREVIEW_MAP, FileType, MIME_TYPE_MAP, ViewType } from './constants'

interface FileSource {
  id?: string | number
  url?: string
  name?: string
  status?: 'success' | 'uploading' | 'fail'
  response?: any
}

export const createUploadFiles = (sources: (FileSource | string | null | undefined)[]): UploadFile[] => {
  let uidCounter = Date.now()

  return sources
    .filter((source): source is FileSource | string => source != null)
    .map(source => {
      if (typeof source === 'string') {
        return {
          name: source,
          url: getAssetUrl(source),
          uid: uidCounter++,
          status: 'success'
        }
      }

      const { id = '', url = '', name = '', status = 'success', response } = source
      const fileId = String(id || url)

      return {
        name: name || fileId,
        url: getAssetUrl(url || fileId),
        uid: uidCounter++,
        status,
        response
      }
    })
}

export const formatFileSize = (size?: number): string => {
  if (!size) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(2)} ${units[index]}`
}

export const getFileType = (file: UploadFile): FileType => {
  const mimeType = file.raw?.type
  const fileName = file.name || ''
  const extension = fileName.split('.').pop()?.toLowerCase()

  // 通过 MIME 类型判断
  if (mimeType) {
    for (const [type, mimes] of Object.entries(MIME_TYPE_MAP)) {
      if (mimes.includes(mimeType)) {
        return type as FileType
      }
    }
  }

  // 通过文件扩展名判断
  if (extension) {
    for (const [type, extensions] of Object.entries(FILE_EXTENSION_MAP)) {
      if (extensions.includes(extension)) {
        return type as FileType
      }
    }
  }

  return FileType.OTHER
}

export const getPreviewComponentType = (file: UploadFile): ViewType => {
  const fileType = getFileType(file)
  return FILE_PREVIEW_MAP[fileType]
}

export const createThumbnail = async (file: File, config = DEFAULT_PREVIEW_CONFIG): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('image/')) {
      reject(new Error('Not an image file'))
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const img = new Image()
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('Failed to get canvas context'))
          return
        }

        const { width, height, quality } = config
        canvas.width = width
        canvas.height = height

        // 计算缩放比例，保持宽高比
        const scale = Math.min(width / img.width, height / img.height)
        const x = (width - img.width * scale) / 2
        const y = (height - img.height * scale) / 2

        ctx.drawImage(img, x, y, img.width * scale, img.height * scale)
        resolve(canvas.toDataURL('image/jpeg', quality))
      }
      img.onerror = () => reject(new Error('Failed to load image'))
      img.src = e.target?.result as string
    }
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsDataURL(file)
  })
}

export const isPreviewable = (file: UploadFile): boolean => {
  const fileType = getFileType(file)
  return fileType !== FileType.OTHER
}
