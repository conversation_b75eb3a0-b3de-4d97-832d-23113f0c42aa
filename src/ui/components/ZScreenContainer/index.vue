<script setup lang="ts">
import { ref, withDefaults } from 'vue'
import VScaleScreen from './main.ts'
import type { ScreenProps, ZScreenContainerExposed, ScaleChangeDetail, MountedDetail } from './types.ts'

const props = withDefaults(defineProps<ScreenProps>(), {
  width: 1920,
  height: 1080,
  autoScale: true,
  delay: 100,
  fullScreen: false,
  boxStyle: () => {
    return {
      background: '#000'
    }
  },
  wrapperStyle: () => {
    return {
      background: '#000'
    }
  },
  bodyOverflowHidden: true,
  loading: true
})

// 定义事件
const emit = defineEmits<{
  mounted: [detail: MountedDetail]
  scalechange: [detail: ScaleChangeDetail]
}>()

const vScaleScreenRef = ref<ZScreenContainerExposed>()

const handleMounted = (detail: MountedDetail) => {
  emit('mounted', detail)
}

const handleScaleChange = (event: CustomEvent<ScaleChangeDetail>) => {
  emit('scalechange', event.detail)
}

// 暴露方法给外部使用
defineExpose({
  onResize: () => vScaleScreenRef.value?.onResize(),
  updateScale: () => vScaleScreenRef.value?.updateScale(),
  initSize: () => vScaleScreenRef.value?.initSize(),
  updateSize: () => vScaleScreenRef.value?.updateSize(),
  triggerScaleChange: () => vScaleScreenRef.value?.triggerScaleChange(),
})

</script>

<template>
  <VScaleScreen
    ref="vScaleScreenRef"
    :width="props.width"
    :height="props.height"
    :delay="props.delay"
    :auto-scale="props.autoScale"
    :full-screen="props.fullScreen"
    :box-style="props.boxStyle"
    :wrapper-style="props.wrapperStyle"
    :body-overflow-hidden="props.bodyOverflowHidden"
    @mounted="handleMounted"
    @scalechange="handleScaleChange"
  >
    <slot />
    <!-- 加载状态 -->
    <div v-if="props.loading" class="loading-overlay">
      <div class="loading-content">
        <span class="loading-text">大屏加载中...</span>
      </div>
    </div>
  </VScaleScreen>
</template>

<style scoped lang="scss">
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  color: #fff;
  font-size: 24px;
  text-align: center;
}

.loading-text {
  display: inline-block;
  margin-top: 20px;
}
</style>
