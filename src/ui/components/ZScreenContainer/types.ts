import type { CSSProperties } from 'vue'

type IAutoScale =
  | boolean
  | {
      x?: boolean
      y?: boolean
    }

export interface ScreenProps {
  /** 设计稿宽度 */
  width?: string | number
  /** 设计稿高度 */
  height?: string | number
  /** 自适应配置，配置为 boolean 类型时，为启动或者关闭自适应，配置为对象时，若 x 为 true，x 轴产生边距，y 为 true 时，y 轴产生边距，启用 fullScreen 时此配置失效 */
  autoScale?: IAutoScale
  /** 窗口变化防抖延迟时间 */
  delay?: number
  /** 全屏自适应，启用此配置项时会存在拉伸效果，同时 autoScale 失效，非必要情况下不建议开启 */
  fullScreen?: boolean
  /** 修改容器样式，如居中展示时侧边背景色，符合 Vue 双向绑定 style 标准格式 */
  boxStyle?: CSSProperties | undefined
  /** 修改自适应区域样式，符合 Vue 双向绑定 style 标准格式 */
  wrapperStyle?: CSSProperties | undefined
  /** 启用后body的样式会自动设置为 overflow: hidden */
  bodyOverflowHidden?: boolean
  /** 是否显示加载状态 */
  loading?: boolean
}

/**
 * 缩放变化事件详情
 */
export interface ScaleChangeDetail {
  /** X轴缩放比例 */
  scaleX: number
  /** Y轴缩放比例 */
  scaleY: number
  /** 整体缩放比例（当 scaleX === scaleY 时） */
  scale?: number
}

/**
 * 组件挂载完成事件详情
 */
export interface MountedDetail {
  /** 当前容器宽度 */
  width: string | number
  /** 当前容器高度 */
  height: string | number
  /** 原始设计宽度 */
  originalWidth: string | number
  /** 原始设计高度 */
  originalHeight: string | number
  /** 容器 DOM 元素 */
  element: HTMLElement | undefined
}

/**
 * ZScreenContainer 组件暴露的方法
 */
export interface ZScreenContainerExposed {
  onResize: () => void
  updateScale: () => void
  initSize: () => Promise<void>
  updateSize: () => void
  triggerScaleChange: () => void
}
