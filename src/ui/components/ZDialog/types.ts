export interface ZDialogProps {
  /**
   * 对话框是否可见
   */
  visible: boolean

  /**
   * 对话框标题
   */
  title?: string

  /**
   * 对话框宽度
   * @default '50%'
   */
  width?: string

  /**
   * 是否显示底部按钮区域
   * @default true
   */
  showFooter?: boolean

  /**
   * 是否在关闭时销毁内容
   * @default false
   */
  destroyOnClose?: boolean

  /**
   * 是否可以通过点击遮罩层关闭对话框
   * @default true
   */
  closeOnClickModal?: boolean
}

export interface ZDialogEmits {
  /**
   * 更新对话框可见状态
   */
  (e: 'update:visible', value: boolean): void

  /**
   * 点击确认按钮时触发
   */
  (e: 'confirm'): void

  /**
   * 点击取消按钮时触发
   */
  (e: 'cancel'): void

  /**
   * 对话框关闭时触发
   */
  (e: 'close'): void
}

export interface ZDialogInstance {
  /**
   * 打开对话框
   */
  open: () => void

  /**
   * 关闭对话框
   */
  close: () => void
}

export interface ZDialogSlots {
  /**
   * 默认插槽，对话框内容
   */
  default?: () => any

  /**
   * 底部插槽，自定义底部按钮区域
   */
  footer?: () => any
}