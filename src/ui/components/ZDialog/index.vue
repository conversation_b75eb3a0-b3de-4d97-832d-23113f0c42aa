<template>
  <el-dialog :title="title" v-model="internalVisible" :width="width" :before-close="handleBeforeClose" v-bind="$attrs"
    class="z-dialog">
    <slot></slot>
    <template #footer v-if="showFooter">
      <div class="z-dialog__footer">
        <el-button @click="handleCancel" class="z-dialog__button z-dialog__button--cancel">
          取消
        </el-button>
        <el-button type="primary" @click="handleConfirm" class="z-dialog__button z-dialog__button--confirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ZDialogProps, ZDialogEmits, ZDialogInstance } from './types'

defineOptions({
  name: 'ZDialog'
})

const props = withDefaults(defineProps<ZDialogProps>(), {
  title: '',
  showFooter: true,
  width: '50%',
  destroyOnClose: false,
  closeOnClickModal: true
})

const emit = defineEmits<ZDialogEmits>()

const internalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleCancel = () => {
  emit('cancel')
  internalVisible.value = false
}

const handleConfirm = () => {
  emit('confirm')
}

const handleBeforeClose = (done: () => void) => {
  emit('close')
  done()
}

defineExpose<ZDialogInstance>({
  open: () => {
    internalVisible.value = true
  },
  close: () => {
    internalVisible.value = false
  }
})
</script>

<style lang="scss" scoped>
.z-dialog {

  // 对话框主体
  &__body {
    padding: 20px;
  }

  // 底部区域
  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
  }

  // 按钮基础样式
  &__button {
    min-width: 80px;

    // 取消按钮
    &--cancel {
      &:hover {
        background-color: var(--el-color-info-light-7);
      }
    }

    // 确认按钮
    &--confirm {
      &:hover {
        background-color: var(--el-color-primary-light-3);
      }
    }
  }

  // 响应式样式
  @media screen and (max-width: 768px) {
    &--mobile {
      width: 90% !important;

      .z-dialog__footer {
        flex-direction: column;
        gap: 8px;
      }

      .z-dialog__button {
        width: 100%;
      }
    }
  }

  // 深度选择器修改 element-plus 对话框样式
  :deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;

    &__header {
      margin: 0;
      padding: 20px;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }

    &__body {
      padding: 20px;
    }

    &__footer {
      margin: 0;
      padding: 20px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
}
</style>
