import { ref, computed } from 'vue'
import { asyncDataCache, type CacheOptions } from '@/utils/cache'
import type {
  UseSelectCacheOptions,
  UseSelectCacheReturn,
  Option
} from './types'

export function useSelectCache(options: UseSelectCacheOptions = {}): UseSelectCacheReturn {
  const loading = ref(false)
  const error = ref<Error | null>(null)

  const generateKey = (baseKey: string | (() => Promise<any>), params?: any): string => {
    if (typeof baseKey === 'string') {
      return options.generateCacheKey && params
        ? `select_${baseKey}_${options.generateCacheKey(params)}`
        : `select_${baseKey}`
    }

    const fnString = baseKey.toString()
    const fnHash = fnString
      .split('')
      .reduce((hash, char) => ((hash << 5) - hash) + char.charCodeAt(0), 0)
      .toString(36)
    const paramsString = params ? JSON.stringify(params) : ''
    return `select_${fnHash}_${paramsString}`
  }

  const getCacheOptions = (cacheOptions: CacheOptions = {}): CacheOptions => {
    return {
      ttl: cacheOptions.ttl || options.cacheTTL || 3600,
      strategy: cacheOptions.strategy === 'none' ? 'none' : (cacheOptions.strategy || options.cacheStrategy || 'memory'),
    }
  }

  const loadOptions = async <T extends Option>(
    fetchFn: () => Promise<T[]>,
    params?: any,
    overrideOptions?: UseSelectCacheOptions
  ): Promise<T[]> => {
    const mergedOptions = { ...options, ...overrideOptions }
    const cacheKey = mergedOptions.cacheKey
      ? generateKey(mergedOptions.cacheKey, params)
      : generateKey(fetchFn, params)

    loading.value = true
    error.value = null

    try {
      const cacheOptions: CacheOptions = {
        ttl: mergedOptions.cacheTTL,
        strategy: mergedOptions.forceRefresh ? 'none' : mergedOptions.cacheStrategy,
      }

      const result = await asyncDataCache.getOrFetch<T[]>(
        cacheKey,
        fetchFn,
        getCacheOptions(cacheOptions)
      )
      return result
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      return []
    } finally {
      loading.value = false
    }
  }

  const refreshCache = (cacheKey: string, params?: any): void => {
    if (!cacheKey) return

    const key = generateKey(cacheKey, params)
    asyncDataCache.clearCache(key, getCacheOptions())
  }

  const clearAllCache = (): void => {
    asyncDataCache.clearAllCache(getCacheOptions())
  }

  return {
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    loadOptions,
    refreshCache,
    clearAllCache,
  }
}
