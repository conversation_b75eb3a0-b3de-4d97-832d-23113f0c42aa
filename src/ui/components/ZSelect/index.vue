<script lang="ts" setup>
import { ref, watch, computed, useAttrs } from 'vue'
import { ElSelect } from 'element-plus'
import { useSelectCache } from './useSelectCache'
import type { SelectProps, SelectEmits, Option } from './types'

defineOptions({ name: 'ZSelect' })

const props = defineProps<SelectProps>()
const emit = defineEmits<SelectEmits>()

const attrs = useAttrs()

const innerValue = ref(props.modelValue)
const localOptions = ref<Option[]>(props.options || [])

const { loading, error, loadOptions, refreshCache } = useSelectCache({
  cacheKey: props.cacheKey,
  cacheTTL: props.cacheTTL,
  cacheStrategy: props.cacheStrategy,
})

const isRemote = computed(() => props.filterable && !!attrs['remote-method'])

const fetchOptionsData = async () => {
  if (!props.fetchOptions) return

  try {
    const strategy = props.forceRefresh ? 'none' : props.cacheStrategy
    const options = await loadOptions(
      () => props.fetchOptions!(props.fetchParams),
      props.fetchParams,
      {
        cacheKey: props.cacheKey,
        cacheStrategy: strategy,
      }
    )

    localOptions.value = options
    emit('options-loaded', options)
  } catch (err) {
    console.error('Failed to fetch options:', err)
    localOptions.value = []

    if (err instanceof Error) {
      emit('options-error', err)
    } else {
      emit('options-error', new Error(String(err)))
    }
  }
}

const handleChange = (value: any) => {
  emit('update:modelValue', value)
  emit('change', value)
}

const refresh = () => {
  if (props.cacheKey) {
    refreshCache(props.cacheKey, props.fetchParams)
  }
  fetchOptionsData()
}

watch(
  () => props.modelValue,
  (newVal) => {
    innerValue.value = newVal
    if (
      props.fetchOptions &&
      props.fetchOnValueChange &&
      newVal &&
      !localOptions.value.length
    ) {
      fetchOptionsData()
    }
  }
)

watch(
  () => props.options,
  (newOptions) => {
    if (newOptions) {
      localOptions.value = newOptions
    }
  }
)

watch(
  () => props.fetchParams,
  () => {
    if (props.fetchOptions) {
      fetchOptionsData()
    }
  },
  { deep: true }
)

if (props.fetchOptions && !isRemote.value) {
  fetchOptionsData()
}

defineExpose({
  refresh,
  options: localOptions,
})
</script>

<template>
  <el-select v-model="innerValue" v-bind="attrs" :remote="isRemote" :loading="loading" :filterable="filterable"
    @change="handleChange">
    <el-option v-for="item in localOptions" :key="item.value" :label="item.label" :value="item.value"
      :disabled="item.disabled" />
    <template v-if="error && localOptions.length === 0" #empty>
      <div class="select-error-message">
        <el-icon>
        </el-icon>
        <span>加载选项失败，请重试</span>
      </div>
    </template>
  </el-select>
</template>

<style scoped>
.select-error-message {
  color: #f56c6c;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
}
</style>
