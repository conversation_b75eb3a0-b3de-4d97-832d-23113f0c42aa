import type { ComputedRef } from 'vue'

export interface Option {
  value: string | number
  label: string
  disabled?: boolean
}

export type FetchOptionsFn = (params?: any) => Promise<Option[]>

export type CacheStrategy = 'memory' | 'local' | 'session' | 'none'

export interface SelectProps {
  modelValue: string | number | any[] | undefined
  options?: Option[]
  fetchOptions?: FetchOptionsFn
  filterable?: boolean
  fetchOnValueChange?: boolean
  cacheKey?: string
  cacheTTL?: number
  cacheStrategy?: CacheStrategy
  forceRefresh?: boolean
  fetchParams?: any
}

export interface SelectEmits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
  (e: 'options-loaded', options: Option[]): void
  (e: 'options-error', error: Error): void
}

export interface UseSelectCacheOptions {
  cacheKey?: string
  cacheTTL?: number
  cacheStrategy?: CacheStrategy
  generateCacheKey?: (params: any) => string
  forceRefresh?: boolean
}

export interface UseSelectCacheReturn {
  loading: ComputedRef<boolean>
  error: ComputedRef<Error | null>
  loadOptions: <T extends Option>(
    fetchFn: () => Promise<T[]>,
    params?: any,
    overrideOptions?: UseSelectCacheOptions
  ) => Promise<T[]>
  refreshCache: (cacheKey: string, params?: any) => void
  clearAllCache: () => void
}
