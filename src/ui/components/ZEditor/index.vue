<template>
  <div class="z-editor">
    <div
      ref="editorRef"
      class="z-editor__container"
      :style="{ height }"
      :class="{ 'is-disabled': disabled }"
    >
      <div class="ql-toolbar ql-snow"></div>
      <div class="ql-container ql-snow"></div>
    </div>

    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileInputChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, markRaw } from 'vue'
import Quill from 'quill'
import { ElMessage } from 'element-plus'
import 'quill/dist/quill.snow.css'
import { createLogger } from '@/utils/logger'
import req from '@/utils/req'
import { getAssetUrl } from '@/utils/utils'
import ImageResize from 'quill-image-resize-vue'

const logger = createLogger('ZEditor')

Quill.register('modules/imageResize', ImageResize)

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '请输入内容...',
  },
  height: {
    type: String,
    default: '300px',
  },
  uploadUrl: {
    type: String,
    default: import.meta.env.VITE_API_UPLOAD_URL,
  },
  maxSize: {
    type: Number,
    default: 10,
  },
  toolbar: {
    type: Array,
    default: () => [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ header: 1 }, { header: 2 }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ direction: 'rtl' }],
      [{ size: ['small', false, 'large', 'huge'] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ['clean'],
      ['link', 'image'],
    ],
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const editorRef = ref<HTMLElement | null>(null)
const quill = ref<Quill | null>(null)
const fileInput = ref<HTMLInputElement | null>(null)
let isInternalChange = false

const uploadImage = async (file: File): Promise<string> => {
  if (file.size / 1024 / 1024 > props.maxSize) {
    throw new Error(`图片大小不能超过 ${props.maxSize}MB`)
  }

  const formData = new FormData()
  formData.append('file', file)

  try {
    const result = await req({
      url: props.uploadUrl,
      method: 'POST',
      data: formData,
    })
    if (result.result !== 0) {
      throw new Error(result.message || '上传失败')
    }

    return getAssetUrl(result.data.absUrl)
  } catch (error) {
    logger.error(error)
    throw new Error('图片上传失败')
  }
}

const handleFileInputChange = async (event: Event) => {
  const input = event.target as HTMLInputElement
  const file = input.files?.[0]
  if (!file) return

  try {
    const url = await uploadImage(file)
    const range = quill.value?.getSelection(true)
    if (range && quill.value) {
      quill.value.insertEmbed(range.index, 'image', url)
      quill.value.setSelection(range.index + 1)
    }
  } catch (error: any) {
    logger.error(error)
    ElMessage.error(error.message)
  } finally {
    input.value = ''
  }
}

const handlePaste = async (event: ClipboardEvent) => {
  const clipboardData = event.clipboardData
  if (!clipboardData) return

  const items = Array.from(clipboardData.items)
  const imageItem = items.find((item) => item.type.startsWith('image/'))

  if (imageItem) {
    event.preventDefault()
    const file = imageItem.getAsFile()
    if (file) {
      try {
        const url = await uploadImage(file)
        const range = quill.value?.getSelection()
        if (range && quill.value) {
          quill.value.insertEmbed(range.index, 'image', url)
          quill.value.setSelection(range.index + 1)
        }
      } catch (error: any) {
        ElMessage.error(error.message)
      }
    }
  }
}

const initQuill = () => {
  if (!editorRef.value) return

  const imageHandler = () => {
    fileInput.value?.click()
  }

  quill.value = markRaw(
    new Quill(editorRef.value, {
      modules: {
        toolbar: {
          container: props.toolbar,
          handlers: {
            image: imageHandler,
          },
        },
        imageResize: {
          displaySize: true,
          modules: ['Resize', 'DisplaySize', 'Toolbar'],
          toolbarStyles: {
            backgroundColor: 'black',
            border: 'none',
            color: '#fff',
          },
          handleStyles: {
            backgroundColor: 'black',
            border: 'none',
            color: '#fff',
          },
        },
      },

      placeholder: props.placeholder,
      readOnly: props.disabled,
      theme: 'snow',
    }),
  )

  quill.value.on('text-change', () => {
    if (!quill.value) return

    const html = quill.value.root.innerHTML
    isInternalChange = true
    emit('update:modelValue', html)
    emit('change', html)
    isInternalChange = false
  })

  quill.value.root.addEventListener('paste', handlePaste)

  if (props.modelValue) {
    quill.value.root.innerHTML = props.modelValue
  }
}

watch(
  () => props.modelValue,
  (newVal) => {
    if (isInternalChange) return
    if (quill.value && newVal !== quill.value.root.innerHTML) {
      quill.value.root.innerHTML = newVal
    }
  },
)

watch(
  () => props.disabled,
  (newVal) => {
    if (quill.value) {
      quill.value.enable(!newVal)
    }
  },
)

onMounted(() => {
  initQuill()
})

onBeforeUnmount(() => {
  if (quill.value) {
    quill.value.root.removeEventListener('paste', handlePaste)
  }
  quill.value = null
})

defineExpose({
  getQuill: () => quill.value,
  getHTML: () => quill.value?.root.innerHTML || '',
  getText: () => quill.value?.getText() || '',
  setHTML: (html: string) => {
    if (quill.value) {
      quill.value.root.innerHTML = html
    }
  },
  clear: () => {
    if (quill.value) {
      quill.value.setText('')
    }
  },
})
</script>

<style lang="scss">
.z-editor {
  &__container {
    background: #fff;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);

    .ql-toolbar {
      border-top: none;
      border-left: none;
      border-right: none;
      border-bottom: 1px solid var(--el-border-color);
      padding: 8px;
    }

    .ql-container {
      border: none;
      height: calc(100% - 42px); // 减去工具栏高度
    }

    &.is-disabled {
      background-color: var(--el-disabled-bg-color);
      cursor: not-allowed;

      .ql-toolbar,
      .ql-container {
        opacity: 0.7;
        cursor: not-allowed;
      }
    }
  }

  .ql-snow.ql-toolbar {
    .ql-picker.ql-header {
      .ql-picker-label::before,
      .ql-picker-item::before {
        content: '普通';
      }

      .ql-picker-label[data-value='1']::before,
      .ql-picker-item[data-value='1']::before {
        content: '标题一';
      }

      .ql-picker-label[data-value='2']::before,
      .ql-picker-item[data-value='2']::before {
        content: '标题二';
      }

      .ql-picker-label[data-value='3']::before,
      .ql-picker-item[data-value='3']::before {
        content: '标题三';
      }

      .ql-picker-label[data-value='4']::before,
      .ql-picker-item[data-value='4']::before {
        content: '标题四';
      }

      .ql-picker-label[data-value='5']::before,
      .ql-picker-item[data-value='5']::before {
        content: '标题五';
      }

      .ql-picker-label[data-value='6']::before,
      .ql-picker-item[data-value='6']::before {
        content: '标题六';
      }
    }

    .ql-picker.ql-size {
      .ql-picker-label::before,
      .ql-picker-item::before {
        content: '普通';
      }

      .ql-picker-label[data-value='small']::before,
      .ql-picker-item[data-value='small']::before {
        content: '小';
      }

      .ql-picker-label[data-value='large']::before,
      .ql-picker-item[data-value='large']::before {
        content: '中';
      }

      .ql-picker-label[data-value='huge']::before,
      .ql-picker-item[data-value='huge']::before {
        content: '大';
      }
    }
  }

  .ql-snow .ql-tooltip::before {
    content: '链接';
  }

  .ql-snow .ql-tooltip a.ql-action::after {
    content: '编辑';
  }

  .ql-snow .ql-tooltip.ql-editing a.ql-action::after {
    content: '保存';
  }

  .ql-snow .ql-tooltip a.ql-remove::before {
    content: '移除';
  }
}
</style>
