<template>
  <div
    ref="containerRef"
    class="z-scrollbar"
    :class="{ 'z-scrollbar--hidden': hidden }"
  >
    <div class="z-scrollbar__wrap">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import PerfectScrollbar from 'perfect-scrollbar'
import 'perfect-scrollbar/css/perfect-scrollbar.css'
import { useResizeObserver } from '@vueuse/core'

interface Props {
  hidden?: boolean
  options?: PerfectScrollbar.Options
}

const props = withDefaults(defineProps<Props>(), {
  hidden: false,
  options: () => ({
    wheelSpeed: 1,
    wheelPropagation: false,
    suppressScrollX: false,
    suppressScrollY: false,
    swipeEasing: true,
  }),
})

const emit = defineEmits<{
  (e: 'scroll', evt: Event): void
  (e: 'scrollY', evt: Event): void
  (e: 'scrollX', evt: Event): void
}>()

const containerRef = ref<HTMLElement | null>(null)
let ps: PerfectScrollbar | null = null

const update = () => {
  if (ps) {
    ps.update()
  }
}

const destroy = () => {
  if (ps) {
    ps.destroy()
    ps = null
  }
}

const init = () => {
  if (containerRef.value) {
    ps = new PerfectScrollbar(containerRef.value, props.options)

  }
}

onMounted(() => {
  init()
  if (containerRef.value) {
    useResizeObserver(containerRef.value, () => {
      update()
    })
  }
})

onUnmounted(() => {
  destroy()
})

defineExpose({
  update,
  destroy,
})
</script>

<style lang="scss">
.z-scrollbar {
  position: relative;
  height: 100%;
  overflow: hidden;

  &__wrap {
    height: 100%;
  }

  &--hidden {
    .ps__rail-x,
    .ps__rail-y {
      display: none !important;
    }
  }
}
</style>
