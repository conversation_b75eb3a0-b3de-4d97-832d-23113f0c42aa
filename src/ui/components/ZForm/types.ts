import type { FormRules, FormItemRule } from 'element-plus'
import type { VNode, Slot, CSSProperties } from 'vue'
import type { CacheOptions } from '@/utils/cache'
import type { hType } from '@/types/vue'

export type ZFormValidateCallback = (valid: boolean, values: Record<string, any>) => void

export interface ZFormTreeNode {
  [key: string]: any
}

export interface ZFormTreeProps {
  label?: string
  children?: string
  value?: string
  disabled?: string
  [key: string]: any
}

export interface ZFormFieldOption {
  label: string
  value: any
  disabled?: boolean
  [key: string]: any
}

export type FieldType =
  | 'input'
  | 'number'
  | 'select'
  | 'custom'
  | 'date'
  | 'radio'
  | 'checkbox'
  | 'switch'
  | 'tree-select'
  | 'composite'

export interface ZFormFieldContext<T extends ZFormField = ZFormField> {
  field: T
  value: any
  formData: Record<string, any>
  disabled: boolean
  loading: boolean
  updateValue: (value: any) => void
  validate: () => void
  clearValidate: () => void
}

// 为每种字段类型定义特定的渲染上下文
export interface InputFieldRenderContext extends ZFormFieldContext<InputField> {
  h: hType
}

export interface NumberFieldRenderContext extends ZFormFieldContext<NumberField> {
  h: hType
}

export interface SelectFieldRenderContext extends ZFormFieldContext<SelectField> {
  h: hType
}

export interface TreeSelectFieldRenderContext extends ZFormFieldContext<TreeSelectField> {
  h: hType
}

export interface DateFieldRenderContext extends ZFormFieldContext<DateField> {
  h: hType
}

export interface RadioFieldRenderContext extends ZFormFieldContext<RadioField> {
  h: hType
}

export interface CheckboxFieldRenderContext extends ZFormFieldContext<CheckboxField> {
  h: hType
}

export interface SwitchFieldRenderContext extends ZFormFieldContext<SwitchField> {
  h: hType
}

export interface CustomFieldRenderContext extends ZFormFieldContext<CustomField> {
  h: hType
}

// 通用渲染上下文（向后兼容）
export interface ZFormRenderContext<T extends ZFormField = ZFormField>
  extends ZFormFieldContext<T> {
  h: hType
}

export interface FieldPropContext {
  model: Record<string, any>
  field: ZFormField
  form?: ZFormInstance
  value?: any
  options?: any[]
  optionsLoading?: boolean
}

export type FieldPropFunction = (context: FieldPropContext) => boolean

export type DependencyHandler = (params: {
  field: ZFormField
  form: ZFormInstance
  dependencyValues: Record<string, any>
  updateModel: (value: any) => void
  updateField: (fieldUpdate: Partial<ZFormField>) => void
  reason: TriggerReason
}) => void | Promise<void>

export interface FieldDependency {
  fields: string[]
  handler?: DependencyHandler
  effect?: 'visible' | 'disabled' | 'value' | 'options'
  condition?: (values: Record<string, any>) => boolean
  transform?: (values: Record<string, any>) => any
}

export interface OptionsContext {
  form: ZFormInstance
  model: Record<string, any>
  field: ZFormField
  dependencyValues: Record<string, any>
}
// 定义各种字段类型的渲染函数类型
export type InputFieldRender = (context: InputFieldRenderContext) => VNode | Promise<VNode> | null
export type NumberFieldRender = (context: NumberFieldRenderContext) => VNode | Promise<VNode> | null
export type SelectFieldRender = (context: SelectFieldRenderContext) => VNode | Promise<VNode> | null
export type TreeSelectFieldRender = (
  context: TreeSelectFieldRenderContext,
) => VNode | Promise<VNode> | null
export type DateFieldRender = (context: DateFieldRenderContext) => VNode | Promise<VNode> | null
export type RadioFieldRender = (context: RadioFieldRenderContext) => VNode | Promise<VNode> | null
export type CheckboxFieldRender = (
  context: CheckboxFieldRenderContext,
) => VNode | Promise<VNode> | null
export type SwitchFieldRender = (context: SwitchFieldRenderContext) => VNode | Promise<VNode> | null
export type CustomFieldRender = (context: CustomFieldRenderContext) => VNode | Promise<VNode> | null

// 通用渲染函数类型（向后兼容）
export type ZFormFieldRender<T extends ZFormField = ZFormField> = (
  context: ZFormRenderContext<T>,
) => VNode | Promise<VNode> | null

// 基础表单字段接口
interface BaseZFormField {
  name: string
  label?: string
  show?: boolean | FieldPropFunction
  disabled?: boolean | FieldPropFunction
  readonly?: boolean | FieldPropFunction
  required?: boolean | FieldPropFunction
  labelWidth?: string | number
  error?: string
  showMessage?: boolean
  size?: 'large' | 'default' | 'small'

  formItemClass?: string | string[] | Record<string, boolean>
  formItemStyle?: string | CSSProperties
  fieldClass?: string | string[] | Record<string, boolean>
  fieldStyle?: string | CSSProperties

  dependencies?: FieldDependency | FieldDependency[]

  rules?: FormItemRule | FormItemRule[]

  formatIn?: (value: any, formValues: Record<string, any>) => any
  formatOut?: (value: any, formValues: Record<string, any>) => any

  // 通用渲染函数（向后兼容）
  render?: ZFormFieldRender
  slotName?: string
}

// 输入框字段
export interface InputField extends Omit<BaseZFormField, 'render'> {
  type: 'input'
  value?: string
  props?: {
    placeholder?: string
    maxlength?: number
    minlength?: number
    showWordLimit?: boolean
    type?: 'text' | 'textarea' | 'password'
    readonly?: never
    disabled?: never
    [key: string]: any
  }
  // 特定于输入框的渲染函数
  render?: InputFieldRender
}

// 数字输入框字段
export interface NumberField extends Omit<BaseZFormField, 'render'> {
  type: 'number'
  value?: number
  props?: {
    min?: number
    max?: number
    step?: number
    precision?: number
    readonly?: never
    disabled?: never
    [key: string]: any
  }
  // 特定于数字输入框的渲染函数
  render?: NumberFieldRender
}

// 选择器字段
export interface SelectField extends Omit<BaseZFormField, 'render'> {
  type: 'select'
  value?: any
  props?: {
    multiple?: boolean
    clearable?: boolean
    readonly?: never
    disabled?: never
    [key: string]: any
  }
  options?: ZFormFieldOption[] | ((context: OptionsContext) => Promise<ZFormFieldOption[]>)
  valueKey?: string
  labelKey?: string
  noCache?: boolean
  cacheKey?: string
  cacheOptions?: CacheOptions
  // 特定于选择器的渲染函数
  render?: SelectFieldRender
}

// 树形选择器字段
export interface TreeSelectField extends Omit<BaseZFormField, 'render'> {
  type: 'tree-select'
  value?: any
  props?: {
    checkStrictly?: boolean
    multiple?: boolean
    readonly?: never
    disabled?: never
    [key: string]: any
  }
  treeOptions?: ZFormTreeNode[] | ((context: OptionsContext) => Promise<ZFormTreeNode[]>)
  treeProps?: ZFormTreeProps
  valueKey?: string
  labelKey?: string
  noCache?: boolean
  cacheKey?: string
  cacheOptions?: CacheOptions
  // 特定于树形选择器的渲染函数
  render?: TreeSelectFieldRender
}

// 日期选择器字段
export interface DateField extends Omit<BaseZFormField, 'render'> {
  type: 'date'
  value?: string | number | Date
  props?: {
    type?: 'year' | 'month' | 'date' | 'datetime' | 'week' | 'datetimerange' | 'daterange'
    format?: string
    valueFormat?: string
    readonly?: never
    disabled?: never
    [key: string]: any
  }
  // 特定于日期选择器的渲染函数
  render?: DateFieldRender
}

// 单选框组字段
export interface RadioField extends Omit<BaseZFormField, 'render'> {
  type: 'radio'
  value?: any
  props?: {
    readonly?: never
    disabled?: never
    [key: string]: any
  }
  options?: ZFormFieldOption[] | ((context: OptionsContext) => Promise<ZFormFieldOption[]>)
  valueKey?: string
  labelKey?: string
  noCache?: boolean
  cacheKey?: string
  cacheOptions?: CacheOptions
  // 特定于单选框组的渲染函数
  render?: RadioFieldRender
}

// 复选框组字段
export interface CheckboxField extends Omit<BaseZFormField, 'render'> {
  type: 'checkbox'
  value?: any[]
  props?: {
    readonly?: never
    disabled?: never
    [key: string]: any
  }
  options?: ZFormFieldOption[] | ((context: OptionsContext) => Promise<ZFormFieldOption[]>)
  valueKey?: string
  labelKey?: string
  noCache?: boolean
  cacheKey?: string
  cacheOptions?: CacheOptions
  // 特定于复选框组的渲染函数
  render?: CheckboxFieldRender
}

// 开关字段
export interface SwitchField extends Omit<BaseZFormField, 'render'> {
  type: 'switch'
  value?: boolean
  props?: {
    activeText?: string
    inactiveText?: string
    readonly?: never
    disabled?: never
    [key: string]: any
  }
  // 特定于开关的渲染函数
  render?: SwitchFieldRender
}

// 自定义字段
export interface CustomField extends Omit<BaseZFormField, 'render'> {
  type: 'custom'
  value?: any
  props?: {
    readonly?: never
    disabled?: never
    [key: string]: any
  }
  // 特定于自定义字段的渲染函数
  render?: CustomFieldRender
}

// 组合字段
export interface CompositeField extends Omit<BaseZFormField, 'render'> {
  type: 'composite'
  name: string
  label?: string
  show?: boolean | FieldPropFunction
  children: ZFormField[]
  layout?: {
    type: 'row' | 'col'
    gutter?: number
  }
  formItemClass?: string | string[] | Record<string, boolean>
  formItemStyle?: string | CSSProperties
  slotName?: string
  error: never
  showMessage: never
  readonly: never
  required: never
  rules: never
  formatIn: never
  formatOut: never
  render: never
}
// 统一导出字段类型
export type ZFormField =
  | InputField
  | NumberField
  | SelectField
  | TreeSelectField
  | DateField
  | RadioField
  | CheckboxField
  | SwitchField
  | CustomField
  | CompositeField

export function isInputField(field: ZFormField): field is InputField {
  return field.type === 'input'
}

export function isNumberField(field: ZFormField): field is NumberField {
  return field.type === 'number'
}

export function isSelectField(field: ZFormField): field is SelectField {
  return field.type === 'select'
}

export function isTreeSelectField(field: ZFormField): field is TreeSelectField {
  return field.type === 'tree-select'
}

export function isDateField(field: ZFormField): field is DateField {
  return field.type === 'date'
}

export function isRadioField(field: ZFormField): field is RadioField {
  return field.type === 'radio'
}

export function isCheckboxField(field: ZFormField): field is CheckboxField {
  return field.type === 'checkbox'
}

export function isSwitchField(field: ZFormField): field is SwitchField {
  return field.type === 'switch'
}

export function isCustomField(field: ZFormField): field is CustomField {
  return field.type === 'custom'
}

export function isCompositeField(field: ZFormField): field is CompositeField {
  return field.type === 'composite'
}

export interface ZFormCacheOptions {
  strategy?: 'memory' | 'local' | 'session' | 'none'
  ttl?: number
  clearOnUnmount?: boolean
}

export interface ZFormProps {
  model: Record<string, any>
  rules?: FormRules
  fields?: ZFormField[]
  validateOnRuleChange?: boolean
  showFooter?: boolean
  disabled?: boolean
  fetchData?: () => Promise<Record<string, any>>
  cacheOptions?: ZFormCacheOptions
  class?: string | string[] | Record<string, boolean>
  style?: string | CSSProperties
}

export interface DependencyAPI {
  trigger: (fieldName: string, payload?: string | TriggerPayload) => Promise<void>
  triggerAll: (fieldNames?: string[], payload?: string | TriggerPayload) => Promise<void>
  getDependencyValues: (fieldName: string) => Record<string, any>
  getDependentFields: (fieldName: string) => string[]
  rebuild: (cause?: string) => void
  clear: (fieldName: string) => void
  clearAll: () => void

  setDependency: (fieldName: string, dependency: FieldDependency) => void
  removeDependency: (fieldName: string) => void
  disableAuto: (fieldName: string) => void
  enableAuto: (fieldName: string) => void
  isEnabled: (fieldName: string) => boolean
}

export interface ZFormExposed {
  getValues: () => Record<string, any>
  validate: (callback?: ZFormValidateCallback) => Promise<boolean>
  resetFields: () => void
  clearValidate: (props?: string | string[]) => void
  refetch: () => Promise<void>
  refetchOptions: (fieldName: string) => Promise<void>
  validateField: (prop: string) => Promise<void>
  clearCache: () => void
  validateFields: (fieldNames?: string[]) => Promise<boolean>
  getFieldValidationStatus: (fieldName: string) => boolean
  getFields: () => ZFormField[]
  getFetchedData: () => Record<string, any> | null
  dependencies: DependencyAPI

  getFieldOptions: (fieldName: string) => ZFormFieldOption[]
  setFieldOptions: (fieldName: string, options: ZFormFieldOption[]) => void
  refreshFieldOptions: (fieldNames?: string | string[]) => Promise<void>
  clearOptionsCache: (fieldNames?: string | string[]) => void
  isOptionsLoading: (fieldName: string) => boolean
  getOptionsLoadingState: () => Record<string, boolean>
}
export type ZFormInstance = ZFormExposed

export type ZFormSlotContext<T extends ZFormField = ZFormField> = ZFormFieldContext<T>

export interface ZFormFooterSlotContext {
  disabled: boolean
  isSubmitting: boolean
  loading: boolean
  validate: () => Promise<boolean>
  reset: () => void
  submit: () => void
}

export type ZFormSlots = {
  [K in `item-${string}`]: Slot<ZFormSlotContext>
} & {
  [K in `composite-${string}`]: Slot<ZFormCompositeSlotContext>
} & {
  default?: Slot
  footer?: Slot<ZFormFooterSlotContext>
}

export interface ZFormCompositeSlotContext extends ZFormFieldContext<CompositeField> {
  children: ZFormField[]
  updateChildValue: (fieldName: string, value: any) => void
}

export interface TriggerPayload {
  action?: string
  params?: any
  source?: string
}
export type TriggerReason =
  | {
      type: 'INIT'
      payload?: never
    }
  | {
      type: 'VALUE_CHANGE'
      payload: {
        oldValue: any
        newValue: any
        field: string
        changedFields: string[]
      }
    }
  | {
      type: 'MANUAL'
      payload?: TriggerPayload
    }
  | {
      type: 'REBUILD'
      payload?: {
        cause?: 'FIELDS_CHANGE' | 'FORCE' | string
      }
    }

export type TriggerType = TriggerReason['type']

export type PayloadByType<T extends TriggerType> = Extract<TriggerReason, { type: T }>['payload']

export interface DependencyTriggerContext {
  field: ZFormField
  form: ZFormInstance
  dependencyValues: Record<string, any>
  updateModel: (value: any) => void
  updateField: (fieldUpdate: Partial<ZFormField>) => void
  reason: TriggerReason
}
