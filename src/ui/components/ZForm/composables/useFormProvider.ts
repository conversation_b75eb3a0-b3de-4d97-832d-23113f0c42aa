import { provide, inject, type Ref, type InjectionKey } from 'vue'
import type { FormInstance, FormValidationResult } from 'element-plus'
import type { ZFormField, ZFormCompositeSlotContext } from '../types'

export interface FormContext {
  formData: Ref<Record<string, any>>
  formRef: Ref<FormInstance | undefined>

  fields: Ref<ZFormField[]>
  fieldOptions: Ref<Record<string, any[]>>
  fieldOptionsLoading: Ref<Record<string, boolean>>

  isLoading: Ref<boolean>
  isSubmitting: Ref<boolean>
  disabled: Ref<boolean>

  updateFieldValue: (fieldName: string, value: any) => void
  validateField: (fieldName: string) => FormValidationResult
  clearValidate: (fieldName?: string | string[]) => void
  refreshFieldOptions: (fieldName?: string) => Promise<void>

  submit: () => Promise<void>
  reset: () => void
}

export interface FormContextForItem {
  formData: Ref<Record<string, any>>
  fieldOptions: Ref<Record<string, any[]>>
  fieldOptionsLoading: Ref<Record<string, boolean>>
  disabled: Ref<boolean>
  isLoading: Ref<boolean>
  updateFieldValue: (fieldName: string, value: any) => void
  validateField: (fieldName: string) => FormValidationResult
  clearValidate: (fieldName?: string | string[]) => void
}

export const formContextKey: InjectionKey<FormContext> = Symbol('FormContext')

export const provideForm = (context: FormContext) => {
  provide(formContextKey, context)
}

export const useForm = () => {
  const context = inject(formContextKey)
  if (!context) {
    throw new Error('useForm must be used within a ZForm component')
  }
  return context
}

export interface FormItemContext {
  field: ZFormField
  value: any
  formData: Record<string, any>
  disabled: boolean
  loading: boolean
  updateValue: (value: any) => void
  validate: () => void
  clearValidate: () => void
}

export const createFormItemContext = (
  field: ZFormField,
  context: FormContextForItem,
): FormItemContext => {
  return {
    field,
    value: context.formData.value[field.name],
    formData: context.formData.value,
    disabled: context.disabled.value,
    loading: context.isLoading.value,
    updateValue: (value: any) => context.updateFieldValue(field.name, value),
    validate: () => context.validateField(field.name),
    clearValidate: () => context.clearValidate(field.name),
  }
}

export const createFormCompositeItemContext = (
  field: ZFormField,
  context: FormContextForItem,
): ZFormCompositeSlotContext => {
  return {
    field,
    value: context.formData.value[field.name],
    formData: context.formData.value,
    disabled: context.disabled.value,
    loading: context.isLoading.value,
    updateValue: (value: any) => context.updateFieldValue(field.name, value),
    validate: () => context.validateField(field.name),
    clearValidate: () => context.clearValidate(field.name),
    children: field.type === 'composite' ? field.children : [],
    updateChildValue: (fieldName: string, value: any) => {
      const newValue = {
        ...context.formData.value[field.name],
        [fieldName]: value,
      }
      context.updateFieldValue(field.name, newValue)
    },
  }
}
