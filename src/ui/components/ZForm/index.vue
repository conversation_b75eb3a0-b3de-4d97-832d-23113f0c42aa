<template>
  <el-form
    ref="formRef"
    :model="localModel"
    :rules="computedRules"
    :validate-on-rule-change="validateOnRuleChange"
    v-bind="$attrs"
  >
    <z-form-loading v-if="isLoading" />
    <template
      v-for="field in props.fields"
      :key="field.name"
    >
      <z-form-item
        :field="field"
        :should-show="shouldShow(field)"
      >
        <template
          v-for="(_, slotName) in $slots"
          :key="slotName"
          #[slotName]="slotProps"
        >
          <slot
            :name="slotName as keyof ZFormSlots"
            v-bind="slotProps"
          />
        </template>
      </z-form-item>
    </template>

    <z-form-footer
      v-if="props.showFooter"
      :disabled="isSubmitting"
      @submit="handleSubmit"
      @reset="handleReset"
    >
      <slot
        name="footer"
        :disabled="isFormDisabled"
        :is-submitting="isSubmitting"
        :loading="isLoading"
        :validate="() => ValidationHelper.validateFields(formRef)"
        :reset="handleReset"
        :submit="handleSubmit"
      />
    </z-form-footer>
  </el-form>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  onMounted,
  getCurrentInstance,
  defineComponent,
  onUnmounted,
} from 'vue'
import { type FormInstance, type FormRules } from 'element-plus'
import { useDialogFormContext } from '../ZDialogForm/context'
import type {
  CompositeField,
  DependencyHandler,
  FieldDependency,
  OptionsContext,
  SelectField,
  TreeSelectField,
  TriggerPayload,
  TriggerReason,
  ZFormCacheOptions,
  ZFormField,
  ZFormFieldOption,
  ZFormInstance,
  ZFormProps,
  ZFormSlots,
  ZFormValidateCallback,
} from './types'
import { ValueProcessor, ValidationHelper, OptionsLoader, dependencyManager } from './utils/index'
import ZFormFooter from './components/ZFormFooter.vue'
import ZFormLoading from './components/ZFormLoading.vue'
import ZFormItem from './components/ZFormItem.vue'
import { asyncDataCache, type CacheOptions } from '@/utils/cache'
import { provideForm } from './composables/useFormProvider'
import { generateFieldCacheKey } from './utils/cache'
import { FieldPropComputer } from './utils/field-props'
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import cloneDeep from 'lodash/cloneDeep'
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import isEqual from 'lodash/isEqual'
import { DEFAULT_CACHE_OPTIONS } from './constants'

defineComponent({
  name: 'ZForm',
})

defineSlots<ZFormSlots>()

const props = withDefaults(defineProps<ZFormProps>(), {
  rules: () => ({}),
  fields: () => [],
  showFooter: false,
  disabled: false,
  fetchData: undefined,
  cacheOptions: () => DEFAULT_CACHE_OPTIONS,
})

const emit = defineEmits<{
  (e: 'submit', values: any): void
  (e: 'reset'): void
  (e: 'update:model', value: any): void
  (e: 'fetch-error', error: any): void
}>()

const formRef = ref<FormInstance>()
const localModel = ref<Record<string, any>>(
  ValueProcessor.formatInitialValue(props.fields, props.model ?? {}),
)
const previousModelValues = ref<Record<string, any>>({})

const isLoading = ref(false)
const isSubmitting = ref(false)
const fetchError = ref<any>(null)
const fieldOptions = ref<Record<string, any[]>>({})
const fieldOptionsLoading = ref<Record<string, boolean>>({})

watch(
  () => props.model,
  (newValue) => {
    if (!isEqual(newValue, localModel.value)) {
      localModel.value = ValueProcessor.formatInitialValue(props.fields, newValue ?? {})
    }
  },
  { deep: true },
)

const handleSubmit = async () => {
  if (isSubmitting.value) return

  try {
    isSubmitting.value = true

    const hiddenFields = props.fields
      .filter((field) => !shouldShow(field))
      .map((field) => field.name)
    formRef.value?.clearValidate(hiddenFields)

    const visibleFields = props.fields
      .filter((field) => shouldShow(field))
      .map((field) => field.name)

    const isValid = await ValidationHelper.validateFields(formRef.value, visibleFields)

    if (isValid) {
      emit('submit', localModel.value)
    } else {
      focusFirstErrorField(visibleFields)
    }
  } finally {
    isSubmitting.value = false
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
  localModel.value = ValueProcessor.formatInitialValue(props.fields, props.model ?? {})
  fetchError.value = null
  fieldOptions.value = {}
  fieldOptionsLoading.value = {}

  clearFormCache()
  loadFieldOptions(props.fields || [])

  emit('reset')
}

const refreshFieldOptions = async (fieldName?: string) => {
  if (fieldName) {
    const field = props.fields.find((f) => f.name === fieldName)
    if (field) {
      await fetchFieldOptions(field)
    }
  } else {
    await loadFieldOptions(props.fields)
  }
}

provideForm({
  formData: localModel,
  formRef,
  fields: computed(() => props.fields),
  fieldOptions,
  fieldOptionsLoading,
  isLoading,
  isSubmitting,
  disabled: computed(() => props.disabled),
  updateFieldValue: (fieldName: string, value: any) => {
    updateModel(fieldName, value)
  },
  validateField: (fieldName: string) => {
    return formRef.value?.validateField(fieldName) ?? Promise.resolve(false)
  },
  clearValidate: (fieldName?: string | string[]) => {
    formRef.value?.clearValidate(fieldName)
  },
  refreshFieldOptions,
  submit: () => handleSubmit(),
  reset: () => handleReset(),
})

const instance = getCurrentInstance()
let dialogCtx: ReturnType<typeof useDialogFormContext> | null = null

try {
  dialogCtx = useDialogFormContext()
} catch {}

onMounted(() => {
  if (dialogCtx?.formRef && instance?.exposed) {
    dialogCtx.formRef.value = instance.exposed as any
  }
})

const globalCacheOptions = computed<CacheOptions>(() => {
  const userOptions = props.cacheOptions || {}
  return {
    ...DEFAULT_CACHE_OPTIONS,
    ...userOptions,
  }
})

const fetchFieldOptions = async (field: ZFormField) => {
  fieldOptionsLoading.value[field.name] = true
  try {
    const context: OptionsContext = {
      form: instance?.exposed as ZFormInstance,
      model: localModel.value,
      field,
      dependencyValues: dependencyManager.getDependencyValues(field.name, localModel.value),
    }

    await OptionsLoader.loadOptions(
      field,
      context,
      globalCacheOptions.value,
      (options) => {
        fieldOptions.value[field.name] = options
      },
      (error) => {
        console.error(`Failed to fetch options for ${field.name}:`, error)
        fieldOptions.value[field.name] = []
      },
    )
  } finally {
    fieldOptionsLoading.value[field.name] = false
  }
}

const loadFieldOptions = async (fields: ZFormField[]) => {
  if (!fields?.length) return

  const fieldsToLoad = fields.filter((field) => {
    if (field.type === 'composite' || (!('options' in field) && !('treeOptions' in field))) {
      return false
    }

    if (field.noCache) {
      return true
    }

    return true
  })

  if (!fieldsToLoad.length) return

  const promises = fieldsToLoad.map((field) => fetchFieldOptions(field))
  await Promise.allSettled(promises)
}

const validateFields = (fields: ZFormField[]) => {
  fields.forEach((field) => {
    if (!field.name) {
      throw new Error(`Field name is required: ${JSON.stringify(field)}`)
    }
    if (field.type === 'composite' && field.children) {
      validateFields(field.children)
    }
  })
}

const computedRules = computed(() => {
  const mergedRules: FormRules = { ...props.rules }
  props.fields?.forEach((field) => {
    if ('rules' in field && field.rules) {
      const fieldRules =
        typeof field.rules === 'function'
          ? (field.rules as (model: Record<string, any>) => FormRules)(localModel.value)
          : field.rules
      mergedRules[field.name] = fieldRules
    }
  })
  return mergedRules
})

const isFormDisabled = computed(() => {
  return props.disabled || isSubmitting.value || isLoading.value
})

interface FieldVisibilityOptions {
  clearHiddenFields: boolean
}

const defaultVisibilityOptions: FieldVisibilityOptions = {
  clearHiddenFields: false,
}

const handleFieldVisibilityChange = (field: ZFormField, isVisible: boolean) => {
  if (!isVisible && defaultVisibilityOptions.clearHiddenFields) {
    localModel.value[field.name] = undefined
    formRef.value?.clearValidate([field.name])
  }
}

const shouldShow = (field: ZFormField) => {
  const context = FieldPropComputer.createContext({
    field,
    model: localModel.value,
    form: instance?.exposed as ZFormInstance,
    options: fieldOptions.value[field.name],
    optionsLoading: fieldOptionsLoading.value[field.name],
  })

  const isVisible = FieldPropComputer.isVisible(field, context) ?? true
  handleFieldVisibilityChange(field, isVisible)
  return isVisible
}

const updateModel = (fieldName: string, value: unknown) => {
  const field = props.fields?.find((f) => f.name === fieldName)
  const formattedValue = field ? ValueProcessor.formatIn(field, value, localModel.value) : value

  localModel.value[fieldName] = formattedValue
  emit('update:model', { ...localModel.value })
}

const fetchedData = ref<Record<string, any> | null>(null)

const fetchFormData = async () => {
  if (!props.fetchData) return

  isLoading.value = true
  try {
    const data = await props.fetchData()
    fetchedData.value = data
    localModel.value = ValueProcessor.formatInitialValue(props.fields, data)
  } catch (error) {
    fetchError.value = error
    emit('fetch-error', error)
  } finally {
    isLoading.value = false
  }
}

const getFieldsWithOptions = (): ZFormField[] => {
  return props.fields.map((field) => {
    const clonedField = { ...field }

    if (fieldOptions.value[field.name]) {
      if (field.type === 'tree-select') {
        ;(clonedField as TreeSelectField).treeOptions = fieldOptions.value[field.name]
      } else {
        ;(clonedField as SelectField).options = fieldOptions.value[field.name]
      }
    }

    if (field.type === 'composite' && field.children) {
      ;(clonedField as CompositeField).children = field.children.map((childField) => ({
        ...childField,
        options:
          'options' in childField
            ? fieldOptions.value[childField.name] || childField.options
            : undefined,
      }))
    }

    return clonedField
  })
}

const focusFirstErrorField = (visibleFieldNames: string[]) => {
  if (!formRef.value?.fields) return
  const formItems = formRef.value.fields

  const firstErrorField = formItems.find(
    (field) =>
      field.prop != null &&
      visibleFieldNames.includes(String(field.prop)) &&
      field.validateState === 'error',
  )

  if (!firstErrorField?.$el) return

  type FocusableElement = HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement

  const focusableElements = firstErrorField.$el.querySelectorAll<FocusableElement>(
    'input:not([type="hidden"]), select, textarea, [contenteditable="true"], [tabindex]:not([tabindex="-1"])',
  )

  const firstFocusable = Array.from(focusableElements).find((el) => {
    const style = window.getComputedStyle(el)

    if (el.disabled) return false

    if ((el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement) && el.readOnly) {
      return false
    }

    return style.display !== 'none' && style.visibility !== 'hidden'
  })

  if (firstFocusable) {
    firstFocusable.focus({ preventScroll: false })
    firstFocusable.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    })
  }
}

defineExpose({
  getValues: () => ValueProcessor.formatSubmitValue(props.fields, localModel.value),
  validate: (callback?: ZFormValidateCallback) => {
    const visibleFields = props.fields
      .filter((field) => shouldShow(field))
      .map((field) => field.name)

    const hiddenFields = props.fields
      .filter((field) => !shouldShow(field))
      .map((field) => field.name)
    formRef.value?.clearValidate(hiddenFields)

    if (!callback) {
      return ValidationHelper.validateFields(formRef.value, visibleFields)
    }

    return ValidationHelper.validateFields(formRef.value, visibleFields).then((valid) => {
      const formValues = ValueProcessor.formatSubmitValue(props.fields, localModel.value)
      callback(valid, formValues)

      if (!valid) {
        focusFirstErrorField(visibleFields)
      }

      return valid
    })
  },
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: (props?: string[]) => {
    formRef.value?.clearValidate(props)
  },
  refetch: fetchFormData,
  refetchOptions: refreshFieldOptions,
  validateField: (fieldName: string) => formRef.value?.validateField(fieldName),
  clearCache: () => {
    asyncDataCache.clearAllCache(globalCacheOptions.value)
    fieldOptions.value = {}
    fieldOptionsLoading.value = {}
  },
  validateFields: (fieldNames?: string[]) =>
    ValidationHelper.validateFields(formRef.value, fieldNames),
  getFieldValidationStatus: (fieldName: string) =>
    ValidationHelper.getFieldValidationStatus(formRef.value, fieldName),
  getFields: getFieldsWithOptions,
  getOriginalFields: () => props.fields,
  getFetchedData: () => fetchedData.value,
  dependencies: {
    trigger: async (fieldName: string, payload?: string | TriggerPayload) => {
      const field = props.fields.find((f) => f.name === fieldName)
      if (!field || !field.dependencies) return

      const reason: TriggerReason = {
        type: 'MANUAL',
        payload: typeof payload === 'string' ? { action: payload } : payload,
      }

      const dependencyValues = dependencyManager.getDependencyValues(fieldName, localModel.value)
      await handleDependency(field, dependencyValues, reason)
    },
    triggerAll: async (fieldNames?: string[], payload?: string | TriggerPayload) => {
      const fieldsToProcess = fieldNames
        ? props.fields.filter((f) => fieldNames.includes(f.name))
        : props.fields.filter((f) => f.dependencies)

      const reason: TriggerReason = {
        type: 'MANUAL',
        payload: typeof payload === 'string' ? { action: payload } : payload,
      }

      for (const field of fieldsToProcess) {
        const dependencyValues = dependencyManager.getDependencyValues(field.name, localModel.value)
        await handleDependency(field, dependencyValues, reason)
      }
    },
    getDependencyValues: (fieldName: string) => {
      return dependencyManager.getDependencyValues(fieldName, localModel.value)
    },
    getDependentFields: (fieldName: string) => {
      return Array.from(dependencyManager.getDependentFields(fieldName))
    },
    rebuild: (cause = 'FORCE') => {
      dependencyManager.clearAll()
      dependencyManager.buildDependencyGraph(props.fields)
      props.fields.forEach(setupFieldDependencies)

      props.fields.forEach(async (field) => {
        if (!field.dependencies) return
        const dependencyValues = dependencyManager.getDependencyValues(field.name, localModel.value)
        await handleDependency(field, dependencyValues, {
          type: 'REBUILD',
          payload: { cause },
        })
      })
    },
    clear: (fieldName: string) => {
      dependencyManager.clear(fieldName)
    },
    clearAll: () => {
      dependencyManager.clearAll()
    },
    setDependency: (fieldName: string, dependency: FieldDependency) => {
      dependencyManager.setDependency(fieldName, dependency)
    },
    removeDependency: (fieldName: string) => {
      dependencyManager.removeDependency(fieldName)
    },
    disableAuto: (fieldName: string) => {
      dependencyManager.disableAuto(fieldName)
    },
    enableAuto: (fieldName: string) => {
      dependencyManager.enableAuto(fieldName)
    },
    isEnabled: (fieldName: string) => {
      return dependencyManager.isEnabled(fieldName)
    },
  },
  getFieldOptions(fieldName: string) {
    return fieldOptions.value[fieldName] || []
  },

  setFieldOptions(fieldName: string, options: ZFormFieldOption[]) {
    fieldOptions.value[fieldName] = [...options]
  },

  async refreshFieldOptions(fieldNames?: string | string[]) {
    const names = typeof fieldNames === 'string' ? [fieldNames] : fieldNames
    const fieldsToRefresh = names
      ? props.fields.filter((f) => names.includes(f.name))
      : props.fields

    await loadFieldOptions(fieldsToRefresh.filter((f) => 'options' in f || 'treeOptions' in f))
  },

  clearOptionsCache(fieldNames?: string | string[]) {
    const names = typeof fieldNames === 'string' ? [fieldNames] : fieldNames
    const fieldsToProcess = names
      ? props.fields.filter((f) => names.includes(f.name))
      : props.fields

    fieldsToProcess.forEach((field) => {
      const cacheKey = generateFieldCacheKey(field)
      asyncDataCache.clearCache(cacheKey, globalCacheOptions.value)
      if (names) {
        delete fieldOptions.value[field.name]
      }
    })
    if (!names) {
      fieldOptions.value = {}
    }
  },

  // 获取指定字段的选项加载状态
  isOptionsLoading(fieldName: string) {
    return !!fieldOptionsLoading.value[fieldName]
  },

  // 获取所有字段的选项加载状态
  getOptionsLoadingState() {
    return { ...fieldOptionsLoading.value }
  },
})

const handleSingleDependency = (field: ZFormField, dependency: FieldDependency) => {
  const handler = async (params: Parameters<DependencyHandler>[0]) => {
    const { dependencyValues } = params

    if (dependency.handler) {
      await dependency.handler(params)
      return
    }

    const meetsCondition = dependency.condition?.(dependencyValues) ?? true

    switch (dependency.effect) {
      case 'visible':
        params.updateField({ show: meetsCondition })
        break
      case 'disabled':
        params.updateField({
          props: { ...('props' in field ? field.props : {}), disabled: !meetsCondition },
        })
        break
      case 'value':
        if (meetsCondition && dependency.transform) {
          params.updateModel(dependency.transform(dependencyValues))
        }
        break
      case 'options':
        if (meetsCondition && dependency.transform) {
          params.updateField({ options: dependency.transform(dependencyValues) })
        }
        break
    }
  }

  dependencyManager.register(field.name, handler)
}

const setupFieldDependencies = (field: ZFormField) => {
  if (!field.dependencies) return

  const dependencies = Array.isArray(field.dependencies) ? field.dependencies : [field.dependencies]

  dependencies.forEach((dependency) => {
    handleSingleDependency(field, dependency)
  })
}

const handleDependency = async (
  field: ZFormField,
  dependencyValues: Record<string, any>,
  reason: TriggerReason,
) => {
  await dependencyManager.trigger(field.name, {
    field,
    form: instance?.exposed as ZFormInstance,
    dependencyValues,
    reason,
    updateModel: (value) => updateModel(field.name, value),
    updateField: (fieldUpdate) => {
      if ('options' in fieldUpdate) {
        fieldOptions.value[field.name] = Array.isArray(fieldUpdate.options)
          ? [...fieldUpdate.options]
          : []
      }
      if ('treeOptions' in fieldUpdate) {
        fieldOptions.value[field.name] = Array.isArray(fieldUpdate.treeOptions)
          ? [...fieldUpdate.treeOptions]
          : []
      }
      Object.assign(field, fieldUpdate)
    },
  })
}

const isHandlingDependency = ref(false)

watch(
  () => localModel.value,
  async (newValue) => {
    if (isHandlingDependency.value) {
      return
    }

    try {
      isHandlingDependency.value = true

      if (!previousModelValues.value) {
        for (const field of props.fields) {
          if (!field.dependencies || !dependencyManager.isEnabled(field.name)) continue
          const dependencyValues = dependencyManager.getDependencyValues(field.name, newValue)
          await handleDependency(field, dependencyValues, { type: 'INIT' })
        }
        previousModelValues.value = cloneDeep(newValue)
        return
      }

      const changedFields = Object.keys(newValue).filter(
        (key) => !isEqual(newValue[key], previousModelValues.value[key]),
      )

      for (const field of props.fields) {
        if (!field.dependencies || !dependencyManager.isEnabled(field.name)) continue

        const dependsOnFields = dependencyManager.getDependsOnFields(field.name)
        if (changedFields.some((changedField) => dependsOnFields.has(changedField))) {
          const dependencyValues = dependencyManager.getDependencyValues(field.name, newValue)
          await handleDependency(field, dependencyValues, {
            type: 'VALUE_CHANGE',
            payload: {
              oldValue: previousModelValues.value[field.name],
              newValue: newValue[field.name],
              field: field.name,
              changedFields,
            },
          })
        }
      }

      previousModelValues.value = cloneDeep(newValue)
    } finally {
      isHandlingDependency.value = false
    }
  },
  { deep: true },
)

watch(
  () => props.fields,
  (newFields) => {
    dependencyManager.buildDependencyGraph(newFields || [])

    newFields?.forEach(setupFieldDependencies)

    loadFieldOptions(newFields?.filter((field) => !fieldOptions.value[field.name]) || [])
  },
  { deep: true, immediate: true },
)

onMounted(() => {
  validateFields(props.fields)
})

onUnmounted(() => {
  dependencyManager.clearAll()

  formRef.value?.clearValidate()

  if ((globalCacheOptions.value as ZFormCacheOptions).clearOnUnmount) {
    clearFormCache()
    fieldOptions.value = {}
  }

  fieldOptionsLoading.value = {}
  fetchedData.value = null
  fetchError.value = null
  isLoading.value = false
  isSubmitting.value = false
  localModel.value = {}
})

const clearFormCache = () => {
  const clearFieldCache = (field: ZFormField) => {
    if ('options' in field || 'treeOptions' in field) {
      const cacheKey = generateFieldCacheKey(field)
      asyncDataCache.clearCache(cacheKey, globalCacheOptions.value)
    }

    if (field.type === 'composite' && 'children' in field && field.children) {
      field.children.forEach(clearFieldCache)
    }
  }

  props.fields.forEach(clearFieldCache)
}

watch(
  () => props.fields.map((field) => shouldShow(field)),
  (newVisible, oldVisible) => {
    if (!formRef.value) return

    props.fields.forEach((field, index) => {
      if (oldVisible[index] && !newVisible[index]) {
        formRef.value?.clearValidate([field.name])
      }
    })
  },
  { deep: true },
)
</script>

<style lang="scss">
.z-form {
  position: relative;

  &__composite {
    &--row {
      display: flex;
      align-items: center;
      gap: var(--el-form-item-margin-bottom);
    }

    &--col {
      display: flex;
      flex-direction: column;
      gap: var(--el-form-item-margin-bottom);
    }

    &-item {
      flex: 1;
      min-width: 0;

      &:last-child {
        .el-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

.z-form__loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.z-form__loading-icon {
  margin-right: 8px;
}

.z-form__loading-text {
  color: var(--el-text-color-secondary);
}

.z-form__error {
  margin-bottom: 16px;
}

.z-form__item {
  margin-bottom: 18px;
}

.z-form__field--custom-render.render-error {
  color: var(--el-color-danger);
}

.z-form__footer {
  margin-top: 24px;
  text-align: right;
}

.z-form__btn {
  margin-left: 12px;
}

.z-form__option,
.z-form__radio,
.z-form__checkbox {
  margin-right: 12px;
}
</style>
