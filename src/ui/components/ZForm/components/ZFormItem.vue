<template>
  <el-form-item
    v-show="shouldShow"
    :label="field.label"
    :prop="propName"
    :required="isRequired"
    :error="field.error"
    :show-message="field.showMessage"
    :label-width="field.labelWidth"
    :size="field.size"
    :class="field.formItemClass"
    :style="field.formItemStyle"
    v-bind="$attrs"
  >
    <template v-if="field.render">
      <component
        :is="renderField"
        :class="['z-form__field z-form__field--custom-render', field.fieldClass]"
        :style="field.fieldStyle"
      />
    </template>

    <template v-else-if="field.type === 'custom'">
      <slot
        :name="`item-${field.name}`"
        v-bind="itemContext"
      >
        <el-input
          v-model="innerValue"
          :placeholder="`自定义组件未定义${field.name}`"
          :readonly="isReadonly"
          :disabled="isDisabled"
          :class="['z-form__field z-form__field--custom', field.fieldClass]"
          :style="field.fieldStyle"
        />
      </slot>
    </template>

    <template v-else-if="field.type === 'composite'">
      <z-form-composite-item
        :field="field"
        :should-show="shouldShow"
      >
        <template
          v-for="(_, slotName) in $slots"
          :key="slotName"
          #[slotName]="slotProps"
        >
          <slot
            :name="slotName"
            v-bind="slotProps"
          />
        </template>
      </z-form-composite-item>
    </template>

    <template v-else>
      <component
        :is="fieldComponent"
        v-model="innerValue"
        v-bind="getFieldProps"
        :class="['z-form__field', `z-form__field--${field.type}`, field.fieldClass]"
        :style="field.fieldStyle"
      >
        <template v-if="hasOptions">
          <component
            :is="optionComponent"
            v-for="option in fieldOptions[field.name]"
            :key="option[valueKey as keyof typeof option]"
            :label="option[labelKey as keyof typeof option]"
            :value="option[valueKey as keyof typeof option]"
            :disabled="option.disabled"
            class="z-form__option"
          >
            {{ option[labelKey as keyof typeof option] }}
          </component>
        </template>
      </component>
    </template>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { h } from 'vue'
import type { ZFormExposed, ZFormField } from '../types'
import { FormRenderer } from '../utils'
import { componentMap } from '../constants'
import ZFormCompositeItem from './ZFormCompositeItem.vue'
import { useForm, createFormItemContext } from '../composables/useFormProvider'
import { FieldPropComputer } from '../utils/field-props'

const props = defineProps<{
  field: ZFormField
  shouldShow?: boolean
  parentPropName?: string
}>()

const propName = computed(() => {
  return props.parentPropName ? `${props.parentPropName}.${props.field.name}` : props.field.name
})

const {
  formData,
  fieldOptions,
  fieldOptionsLoading,
  formRef,
  disabled: formDisabled,
  isLoading,
  updateFieldValue,
  validateField,
  clearValidate,
} = useForm()

const getFieldContext = computed(() => {
  return FieldPropComputer.createContext({
    field: props.field,
    model: formData.value,
    form: formRef.value as unknown as ZFormExposed,
    options: fieldOptions.value[props.field.name],
    optionsLoading: fieldOptionsLoading.value[props.field.name],
  })
})

const innerValue = computed({
  get: () => formData.value[props.field.name],
  set: (value) => updateFieldValue(props.field.name, value),
})

const isReadonly = computed(() => FieldPropComputer.isReadonly(props.field, getFieldContext.value))

const isDisabled = computed(
  () =>
    formDisabled.value ||
    FieldPropComputer.isDisabled(props.field, getFieldContext.value) ||
    isLoading.value,
)

const fieldComponent = computed(() => {
  if (props.field.type === 'composite') return null
  return componentMap[props.field.type || 'input']
})

const hasOptions = computed(() => ['select', 'radio', 'checkbox'].includes(props.field.type || ''))

const optionComponent = computed(() => {
  switch (props.field.type) {
    case 'select':
      return 'el-option'
    case 'radio':
      return 'el-radio'
    case 'checkbox':
      return 'el-checkbox'
    default:
      return null
  }
})

const valueKey = computed(() =>
  props.field.type !== 'composite'
    ? 'valueKey' in props.field
      ? props.field.valueKey
      : 'value'
    : 'value',
)

const labelKey = computed(() =>
  props.field.type !== 'composite'
    ? 'labelKey' in props.field
      ? props.field.labelKey
      : 'label'
    : 'label',
)

const itemContext = computed(() =>
  createFormItemContext(props.field, {
    formData,
    fieldOptions,
    fieldOptionsLoading,
    disabled: formDisabled,
    isLoading,
    updateFieldValue,
    validateField,
    clearValidate,
  }),
)

const renderField = computed(() => {
  return FormRenderer.renderField(props.field, {
    formData,
    disabled: formDisabled,
    isLoading,
    updateFieldValue,
    validateField,
    clearValidate,
    h,
  })
})

const getFieldProps = computed(() => {
  const baseProps = {
    ...(props.field.type !== 'composite' && 'props' in props.field ? props.field.props : {}),
    readonly: isReadonly.value,
    disabled: isDisabled.value,
  }

  switch (props.field.type) {
    case 'select':
      return {
        ...baseProps,
        loading: fieldOptionsLoading.value[props.field.name],
        valueKey: props.field.valueKey || 'value',
      }
    case 'tree-select':
      return {
        ...baseProps,
        loading: fieldOptionsLoading.value[props.field.name],
        data: fieldOptions.value[props.field.name],
        props: {
          label: 'label',
          children: 'children',
          ...props.field.treeProps,
        },
        valueKey: props.field.valueKey || 'value',
      }
    default:
      return baseProps
  }
})

const isRequired = computed(() => FieldPropComputer.isRequired(props.field, getFieldContext.value))

const shouldShow = computed(
  () =>
    props.shouldShow !== false && FieldPropComputer.isVisible(props.field, getFieldContext.value),
)
</script>
