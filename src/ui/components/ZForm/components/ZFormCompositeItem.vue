<template>
  <div
    v-show="shouldShow"
    class="z-form-composite-item"
  >
    <slot
      v-if="$slots[`composite-${field.name}`]"
      :name="`composite-${field.name}`"
      v-bind="slotContext"
    />

    <template v-else>
      <div
        :class="[
          'z-form-composite-item__layout',
          `z-form-composite-item__layout--${field.layout?.type || 'row'}`,
        ]"
        :style="layoutStyle"
      >
        <div
          v-for="(childField, index) in field.children"
          :key="childField.name || index"
          class="z-form-composite-item__field"
        >
          <z-form-item
            :field="childField"
            :should-show="computeChildShouldShow(childField)"
            :parent-prop-name="propName"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ZFormExposed, ZFormField } from '../types'
import ZFormItem from './ZFormItem.vue'
import { createFormCompositeItemContext, useForm } from '../composables/useFormProvider'
import { FieldPropComputer } from '../utils/field-props'

const props = defineProps<{
  field: ZFormField & { type: 'composite' }
  shouldShow?: boolean
}>()

const propName = computed(() => (props.shouldShow ? props.field.name : ''))

const {
  formData,
  formRef,
  disabled: formDisabled,
  isLoading,
  fieldOptions,
  fieldOptionsLoading,
  updateFieldValue,
  validateField,
  clearValidate,
} = useForm()

const layoutStyle = computed(() => {
  const { gutter = 8 } = props.field.layout || {}
  return {
    gap: `${gutter}px`,
  }
})

const slotContext = computed(() =>
  createFormCompositeItemContext(props.field, {
    formData,
    fieldOptions,
    fieldOptionsLoading,
    disabled: formDisabled,
    isLoading,
    updateFieldValue,
    validateField,
    clearValidate,
  }),
)

const computeChildShouldShow = (childField: ZFormField) => {
  if (!props.shouldShow) {
    return false
  }

  const context = FieldPropComputer.createContext({
    field: childField,
    model: formData.value,
    form: formRef.value as unknown as ZFormExposed,
    options: fieldOptions.value[childField.name],
    optionsLoading: fieldOptionsLoading.value[childField.name],
  })

  return FieldPropComputer.isVisible(childField, context)
}
</script>

<style lang="scss">
.z-form-composite-item {
  width: 100%;

  &__layout {
    display: flex;
    width: 100%;

    &--row {
      align-items: center;
    }

    &--col {
      flex-direction: column;
    }
  }

  &__field {
    flex: 1;
    min-width: 0;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }
}
</style>
