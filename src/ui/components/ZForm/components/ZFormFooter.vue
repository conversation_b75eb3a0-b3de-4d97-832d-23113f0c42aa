<template>
  <el-form-item class="z-form__footer">
    <slot>
      <el-button :disabled="disabled" @click="$emit('reset')" class="z-form__btn z-form__btn--reset">
        重置
      </el-button>
      <el-button type="primary" :disabled="disabled" @click="$emit('submit')" class="z-form__btn z-form__btn--submit">
        提交
      </el-button>
    </slot>
  </el-form-item>
</template>

<script setup lang="ts">
defineProps<{
  disabled: boolean
}>()

defineEmits<{
  (e: 'reset'): void
  (e: 'submit'): void
}>()
</script>
