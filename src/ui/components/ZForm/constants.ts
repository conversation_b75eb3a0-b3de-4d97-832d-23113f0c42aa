import type { Component } from 'vue'
import {
  ElInput,
  ElInputNumber,
  ElSelect,
  ElDatePicker,
  ElRadioGroup,
  ElCheckboxGroup,
  ElSwitch,
  ElTreeSelect,
} from 'element-plus'
import ZFormCompositeItem from './components/ZFormCompositeItem.vue'
import type { ZFormCacheOptions } from './types'

export const componentMap: Record<string, Component> = {
  input: ElInput,
  number: ElInputNumber,
  select: ElSelect,
  date: ElDatePicker,
  radio: ElRadioGroup,
  checkbox: ElCheckboxGroup,
  switch: ElSwitch,
  'tree-select': ElTreeSelect,
  composite: ZFormCompositeItem,
} as const

export const DEFAULT_CACHE_OPTIONS: Required<ZFormCacheOptions> = {
  strategy: 'memory',
  ttl: 3600,
  clearOnUnmount: true,
}
