import { asyncData<PERSON>ache, type CacheOptions } from '@/utils/cache'
import { generateFieldCacheKey } from './cache'
import type { OptionsContext, ZFormField } from '../types'

export class OptionsLoader {
  static async loadOptions(
    field: ZFormField,
    context: OptionsContext,
    globalCacheOptions: CacheOptions,
    onSuccess: (options: any[]) => void,
    onError: (error: any) => void,
  ): Promise<void> {
    try {
      const fetchFn = async () => {
        if (field.type === 'tree-select' && field.treeOptions) {
          return typeof field.treeOptions === 'function'
            ? await field.treeOptions(context)
            : field.treeOptions
        } else if ('options' in field && field.options) {
          return typeof field.options === 'function' ? await field.options(context) : field.options
        }
        return []
      }

      const cacheKey = generateFieldCacheKey(field)

      const cacheOptions: CacheOptions = {
        ...globalCacheOptions,
        ...('cacheOptions' in field ? field.cacheOptions : {}),
        ...('noCache' in field && field.noCache && { strategy: 'none' }),
      }

      const options = await asyncDataCache.getOrFetch(cacheKey, fetchFn, cacheOptions)

      onSuccess(options)
    } catch (error) {
      onError(error)
    }
  }
}
