import { generateCache<PERSON>ey } from '@/utils/utils'
import type { SelectField, TreeSelectField, ZFormField } from '../types'

 function generateHash(content: string): string {
  return content
    .split('')
    .reduce((hash, char) => (hash << 5) - hash + char.charCodeAt(0), 0)
    .toString(36)
}

 function getOptionsIdentifier(
  options: SelectField['options'] | TreeSelectField['treeOptions'],
  fieldName: string,
): string {
  if (!options) return ''

  if (typeof options !== 'function') {
    const optionsHash = generateHash(JSON.stringify(options))
    return `static_${fieldName}_${optionsHash}`
  }

  const fnString = options.toString()
  const apiCallMatch = fnString.match(/=>\s*([\w.]+)\.([\w]+)\(/)
  if (apiCallMatch) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [_, apiModule, methodName] = apiCallMatch
    return `${apiModule}_${methodName}`
  }

  return fnString
}

export function generateFieldCacheKey(field: ZFormField): string {
  if ('cacheKey' in field && field.cacheKey) {
    return field.cacheKey
  }

  if (!('options' in field) && !('treeOptions' in field)) {
    return ''
  }

  return generateHash(
    generateCacheKey({
      scope: 'zform_options',
      name: field.name,
      source: getOptionsIdentifier(
        'options' in field ? field.options : 'treeOptions' in field ? field.treeOptions : undefined,
        field.name,
      ),
      ...(field.valueKey !== 'value' && { valueKey: field.valueKey }),
      ...(field.labelKey !== 'label' && { labelKey: field.labelKey }),
      ...(field.dependencies && { deps: field.dependencies }),
    }),
  )
}
