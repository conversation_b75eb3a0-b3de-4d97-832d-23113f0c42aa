import type { ZFormField } from '../types';

export class ValueProcessor {
  static formatIn(field: ZFormField, value: any, formValues: Record<string, any>) {
    if (field.type === 'composite') {
      if (!field.children) {
        return {}
      }
      return field.children.reduce((acc, child) => {
        acc[child.name] = this.formatIn(child, value?.[child.name], formValues)
        return acc
      }, {} as Record<string, any>)
    }

    if (field.formatIn) {
      return field.formatIn(value, formValues)
    }
    return value
  }

  static formatOut(field: ZFormField, value: any, formValues: Record<string, any>) {
    if (field.type === 'composite' && field.children) {
      return field.children.reduce((acc, child) => {
        acc[child.name] = this.formatOut(child, value?.[child.name], formValues)
        return acc
      }, {} as Record<string, any>)
    }

    if (field.formatOut) {
      return field.formatOut(value, formValues)
    }
    return value
  }

  static formatSubmitValue(fields: ZFormField[] | undefined, values: Record<string, any>) {
    if (!fields) return values

    return fields.reduce((acc, field) => {
      if (field.type === 'composite' && field.children) {
        acc[field.name] = this.formatSubmitValue(field.children, values[field.name])
      } else {
        acc[field.name] = this.formatOut(field, values[field.name], values)
      }
      return acc
    }, {} as Record<string, any>)
  }

  static formatInitialValue(fields: ZFormField[] | undefined, values: Record<string, any>) {
    if (!fields) return values;

    return fields.reduce((acc, field) => {
      if (field.type === 'composite') {
        if (!field.children) {
          acc[field.name] = {}
        } else {
          acc[field.name] = this.formatInitialValue(field.children, values?.[field.name] ?? {});
        }
      } else {
        acc[field.name] = this.formatIn(field, values?.[field.name], values);
      }
      return acc;
    }, {} as Record<string, any>);
  }
}
