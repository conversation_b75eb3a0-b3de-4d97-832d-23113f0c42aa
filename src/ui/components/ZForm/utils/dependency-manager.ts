import type { DependencyTriggerContext, FieldDependency, TriggerReason, ZFormField, ZFormInstance } from "../types";

export type DependencyHandler = (params: {
  field: ZFormField;
  form: ZFormInstance;
  dependencyValues: Record<string, any>;
  updateModel: (value: any) => void;
  updateField: (fieldUpdate: Partial<ZFormField>) => void;
  reason: TriggerReason;
}) => Promise<void> | void;

export class DependencyManager {
  private handlers = new Map<string, Set<DependencyHandler>>()
  private processedFields = new Set<string>()
  private dependencyGraph = new Map<string, Set<string>>()
  private autoTriggerEnabled = new Set<string>()
  private dependencies = new Map<string, FieldDependency>()

  constructor() {
    this.clearAll()
  }

  setDependency(fieldName: string, dependency: FieldDependency) {
    this.dependencies.set(fieldName, dependency)
    this.autoTriggerEnabled.add(fieldName)
    this.rebuildGraph()
  }

  removeDependency(fieldName: string) {
    this.dependencies.delete(fieldName)
    this.autoTriggerEnabled.delete(fieldName)
    this.clear(fieldName)
    this.rebuildGraph()
  }

  disableAuto(fieldName: string) {
    this.autoTriggerEnabled.delete(fieldName)
  }

  enableAuto(fieldName: string) {
    if (this.dependencies.has(fieldName)) {
      this.autoTriggerEnabled.add(fieldName)
    }
  }

  isEnabled(fieldName: string): boolean {
    return this.autoTriggerEnabled.has(fieldName)
  }

  private clearGraph() {
    this.dependencyGraph.clear();
    this.processedFields.clear();
  }

  private addToDependencyGraph(fieldName: string, dependency: FieldDependency) {
    if (!dependency.fields) return;

    const dependencyFields = Array.isArray(dependency.fields)
      ? dependency.fields
      : [dependency.fields];

    dependencyFields.forEach(depField => {
      if (!this.dependencyGraph.has(depField)) {
        this.dependencyGraph.set(depField, new Set());
      }
      this.dependencyGraph.get(depField)!.add(fieldName);
    });
  }

  private rebuildGraph() {
    this.clearGraph();
    for (const [fieldName, dependency] of this.dependencies) {
      this.addToDependencyGraph(fieldName, dependency);
    }
    this.detectCircularDependencies();
  }

  /**
   * 获取依赖于指定字段的所有字段
   * @param sourceField 源字段名
   * @returns 所有依赖于该字段的字段名集合
   */
  getDependentFields(sourceField: string): Set<string> {
    return this.dependencyGraph.get(sourceField) || new Set();
  }

  /**
   * 获取指定字段依赖的所有字段
   * @param fieldName 字段名
   * @returns 该字段依赖的所有字段名集合
   */
  getDependsOnFields(fieldName: string): Set<string> {
    const result = new Set<string>();
    const dependency = this.dependencies.get(fieldName);
    if (dependency) {
      const fields = Array.isArray(dependency.fields)
        ? dependency.fields
        : [dependency.fields];
      fields.forEach(field => result.add(field));
    }
    return result;
  }

  getDependencyValues(fieldName: string, formValues: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {};
    for (const [sourceField, targetFields] of this.dependencyGraph.entries()) {
      if (targetFields.has(fieldName)) {
        result[sourceField] = formValues[sourceField];
      }
    }

    return result;
  }

  register(fieldName: string, handler: DependencyHandler) {
    if (!this.handlers.has(fieldName)) {
      this.handlers.set(fieldName, new Set());
    }
    this.handlers.get(fieldName)!.add(handler);
  }

  buildDependencyGraph(fields: ZFormField[]) {
    this.clearAll();
    fields.forEach(field => {
      if (!field.dependencies) return;

      const dependencies = Array.isArray(field.dependencies)
        ? field.dependencies
        : [field.dependencies];

      dependencies.forEach(dep => {
        this.dependencies.set(field.name, dep);
        this.autoTriggerEnabled.add(field.name);

        const depFields = Array.isArray(dep.fields) ? dep.fields : [dep.fields];
        depFields.forEach(depField => {
          if (!this.dependencyGraph.has(depField)) {
            this.dependencyGraph.set(depField, new Set());
          }
          this.dependencyGraph.get(depField)!.add(field.name);
        });
      });
    });

    this.detectCircularDependencies();
  }

  /**
   * 清除所有依赖状态
   */
  clearAll() {
    this.handlers.clear();
    this.dependencyGraph.clear();
    this.dependencies.clear();
    this.autoTriggerEnabled.clear();
    this.processedFields.clear();
  }

  private detectCircularDependencies() {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const detectCycle = (field: string, path: string[] = []): boolean => {
      if (recursionStack.has(field)) {
        console.warn('Circular dependency detected:', [...path, field].join(' -> '));
        return true;
      }

      if (visited.has(field)) return false;

      visited.add(field);
      recursionStack.add(field);

      const dependencies = this.dependencyGraph.get(field) || new Set();
      for (const dep of dependencies) {
        if (detectCycle(dep, [...path, field])) {
          return true;
        }
      }

      recursionStack.delete(field);
      return false;
    };

    for (const field of this.dependencyGraph.keys()) {
      if (!visited.has(field)) {
        detectCycle(field);
      }
    }
  }

  async trigger(fieldName: string, context: DependencyTriggerContext) {
    if (!this.isEnabled(fieldName)) return

    const handlers = this.handlers.get(fieldName)
    if (!handlers) return

    for (const handler of handlers) {
      await handler(context)
    }
  }

  /**
   * 清除指定字段的所有状态
   * @param fieldName 字段名
   */
  clear(fieldName: string) {
    this.handlers.delete(fieldName);
    this.dependencyGraph.delete(fieldName);
  }
}

export const dependencyManager = new DependencyManager();
