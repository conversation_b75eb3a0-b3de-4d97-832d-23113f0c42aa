import type { FormInstance } from 'element-plus';

export class ValidationHelper {
  static validateFields(form: FormInstance | undefined, fields?: string[]): Promise<boolean> {
    return new Promise((resolve) => {
      if (!form) {
        resolve(false);
        return;
      }

      if (fields?.length) {
        form.validateField(fields, (valid: boolean) => {
          resolve(valid);
        });
      } else {
        form.validate((valid: boolean) => {
          resolve(valid);
        });
      }
    });
  }

  static getFieldValidationStatus(form: FormInstance | undefined, fieldName: string): boolean {
    if (!form) return false;

    const field = form.validateField(fieldName);
    return field !== undefined;
  }
}
