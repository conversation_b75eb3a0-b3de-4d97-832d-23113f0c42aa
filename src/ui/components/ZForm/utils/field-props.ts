import type { FieldPropContext, FieldPropFunction, ZFormField, ZFormInstance } from '../types'

export class FieldPropComputer {
  static createContext(params: {
    field: ZFormField
    model: Record<string, any>
    form: ZFormInstance
    options?: any[]
    optionsLoading?: boolean
  }): FieldPropContext {
    const { field, model, form, options, optionsLoading } = params
    return {
      model,
      field,
      form,
      value: model[field.name],
      options,
      optionsLoading
    }
  }

  static computeProp(
    prop: boolean | FieldPropFunction | undefined,
    context: FieldPropContext,
    defaultValue?: boolean
  ) {
    if (typeof prop === 'function') {
      return prop(context)
    }
    return prop ?? defaultValue
  }

  static isVisible(field: ZForm<PERSON>ield, context: FieldPropContext) {
    return this.computeProp(field.show, context, true)
  }

  static isDisabled(field: <PERSON><PERSON>orm<PERSON>ield, context: FieldPropContext) {
    return this.computeProp(field.disabled, context)
  }

  static isReadonly(field: <PERSON><PERSON><PERSON><PERSON><PERSON>, context: FieldPropContext) {
    return this.computeProp(field.readonly, context)
  }

  static isRequired(field: <PERSON>Form<PERSON>ield, context: FieldPropContext) {
    return this.computeProp(field.required, context)
  }
}
