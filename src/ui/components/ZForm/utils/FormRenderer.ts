import type { VNode } from 'vue';
import { defineAsyncComponent } from 'vue';
import type { ZFormField, ZFormRenderContext } from '../types';
import type { FormContextForItem } from '../composables/useFormProvider';
import type { hType } from '@/types/vue';

export class FormRenderer {
  static renderField(
    field: ZFormField,
    context: Omit<FormContextForItem, 'fieldOptions' | 'fieldOptionsLoading'> & { h: hType }
  ): VNode | (() => VNode) {
    if (field.type !== 'composite' && !field.render) {
      throw new Error(`No render function provided for field ${field.name}`);
    }

    const renderContext: ZFormRenderContext = {
      h: context.h,
      field,
      value: context.formData.value[field.name],
      formData: context.formData.value,
      disabled: context.disabled.value,
      loading: context.isLoading.value,
      updateValue: (value: any) => context.updateFieldValue(field.name, value),
      validate: () => context.validateField(field.name),
      clearValidate: () => context.clearValidate(field.name)
    };

    try {
      const result = field.type !== 'composite' ? field.render!(renderContext) : null;

      if (result instanceof Promise) {
        return defineAsyncComponent({
          loader: async () => {
            try {
              const vnode = await result;
              return () => vnode;
            } catch (error) {
              console.error(`Render failed for ${field.name}:`, error);
              return () => context.h('div', { class: 'render-error' }, '渲染失败');
            }
          },
          loadingComponent: {
            render: () => context.h('div', { class: 'field-loading' }, '加载中...')
          },
          errorComponent: {
            render: (props: { error: Error }) =>
              context.h('div', { class: 'render-error' }, `渲染错误: ${props.error}`)
          },
          timeout: 3000
        });
      }

      return result || context.h('div', { class: 'render-error' }, '无效的渲染结果');
    } catch (error) {
      console.error(`Error rendering field ${field.name}:`, error);
      return context.h('div', { class: 'render-error' }, `渲染错误: ${error}`);
    }
  }
}
