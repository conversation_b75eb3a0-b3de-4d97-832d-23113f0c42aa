import type { Ref } from 'vue'
import type { ZFormInstance } from '../ZForm/types'

export interface ZDialogFormProps {
  visible: boolean
  title?: string
  width?: string
  showFooter?: boolean
  closeOnClickModal?: boolean
  destroyOnClose?: boolean
  preventCloseOnSubmit?: boolean
  resetOnClose?: boolean
  fullscreen?: boolean
}

export interface ZDialogFormConfirmContext {
  form: ZFormInstance | undefined
  isSubmitting: Ref<boolean>
  validate: () => Promise<boolean>
  close: () => void
}

export interface ZDialogFormEmits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', context: ZDialogFormConfirmContext): void
  (e: 'cancel'): void
  (e: 'close'): void
}

export interface ZDialogFormContext {
  formRef: Ref<ZFormInstance | undefined>
  isSubmitting: Ref<boolean>
  isOpen: Ref<boolean>
  onOpen: (hook: ZDialogFormLifecycleHook) => () => void
  onReady: (hook: ZDialogFormLifecycleHook) => () => void
  onClose: (hook: ZDialogFormLifecycleHook) => () => void
  onClosed: (hook: ZDialogFormLifecycleHook) => () => void
}

export type ZDialogFormLifecycleHookType = 'open' | 'ready' | 'close' | 'closed'
export type ZDialogFormLifecycleHook = () => void | Promise<void>

export interface ZDialogFormFooterScope {
  isSubmitting: boolean
  setSubmitting: (value: boolean) => void
  form: ZFormInstance | undefined
  validate: () => Promise<boolean>
  close: () => void
  cancel: () => void
  confirm: () => void
}

export interface ZDialogFormSlots {
  default: () => any
  footer?: (scope: { scope: ZDialogFormFooterScope }) => any
}
