import { provide, inject, ref, type InjectionKey, type Ref, watch } from 'vue'
import type { ZFormInstance } from '../ZForm/types'

type LifecycleHookType = 'open' | 'ready' | 'close' | 'closed'
type LifecycleHook = (form: ZFormInstance | undefined) => void | Promise<void>

export interface DialogFormContext {
  formRef: Ref<ZFormInstance | undefined>
  isSubmitting: Ref<boolean>
  isOpen: Ref<boolean>
  onOpen: (hook: LifecycleHook) => () => void
  onReady: (hook: LifecycleHook) => () => void
  onClose: (hook: LifecycleHook) => () => void
  onClosed: (hook: LifecycleHook) => () => void
}

const DIALOG_FORM_KEY = Symbol('dialog-form') as InjectionKey<DialogFormContext>

export function provideDialogForm(isOpen: Ref<boolean>) {
  const formRef = ref<ZFormInstance>()
  const isSubmitting = ref(false)

  const lifecycleHooks = {
    open: new Set<LifecycleHook>(),
    ready: new Set<LifecycleHook>(),
    close: new Set<LifecycleHook>(),
    closed: new Set<LifecycleHook>()
  }

  const clearHooks = () => {
    Object.values(lifecycleHooks).forEach(hooks => hooks.clear())
  }

  watch(isOpen, (newVal) => {
    if (!newVal) {
      clearHooks()
    }
  })

  const registerHook = (type: LifecycleHookType, hook: LifecycleHook) => {
    lifecycleHooks[type].add(hook)
    return () => lifecycleHooks[type].delete(hook)
  }

  const triggerHook = async (type: LifecycleHookType) => {
    for (const hook of lifecycleHooks[type]) {
      try {
        await hook(formRef.value)
      } catch (error) {
        console.error(`Error executing ${type} hook:`, error)
      }
    }
  }

  const context: DialogFormContext = {
    formRef,
    isSubmitting,
    isOpen,
    onOpen: (hook) => registerHook('open', hook),
    onReady: (hook) => registerHook('ready', hook),
    onClose: (hook) => registerHook('close', hook),
    onClosed: (hook) => registerHook('closed', hook)
  }

  provide(DIALOG_FORM_KEY, context)

  return {
    formRef,
    isSubmitting,
    lifecycleHooks,
    triggerHook,
    clearHooks
  }
}

export function useDialogFormContext() {
  const context = inject(DIALOG_FORM_KEY)
  if (!context) {
    throw new Error('useDialogFormContext must be used within ZDialogForm')
  }
  return context
}
