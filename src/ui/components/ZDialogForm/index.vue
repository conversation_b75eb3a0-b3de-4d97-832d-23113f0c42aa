<template>
  <el-dialog
    :destroy-on-close="destroyOnClose"
    :close-on-click-modal="closeOnClickModal"
    :title="title"
    v-model="internalVisible"
    :width="width"
    :before-close="handleBeforeClose"
    v-bind="$attrs"
    class="z-dialog-form"
    :class="{
      'z-dialog-form--submitting': isSubmitting && preventCloseOnSubmit,
      'z-dialog-form--mobile': isMobile,
      'z-dialog-form--fullscreen': props.fullscreen,
    }"
    @open="handleDialogOpen"
    @closed="handleDialogClosed"
    @close="handleClose"
  >
    <div class="z-dialog-form__content">
      <slot></slot>
    </div>

    <template
      #footer
      v-if="showFooter"
    >
      <slot
        name="footer"
        :scope="{
          isSubmitting,
          setSubmitting,
          form: formRef,
          validate: validateForm,
          close: handleClose,
          cancel: handleCancel,
          confirm: handleConfirm,
        }"
      >
        <div class="z-dialog-form__footer">
          <el-button
            :disabled="isSubmitting && preventCloseOnSubmit"
            @click="handleCancel"
            class="z-dialog-form__button z-dialog-form__button--cancel"
            :class="{ 'z-dialog-form__button--disabled': isSubmitting && preventCloseOnSubmit }"
          >
            取消
          </el-button>
          <el-button
            :loading="isSubmitting"
            type="primary"
            @click="handleConfirm"
            class="z-dialog-form__button z-dialog-form__button--confirm"
            :class="{ 'z-dialog-form__button--loading': isSubmitting }"
          >
            确定
          </el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, nextTick } from 'vue'
import { provideDialogForm } from './context'
import type {
  ZDialogFormProps,
  ZDialogFormConfirmContext,
  ZDialogFormEmits,
  ZDialogFormSlots,
} from './types'

const props = withDefaults(defineProps<ZDialogFormProps>(), {
  title: '',
  showFooter: true,
  width: '50%',
  closeOnClickModal: false,
  preventCloseOnSubmit: true,
  destroyOnClose: true,
  resetOnClose: true,
  fullscreen: false,
})

const emit = defineEmits<ZDialogFormEmits>()

const internalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const { formRef, isSubmitting, triggerHook, clearHooks } = provideDialogForm(internalVisible)

const setSubmitting = (v: boolean) => {
  isSubmitting.value = v
}

const validateForm = (): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!formRef.value) {
      resolve(false)
      return
    }

    formRef.value.validate((valid: boolean) => {
      resolve(valid)
    })
  })
}

const resetForm = () => {
  if (props.resetOnClose) {
    formRef.value?.resetFields()
  }
}

const handleDialogOpen = async () => {
  await triggerHook('open')
  await nextTick()
  await triggerHook('ready')
}

const handleDialogClosed = () => {
  triggerHook('closed')

  // 确保在对话框完全关闭后也清理状态
  if (formRef.value?.dependencies) {
    formRef.value.dependencies.clearAll()
  }
}

const handleClose = async (done?: () => void) => {
  if (props.preventCloseOnSubmit && isSubmitting.value) {
    return
  }

  isSubmitting.value = false

  await triggerHook('close')

  if (formRef.value?.dependencies) {
    formRef.value.dependencies.clearAll()
  }

  resetForm()

  emit('close')

  if (done) {
    done()
  } else {
    internalVisible.value = false
  }
}

const handleCancel = async () => {
  if (props.preventCloseOnSubmit && isSubmitting.value) {
    return
  }

  emit('cancel')
  await handleClose()
}

const handleConfirm = () => {
  emit('confirm', {
    form: formRef.value,
    isSubmitting,
    validate: validateForm,
    close: () => {
      internalVisible.value = false
    },
  } satisfies ZDialogFormConfirmContext)
}

const handleBeforeClose = async (done: () => void) => {
  await handleClose(done)
}

const isMobile = ref(window.innerWidth <= 768)

const handleResize = () => {
  isMobile.value = window.innerWidth <= 768
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  // 清除所有状态
  clearHooks()
  if (formRef.value?.dependencies) {
    formRef.value.dependencies.clearAll()
  }
})

defineExpose({
  formRef,
  resetForm,
  validateForm,
})

defineSlots<ZDialogFormSlots>()
</script>

<style lang="scss">
.z-dialog-form {
  &__content {
    .z-dialog-form--submitting & {
      opacity: 0.7;
      pointer-events: none;
    }
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  &__button {
    min-width: 80px;

    &--cancel {
      &:hover {
        background-color: var(--el-color-info-light-7);
      }
    }

    &--confirm {
      &:hover {
        background-color: var(--el-color-primary-light-3);
      }
    }

    &--loading {
      cursor: not-allowed;
      opacity: 0.7;
    }

    &--disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  &--fullscreen {
    .z-dialog-form__content {
      height: calc(100vh - 200px);
      overflow-y: auto;
    }
  }

  @media screen and (max-width: 768px) {
    &--mobile {
      width: 90% !important;

      .z-dialog-form__footer {
        flex-direction: column;
        gap: 8px;
      }

      .z-dialog-form__button {
        width: 100%;
      }
    }
  }

  :deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;

    .el-dialog__header {
      margin: 0;
      padding: 20px;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }

    .el-dialog__body {
      padding: 0;
    }

    .el-dialog__footer {
      margin: 0;
      padding: 0;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
}
</style>
