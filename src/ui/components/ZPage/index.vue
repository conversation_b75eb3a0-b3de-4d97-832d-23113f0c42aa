<script setup lang="ts">
import { cn } from '@/utils/utils';
import { type StyleValue, onUnmounted } from 'vue'
import { useLayout } from '@/composables/useLayout'

interface Props {
  style?: StyleValue | string
  class?: string | string[] | object
  stretch?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  stretch: false
})

const { updateContentStyle, resetContent } = useLayout()

if (props.stretch) {
  updateContentStyle({
    height: 'calc(100vh - var(--el-menu-horizontal-height) - 32px)',
    display: 'flex',
    flexDirection: 'column'
  })
}

onUnmounted(() => {
  if (props.stretch) {
    resetContent()
  }
})
</script>
<template>
  <div :style="[
    props.style,
    props.stretch ? { flex: 1, height: '100%' } : {}
  ]" :class="cn('p-5  bg-background', props.class)">
    <slot />
  </div>
</template>
