export const ICON_CATEGORIES = {
  common: ['Plus', 'Edit', 'Delete', 'Search', 'Refresh', 'Close', 'Check', 'Info', 'Warning', 'Question'],
  direction: ['Back', 'Right', 'Bottom', 'Top', 'Left', 'ArrowDown', 'ArrowUp', 'ArrowLeft', '<PERSON>Right'],
  action: ['Upload', 'Download', 'Share', 'View', 'Hide', 'Link', 'Copy', 'Setting', 'More'],
  data: ['Document', 'Folder', 'Calendar', 'List', 'Files', 'Grid', 'Menu', 'Star', 'Bell'],
  media: ['Camera', 'Picture', 'Video', 'Microphone', 'Music', 'Headset'],
  device: ['Mobile', 'Laptop', 'Printer', 'Monitor', 'Mouse', 'Key'],
} as const

export const getCategoryName = (category: keyof typeof ICON_CATEGORIES): string => {
  const names: Record<keyof typeof ICON_CATEGORIES, string> = {
    common: '常用图标',
    direction: '方向图标',
    action: '操作图标',
    data: '数据图标',
    media: '媒体图标',
    device: '设备图标'
  }
  return names[category]
}