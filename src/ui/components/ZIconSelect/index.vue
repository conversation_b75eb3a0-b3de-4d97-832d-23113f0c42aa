<template>
  <el-popover
    v-model:visible="visible"
    trigger="click"
    :width="520"
    @show="visible = true"
    @hide="visible = false"
  >
    <template #reference>
      <div
        class="z-icon-select-trigger"
        :class="{ 'is-selected': !!modelValue }"
      >
        <el-icon v-if="modelValue">
          <component :is="ElementPlusIconsVue[modelValue as keyof typeof ElementPlusIconsVue]" />
        </el-icon>
        <span v-else>请选择图标</span>
        <el-icon class="arrow-icon">
          <arrow-down />
        </el-icon>
      </div>
    </template>

    <div class="z-icon-select">
      <el-input
        v-model="searchText"
        placeholder="搜索图标"
        clearable
        prefix-icon="Search"
        class="z-icon-select__search"
      />

      <div class="z-icon-select__container">
        <div
          v-for="(icons, category) in filteredIcons"
          :key="category"
          class="z-icon-select__group"
        >
          <div class="z-icon-select__group-title">{{ getCategoryName(category as IconCategory) }}</div>
          <div class="z-icon-select__grid">
            <div
              v-for="icon in icons"
              :key="icon.name"
              class="z-icon-select__item"
              :class="{ 'is-selected': modelValue === icon.name }"
              @click="handleSelect(icon.name)"
            >
              <el-icon>
                <component :is="icon.component" />
              </el-icon>
              <span class="z-icon-select__item-name">{{ icon.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { ICON_CATEGORIES, getCategoryName } from './constants'
import type { IconOption } from './types'

type IconCategory = keyof typeof ICON_CATEGORIES

defineOptions({ name: 'ZIconSelect' })

defineProps<{
  modelValue?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}>()

const visible = ref(false)
const searchText = ref('')

const filteredIcons = computed(() => {
  const groups: Record<IconCategory, IconOption[]> = {} as Record<IconCategory, IconOption[]>
  const search = searchText.value.toLowerCase()

  Object.entries(ICON_CATEGORIES).forEach(([category, iconNames]) => {
    const filtered = iconNames
      .filter((name) => !search || name.toLowerCase().includes(search))
      .map((name) => ({
        name,
        component: ElementPlusIconsVue[name as keyof typeof ElementPlusIconsVue],
      }))
      .filter((icon) => icon.component)

    if (filtered.length > 0) {
      groups[category as IconCategory] = filtered
    }
  })

  return groups
})

const handleSelect = (iconName: string) => {
  emit('update:modelValue', iconName)
  emit('change', iconName)
  visible.value = false
}
</script>

<style lang="scss" scoped>
.z-icon-select-trigger {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 5px 15px;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  cursor: pointer;
  min-width: 120px;
  height: 32px;
  transition: all 0.2s;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-regular);

  &:hover {
    border-color: var(--el-color-primary);
  }

  &.is-selected {
    color: var(--el-text-color-primary);
  }

  .arrow-icon {
    margin-left: auto;
    transition: transform 0.2s;
  }

  &:hover .arrow-icon {
    transform: rotate(180deg);
  }
}

.z-icon-select {
  &__search {
    margin-bottom: 12px;
  }

  &__container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
    background-color: var(--el-bg-color);
  }

  &__group {
    &:not(:last-child) {
      border-bottom: 1px solid var(--el-border-color-lighter);
    }
  }

  &__group-title {
    padding: 8px 12px;
    font-size: 13px;
    color: var(--el-text-color-secondary);
    background-color: var(--el-fill-color-light);
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 4px;
    padding: 8px;
  }

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px 4px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }

    &.is-selected {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }

    .el-icon {
      font-size: 20px;
    }
  }

  &__item-name {
    font-size: 12px;
    text-align: center;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 4px;
  }
}
</style>
