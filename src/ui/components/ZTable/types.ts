import type { Component, VNode } from 'vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ZTable from './index.vue'
import type { CacheOptions } from '@/utils/cache'

export type ZTableInstance = InstanceType<typeof ZTable>

export interface AsyncCellFormatter<T = any> {
  fetch: (row: any, column: ZTableColumn, cellValue: any) => Promise<T>
  render?: (value: T) => (row: any) => Component | VNode | string
  loading?: string | Component
  error?: string | Component
  cacheKey?: string | ((row: any, column: ZTableColumn, cellValue: any) => string)
  cacheOptions?: Partial<CacheOptions>
}

export interface ZTablePaginationLayout {
  layout?: string[] | string
  align?: 'left' | 'center' | 'right'
  simple?: boolean
  background?: boolean
  small?: boolean
  hideOnSinglePage?: boolean
  quickButtons?: boolean
  showTotal?: (total: number, range: [number, number]) => string
}

export interface ZTableColumn {
  prop?: string
  label?: string
  width?: number | string
  minWidth?: number | string
  showOverflowTooltip?: boolean
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  type?: string
  align?: 'left' | 'center' | 'right'
  formatter?: (row: any, column: any, cellValue: any, index: number) => string | VNode
  asyncFormatter?: AsyncCellFormatter
  slotName?: string
  extraProps?: Record<string, any>
}

export interface ZTableColumnSlotContext {
  row: any
  column: any
  $index: number
  [key: string]: any
}

export type ElementPlusIconName = keyof typeof ElementPlusIconsVue
export type ElementPlusIconComponent = (typeof ElementPlusIconsVue)[ElementPlusIconName]
export type IconName = ElementPlusIconName | (string & {})
export type IconComponent = ElementPlusIconComponent | Component

export interface ZTableToolbarContext {
  tool: ZTableToolbarTool
  selectedRows: any[]
  query: Record<string, any>
  refresh: (options?: boolean | RefreshOptions) => void
  reset: () => void
  search: () => void
  clearSelection: () => void
  setPage: (page: number, pageSize?: number) => void
  getPagination: () => { currentPage: number; pageSize: number; total: number }
}

export interface TreeSelectProps {
  multiple?: boolean
  checkStrictly?: boolean
  valueKey?: string
  labelKey?: string
  childrenKey?: string
  showCheckbox?: boolean
  checkOnClickNode?: boolean
  [key: string]: any
}

export interface ZTableToolOption {
  label?: string
  value?: string | number | boolean
  disabled?: boolean
  [key: string]: any
}

export interface ZTableTreeOption {
  label?: string
  value?: string | number
  disabled?: boolean
  children?: any[]
  [key: string]: any
}

// Discriminated Union
interface BaseZTableToolbarTool {
  key: string
  field?: string
  label?: string
  labelColon?: boolean | string
  disabled?: (context: ZTableToolbarContext) => boolean
  class?: string | string[] | Record<string, boolean>
  style?: string | Record<string, string | number>
  show?: boolean | ((context: ZTableToolbarContext) => boolean)
  width?: string | number
  minWidth?: string | number
  maxWidth?: string | number
  flexible?: boolean
  fullWidth?: boolean
  render?: (ctx: ZTableToolbarContext) => VNode
}

export interface ButtonTool extends BaseZTableToolbarTool {
  type: 'button'
  plain?: boolean
  icon?: IconComponent | IconName
  iconPosition?: 'left' | 'right'
  onlyIcon?: boolean
  buttonType?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  onClick?: (ctx: ZTableToolbarContext) => void
}

export interface InputTool extends BaseZTableToolbarTool {
  type: 'input'
  placeholder?: string
  onChange?: (value: any, ctx: ZTableToolbarContext) => void
}

export interface SelectTool extends BaseZTableToolbarTool {
  type: 'select'
  placeholder?: string
  valueKey?: string
  labelKey?: string
  options?: ZTableToolOption[] | (() => Promise<ZTableToolOption[]>)
  cacheOptions?: CacheOptions
  onChange?: (value: any, ctx: ZTableToolbarContext) => void
}

export interface TreeSelectTool extends BaseZTableToolbarTool {
  type: 'tree-select'
  placeholder?: string
  valueKey?: string
  options?: ZTableTreeOption[] | (() => Promise<ZTableTreeOption[]>)
  cacheOptions?: CacheOptions
  onChange?: (value: any, ctx: ZTableToolbarContext) => void
  selectProps?: {
    value?: string
    label?: string
    children?: string
    disabled?: string
    checkStrictly?: boolean
    showCheckbox?: boolean
    checkOnClickNode?: boolean
    multiple?: boolean
  }
}

export interface GroupTool extends BaseZTableToolbarTool {
  type: 'group'
  group?: ZTableToolbarGroup
}

export type ZTableToolbarTool = ButtonTool | InputTool | SelectTool | TreeSelectTool | GroupTool

export type ZTableGroupStyleMode = 'default' | 'custom' | 'inherit'
export type ZTableContentAlign = 'left' | 'right' | 'center' | 'space-between' | 'space-around'

export interface ZTableToolbarGroup {
  title?: string
  tools: ZTableToolbarTool[]
  collapsible?: boolean
  collapsed?: boolean
  styleMode?: ZTableGroupStyleMode
  contentAlign?: ZTableContentAlign
  class?: string
  style?: Record<string, string | number>
  noMargin?: boolean
  fullWidth?: boolean
}

export interface ZTableToolbarRow {
  tools: ZTableToolbarTool[]
  contentAlign?: ZTableContentAlign
  spacing?: number | string
  class?: string | string[] | Record<string, boolean>
  style?: Record<string, string | number>
}

export interface ZTableTreeConfig {
  children?: string
  hasChildren?: string
  indent?: number
  load?: (row: any, treeNode: any, resolve: (data: any[]) => void) => void
}

export interface BaseTableParams {
  pageNum?: number
  pageSize?: number
}

export type ZTableParams<T extends Record<string, any> = Record<string, any>> = BaseTableParams & T

export interface RefreshOptions {
  resetPage?: boolean
  extraParams?: Record<string, any>
}

export interface GetTableDataAsyncOptions {
  waitForLoading?: boolean
  timeout?: number
}

export type TableDataResult = any[]

export interface ZTableMethods {
  refresh: (options?: boolean | RefreshOptions) => void
  reset: () => void
  getSelection: () => any[]
  clearSelection: () => void
  setPage: (page: number, pageSize?: number) => void
  getPagination: () => { currentPage: number; pageSize: number; total: number }
  toggleRowSelection: (row: any, selected?: boolean) => void
  toggleRowExpansion: (row: any, expanded?: boolean) => void
  getExpandRowKeys: () => (string | number)[]

  /**
   * 同步获取表格数据
   * @returns 返回表格数据
   */
  getTableData: () => TableDataResult

  /**
   * 异步获取表格数据
   * @param options 配置选项
   * @returns 返回Promise，解析为表格数据
   */
  getTableDataAsync: (options?: GetTableDataAsyncOptions) => Promise<TableDataResult>
}

export type ZTableSlots = {
  [K in `toolbar-item-${string}`]: (ctx: ZTableToolbarContext) => any
} & {
  [key: string]: (ctx: ZTableColumnSlotContext) => any
}
