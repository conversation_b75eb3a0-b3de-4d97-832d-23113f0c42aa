<script setup lang="ts">
import {
  h,
  ref,
  computed,
  watch,
  useSlots,
  type Component,
  onMounted,
  onBeforeUnmount,
  type VNode,
} from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElInput,
  ElSelect,
  ElOption,
  ElIcon,
  ElPagination,
  ElTreeSelect,
} from 'element-plus'
import { ArrowDown, ArrowRight } from '@element-plus/icons-vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { defaultPaginationLayout } from './constants'
import type {
  ElementPlusIconName,
  IconComponent,
  IconName,
  ZTableColumn,
  ZTableContentAlign,
  ZTableGroupStyleMode,
  ZTableMethods,
  ZTablePaginationLayout,
  ZTableParams,
  ZTableSlots,
  ZTableToolbarContext,
  ZTableToolbarRow,
  ZTableToolbarTool,
  ZTableTreeConfig,
  ZTableToolOption,
  ButtonTool,
  GroupTool,
  SelectTool,
  TreeSelectTool,
  GetTableDataAsyncOptions,
  TableDataResult,
} from './types'
import { asyncDataCache } from '@/utils/cache'
import { generateToolCacheKey } from './utils'
import { createLogger } from '@/utils/logger'
import AsyncCell from './components/AsyncCell'

const logger = createLogger('ZTable')

defineSlots<ZTableSlots>()

const props = withDefaults(
  defineProps<{
    fetchData?: (params: ZTableParams<any>) => Promise<any>
    data?: any[]
    columns: ZTableColumn[]
    stripe?: boolean
    border?: boolean
    selectable?: boolean
    reserveSelection?: boolean
    pageSize?: number
    pageSizes?: number[]
    usePagination?: boolean
    listKey?: string
    totalKey?: string
    showToolbar?: boolean
    toolbarRows?: ZTableToolbarRow[]
    query?: Record<string, any>
    filter?: (data: any[], query: Record<string, any>) => any[]
    paginationMode?: 'local' | 'server'
    paginationLayout?: ZTablePaginationLayout
    groupStyleMode?: ZTableGroupStyleMode
    toolbarLabelColon?: boolean | string
    rowKey?: string | ((row: any) => string) | undefined
    treeConfig?: ZTableTreeConfig
    defaultExpandAll?: boolean
    expandRowKeys?: string[]
    autoLoad?: boolean
  }>(),
  {
    stripe: true,
    border: true,
    selectable: false,
    pageSize: 10,
    pageSizes: () => [10, 20, 30, 50],
    data: () => [],
    usePagination: true,
    listKey: 'data',
    totalKey: 'total',
    showToolbar: false,
    toolbarRows: () => [],
    query: () => ({}),
    filter: undefined,
    paginationMode: undefined,
    paginationLayout: () => ({ ...defaultPaginationLayout }),
    groupStyleMode: 'custom',
    toolbarLabelColon: true,
    treeConfig: () => ({}),
    defaultExpandAll: false,
    autoLoad: true,
  },
)

const mergedPaginationLayout = computed(() => {
  return {
    ...defaultPaginationLayout,
    ...props.paginationLayout,
  }
})

const actualPaginationMode = computed(() => {
  if (props.paginationMode) return props.paginationMode

  if (!props.fetchData) return 'local'

  return 'server'
})

const emit = defineEmits<{
  (e: 'selection-change', value: any[]): void
  (e: 'selection', selection: any[], row: any): void
  (e: 'select-all', selection: any[]): void
  (e: 'pagination-change', value: { page: number; size: number }): void
  (e: 'toolbar-action', key: string, context: ZTableToolbarContext): void
  (e: 'search', query: Record<string, any>): void
  (e: 'update:query', query: Record<string, any>): void
}>()

const tableData = ref<any[]>([])
const loading = ref(false)
const currentPage = ref(1)
const innerPageSize = ref(props.pageSize)
const total = ref(0)
const tableRef = ref<InstanceType<typeof ElTable> | null>(null)
const selectedRows = ref<any[]>([])
const localQuery = ref({ ...props.query })

watch(
  () => props.query,
  (newQuery) => {
    localQuery.value = { ...newQuery }
  },
  { deep: true, immediate: true },
)

const isAsync = computed(() => !!props.fetchData)
const showToolbar = computed(() => {
  return (props.showToolbar && props.toolbarRows.length > 0) || !!slots.toolbar
})

const paginateData = (data: any[], page: number, size: number): any[] => {
  if (!props.usePagination || data.length <= size) return data
  const start = (page - 1) * size
  const end = start + size
  return data.slice(start, end)
}

const fullData = computed(() => {
  const source = isAsync.value ? tableData.value : props.data
  return props.filter ? props.filter(source, localQuery.value) : source
})

const showPagination = computed(() => {
  if (!props.usePagination) return false

  if (actualPaginationMode.value === 'server') {
    if (mergedPaginationLayout.value.hideOnSinglePage && total.value <= innerPageSize.value) {
      return false
    }
    return total.value > 0
  }

  const totalItems = fullData.value.length
  if (mergedPaginationLayout.value.hideOnSinglePage && totalItems <= innerPageSize.value) {
    return false
  }
  return totalItems > 0
})

const displayData = computed(() => {
  const data = fullData.value

  if (actualPaginationMode.value === 'local' && props.usePagination) {
    return paginateData(data, currentPage.value, innerPageSize.value)
  }

  return data
})

const loadData = async () => {
  if (!props.fetchData) {
    tableData.value = props.data
    total.value = props.data.length
    return
  }

  try {
    loading.value = true
    const params: ZTableParams = {
      ...localQuery.value,
    }

    if (actualPaginationMode.value === 'server' && props.usePagination) {
      params.pageNum = currentPage.value
      params.pageSize = innerPageSize.value
    }

    const response = await props.fetchData(params)

    if (actualPaginationMode.value === 'server') {
      if (response && props.listKey in response && props.totalKey in response) {
        tableData.value = response[props.listKey] || []
        total.value = response[props.totalKey] || 0
      } else {
        tableData.value = Array.isArray(response) ? response : []
        total.value = tableData.value.length
      }
    } else {
      const data = Array.isArray(response)
        ? response
        : response && props.listKey in response
          ? response[props.listKey]
          : []
      tableData.value = data
      total.value = data.length
    }
  } catch (error) {
    logger.error('Failed to fetch data:', error)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const updateQuery = (field: string, value: any) => {
  localQuery.value[field] = value
  emit('update:query', { ...localQuery.value })
}

const refreshTable = (
  options?: boolean | { resetPage?: boolean; extraParams?: Record<string, any> },
) => {
  localQuery.value = { ...props.query }

  const resetPage = typeof options === 'boolean' ? options : options?.resetPage
  if (resetPage) {
    currentPage.value = 1
  }

  if (typeof options === 'object' && options.extraParams) {
    Object.assign(localQuery.value, options.extraParams)
  }

  loadData()
}

const createToolbarContext = (tool: ZTableToolbarTool): ZTableToolbarContext => ({
  tool,
  selectedRows: selectedRows.value,
  query: localQuery.value,
  refresh: refreshTable,
  reset: () => {
    currentPage.value = 1
    localQuery.value = {}
    emit('update:query', { ...localQuery.value })
    loadData()
  },
  search: handleSearch,
  clearSelection: () => tableRef.value?.clearSelection(),
  setPage: (page: number, pageSize?: number) => {
    if (page < 1) return
    currentPage.value = page
    if (pageSize && pageSize > 0) innerPageSize.value = pageSize
    loadData()
  },
  getPagination: () => ({
    currentPage: currentPage.value,
    pageSize: innerPageSize.value,
    total: total.value,
  }),
})

const handleToolbarAction = (tool: ButtonTool) => {
  const context = createToolbarContext(tool)

  if (tool.onClick) {
    try {
      tool.onClick(context)
    } catch (error) {
      logger.error(`Error in onClick for tool ${tool.key}:`, error)
    }
  } else {
    emit('toolbar-action', tool.key, context)
  }
}

const handleSearch = () => {
  emit('search', { ...localQuery.value })

  if (actualPaginationMode.value === 'server') {
    // 使用共享的 refresh 函数，并指定从第一页开始
    refreshTable(true)
  } else {
    // 本地分页模式下，只需要重置页码
    currentPage.value = 1
  }
}

const handleSizeChange = (val: number) => {
  innerPageSize.value = val
  currentPage.value = 1

  if (actualPaginationMode.value === 'server') {
    loadData()
  }

  emit('pagination-change', { page: currentPage.value, size: val })
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val

  if (actualPaginationMode.value === 'server') {
    loadData()
  }

  emit('pagination-change', { page: val, size: innerPageSize.value })
}

const handleSelectionChange = (val: any[]) => {
  selectedRows.value = val
  emit('selection-change', val)
}

const handleSelect = (selection: any[], row: any) => {
  emit('selection', selection, row)
}

const handleSelectAll = (selection: any[]) => {
  emit('select-all', selection)
}

defineExpose<ZTableMethods>({
  refresh: refreshTable,
  reset: () => {
    currentPage.value = 1
    localQuery.value = {}
    emit('update:query', { ...localQuery.value })
    loadData()
  },
  getSelection: () => selectedRows.value,
  clearSelection: () => tableRef.value?.clearSelection(),
  setPage: (page: number, pageSize?: number) => {
    if (page < 1) return
    currentPage.value = page
    if (pageSize && pageSize > 0) innerPageSize.value = pageSize
    loadData()
  },
  getPagination: () => ({
    currentPage: currentPage.value,
    pageSize: innerPageSize.value,
    total: total.value,
  }),
  toggleRowSelection: (row: any, selected?: boolean) => {
    tableRef.value?.toggleRowSelection(row, selected)
  },
  toggleRowExpansion: (row: any, expanded?: boolean) => {
    tableRef.value?.toggleRowExpansion(row, expanded)
  },
  getExpandRowKeys: (): (string | number)[] => {
    if (tableRef.value) {
      return tableRef.value.expandRowKeys ?? []
    }
    return []
  },
  getTableData: (): TableDataResult => {
    return displayData.value
  },

  getTableDataAsync: async (options?: GetTableDataAsyncOptions): Promise<TableDataResult> => {
    const defaultOptions = {
      waitForLoading: true,
      timeout: 10000,
    }

    const opts = { ...defaultOptions, ...options }

    if (!loading.value) {
      return displayData.value
    }

    if (!opts.waitForLoading) {
      return displayData.value
    }

    return new Promise<TableDataResult>((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('获取表格数据超时'))
      }, opts.timeout)

      const checkLoading = () => {
        if (!loading.value) {
          clearTimeout(timeoutId)
          resolve(displayData.value)
        } else {
          setTimeout(checkLoading, 100)
        }
      }

      checkLoading()
    })
  },
})

const slots = useSlots()

if (props.autoLoad) {
  loadData()
}

watch(
  () => props.fetchData,
  () => {
    currentPage.value = 1
    loadData()
  },
)

watch(
  () => props.data,
  () => {
    if (!isAsync.value) {
      currentPage.value = 1
      loadData()
    }
  },
  { deep: true },
)

const computedLayout = computed(() => {
  const layout = mergedPaginationLayout.value.layout
  if (typeof layout === 'string') {
    return layout
  }
  return (layout ?? []).join(', ')
})

const totalPages = computed(() => {
  return Math.ceil(total.value / innerPageSize.value)
})

const jumpToFirst = () => {
  currentPage.value = 1
}

const jumpToLast = () => {
  currentPage.value = totalPages.value
}

const groupStates = ref<Record<string, { collapsed: boolean }>>({})

const initGroupState = (tool: GroupTool) => {
  if (!groupStates.value[tool.key]) {
    let initialState = false

    try {
      const savedState = localStorage.getItem(`ztable-group-${tool.key}`)
      if (savedState !== null) {
        initialState = savedState === '1'
      } else if (tool.group?.collapsed !== undefined) {
        initialState = tool.group.collapsed
      }
    } catch {
      initialState = tool.group?.collapsed !== undefined ? tool.group.collapsed : false
    }

    groupStates.value[tool.key] = {
      collapsed: initialState,
    }
  }
}

const toggleGroupCollapse = (tool: GroupTool) => {
  if (!tool.group?.collapsible) return

  initGroupState(tool)
  groupStates.value[tool.key].collapsed = !groupStates.value[tool.key].collapsed

  try {
    localStorage.setItem(
      `ztable-group-${tool.key}`,
      groupStates.value[tool.key].collapsed ? '1' : '0',
    )
  } catch (error) {
    logger.warn('Failed to save group state to localStorage', error)
  }
}

const isGroupCollapsed = (tool: GroupTool) => {
  initGroupState(tool)
  return groupStates.value[tool.key].collapsed
}

const getLabelSeparator = (tool: ZTableToolbarTool) => {
  if (tool.labelColon !== undefined) {
    if (typeof tool.labelColon === 'string') {
      return tool.labelColon
    }
    return tool.labelColon ? '：' : ''
  }

  if (typeof props.toolbarLabelColon === 'string') {
    return props.toolbarLabelColon
  }
  return props.toolbarLabelColon ? '：' : ''
}

const renderLabel = (tool: ZTableToolbarTool) => {
  if (!tool.label) return null

  const separator = getLabelSeparator(tool)

  return h(
    'span',
    {
      class: 'z-table__toolbar-label',
    },
    [
      tool.label,
      separator ? h('span', { class: 'z-table__toolbar-label-separator' }, separator) : null,
    ],
  )
}

const getToolItemClass = (tool: ZTableToolbarTool) => {
  const classes = ['z-table__toolbar-item']

  if (tool.flexible || tool.type === 'input' || tool.type === 'select') {
    if (!tool.width) {
      classes.push('z-table__toolbar-item--flexible')
    }
  }

  if (tool.fullWidth) {
    classes.push('z-table__toolbar-item--full-width')
  }

  if (tool.class) {
    if (typeof tool.class === 'string') {
      classes.push(tool.class)
    } else if (Array.isArray(tool.class)) {
      classes.push(...tool.class)
    } else {
      Object.entries(tool.class).forEach(([className, condition]) => {
        if (condition) {
          classes.push(className)
        }
      })
    }
  }

  return classes
}

const toolOptionsMap = ref<Map<string, ZTableToolOption[]>>(new Map())

const toolLoadingMap = ref<Map<string, boolean>>(new Map())

const toolErrorMap = ref<Map<string, Error | null>>(new Map())

const renderToolItem = (tool: ZTableToolbarTool) => {
  const toolbarContext = createToolbarContext(tool)

  const isVisible =
    typeof tool.show === 'function' ? tool.show(toolbarContext) : (tool.show ?? true)

  if (!isVisible) {
    return null
  }

  const getToolItemStyle = (tool: ZTableToolbarTool) => {
    const style: Record<string, string | number> = {}
    if (tool.width) {
      style.width = typeof tool.width === 'number' ? `${tool.width}px` : tool.width
    }
    if (tool.minWidth) {
      style.minWidth = typeof tool.minWidth === 'number' ? `${tool.minWidth}px` : tool.minWidth
    }
    if (tool.maxWidth) {
      style.maxWidth = typeof tool.maxWidth === 'number' ? `${tool.maxWidth}px` : tool.maxWidth
    }
    if (tool.fullWidth) {
      style.width = '100%'
    }
    if (tool.style) {
      Object.assign(style, typeof tool.style === 'string' ? { style: tool.style } : tool.style)
    }
    return style
  }

  const slotName = `toolbar-item-${tool.key}`
  if (slots[slotName]) {
    return h(
      'div',
      {
        class: getToolItemClass(tool),
        style: getToolItemStyle(tool),
      },
      slots[slotName](toolbarContext),
    )
  }

  if (tool.render) {
    return h(
      'div',
      {
        class: getToolItemClass(tool),
        style: getToolItemStyle(tool),
      },
      [h(tool.render, toolbarContext)],
    )
  }

  switch (tool.type) {
    case 'button':
      return h(
        'div',
        {
          class: getToolItemClass(tool),
          style: getToolItemStyle(tool),
        },
        [renderButton(tool)],
      )

    case 'input':
      return h(
        'div',
        {
          class: getToolItemClass(tool),
          style: getToolItemStyle(tool),
        },
        [
          renderLabel(tool),
          h(ElInput, {
            modelValue: toolbarContext.query[tool.field || tool.key],
            'onUpdate:modelValue': (value) => {
              toolbarContext.query[tool.field || tool.key] = value
              if (tool.onChange) {
                tool.onChange(value, toolbarContext)
              } else {
                updateQuery(tool.field || tool.key, value)
              }
            },
            placeholder: tool.placeholder || '搜索',
            clearable: true,
          }),
        ],
      )

    case 'select': {
      const isLoading = toolLoadingMap.value.get(tool.key) || false
      const error = toolErrorMap.value.get(tool.key)
      return h(
        'div',
        {
          class: [...getToolItemClass(tool), error ? 'z-table__toolbar-item--error' : ''],
          style: getToolItemStyle(tool),
        },
        [
          renderLabel(tool),
          h(
            ElSelect,
            {
              modelValue: toolbarContext.query[tool.field || tool.key],
              'onUpdate:modelValue': (value) => {
                toolbarContext.query[tool.field || tool.key] = value
                if (tool.onChange) {
                  tool.onChange(value, toolbarContext)
                } else {
                  updateQuery(tool.field || tool.key, value)
                }
              },
              placeholder: tool.placeholder || '选择',
              clearable: true,
              loading: isLoading,
              disabled: isLoading,
              style: { width: '100%' },
            },
            () => {
              if (error) {
                return h(
                  ElOption,
                  {
                    value: '',
                    label: '加载失败',
                    disabled: true,
                  },
                  {
                    default: () => [
                      h('div', { class: 'select-error-option' }, [
                        h('span', error.message),
                        h(
                          ElButton,
                          {
                            size: 'small',
                            type: 'primary',
                            onClick: (e) => {
                              e.stopPropagation()
                              loadToolOptions(tool)
                            },
                          },
                          '重试',
                        ),
                      ]),
                    ],
                  },
                )
              }

              return (toolOptionsMap.value.get(tool.key) || []).map((option) =>
                h(ElOption, {
                  key: option.value as any,
                  label: option.label,
                  value: option.value!,
                  disabled: option.disabled ?? false,
                }),
              )
            },
          ),
        ],
      )
    }

    case 'tree-select': {
      const isLoading = toolLoadingMap.value.get(tool.key) || false
      const error = toolErrorMap.value.get(tool.key)

      return h(
        'div',
        {
          class: [...getToolItemClass(tool), error ? 'z-table__toolbar-item--error' : ''],
          style: getToolItemStyle(tool),
        },
        [
          renderLabel(tool),
          h(
            ElTreeSelect,
            {
              modelValue: toolbarContext.query[tool.field || tool.key],
              'onUpdate:modelValue': (value: string | number | string[] | number[] | undefined) => {
                toolbarContext.query[tool.field || tool.key] = value
                if (tool.onChange) {
                  tool.onChange(value, toolbarContext)
                } else {
                  updateQuery(tool.field || tool.key, value)
                }
              },
              data: toolOptionsMap.value.get(tool.key) || [],
              props: {
                value: tool.selectProps?.value || 'value',
                label: tool.selectProps?.label || 'label',
                children: tool.selectProps?.children || 'children',
                disabled: tool.selectProps?.disabled || 'disabled',
                ...tool.selectProps,
              },
              placeholder: tool.placeholder || '请选择',
              clearable: true,
              valueKey: tool.valueKey,
              loading: isLoading,
              disabled: isLoading,
              checkStrictly: tool.selectProps?.checkStrictly ?? false,
              multiple: tool.selectProps?.multiple ?? false,
              showCheckbox: tool.selectProps?.multiple && (tool.selectProps?.showCheckbox ?? false),
              checkOnClickNode: tool.selectProps?.checkOnClickNode ?? false,
              style: { width: '100%' },
            },
            {
              empty: error
                ? () =>
                    h('div', { class: 'select-error-option' }, [
                      h('span', error.message),
                      h(
                        ElButton,
                        {
                          size: 'small',
                          type: 'primary',
                          onClick: (e: Event) => {
                            e.stopPropagation()
                            loadToolOptions(tool)
                          },
                        },
                        () => '重试',
                      ),
                    ])
                : null,
            },
          ),
        ],
      )
    }

    default:
      return null
  }
}

const renderButton = (tool: ButtonTool) => {
  const context = createToolbarContext(tool)

  const buttonProps = {
    plain: tool.plain,
    type: tool.buttonType || 'primary',
    disabled: tool.disabled ? tool.disabled(context) : false,
    class: tool.class,
    style: tool.style,
    onClick: () => handleToolbarAction(tool),
  }

  if (!tool.icon) {
    return h(ElButton, buttonProps, () => tool.label)
  }

  const renderIcon = (icon: IconComponent | IconName) => {
    if (typeof icon === 'string') {
      const elementIconName = icon as ElementPlusIconName
      if (elementIconName in ElementPlusIconsVue) {
        const ImportedIcon = ElementPlusIconsVue[elementIconName]
        return h(ElIcon, {}, () => h(ImportedIcon))
      }
      logger.warn(`Icon "${icon}" not found. Make sure it's registered globally or imported.`)
      return null
    }

    if (icon) {
      const isElementIcon = Object.values(ElementPlusIconsVue).includes(icon as any)
      return isElementIcon ? h(ElIcon, {}, () => h(icon as Component)) : h(icon as Component)
    }

    return null
  }

  const iconComponent = tool.icon ? renderIcon(tool.icon) : null

  if (tool.onlyIcon) {
    return h(ElButton, { ...buttonProps, circle: true }, () => iconComponent)
  }

  const content =
    tool.iconPosition === 'right'
      ? [tool.label, h('span', { class: 'icon-spacing' }), iconComponent]
      : [iconComponent, h('span', { class: 'icon-spacing' }), tool.label]

  return h(ElButton, buttonProps, () => content)
}

const renderToolGroup = (tool: ZTableToolbarTool) => {
  if (tool.type !== 'group' || !tool.group) return null

  const { group } = tool
  const isCollapsed = isGroupCollapsed(tool)

  const styleMode =
    group.styleMode === 'inherit' || group.styleMode === undefined
      ? props.groupStyleMode
      : group.styleMode

  const styleClasses = [
    'z-table__group',
    styleMode === 'default' ? 'z-table__group--default' : '',
    styleMode === 'custom' ? 'z-table__group--custom' : '',
    isCollapsed ? 'z-table__group--collapsed' : '',
    group.noMargin ? 'z-table__group--no-margin' : '',
    group.fullWidth ? 'z-table__group--full-width' : '',
  ].filter(Boolean)

  if (group.class) {
    if (typeof group.class === 'string') {
      styleClasses.push(group.class)
    } else if (Array.isArray(group.class)) {
      styleClasses.push(...(group.class as string[]))
    } else {
      Object.entries(group.class).forEach(([className, condition]) => {
        if (condition) {
          styleClasses.push(className)
        }
      })
    }
  }

  return h(
    'div',
    {
      class: styleClasses,
      style: group.style,
    },
    [
      group.title
        ? h(
            'div',
            {
              class: 'z-table__group-header',
              onClick: group.collapsible ? () => toggleGroupCollapse(tool) : undefined,
            },
            [
              h('span', { class: 'z-table__group-title' }, group.title),
              group.collapsible
                ? h(ElIcon, { class: 'z-table__group-icon' }, () =>
                    h(isCollapsed ? ArrowRight : ArrowDown),
                  )
                : null,
            ],
          )
        : null,

      !isCollapsed
        ? h(
            'div',
            {
              class: [
                'z-table__group-content',
                group.contentAlign ? `z-table__group-content--${group.contentAlign}` : '',
              ],
            },
            group.tools.map((groupTool) => renderToolItem(groupTool)),
          )
        : null,
    ],
  )
}

const renderToolbarRow = (
  row: {
    tools: ZTableToolbarTool[]
    contentAlign?: ZTableContentAlign
    spacing?: number | string
    class?: string | string[] | Record<string, boolean>
    style?: Record<string, string | number>
  },
  rowIndex: number,
) => {
  return h(
    'div',
    {
      key: rowIndex,
      class: ['z-table__toolbar-row', row.class],
      style: {
        justifyContent: row.contentAlign || 'flex-start',
        gap: row.spacing || '12px',
        ...row.style,
      },
    },
    row.tools.map((tool) => {
      return tool.type === 'group' && tool.group ? renderToolGroup(tool) : renderToolItem(tool)
    }),
  )
}

const renderToolbar = () => {
  if (!showToolbar.value) return null

  if (slots.toolbar) {
    return h(
      'div',
      {
        class: 'z-table__toolbar',
      },
      slots.toolbar(),
    )
  }

  return h(
    'div',
    {
      class: 'z-table__toolbar',
    },
    props.toolbarRows.map((row, rowIndex) => renderToolbarRow(row, rowIndex)),
  )
}

const loadToolOptions = async (tool: SelectTool | TreeSelectTool) => {
  if (!tool.options) return

  const cacheKey = generateToolCacheKey(tool)
  toolErrorMap.value.delete(tool.key)
  toolLoadingMap.value.set(tool.key, true)

  try {
    const result = await asyncDataCache.getOrFetch<any>(
      cacheKey,
      async () => {
        return typeof tool.options === 'function' ? await tool.options() : tool.options
      },
      {
        ttl: tool.cacheOptions?.ttl || 3600,
        strategy: tool.cacheOptions?.strategy || 'memory',
      },
    )

    let options = []
    if (Array.isArray(result)) {
      if (tool.type === 'select') {
        options = result.map((item) => ({
          label: item[tool.labelKey || 'label'],
          value: item[tool.valueKey || 'value'],
        }))
      } else {
        options = result
      }
    }

    toolOptionsMap.value.set(tool.key, options)
  } catch (err) {
    toolErrorMap.value.set(tool.key, err as Error)
  } finally {
    toolLoadingMap.value.set(tool.key, false)
  }
}

const loadAllToolOptions = async (rows: typeof props.toolbarRows) => {
  if (!rows?.length) return
  const loadPromises: Array<Promise<void>> = []

  const processTools = async (tools: ZTableToolbarTool[]): Promise<void> => {
    for (const tool of tools) {
      if ((tool.type === 'select' || tool.type === 'tree-select') && tool.options) {
        const promise = loadToolOptions(tool)
        loadPromises.push(promise as unknown as Promise<void>)
      }

      if (tool.type === 'group' && tool.group?.tools) {
        await processTools(tool.group.tools)
      }
    }
  }

  for (const row of rows) {
    await processTools(row.tools)
  }

  await Promise.allSettled(loadPromises)
}

watch(() => props.toolbarRows, loadAllToolOptions, { deep: true })

onMounted(() => loadAllToolOptions(props.toolbarRows))

onBeforeUnmount(() => {
  toolOptionsMap.value.clear()
  toolLoadingMap.value.clear()
  toolErrorMap.value.clear()
})

const renderCell = (column: ZTableColumn, scope: any): VNode => {
  const asyncFormatter = column.asyncFormatter!
  const cacheKey =
    typeof asyncFormatter.cacheKey === 'function'
      ? asyncFormatter.cacheKey(scope.row, column, scope.row[column.prop!])
      : asyncFormatter.cacheKey || `ztable_cell_${column.prop}_${scope.$index}`

  return h(AsyncCell, {
    cacheKey,
    fetchFn: () => asyncFormatter.fetch(scope.row, column, scope.row[column.prop!]),
    render: asyncFormatter.render ? (value) => asyncFormatter.render!(value)(scope.row) : undefined,
    loadingContent: asyncFormatter.loading,
    errorContent: asyncFormatter.error,
    cacheOptions: asyncFormatter.cacheOptions,
  })
}
</script>

<template>
  <div
    class="z-table"
    v-loading="loading"
  >
    <component :is="renderToolbar()" />
    <el-table
      ref="tableRef"
      :data="displayData"
      :stripe="stripe"
      :border="border"
      :row-key="rowKey"
      :tree-props="treeConfig"
      :default-expand-all="defaultExpandAll"
      :expand-row-keys="expandRowKeys"
      v-bind="$attrs"
      @selection-change="handleSelectionChange"
      @select="handleSelect"
      @select-all="handleSelectAll"
    >
      <el-table-column
        v-if="selectable"
        :reserveSelection="!!(rowKey && reserveSelection)"
        type="selection"
        width="55"
      />
      <el-table-column
        v-for="(column, index) in columns"
        :key="column.prop || column.label || index"
        v-bind="column"
      >
        <template
          #default="scope"
          v-if="column.slotName || (column.prop && $slots[column.prop])"
        >
          <slot
            :name="column.slotName || column.prop"
            v-bind="scope"
            :row="scope.row"
            :column="column"
          />
        </template>
        <template
          #default="scope"
          v-else-if="column.asyncFormatter"
        >
          <component :is="renderCell(column, scope)" />
        </template>
      </el-table-column>
    </el-table>

    <div
      v-if="showPagination"
      class="z-table__pagination-wrapper"
    >
      <div
        :class="[
          'z-table__pagination',
          `z-table__pagination--${mergedPaginationLayout.align}`,
          { 'z-table__pagination--simple': mergedPaginationLayout.simple },
        ]"
      >
        <el-pagination
          v-if="!mergedPaginationLayout.simple"
          v-model:current-page="currentPage"
          v-model:page-size="innerPageSize"
          :total="total"
          :page-sizes="pageSizes"
          :layout="computedLayout"
          :background="mergedPaginationLayout.background"
          :small="mergedPaginationLayout.small"
          :hide-on-single-page="mergedPaginationLayout.hideOnSinglePage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <template
            v-if="mergedPaginationLayout.showTotal"
            #total
          >
            {{
              mergedPaginationLayout.showTotal(total, [
                (currentPage - 1) * innerPageSize + 1,
                Math.min(currentPage * innerPageSize, total),
              ])
            }}
          </template>

          <template
            v-if="mergedPaginationLayout.quickButtons"
            #quickButtons
          >
            <el-button-group>
              <el-button @click="jumpToFirst">首页</el-button>
              <el-button @click="jumpToLast">末页</el-button>
            </el-button-group>
          </template>
        </el-pagination>

        <div
          v-else
          class="z-table__pagination-simple"
        >
          <el-button
            :disabled="currentPage === 1"
            @click="handleCurrentChange(currentPage - 1)"
          >
            上一页
          </el-button>
          <span class="z-table__pagination-info">{{ currentPage }}/{{ totalPages }}</span>
          <el-button
            :disabled="currentPage === totalPages"
            @click="handleCurrentChange(currentPage + 1)"
          >
            下一页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
// 变量
$border-color: #ebeef5;
$background-color: #f5f7fa;
$text-color: #303133;
$text-color-secondary: #606266;
$border-radius: 4px;
$spacing-small: 8px;
$spacing-medium: 10px;
$spacing-large: 12px;
$spacing-xl: 16px;
$spacing-xxl: 20px;
$transition-duration: 0.3s;

// 主容器
.z-table {
  width: 100%;

  // 工具栏
  &__toolbar {
    display: flex;
    flex-direction: column;
    gap: $spacing-medium;
    width: 100%;
    margin-bottom: $spacing-xxl;

    &-row {
      display: flex;
      align-items: center;
      width: 100%;
      flex-wrap: wrap;
    }

    &-item {
      display: flex;
      align-items: center;
      gap: $spacing-small; // 8px
      min-width: 120px;
      max-width: 100%;
      flex: 0 0 auto;

      // 当有冒号时，减小label和内容之间的间距
      .z-table__toolbar-label:has(.z-table__toolbar-label-separator) + * {
        margin-left: -4px; // 补偿冒号占用的空间
      }

      &--flexible {
        flex: 1 1 auto;

        &:not([style*='width']) {
          min-width: 200px;
          max-width: 400px;
        }
      }

      // 按钮类型特殊处理
      &:has(.el-button) {
        min-width: unset;
        flex: 0 0 auto;
      }

      // 输入框和选择框容器
      .el-input,
      .el-select {
        width: 100%;
        max-width: 100%;
      }

      // 全宽度时覆盖其他宽度设置
      &--full-width {
        width: 100% !important;
        max-width: 100% !important;
      }
    }

    // 标签样式优化
    &-label {
      font-size: 14px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
      flex-shrink: 0;
      &-separator {
        margin-left: 2px;
      }
    }

    .icon-spacing {
      display: inline-block;
      width: 0px;
    }

    .el-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        margin: 0;
      }
    }
  }

  // 工具栏分组
  &__group {
    display: flex;
    flex-direction: column;
    min-width: min-content;
    flex: 0 1 auto;
    margin-bottom: $spacing-medium;
    gap: $spacing-small;

    // 全宽模式
    &--full-width {
      width: 100%;
    }

    // 无边距模式
    &--no-margin {
      margin-bottom: 0;
    }

    // 默认主题
    &--default {
      border: 1px solid var(--el-border-color);
      border-radius: var(--el-border-radius-base);
      background-color: var(--el-bg-color);
    }

    // 折叠状态
    &--collapsed {
      .z-table__group-content {
        display: none;
      }
    }

    // 分组头部
    &-header {
      display: flex;
      align-items: center;
      padding: $spacing-medium;
      cursor: pointer;
      user-select: none;

      .z-table__group--default & {
        border-bottom: 1px solid var(--el-border-color);
      }
    }

    // 分组标题
    &-title {
      flex: 1;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    // 分组图标
    &-icon {
      margin-left: $spacing-small;
      transition: transform 0.3s;
      color: var(--el-text-color-secondary);
    }

    // 分组内容
    &-content {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-medium;
      min-width: 0;

      .z-table__group--default & {
        padding: $spacing-medium;
      }

      // 内容对齐
      &--left {
        justify-content: flex-start;
      }

      &--right {
        justify-content: flex-end;
      }

      &--center {
        justify-content: center;
      }

      &--space-between {
        justify-content: space-between;
      }

      &--space-around {
        justify-content: space-around;
      }
    }
  }

  // 分页
  &__pagination-wrapper {
    margin-top: $spacing-xl;
    width: 100%;
  }

  &__pagination {
    display: flex;

    &--left {
      justify-content: flex-start;
    }

    &--center {
      justify-content: center;
    }

    &--right {
      justify-content: flex-end;
    }

    :deep(.el-pagination) {
      justify-content: inherit; // 继承父元素的对齐方式
    }

    &--simple {
      .el-pagination {
        padding: 0;
      }
    }

    &-simple {
      display: inline-flex;
      align-items: center;
      gap: $spacing-small;

      .z-table__pagination-info {
        min-width: 60px;
        text-align: center;
      }

      .el-button {
        margin: 0 5px;
      }

      .z-table__pagination-info {
        margin: 0 $spacing-medium;
      }
    }
  }
}

// 分页简单模式
.z-table__pagination-simple {
  display: flex;
  align-items: center;
  gap: $spacing-medium;
}

// 响应式布局优化
@media (max-width: 768px) {
  .z-table {
    &__toolbar {
      &-row {
        flex-direction: column;
        align-items: stretch;
      }

      &-item {
        width: 100%;
        min-width: unset;
        max-width: none;

        &--flexible {
          max-width: none;
        }
      }
    }

    &__group {
      width: 100%;

      &-content {
        flex-direction: column;
        align-items: stretch;
      }
    }
  }
}
</style>
