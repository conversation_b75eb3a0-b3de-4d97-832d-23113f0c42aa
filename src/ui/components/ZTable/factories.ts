import i18n from '@/i18n'
import type { ButtonTool, ZTableToolbarTool } from './types'

export const ZTableToolbarFactory = {
  createAdd(options?: Partial<Pick<ButtonTool, 'onClick' | 'show' | 'disabled' | 'buttonType' | 'plain'>>): ZTableToolbarTool {
    return {
      key: 'add',
      type: 'button',
      icon: 'Plus',
      label: i18n.global.t('common.add'),
      plain: true,
      buttonType: 'primary',
      ...options
    }
  },

  createDelete(options?: Partial<Pick<ButtonTool, 'onClick' | 'show' | 'disabled' | 'buttonType' | 'plain'>>): ZTableToolbarTool {
    return {
      key: 'delete',
      type: 'button',
      icon: 'Delete',
      label: i18n.global.t('common.delete'),
      plain: true,
      buttonType: 'danger',
      disabled: ({ selectedRows }) => selectedRows.length === 0,
      ...options
    }
  },

  createSearch(options?: Partial<Pick<ButtonTool, 'onClick' | 'show' | 'disabled' | 'buttonType' | 'plain'>>): ZTableToolbarTool {
    return {
      key: 'search',
      type: 'button',
      icon: 'Search',
      label: i18n.global.t('common.query'),
      buttonType: 'primary',
      onClick: options?.onClick ?? (({ refresh }) => refresh()),
      ...options
    }
  },

  createReset(options?: Partial<Pick<ButtonTool, 'onClick' | 'show' | 'disabled' | 'buttonType' | 'plain'>>): ZTableToolbarTool {
    return {
      key: 'reset',
      type: 'button',
      icon: 'Refresh',
      label: i18n.global.t('common.reset'),
      buttonType: 'info',
      onClick: options?.onClick ?? (({ reset }) => reset()),
      ...options
    }
  },

  createExport(options?: Partial<Pick<ButtonTool, 'onClick' | 'show' | 'disabled' | 'buttonType' | 'plain'>>): ZTableToolbarTool {
    return {
      key: 'export',
      type: 'button',
      icon: 'Download',
      label: i18n.global.t('common.export'),
      plain: true,
      buttonType: 'primary',
      ...options
    }
  },

  createImport(options?: Partial<Pick<ButtonTool, 'onClick' | 'show' | 'disabled' | 'buttonType' | 'plain'>>): ZTableToolbarTool {
    return {
      key: 'import',
      type: 'button',
      icon: 'Upload',
      label: i18n.global.t('common.import'),
      plain: true,
      buttonType: 'primary',
      ...options
    }
  }
}
