import type { Component, PropType, VNode } from 'vue'
import { defineComponent, h, ref, watchEffect } from 'vue'
import { asyncDataCache } from '@/utils/cache'
import { logger } from '@/utils/logger'
import type { CacheOptions } from '@/utils/cache'

export interface AsyncCellProps {
  cacheKey: string
  fetchFn: () => Promise<any>
  render?: (value: any) => Component | VNode | string
  loadingContent?: string | Component
  errorContent?: string | Component
  cacheOptions?: Partial<CacheOptions>
}

export default defineComponent({
  name: 'AsyncCell',
  props: {
    cacheKey: { type: String, required: true },
    fetchFn: { type: Function as PropType<() => Promise<any>>, required: true },
    render: Function as PropType<AsyncCellProps['render']>,
    loadingContent: [String, Object],
    errorContent: [String, Object],
    cacheOptions: Object as PropType<Partial<CacheOptions>>
  },
  setup(props) {
    const rawValue = ref<any>(null)
    const loading = ref(true)
    const error = ref<Error | null>(null)

    watchEffect(async ()=>{
      try {
        const result = await asyncDataCache.getOrFetch(
          props.cacheKey,
          props.fetchFn,
          props.cacheOptions || { ttl: 3600, strategy: 'memory' }
        )
        rawValue.value = result
      } catch (err) {
        error.value = err as Error
        logger.error('AsyncCell error:', err)
      } finally {
        loading.value = false
      }
    })

    return () => {
      if (loading.value) {
        return props.loadingContent || h('div', { class: 'async-cell--loading' }, '加载中...')
      }

      if (error.value) {
        return props.errorContent || h('div', { class: 'async-cell--error' }, '加载失败')
      }

      if (props.render) {
        return props.render(rawValue.value)
      }

      return rawValue.value?.toString() || ''
    }
  }
})
