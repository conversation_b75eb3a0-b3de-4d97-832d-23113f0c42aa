import { generateCache<PERSON><PERSON> } from '@/utils/utils'
import type { SelectTool, TreeSelectTool } from './types'

export function generateToolCache<PERSON>ey(tool: SelectTool | TreeSelectTool): string {
  if (tool.cacheOptions?.cacheKey) {
    return tool.cacheOptions.cacheKey;
  }

  const cacheKeyConfig = {
    key: tool.key,
    field: tool.field,
    optionsType: typeof tool.options === 'function' ? 'function' : 'static',
    optionsHash: typeof tool.options === 'function'
      ? tool.options.toString()
        .split('')
        .reduce((hash, char) => ((hash << 5) - hash) + char.charCodeAt(0), 0)
        .toString(36)
      : undefined,

    ...tool.cacheOptions
  }

  return `ztable_tool_${generateCacheKey(cacheKeyConfig)}`
}
