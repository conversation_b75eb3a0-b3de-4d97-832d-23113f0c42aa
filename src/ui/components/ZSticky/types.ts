import ZSticky from './index.vue'
import type { StickyEventHandler } from './Sticky'

export type ZStickyInstance = InstanceType<typeof ZSticky>

export interface ZStickyEvents {
  onSticky: (handler: StickyEventHandler) => void
  onUnsticky: (handler: StickyEventHandler) => void
  onUpdate: (handler: StickyEventHandler) => void
  offSticky: (handler?: StickyEventHandler) => void
  offUnsticky: (handler?: StickyEventHandler) => void
  offUpdate: (handler?: StickyEventHandler) => void
  isSticky: (element?: HTMLElement) => boolean
  getStickyStatus: () => Map<HTMLElement, boolean>
}
