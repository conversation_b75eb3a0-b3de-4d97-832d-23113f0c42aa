export type StickyEventType = 'sticky' | 'unsticky' | 'update'

export type StickyEventHandler = (element: HTMLElement, isSticky: boolean) => void

interface StickyOptions {
  wrap?: boolean
  wrapWith?: string
  marginTop?: number
  marginBottom?: number
  stickyFor?: number
  stickyClass?: string | null
  stickyContainer?: string
  scrollContainer?: string | null
}

interface StickyData {
  active: boolean
  marginTop: number
  marginBottom: number
  stickyFor: number
  stickyClass: string | null
  wrap: boolean
  container: HTMLElement
  rect: Rectangle
}

interface Rectangle {
  top: number
  left: number
  width: number
  height: number
}

interface ViewportSize {
  width: number
  height: number
}

interface StickyElement extends HTMLElement {
  sticky: StickyData
}

export class Sticky {
  private selector: string
  private elements: StickyElement[] = []
  private vp: ViewportSize
  private body: HTMLElement
  private options: StickyOptions
  private scrollTop: number = 0
  private resizeObserver: ResizeObserver
  private intersectionObserver: IntersectionObserver
  private scrollContainer: HTMLElement | Window
  private scrollHandler: () => void

  private eventHandlers: Map<StickyEventType, Set<StickyEventHandler>> = new Map()

  constructor(selector: string = '', options: StickyOptions = {}) {
    this.selector = selector
    const body = document.querySelector('body')
    if (!body) throw new Error('Document body not found')
    this.body = body

    this.vp = this.getViewportSize()
    this.options = {
      wrap: options.wrap ?? false,
      wrapWith: options.wrapWith ?? '<div></div>',
      marginTop: options.marginTop ?? 0,
      marginBottom: options.marginBottom ?? 0,
      stickyFor: options.stickyFor ?? 0,
      stickyClass: options.stickyClass ?? null,
      stickyContainer: options.stickyContainer ?? 'body',
      scrollContainer: options.scrollContainer ?? null,
    }

    this.scrollContainer = this.options.scrollContainer
      ? (document.querySelector(this.options.scrollContainer) as HTMLElement) || window
      : window

    this.intersectionObserver = new IntersectionObserver(this.handleIntersection.bind(this), {
      root: this.scrollContainer instanceof Window ? null : this.scrollContainer,
      threshold: 0,
    })

    this.resizeObserver = new ResizeObserver(this.handleResize.bind(this))
    this.resizeObserver.observe(this.body)

    this.scrollHandler = this.throttle(this.handleScroll.bind(this), 16)
    this.scrollContainer.addEventListener('scroll', this.scrollHandler)

    this.init()
  }

  private init(): void {
    document.addEventListener('DOMContentLoaded', () => {
      const elements = document.querySelectorAll(this.selector)
      elements.forEach((element) => this.renderElement(element as HTMLElement))
    })
  }

  public renderElement(element: HTMLElement): void {
    const stickyElement = element as StickyElement
    stickyElement.sticky = {
      active: false,
      marginTop: Number.parseInt(element.dataset.marginTop || '', 10) || this.options.marginTop!,
      marginBottom:
        Number.parseInt(element.dataset.marginBottom || '', 10) || this.options.marginBottom!,
      stickyFor: Number.parseInt(element.dataset.stickyFor || '', 10) || this.options.stickyFor!,
      stickyClass: element.dataset.stickyClass || this.options.stickyClass || '',
      wrap: element.hasAttribute('data-sticky-wrap') || this.options.wrap!,
      container: this.getStickyContainer(element),
      rect: this.getRectangle(element),
    }

    if (element.tagName.toLowerCase() === 'img') {
      const img = element as HTMLImageElement
      if (img.complete) {
        stickyElement.sticky.rect = this.getRectangle(element)
      } else {
        img.onload = () => (stickyElement.sticky.rect = this.getRectangle(element))
        img.onerror = () => console.warn(`Image ${img.src} failed to load`)
      }
    }

    if (stickyElement.sticky.wrap) {
      this.wrapElement(element)
    }
    this.elements.push(stickyElement)
    this.intersectionObserver.observe(element)
    this.activate(stickyElement)
  }

  private wrapElement(element: HTMLElement): void {
    const wrapWith = element.dataset.stickyWrapWith || this.options.wrapWith!
    element.insertAdjacentHTML('beforebegin', wrapWith)
    const wrapper = element.previousElementSibling as HTMLElement
    wrapper.appendChild(element)
  }

  private activate(element: StickyElement): void {
    const { rect, container, stickyFor } = element.sticky
    if (
      rect.top + rect.height < container.offsetTop + container.offsetHeight &&
      stickyFor < this.vp.width
    ) {
      element.sticky.active = true
      this.setPosition(element)
    }
  }

  private setPosition(element: StickyElement): void {
    const { rect, container, marginTop, marginBottom, stickyClass, wrap } = element.sticky
    const wasSticky = element.style.position === 'fixed'

    this.applyStyles(element, { position: '', width: '', top: '', left: '' })

    if (this.vp.height < rect.height || !element.sticky.active) return

    if (wrap && element.parentElement) {
      this.applyStyles(element.parentElement as HTMLElement, {
        display: 'block',
        width: `${rect.width}px`,
        height: `${rect.height}px`,
      })
    }
    const containerOffsetTop =
      this.scrollContainer instanceof Window
        ? container.offsetTop
        : container.offsetTop - (this.scrollContainer as HTMLElement).getBoundingClientRect().top

    let isNowSticky = false

    if (rect.top <= 0 && container === this.body && this.scrollContainer instanceof Window) {
      this.applyStyles(element, {
        position: 'fixed',
        top: '0px',
        left: `${rect.left}px`,
        width: `${rect.width}px`,
      })
      if (stickyClass) element.classList.add(stickyClass)
      isNowSticky = true
    } else if (this.scrollTop > rect.top - marginTop) {
      this.applyStyles(element, {
        position: 'fixed',
        width: `${rect.width}px`,
        left: `${rect.left}px`,
      })

      const containerBottom = containerOffsetTop + container.offsetHeight - marginBottom
      const elementBottom = this.scrollTop + rect.height + marginTop

      if (elementBottom > containerBottom) {
        if (stickyClass) element.classList.remove(stickyClass)
        this.applyStyles(element, {
          top: `${containerBottom - (this.scrollTop + rect.height)}px`,
        })
        isNowSticky = true
      } else {
        if (stickyClass) element.classList.add(stickyClass)
        this.applyStyles(element, { top: `${marginTop}px` })
        isNowSticky = true
      }
    } else {
      if (stickyClass) element.classList.remove(stickyClass)
      this.applyStyles(element, { position: '', width: '', top: '', left: '' })
      if (wrap && element.parentElement) {
        this.applyStyles(element.parentElement as HTMLElement, {
          display: '',
          width: '',
          height: '',
        })
      }
      isNowSticky = false
    }

    this.emit('update', element, isNowSticky)

    if (wasSticky !== isNowSticky) {
      if (isNowSticky) {
        this.emit('sticky', element, true)
      } else {
        this.emit('unsticky', element, false)
      }
    }
  }

  private handleIntersection(entries: IntersectionObserverEntry[]): void {
    entries.forEach((entry) => {
      const element = entry.target as StickyElement
      if (element.sticky) {
        element.sticky.rect = this.getRectangle(element)
        this.activate(element)
      }
    })
  }

  private handleResize(): void {
    this.vp = this.getViewportSize()
    this.elements.forEach((element) => {
      element.sticky.rect = this.getRectangle(element)
      element.sticky.container = this.getStickyContainer(element)
      this.activate(element)
    })
  }

  private handleScroll(): void {
    this.scrollTop =
      this.scrollContainer instanceof Window
        ? window.pageYOffset || document.documentElement.scrollTop
        : (this.scrollContainer as HTMLElement).scrollTop
    this.elements.forEach((element) => {
      if (element.sticky.active) this.setPosition(element)
    })
  }

  public update(): void {
    this.elements.forEach((element) => {
      element.sticky.rect = this.getRectangle(element)
      element.sticky.container = this.getStickyContainer(element)
      this.activate(element)
    })
  }

  /**
   * 添加事件监听器
   * @param eventType 事件类型：'sticky'（变为粘性）, 'unsticky'（取消粘性）, 'update'（状态更新）
   * @param handler 事件处理函数
   */
  public on(eventType: StickyEventType, handler: StickyEventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set())
    }
    this.eventHandlers.get(eventType)?.add(handler)
  }

  /**
   * 移除事件监听器
   * @param eventType 事件类型
   * @param handler 事件处理函数，如果不提供则移除该事件类型的所有处理函数
   */
  public off(eventType: StickyEventType, handler?: StickyEventHandler): void {
    if (!this.eventHandlers.has(eventType)) return

    if (handler) {
      this.eventHandlers.get(eventType)?.delete(handler)
    } else {
      this.eventHandlers.delete(eventType)
    }
  }

  /**
   * 触发事件
   * @param eventType 事件类型
   * @param element 相关元素
   * @param isSticky 是否处于粘性状态
   */
  private emit(eventType: StickyEventType, element: HTMLElement, isSticky: boolean): void {
    if (!this.eventHandlers.has(eventType)) return

    this.eventHandlers.get(eventType)?.forEach(handler => {
      handler(element, isSticky)
    })
  }

  /**
   * 获取元素的粘性状态
   * @param element 要检查的元素
   * @returns 是否处于粘性状态
   */
  public isSticky(element: HTMLElement): boolean {
    const stickyElement = element as StickyElement
    return stickyElement.sticky?.active || false
  }

  /**
   * 获取所有元素的粘性状态
   * @returns 元素及其粘性状态的映射
   */
  public getStickyStatus(): Map<HTMLElement, boolean> {
    const statusMap = new Map<HTMLElement, boolean>()
    this.elements.forEach(element => {
      statusMap.set(element, element.sticky.active)
    })
    return statusMap
  }

  public destroy(): void {
    this.scrollContainer.removeEventListener('scroll', this.scrollHandler)
    this.resizeObserver.disconnect()
    this.intersectionObserver.disconnect()
    this.elements.forEach((element) => {
      this.applyStyles(element, { position: '', width: '', top: '', left: '' })
      delete (element as any).sticky
    })
    this.elements = []
    this.eventHandlers.clear()
  }

  private getStickyContainer(element: HTMLElement): HTMLElement {
    let container: HTMLElement | null = element.parentElement as HTMLElement
    while (
      container &&
      !container.hasAttribute('data-sticky-container') &&
      !container.matches(this.options.stickyContainer!)
    ) {
      container = container.parentElement as HTMLElement
    }
    return container || this.body
  }

  private getRectangle(element: HTMLElement): Rectangle {
    this.applyStyles(element, { position: '', width: '', top: '', left: '' })
    const rect = element.getBoundingClientRect()
    const containerRect =
      this.scrollContainer instanceof Window
        ? { top: 0, left: 0 }
        : this.scrollContainer.getBoundingClientRect()

    return {
      top: rect.top + this.scrollTop - containerRect.top,
      left: rect.left - containerRect.left,
      width: Math.max(element.offsetWidth, element.clientWidth, element.scrollWidth),
      height: Math.max(element.offsetHeight, element.clientHeight, element.scrollHeight),
    }
  }

  private getViewportSize(): ViewportSize {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    }
  }

  private applyStyles(element: HTMLElement, styles: Partial<CSSStyleDeclaration>): void {
    Object.assign(element.style, styles)
  }

  private throttle(fn: (...args: any[]) => void, wait: number): (...args: any[]) => void {
    let timeout: number | null = null
    return (...args: any[]) => {
      if (!timeout) {
        timeout = window.setTimeout(() => {
          fn(...args)
          timeout = null
        }, wait)
      }
    }
  }
}
