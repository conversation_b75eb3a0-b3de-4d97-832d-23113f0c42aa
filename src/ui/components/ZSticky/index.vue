<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, markRaw } from 'vue'
import { Sticky } from './Sticky'

interface Props {
  marginTop?: number
  marginBottom?: number
  stickyFor?: number
  stickyClass?: string | null
  stickyContainer?: string
  scrollContainer?: string | null
  wrap?: boolean
  wrapWith?: string
}

const props = withDefaults(defineProps<Props>(), {
  marginTop: 0,
  marginBottom: 0,
  stickyFor: 0,
  stickyClass: null,
  stickyContainer: 'body',
  scrollContainer: null,
  wrap: false,
  wrapWith: '<div></div>',
})

const emit = defineEmits<{
  (e: 'sticky-change', isSticky: boolean): void
  (e: 'sticky', element: HTMLElement): void
  (e: 'unsticky', element: HTMLElement): void
}>()

const stickyRef = ref<HTMLElement | null>(null)
let stickyInstance: Sticky | null = null
const isComponentSticky = ref(false)

const createSticky = () => {
  if (stickyRef.value) {
    stickyInstance = markRaw(
      new Sticky('', {
        marginTop: props.marginTop,
        marginBottom: props.marginBottom,
        stickyFor: props.stickyFor,
        stickyClass: props.stickyClass,
        stickyContainer: props.stickyContainer,
        scrollContainer: props.scrollContainer,
        wrap: props.wrap,
        wrapWith: props.wrapWith,
      }),
    )

    // 添加事件监听
    stickyInstance.on('sticky', (element, _isSticky) => {
      isComponentSticky.value = true
      emit('sticky-change', true)
      emit('sticky', element)
    })

    stickyInstance.on('unsticky', (element, _isSticky) => {
      isComponentSticky.value = false
      emit('sticky-change', false)
      emit('unsticky', element)
    })

    stickyInstance.renderElement(stickyRef.value)
  }
}

// 销毁sticky实例
const destroySticky = () => {
  if (stickyInstance) {
    stickyInstance.destroy()
    stickyInstance = null
  }
}

// 重建sticky实例
const rebuildSticky = () => {
  destroySticky()
  createSticky()
}

onMounted(() => {
  createSticky()
})

onUnmounted(() => {
  destroySticky()
})

watch(
  () => props,
  () => {
    rebuildSticky()
  },
  { deep: true },
)

// 暴露 sticky 实例和方法
defineExpose({
  sticky: () => stickyInstance,
  update: () => stickyInstance?.update(),
  destroy: () => destroySticky(),
  rebuild: () => rebuildSticky(),
  // 添加事件监听方法
  onSticky: (handler: (element: HTMLElement, isSticky: boolean) => void) =>
    stickyInstance?.on('sticky', handler),
  onUnsticky: (handler: (element: HTMLElement, isSticky: boolean) => void) =>
    stickyInstance?.on('unsticky', handler),
  onUpdate: (handler: (element: HTMLElement, isSticky: boolean) => void) =>
    stickyInstance?.on('update', handler),
  // 移除事件监听方法
  offSticky: (handler?: (element: HTMLElement, isSticky: boolean) => void) =>
    stickyInstance?.off('sticky', handler),
  offUnsticky: (handler?: (element: HTMLElement, isSticky: boolean) => void) =>
    stickyInstance?.off('unsticky', handler),
  offUpdate: (handler?: (element: HTMLElement, isSticky: boolean) => void) =>
    stickyInstance?.off('update', handler),
  // 获取状态方法
  isSticky: (element?: HTMLElement) => {
    if (!stickyInstance) return false
    if (element) {
      return stickyInstance.isSticky(element)
    }
    // 如果没有提供元素，则检查第一个元素
    const status = stickyInstance.getStickyStatus()
    return status.size > 0 ? Array.from(status.values())[0] : false
  },
  getStickyStatus: () => stickyInstance?.getStickyStatus() || new Map(),
  // 暴露当前组件的 sticky 状态
  isComponentSticky: () => isComponentSticky.value,
})
</script>

<template>
  <div
    ref="stickyRef"
    class="sticky-wrapper"
  >
    <slot :is-sticky="isComponentSticky" />
  </div>
</template>

<style scoped>
.sticky-wrapper {
  display: inline-block;
}
</style>
