const LoginRoute = {
  path: '/login',
  name: 'login',
  component: () => import('@/views/LoginView.vue'),
  meta: {
    title: '登录',
    layout: 'blank',
  },
}

const MapRoute = {
  path: '/map',
  name: 'Map',
  component: () => import('@/views/_test/MapView.vue'),
  meta: {
    layout: 'blank',
  },
}

const MapTestRoute = {
  path: '/mapTest',
  name: 'MapTest',
  component: () => import('@/views/_map/MapView.vue'),
  meta: {
    layout: 'blank',
  },
}

const NotFoundRoute = {
  path: '/:pathMatch(.*)*',
  name: 'NotFound',
  meta: {
    layout: 'blank',
  },
  component: () => import('@/views/errors/NotFoundView.vue'),
}

const annoRoutes = [LoginRoute, MapRoute, MapTestRoute]

const constantRoutes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomeView.vue'),
    meta: {
      layout: 'blank',
    },
  },

  LoginRoute,
  MapRoute,
  MapTestRoute,
  NotFoundRoute,
]

export { LoginRoute, NotFoundRoute, annoRoutes, constantRoutes }
