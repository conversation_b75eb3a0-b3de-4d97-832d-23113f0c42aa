import { useSettingsStore } from '@/stores/settings'
import { useUserStore } from '@/stores/user'
import type { Router, RouteRecordSingleView } from 'vue-router'
import { annoRoutes, LoginRoute, NotFoundRoute } from './routes'
import { createLogger } from '@/utils/logger'
import { useAppStore, type LayoutKey } from '@/stores/app'
import { useNProgress } from '@vueuse/integrations/useNProgress'
import { cancelAllRequests } from '@/utils/req'
import { nextTick } from 'vue'
import { DYNAMIC_ROUTES_KEY } from '@/utils/conts'
import { getStorage } from '@/utils/storage'
import { loadView } from '@/utils/viewLoader'

const logger = createLogger('Router')

function ensureNotFoundRoute(router: Router, action: 'add' | 'remove' = 'add') {
  if (action === 'remove' && router.hasRoute(NotFoundRoute.name)) {
    router.removeRoute(NotFoundRoute.name)
    return
  }

  if (action === 'add') {
    router.addRoute(NotFoundRoute)
    logger.log('NotFoundRoute has been added as the last route')
  }
}

function addRoutes(router: Router, routes: RouteRecordSingleView[]) {
  ensureNotFoundRoute(router, 'remove')

  routes.forEach((route) => {
    if (router.hasRoute(route.name!)) {
      logger.warn(`Route "${String(route.name)}" already exists, skipping...`)
      return
    }

    try {
      if (typeof route.component === 'string') {
        route.component = loadView(route.component as string)
      }
      router.addRoute(route)
      logger.log(`Route added: ${String(route.name)}`)
    } catch (error) {
      logger.error(`Failed to add route: ${String(route.name)}`, error)
    }
  })

  ensureNotFoundRoute(router, 'add')
}

function restoreDynamicRoutes(router: Router) {
  try {
    const savedRoutes = getStorage<RouteRecordSingleView[]>(DYNAMIC_ROUTES_KEY, [])
    if (!savedRoutes?.length) return

    addRoutes(router, savedRoutes)
  } catch (error) {
    logger.error('Failed to restore dynamic routes:', error)
  }
}

function setupLayouit(router: Router) {
  router.beforeEach((to, from, next) => {
    const appStore = useAppStore()
    if (to.meta.layout) {
      appStore.setLayout(to.meta.layout as LayoutKey)
    }
    if (to.meta.layout !== from.meta.layout) {
      nextTick().then(() => {
        cancelAllRequests()
      })
    }
    next()
  })
}

function setupTitle(router: Router) {
  router.afterEach((to) => {
    const settingsStore = useSettingsStore()
    settingsStore.setTitle(to.meta.title)
  })
}

function setupNProgress(router: Router) {
  const { isLoading } = useNProgress()

  router.beforeEach((to, from, next) => {
    const settingsStore = useSettingsStore()
    if (settingsStore.settings.theme.nProgress) {
      isLoading.value = true
    }
    next()
  })

  router.afterEach(() => {
    isLoading.value = false
  })
}

function setupApp(router: Router) {
  router.beforeEach((to, from, next) => {
    const appStore = useAppStore()
    const userStore = useUserStore()

    if (to.meta.application !== appStore.currentApp?.id) {
      const target = userStore.applications?.find((app) => app.id === to.meta.application)
      if (target && !target.routes?.length) {
        const routes = userStore.routes?.filter((r) => r.meta?.application === target.id)
        target.routes = routes
        logger.log(`Register application `, target)
      }

      appStore.setApp(target ?? null)
    }

    next()
  })
}

function setupRoutes(router: Router) {
  router.beforeEach(async (to, from, next) => {
    if (to.name === LoginRoute.name) {
      return next()
    }
    restoreDynamicRoutes(router)

    if (annoRoutes.find((r) => r.name === to.name)) {
      return next()
    }

    const userStore = useUserStore()

    if (!userStore.token) {
      return next({
        name: LoginRoute.name,
        query: { redirect: to.fullPath },
      })
    }

    if (!userStore.isAuthenticated) {
      try {
        const { routes } = await userStore.fetchAuthorizationInfo()
        addRoutes(router, routes as any)

        return next({
          path: to.path,
          query: to.query,
          replace: true,
        })
      } catch (e) {
        logger.error(e)
        return next({
          name: LoginRoute.name,
          query: { redirect: to.fullPath },
        })
      }
    }

    next()
  })
}

function apply(router: Router) {
  setupNProgress(router)
  setupRoutes(router)
  setupTitle(router)
  setupLayouit(router)
  setupApp(router)
}

export default {
  apply,
}
