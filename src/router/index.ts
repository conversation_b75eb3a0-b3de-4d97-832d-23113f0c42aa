import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { loadingFadeOut } from 'virtual:app-loading'
import guard from './guard'
import { constantRoutes } from './routes'

const baseURL = import.meta.env.BASE_URL

const router = createRouter({
  history: createWebHistory(baseURL),
  routes: constantRoutes as RouteRecordRaw[]
})

guard.apply(router)

router.isReady().then(() => {
  loadingFadeOut()
})

export default router


