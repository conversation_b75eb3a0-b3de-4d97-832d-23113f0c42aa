import './assets/styles/global.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { initializeLanguage } from './composables/useLang'
import { setupDirectives } from './directives'
import { checkAndCleanupOnBuildTimeChange } from '@/utils/version'
import { createLogger } from '@/utils/logger'

import App from './App.vue'
import router from './router'
import { ElementUI } from './ui/providers'
import i18n from './i18n'

import 'virtual:svg-icons-register'
import 'virtual:uno.css'

const logger = createLogger('main')

if (checkAndCleanupOnBuildTimeChange()) {
  logger.info('Application version or build time changed, cleared dynamic routes')
}

const app = createApp(App)
const pinia = createPinia()
app.use(pinia)

setupDirectives(app)

initializeLanguage()

app.use(router)
app.use(ElementUI)
app.use(i18n)

app.mount('#app')
