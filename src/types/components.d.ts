/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ZDialog: typeof import('./../ui/components/ZDialog/index.vue')['default']
    ZDialogForm: typeof import('./../ui/components/ZDialogForm/index.vue')['default']
    ZDynamicRoute: typeof import('./../ui/components/ZDynamicRoute/index.vue')['default']
    ZEditor: typeof import('./../ui/components/ZEditor/index.vue')['default']
    ZForm: typeof import('./../ui/components/ZForm/index.vue')['default']
    ZIconSelect: typeof import('./../ui/components/ZIconSelect/index.vue')['default']
    ZPage: typeof import('./../ui/components/ZPage/index.vue')['default']
    ZScreenContainer: typeof import('./../ui/components/ZScreenContainer/index.vue')['default']
    ZScrollbar: typeof import('./../ui/components/ZScrollbar/index.vue')['default']
    ZSelect: typeof import('./../ui/components/ZSelect/index.vue')['default']
    ZSticky: typeof import('./../ui/components/ZSticky/index.vue')['default']
    ZSvg: typeof import('./../ui/components/ZSvg/index.vue')['default']
    ZTable: typeof import('./../ui/components/ZTable/index.vue')['default']
    ZUpload: typeof import('./../ui/components/ZUpload/index.vue')['default']
  }
}
