declare global {
  namespace T {
    interface MapOptions {
      /** 指定地图的投影方式，目前支持的地图投影方式有：EPSG:900913(墨卡托投影)，EPSG:4326(大地平面投影) */
      projection?: string
      /** 地图允许展示的最小级别 */
      minZoom?: number
      /** 地图允许展示的最大级别 */
      maxZoom?: number
      /** 当这个选项被设置后，地图被限制在给定的地理边界内，当用户平移将地图拖动到视图以外的范围时会出现弹回的效果，并且也不允许缩小视图到给定范围以外的区域（这取决于地图的尺寸）。使用setMaxBounds方法可以动态地设置这种约束 */
      maxBounds?: LngLatBounds
      /** 地图的初始化中心点 */
      center?: LngLat
      /** 地图的初始化级别 */
      zoom?: number
    }

    interface TileLayerOptions {
      minZoom?: number
      maxZoom?: number
      tileSize?: number
      opacity?: number
      zIndex?: number
      visible?: boolean
    }

    interface PolygonOptions {
      color?: string
      weight?: number
      opacity?: number
      fillColor?: string
      fillOpacity?: number
      lineStyle?: 'solid' | 'dashed'
    }

    class Map {
      constructor(container: string | HTMLElement, opts?: MapOptions)

      enableDrag(): void
      disableDrag(): void
      isDrag(): boolean

      enableScrollWheelZoom(): void
      disableScrollWheelZoom(): void
      isScrollWheelZoom(): boolean

      enableDoubleClickZoom(): void
      disableDoubleClickZoom(): void
      isDoubleClickZoom(): boolean

      enableKeyboard(): void
      disableKeyboard(): void
      isKeyboard(): boolean

      enableInertia(): void
      disableInertia(): void
      isInertia(): boolean

      enableContinuousZoom(): void
      disableContinuousZoom(): void
      isContinuousZoom(): boolean

      enablePinchToZoom(): void
      disablePinchToZoom(): void
      isPinchToZoom(): boolean

      enableAutoResize(): void
      disableAutoResize(): void

      getCode(): string
      getBounds(): LngLatBounds
      getCenter(): LngLat
      getContainer(): HTMLElement
      getSize(): Point
      getZoom(): number
      getDistance(start: LngLat, end: LngLat): number
      getViewport(view: Array<LngLat>): { center: LngLat; zoom: number }

      containerPointToLngLat(pixel: Point): LngLat
      lngLatToContainerPoint(lnglat: LngLat): Point
      lngLatToLayerPoint(lnglat: LngLat): Point
      layerPointToLngLat(pixel: Point): LngLat

      centerAndZoom(center: LngLat | [number, number], zoom: number): void
      panTo(lnglat: LngLat, zoom?: number): void
      panBy(position: Point): void
      setZoom(zoom: number): void
      setCenter(center: LngLat): void
      setMapStyle(style: 'black' | 'indigo'): void
      setMaxBounds(bounds: LngLatBounds): void

      addOverLay(overlay: Overlay): void
      removeOverLay(overlay: Overlay): void
      clearOverLays(): void
      getOverlays(): Overlay[]

      addLayer(layer: TileLayer | any): void
      removeLayer(layer: TileLayer | any): void
      hasLayer(layer: TileLayer | any): boolean

      addControl(control: Control): void
      removeControl(control: Control): void
      getContainer(): HTMLElement

      openInfoWindow(infowin: InfoWindow, lnglat: LngLat): void
      closeInfoWindow(): void
      getInfoWindow(): InfoWindow | null

      addEventListener(event: string, handler: Function): void
      removeEventListener(event: string, handler: Function): void
      clearEventListeners(): void

      destroy(): void
      resize(): void
      getPanes(): {
        mapPane: HTMLElement
        overlayPane: HTMLElement
        markerPane: HTMLElement
        infoWindowPane: HTMLElement
        maskPane: HTMLElement
      }

      zoomIn(): void
      zoomOut(): void
      checkResize(): void
      setMinZoom(level: number): void
      setMaxZoom(level: number): void

      setMaxBounds(bounds: LngLatBounds): void
      setViewport(view: Array<LngLat>): void

      setStyle(style: 'black' | 'indigo'): void
    }

    class LngLat {
      /**
       * 经度
       */
      lng: number

      /**
       * 纬度
       */
      lat: number

      /**
       * 根据给定经度和纬度创建地理位置坐标点
       * @param lng 地理经度
       * @param lat 地理纬度
       */
      constructor(lng: number, lat: number)

      /**
       * 获取地理坐标点的经度
       */
      getLng(): number

      /**
       * 获取地理坐标点的纬度
       */
      getLat(): number

      /**
       * 计算当前地理坐标点与给定坐标点之间的距离
       * @param other 经纬度坐标
       */
      distanceTo(other: LngLat): number

      /**
       * 判断坐标点是否相等，当且仅当两点的经度和纬度均相等时返回true
       * @param other 地理坐标
       */
      equals(other: LngLat): boolean
    }

    class LngLatBounds {
      constructor(sw: LngLat, ne: LngLat)
      contains(point: LngLat): boolean
      equals(bounds: LngLatBounds): boolean
      extend(point: LngLat): LngLatBounds
      getCenter(): LngLat
      getNorthEast(): LngLat
      getSouthWest(): LngLat
      intersects(bounds: LngLatBounds): boolean
      isEmpty(): boolean
      toSpan(): LngLat
    }

    class TileLayer {
      constructor(url: string, opts?: TileLayerOptions)
      setOpacity(opacity: number): void
      getOpacity(): number
      show(): void
      hide(): void
      /**
       * 获取指定坐标位置的瓦片URL
       * @param x 瓦片x坐标
       * @param y 瓦片y坐标
       * @param z 缩放级别
       */
      getTileUrl(x: number, y: number, z: number): string
    }

    interface OverlayOptions {
      type?: number
    }

    class Overlay {
      /**
       * 构造函数时传递参数，对OverlayOptions属性值进行赋值
       * @param opts 叠加层配置项
       */
      constructor(opts?: OverlayOptions)

      /**
       * 构造函数时传递参数，对OverlayOptions属性值进行赋值
       * @param opt 叠加层配置项
       */
      initialize(opt: OverlayOptions): void

      /**
       * 向地图上添加叠加物。当调用map.addOverLay时，API将调用此方法。
       * 自定义叠加物时需要实现此方法。自定义叠加物时需要将覆盖物对应的HTML元素返回。
       * @param map Map对象
       */
      onAdd(map: Map): HTMLElement

      /**
       * 移除叠加物，释放覆盖物对象所占用的内存。
       * 自定义叠加物时需要实现此方法。
       */
      onRemove(): void

      /**
       * 当地图状态发生变化时，由系统调用对覆盖物进行绘制。
       * 自定义覆盖物需要实现此方法。
       */
      update(): void

      /**
       * 在地图上显示叠加层
       */
      show(): void

      /**
       * 在地图上隐藏叠加层
       */
      hide(): void

      /**
       * 判断叠加层是否隐藏
       * @returns true 表示当前叠加层是隐藏的，否则是处于显示状态
       */
      isHidden(): boolean

      /**
       * 对OverlayOptions属性值赋值
       * @param opt 叠加层配置项
       */
      setOptions(opt: OverlayOptions): void

      /**
       * 返回叠加物所在的容器的标签
       */
      getElement(): HTMLElement

      /**
       * 继承方法，用于扩展自定义覆盖物
       * @param methods 自定义方法集合
       */
      static extend(methods: Record<string, any>): any
    }

    const enum OverlayType {
      Label = 1,
      Marker = 2,
      InfoWindow = 3,
      Polyline = 4,
      Polygon = 5,
      Rectangle = 6,
      Circle = 8,
    }

    interface InfoWindowOptions extends OverlayOptions {
      minWidth?: number
      maxWidth?: number
      maxHeight?: number
      autoPan?: boolean
      closeButton?: boolean
      offset?: Point
      autoPanPadding?: Point
      closeOnClick?: boolean
    }

    class InfoWindow extends Overlay {
      constructor(content: string | HTMLElement, opts?: InfoWindowOptions)

      setContent(content: string | HTMLElement): void
      getContent(): string | HTMLElement

      setLngLat(lnglat: LngLat): void
      getLngLat(): LngLat
      setOffset(point: Point): void
      getOffset(): Point

      isOpen(): boolean
      update(): void

      closeInfoWindow(): void

      addEventListener(event: string, handler: Function): void
      removeEventListener(event: string, handler: Function): void
    }

    class Point {
      constructor(x: number, y: number)
      x: number
      y: number
    }

    type MarkerEventType =
      | 'click'
      | 'dblclick'
      | 'mousedown'
      | 'mouseup'
      | 'mouseout'
      | 'mouseover'
      | 'dragstart'
      | 'drag'
      | 'dragend'

    class Marker extends Overlay {
      /**
       * 创建一个地图标注
       * @param lnglat 标注的位置
       * @param opts 标注的配置项
       */
      constructor(lnglat: LngLat, opts?: MarkerOptions)

      /**
       * 打开标注的信息窗口
       * @param infowin 信息窗口对象
       */
      openInfoWindow(infowin: InfoWindow): void

      /**
       * 关闭标注的信息窗口
       */
      closeInfoWindow(): void

      /**
       * 设置标注的位置
       * @param lnglat 标注的位置
       */
      setLngLat(lnglat: LngLat): void

      /**
       * 获取标注的位置
       * @returns 标注的位置
       */
      getLngLat(): LngLat

      /**
       * 设置标注的图标
       * @param icon 标注的图标
       */
      setIcon(icon: Icon): void

      /**
       * 获取标注的图标
       */
      getIcon(): Icon

      /**
       * 设置标注的标题
       * @param title 标注的标题
       */
      setTitle(title: string): void

      /**
       * 获取标注的标题
       */
      getTitle(): string

      /**
       * 设置标注的层级
       * @param offset 标注的层级偏移量
       */
      setZIndexOffset(offset: number): void

      /**
       * 获取标注的层级
       */
      getZIndexOffset(): number

      /**
       * 设置标注的透明度
       * @param opacity 透明度，取值范围[0,1]
       */
      setOpacity(opacity: number): void

      /**
       * 获取标注的透明度
       */
      getOpacity(): number

      /**
       * 开启标注拖拽功能
       */
      enableDragging(): void

      /**
       * 关闭标注拖拽功能
       */
      disableDragging(): void

      /**
       * 获取标注是否处于拖拽状态
       */
      isDragging(): boolean

      /**
       * 获取标注的DOM元素
       */
      getElement(): HTMLElement

      /**
       * 显示标注
       */
      show(): void

      /**
       * 隐藏标注
       */
      hide(): void

      /**
       * 移除标注
       */
      remove(): void

      /**
       * 添加事件监听函数
       * @param event 事件名称
       * @param handler 事件处理函数
       */
      addEventListener(event: MarkerEventType, handler: Function): void

      /**
       * 移除事件监听函数
       * @param event 事件名称
       * @param handler 事件处理函数
       */
      removeEventListener(event: MarkerEventType, handler: Function): void
    }

    interface LabelOptions {
      text: string
      offset?: Point
      position: LngLat
    }

    type LabelEventType = 'click' | 'dblclick' | 'mousedown' | 'mouseup' | 'mouseout'

    interface LabelEventParams {
      type: LabelEventType
      target: any
      lnglat: LngLat
      containerPoint: Point
    }

    type LabelEventHandler = (event: LabelEventParams) => void

    class Label extends Overlay {
      constructor(opts: LabelOptions)

      getType(): number
      getLngLat(): LngLat
      setLngLat(lnglat: LngLat): void
      setOffset(offset: Point): void
      getOffset(): Point
      setLabel(content: string): void
      getLabel(): string
      setTitle(title: string): void
      getTitle(): string
      setZindex(): void
      setFontSize(size: number): void
      getFontSize(): number
      setFontColor(color: string): void
      getFontColor(): string
      setBackgroundColor(color: string): void
      getBackgroundColor(): string
      setBorderLine(width: number): void
      getBorderLine(): number
      setBorderColor(color: string): void
      getBorderColor(): string
      setOpacity(opacity: number): void
      getOpacity(): number
      show(): void
      hide(): void
      setVisible(visible: boolean): void
      getVisible(): boolean
      setClickable(clickable: boolean): void
      getClickable(): boolean
      setDraggable(draggable: boolean): void
      getDraggable(): boolean
      addEventListener(event: LabelEventType, handler: LabelEventHandler): void
      removeEventListener(event: LabelEventType, handler: LabelEventHandler): void
    }

    interface IconOptions {
      iconUrl: string
      iconSize?: Point
      iconAnchor?: Point
    }

    class Icon {
      constructor(opts: IconOptions)

      setIconUrl(iconUrl: string): void
      getIconUrl(): string

      setIconSize(size: Point): void
      getIconSize(): Point

      setIconAnchor(anchor: Point): void
      getIconAnchor(): Point
    }

    interface MarkerOptions extends OverlayOptions {
      icon?: Icon
      draggable?: boolean
      title?: string
      zIndexOffset?: number
      opacity?: number
      position: LngLat | [number, number]
    }

    /**
     * 控件配置选项接口
     */
    interface ControlOptions {
      /**
       * 控件的停靠位置
       * @default T_ANCHOR_TOP_RIGHT
       */
      position?: string
    }

    /**
     * 控件基类
     * 所有控件类的基类，可以通过此类来自定义控件
     */
    class Control {
      constructor(opts?: ControlOptions)

      /**
       * 设置控件的位置
       * @param position 控件位置枚举值
       */
      setPosition(position: number): void

      /**
       * 返回控件的位置
       */
      getPosition(): number

      /**
       * 向地图上添加控件
       * 当调用map.addControl时，API将调用此方法
       * 自定义控件时需要实现此方法，并将控件对应的HTML元素返回
       * @param map 地图实例
       */
      onAdd(map: Map): void

      /**
       * 移除控件
       * 释放控件对象所占用的内存
       */
      onRemove(): void

      /**
       * 返回控件所在的容器的标签
       */
      getContainer(): HTMLElement

      /**
       * 显示控件
       */
      show(): void

      /**
       * 隐藏控件
       */
      hide(): void

      /**
       * 判断控件的可见性
       */
      isVisible(): boolean

      /**
       * 设置控件停靠的偏移量
       * @param offset 偏移量
       */
      setOffset(offset: Point): void

      /**
       * 返回控件停靠的偏移量
       */
      getOffset(): Point

      /**
       * 对ControlOptions属性值赋值
       * @param opt 控件配置选项
       */
      setOptions(opt: ControlOptions): void
    }

    type MapEventType =
      | 'click'
      | 'dblclick'
      | 'contextmenu'
      | 'mousedown'
      | 'mouseup'
      | 'mousemove'
      | 'mouseout'
      | 'mouseover'
      | 'movestart'
      | 'move'
      | 'moveend'
      | 'zoomstart'
      | 'zoomend'
      | 'layeradd'
      | 'layerremove'
      | 'load'
      | 'dragstart'
      | 'drag'
      | 'dragend'

    interface LocalSearchOptions {
      /** 每页容量 */
      pageCapacity?: number
      /** 检索结束后的回调函数 */
      onSearchComplete?: (result: LocalSearchResult) => void
    }

    class LocalSearch {
      constructor(map: Map, opts?: LocalSearchOptions)

      search(keyword: string, type: number): void
      searchInBounds(keyword: string, bounds: LngLatBounds): void
      searchNearby(keyword: string, center: LngLat, radius: number): void

      getResults(): LocalSearchResult
      clearResults(): void
      gotoPage(page: number): void
      firstPage(): void
      nextPage(): void
      previousPage(): void
      lastPage(): void

      setSpecifyAdminCode(code: number): void
      setQueryType(type: number): void
      getQueryType(): number
      setPageCapacity(count: number): void
      getPageCapacity(): number
      setSearchCompleteCallback(callback: (result: LocalSearchResult) => void): void

      getCountNumber(): number
      getCountPage(): number
      getPageIndex(): string
    }

    class LocalSearchResult {
      getResultType(): number
      getCount(): number
      getKeyword(): string

      getPois(): Array<{
        phone: string
        lonlat: string
        address: string
        name: string
        poiType?: string
      }>

      getStatistics(): {
        priorityCitys: Array<{
          count: string
          name: string
          adminCode: number
        }>
        keyword: string
        countryCount: number
        citysCount: number
        allAdmins: any[]
      }

      getArea(): {
        level: string
        lonlat: string
        name: string
        points: Array<{
          region: string
        }>
        type: string
      }

      getSuggests(): Array<{
        address: string
        name: string
        gbCode: string
      }>

      getLineData(): Array<{
        poiType: string
        stationNum: string
        name: string
        uuid: string
      }>

      getPrompt(): Array<{
        keyword: string
        admins: Array<{
          name: string
          adminCode: number
        }>
        type: number
      }>
    }

    namespace Control {
      /**
       * 鹰眼地图控件配置选项
       */
      interface OverviewMapOptions extends ControlOptions {
        /**
         * 缩略地图控件的大小
         * @default new Point(0, 0)
         */
        size?: Point

        /**
         * 缩略地图添加到地图后的开合状态
         * @default false - true表示显示，false表示隐藏
         */
        isOpen?: boolean
      }

      /**
       * 鹰眼地图控件类
       * 用来显示一个鹰眼地图, 继承自Control基类
       */
      class OverviewMap extends Control {
        constructor(opts?: OverviewMapOptions)

        /**
         * 设置鹰眼地图的按钮图片
         * @param imgOpen 鹰眼地图在打开状态下的按钮图片
         * @param imgClose 鹰眼地图在关闭状态下的按钮图片
         */
        setButtonImage(imgOpen: string, imgClose: string): void

        /**
         * 设置鹰眼地图和主地图之间空隙的背景颜色
         * @param color 颜色值
         */
        setBorderColor(color: string): void

        /**
         * 设置鹰眼地图上的矩形框边框颜色
         * @param color 颜色值
         */
        setRectBorderColor(color: string): void

        /**
         * 设置鹰眼地图上的矩形框背景颜色
         * @param color 颜色值
         */
        setRectBackColor(color: string): void

        /**
         * 切换鹰眼地图的开-合状态
         */
        changeView(): void

        /**
         * 返回该鹰眼的视图是否被打开
         */
        isOpen(): boolean

        /**
         * 返回该鹰眼的地图对象
         */
        getMiniMap(): Map

        /**
         * 添加事件监听函数
         * @param event 事件名
         * @param handler 处理函数
         */
        addEventListener(event: 'viewchange', handler: (e: ViewChangeEvent) => void): void

        /**
         * 移除事件监听函数
         * @param event 事件名
         * @param handler 处理函数
         */
        removeEventListener(event: 'viewchange', handler: (e: ViewChangeEvent) => void): void
      }
    }

    /**
     * 鹰眼视图变化事件
     */
    interface ViewChangeEvent {
      /** 事件类型 */
      type: string
      /** 事件目标 */
      target: Control.OverviewMap
      /** 状态变化后鹰眼地图是否在打开的状态中 */
      isOpen: boolean
    }

    interface MarkToolOptions {
      /** 图标类用来表达注记。默认设置为new T.Icon.Default() */
      icon?: Icon
      /** 标记图标是否跟随鼠标 */
      follow?: boolean
    }

    /** 标注工具事件对象 */
    interface MarkToolEvent {
      /** 事件类型 */
      type: string
      /** 事件目标 */
      target: MarkTool
      /** 用户在地图上标的坐标 */
      currentLnglat: LngLat
      /** 用户当前的标注点对象 */
      currentMarker: Marker
      /** 用户使用工具所有的标注点对象 */
      allMarkers: Marker[]
    }

    /**
     * 标注工具类
     * 用来让用户在地图上标注一个点，可以通过该工具获得用户标点的经纬度位置
     */
    class MarkTool {
      /**
       * 构造标注工具
       * @param map 地图实例
       * @param opts 配置选项
       */
      constructor(map: Map, opts?: MarkToolOptions)

      /**
       * 设置标注工具显示的标注图标路径
       * @param url 标注图标的路径URL
       */
      setPointImage(url: string): void

      /**
       * 获取用户标注点的坐标，如果用户尚未操作，则返回 null
       * @returns 经纬度坐标
       */
      getMarkControlPoint(): LngLat | null

      /**
       * 开启标注工具
       * @returns 是否成功开启
       */
      open(): boolean

      /**
       * 关闭标注工具
       */
      close(): void

      /**
       * 清除工具绘制的所有标注图标
       */
      clear(): void

      /**
       * 获取所有工具绘制的标注图标
       * @returns 标注点数组
       */
      getMarkers(): Marker[]

      /**
       * 添加事件监听函数
       * @param event 事件名称
       * @param handler 事件处理函数
       */
      addEventListener(event: string, handler: (e: MarkToolEvent) => void): void

      /**
       * 移除事件监听函数
       * @param event 事件名称
       * @param handler 事件处理函数
       */
      removeEventListener(event: string, handler: (e: MarkToolEvent) => void): void
    }

    /** 地址解析器类 */
    class Geocoder {
      constructor()
      /** 对指定的坐标点进行反地址解析 */
      getLocation(point: LngLat, callback: (result: GeocoderResult | null) => void): void
      /** 对指定的地址进行地址解析 */
      getPoint(location: string, callback: (result: GeocoderResult | null) => void): void
    }

    /** 地址解析结果类 */
    class GeocoderResult {
      /** 获取状态码 */
      getStatus(): number
      /** 获取响应信息 */
      getMsg(): string
      /** 获取坐标点 */
      getLocationPoint(): LngLat
      /** 获取详细地址 */
      getAddress(): string
      /** 获取详细信息 */
      getAddressComponent(): AddressComponent
      /** 获取点类别 */
      getLocationLevel(): string
    }

    /** 地址解析结果的层次化地址信息 */
    interface AddressComponent {
      /** 此点最近地点信息 */
      address: string
      /** 此点距离最近地点信息距离 */
      address_distance: number
      /** 此点在最近地点信息方向 */
      address_position: string
      /** 此点所在国家或城市或区县 */
      city: string
      /** 最近POI点 */
      poi?: string
      /** 距离最近POI点的距离 */
      poi_distance?: number
      /** 相对于最近POI点的方向 */
      poi_position?: string
      /** 最近的路 */
      road?: string
      /** 距离最近路的距离 */
      road_distance?: number
      /** 相对于最近路的方向 */
      road_position?: string
    }

    interface SVGOptions {
      padding?: number
    }

    class SVG {
      constructor(opts?: SVGOptions)

      /**
       * 根据传入的字符串，只创建标准的SVG标签元素
       * @param name SVG标签名称
       * @returns SVG元素
       */
      create(name: string): SVGElement

      /**
       * 根据屏幕坐标的数组拼接类似的"M..L..L.."字符串，用于生成<path>标签的路径数据
       * @param rings 点坐标数组
       * @param closed 是否闭合路径
       * @returns 路径数据字符串
       */
      pointsToPath(rings: Point[], closed: boolean): string
    }

    interface PolylineToolOptions {
      /** 折线颜色，默认"#0000FF" */
      color?: string
      /** 折线的宽度，以像素为单位，默认3 */
      weight?: number
      /** 折线的透明度（范围0-1 之间），默认0.5 */
      opacity?: number
      /** 折线样式，solid或dashed */
      lineStyle?: 'solid' | 'dashed'
      /** 是否显示距离标签 */
      showLabel?: boolean
      /** 是否允许编辑 */
      enableEditing?: boolean
    }
    /** 折线工具事件对象 */
    interface PolylineToolEvent {
      /** 事件类型 */
      type: string
      /** 事件目标 */
      target: PolylineTool
      /** 当前折线的所有坐标点 */
      currentLnglats: LngLat[]
      /** 当前折线的距离 */
      currentDistance: number
      /** 当前绘制的折线对象 */
      currentPolyline: Polyline
      /** 所有绘制的折线对象 */
      allPolylines: Polyline[]
    }
    class PolylineTool {
      constructor(map: Map, opts?: PolylineToolOptions)

      /**
       * 设置跟随鼠标移动的说明文字
       * @param text 文本内容
       */
      setTips(text: string): void

      /**
       * 计算一系列地理坐标点的距离总和
       * @param lngLats 点坐标数组
       */
      getDistance(lngLats: LngLat[]): number

      /**
       * 完成一个折线的绘制，相当于用户双击结束当前折线的绘制
       */
      endDraw(): void

      /**
       * 清除绘制的所有折线
       */
      clear(): void

      /**
       * 开启绘制工具
       */
      open(): void

      /**
       * 关闭绘制工具
       */
      close(): void

      /**
       * 获取用户绘制的所有折线对象
       */
      getPolylines(): Polyline[]

      /**
       * 添加事件监听函数
       * @param event 事件名称
       * @param handler 事件处理函数
       */
      addEventListener(event: string, handler: (e: PolylineToolEvent) => void): void

      /**
       * 移除事件监听函数
       * @param event 事件名称
       * @param handler 事件处理函数
       */
      removeEventListener(event: string, handler: (e: PolylineToolEvent) => void): void
    }

    interface PolygonToolOptions {
      /** 多边形边线颜色，默认"#0000FF" */
      color?: string
      /** 多边形边线的宽度，以像素为单位，默认3 */
      weight?: number
      /** 多边形边线的透明度（范围0-1 之间），默认0.5 */
      opacity?: number
      /** 多边形填充颜色，默认"#0000FF" */
      fillColor?: string
      /** 多边形填充的透明度（范围0-1 之间），默认0.2 */
      fillOpacity?: number
      /** 边框样式，solid或dashed */
      lineStyle?: 'solid' | 'dashed'
      /** 是否显示面积信息 */
      showLabel?: boolean
    }

    /** 多边形工具事件对象 */
    interface PolygonToolEvent {
      /** 事件类型 */
      type: string
      /** 事件目标 */
      target: PolygonTool
      /** 当前多边形的所有坐标点 */
      currentLnglats: LngLat[]
      /** 当前多边形的面积 */
      currentArea: number
      /** 当前绘制的多边形对象 */
      currentPolygon: Polygon
      /** 所有绘制的多边形对象 */
      allPolygons: Polygon[]
    }

    class PolygonTool {
      constructor(map: Map, opts?: PolygonToolOptions)

      /**
       * 设置跟随鼠标移动的说明文字
       * @param text 文本内容
       */
      setTips(text: string): void

      /**
       * 计算一系列地理坐标点组成的面积
       * @param lngLats 点坐标数组
       * @returns 面积值，单位：平方米
       */
      getArea(lngLats: LngLat[]): number

      /**
       * 完成一个多边形的绘制，相当于用户双击结束当前多边形的绘制
       */
      endDraw(): void

      /**
       * 清除绘制的所有多边形
       */
      clear(): void

      /**
       * 开启绘制工具
       */
      open(): void

      /**
       * 关闭绘制工具
       */
      close(): void

      /**
       * 获取用户绘制的所有多边形对象
       */
      getPolygons(): Polygon[]

      /**
       * 添加事件监听函数
       * @param event 事件名称
       * @param handler 事件处理函数
       */
      addEventListener(event: string, handler: (e: PolygonToolEvent) => void): void

      /**
       * 移除事件监听函数
       * @param event 事件名称
       * @param handler 事件处理函数
       */
      removeEventListener(event: string, handler: (e: PolygonToolEvent) => void): void
    }

    interface PolylineOptions {
      /** 折线颜色，默认"#0000FF" */
      color?: string
      /** 折线的宽度，以像素为单位，默认3 */
      weight?: number
      /** 折线的透明度（范围0-1 之间），默认0.5 */
      opacity?: number
      /** 折线样式，solid或dashed */
      lineStyle?: 'solid' | 'dashed'
    }

    class Polyline extends Overlay {
      /**
       * 创建折线覆盖物对象
       * @param points 坐标数组
       * @param opts 线形的属性对象
       */
      constructor(points: LngLat[], opts?: PolylineOptions)

      /** 返回折线的点数组 */
      getLngLats(): LngLat[]

      /** 设置折线的点数组 */
      setLngLats(lnglats: LngLat[]): void

      /** 返回折线的颜色 */
      getColor(): string

      /** 设置折线的颜色 */
      setColor(color: string): void

      /** 返回折线的透明度 */
      getOpacity(): number

      /** 设置折线的透明度 */
      setOpacity(opacity: number): void

      /** 返回线的宽度 */
      getWeight(): number

      /** 设置折线的宽度 */
      setWeight(weight: number): void

      /** 返回当前折线样式状态，实线或者虚线 */
      getLineStyle(): string

      /** 设置折线是为实线或虚线 */
      setLineStyle(style: 'solid' | 'dashed'): void

      /** 返回折线的地理区域范围 */
      getBounds(): LngLatBounds

      /** 返回覆盖物所在的map对象 */
      getMap(): Map

      /** 添加事件监听函数 */
      addEventListener(event: PolylineEventType, handler: (e: PolylineEvent) => void): void

      /** 移除事件监听函数 */
      removeEventListener(event: PolylineEventType, handler: (e: PolylineEvent) => void): void

      /** 开启编辑功能 */
      enableEdit(): void

      /** 关闭编辑功能 */
      disableEdit(): void

      /** 是否启用线编辑功能，true表示启用，false表示禁止 */
      isEditable(): boolean

      /** 显示折线 */
      show(): void

      /** 隐藏折线 */
      hide(): void
    }

    type PolylineEventType =
      | 'click'
      | 'dblclick'
      | 'mousedown'
      | 'mouseup'
      | 'mouseout'
      | 'mouseover'
      | 'remove'

    interface PolylineEvent {
      /** 事件类型 */
      type: PolylineEventType
      /** 事件目标 */
      target: Polyline
      /** 经纬度坐标 */
      lnglat: LngLat
      /** 容器内点坐标 */
      containerPoint: Point
    }

    type PolygonEventType = PolylineEventType

    interface PolygonEvent {
      /** 事件类型 */
      type: PolygonEventType
      /** 事件目标 */
      target: Polygon
      /** 经纬度坐标 */
      lnglat: LngLat
      /** 容器内点坐标 */
      containerPoint: Point
    }

    class Polygon extends Overlay {
      /**
       * 创建多边形覆盖物
       * @param points 坐标数组
       * @param opts 多边形的属性对象
       */
      constructor(points: LngLat[] | LngLat[][], opts?: PolygonOptions)

      /**
       * 返回覆盖物类型
       */
      getType(): number

      /**
       * 设置多边形的点数组
       * @param lnglats 顶点数组
       */
      setLngLats(lnglats: LngLat[][]): void
      /**
       * 返回多边形的点数组
       */
      getLngLats(): LngLat[][]

      /**
       * 设置多边形边线的颜色
       * @param color 合法的CSS颜色值
       */
      setColor(color: string): void

      /**
       * 返回多边形边线的颜色
       */
      getColor(): string

      /**
       * 设置多边形边线的透明度
       * @param opacity 范围0-1之间
       */
      setOpacity(opacity: number): void

      /**
       * 返回多边形边线的透明度
       */
      getOpacity(): number

      /**
       * 设置多边形边线的宽度
       * @param weight 范围为大于等于1的整数
       */
      setWeight(weight: number): void

      /**
       * 返回多边形边线的宽度
       */
      getWeight(): number

      /**
       * 设置多边形边线是为实线或虚线
       * @param style solid或dashed
       */
      setLineStyle(style: 'solid' | 'dashed'): void

      /**
       * 返回当前多边形边线样式状态，实线或者虚线
       */
      getLineStyle(): string

      /**
       * 设置多边形的填充颜色，参数为合法的CSS颜色值。当参数为空字符串时，折线覆盖物填充颜色与边线颜色相同
       * @param color 合法的CSS颜色值
       */
      setFillColor(color: string): void

      /**
       * 返回多边形的填充颜色
       */
      getFillColor(): string

      /**
       * 设置多边形的填充透明度。当参数为0时，折线覆盖物将没有填充效果
       * @param opacity 取值范围0-1之间
       */
      setFillOpacity(opacity: number): void

      /**
       * 返回多边形的填充透明度
       */
      getFillOpacity(): number

      /**
       * 返回多边形的地理区域范围
       */
      getBounds(): LngLatBounds

      /**
       * 返回多边形所在的map对象
       */
      getMap(): Map

      /**
       * 添加事件监听函数
       * @param event 事件名称
       * @param handler 事件处理函数
       */
      addEventListener(event: PolygonEventType, handler: (e: PolygonEvent) => void): void

      /**
       * 移除事件监听函数
       * @param event 事件名称
       * @param handler 事件处理函数
       */
      removeEventListener(event: PolygonEventType, handler: (e: PolygonEvent) => void): void

      /**
       * 启用线编辑功能
       */
      enableEdit(): void

      /**
       * 禁用线编辑功能
       */
      disableEdit(): void

      /**
       * 是否启用线编辑功能，true表示启用，false表示禁止
       */
      isEditable(): boolean
    }
  }

  interface Window {
    T: typeof T
  }
}

export {}
