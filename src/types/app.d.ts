declare global {
  const __SYSTEM_INFO__: {
    pkg: {
      version: string
      dependencies: Record<string, string>
      devDependencies: Record<string, string>
    }
    lastBuildTime: string
  }
}

/**
 * 应用设置类型定义
 */
export namespace AppSettings {
  /**
   * 主题相关类型
   */
  type ThemeType = 'blue' | 'green' | 'red' | 'purple' | 'yellow'
  type ColorMode = 'light' | 'dark'

  /**
   * 布局相关类型
   */
  type SidebarMode = 'vertical' | 'horizontal'

  /**
   * 主题设置
   */
  interface Theme {
    /**
     * 颜色模式: light, dark, 或空字符串(跟随系统)
     */
    colorScheme?: ColorMode | ''
    /**
     * 主题类型: blue, green, red
     */
    themeType: ThemeType
    /**
     * 是否跟随系统主题
     */
    followSystemTheme: boolean
    /**
     * 圆角大小: none, small, medium, large
     */
    borderRadius: 'none' | 'small' | 'medium' | 'large'
  }

  /**
   * 应用功能设置
   */
  interface Features {
    /**
     * 动态标题
     */
    dynamicTitle: boolean
    /**
     * 启用 nprogress
     */
    nProgress: boolean
  }

  /**
   * 布局设置
   */
  interface Layout {
    /**
     * 侧边栏模式: vertical(垂直) 或 horizontal(水平)
     */
    sidebarMode: SidebarMode
    /**
     * 是否折叠侧边栏
     */
    sidebarCollapse: boolean
    /**
     * 是否固定侧边栏
     */
    sidebarFixed: boolean

    navbarFixed: boolean
  }

  /**
   * 用户偏好设置
   */
  interface UserPreferences {
    /**
     * 欢迎语
     */
    welcome: string
  }

  /**
   * 完整的应用设置
   */
  interface Config {
    /**
     * 主题和外观设置
     */
    theme: Theme & Features
    /**
     * 布局设置
     */
    layout: Layout
    /**
     * 用户偏好设置
     */
    user: UserPreferences
  }
}
