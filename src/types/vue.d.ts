import type { PermissionBinding, TimeBinding } from '@/directives'
import { usePermission } from '@/directives/permission'
import type { h } from 'vue'

declare module '@vue/runtime-core' {
  export interface ComponentCustomProperties {
    $permission: ReturnType<typeof usePermission>
    vTime: (el: HTMLElement, binding: TimeBinding) => void
    vPermission: (el: HTMLElement, binding: PermissionBinding) => void
  }
}

export type hType = typeof h;
