import type { RouteRecordRaw } from "vue-router"

export interface Account {
  accountType: number
  accountResources: string
  accountName: string
  accountId: number
}

export interface Application {
  applicationId: number
  applicationName: string
  applicationEnglishName: string
  applicationCode: string
  applicationIcon: string
  applicationIconActive: string
  applicationSort: number | null
  applicationParent: number
  remark: string | null
  children: Application[]
  functionList: Func[]
}

export interface Func {
  functionId: number
  functionType: string
  functionIcon: string
  functionName: string
  functionSort: number
  functionStatus: string
  functionLayout: string
  functionShowStatus: string
  functionRoutePath: string
  functionRouteParameter: string
  functionComponentPath: string
  functionPermissionCode: string
  functionParent: number
  remark: string | null
  functionApplication: number
  children: Func[]
  meta: string
}

export interface SystemApp {
  id: number
  code: string
  name: string
  englishName: string
  icon: string
  activeIcon: string
  homePage: string
  personal: boolean
  routes?: RouteRecordRaw[]
}

