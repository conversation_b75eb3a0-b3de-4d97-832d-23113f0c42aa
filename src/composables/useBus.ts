import mitt, { type Emitter } from 'mitt'
export interface MonitoringPoint {
  monitorDeviceCode: string
  monitorDeviceName: string
  previewUrl: string
  monitorDeviceInfo: string
  isFavorite?: boolean
  playBackUrl?: string
  havePtz: boolean
}

export interface MonitoringGrid {
  id: string
  point?: MonitoringPoint
}

type Events = {
  test: string
  SETCMAERAINFO: MonitoringGrid
  REQUEST_SCALE_CHANGE: void
}

export const emitter: Emitter<Events> = mitt<Events>()

export const useBus = () => {
  const on = <T extends keyof Events>(type: T, handler: (e: Events[T]) => void) => {
    emitter.on(type, handler)
  }

  const off = <T extends keyof Events>(type: T, handler?: (e: Events[T]) => void) => {
    emitter.off(type, handler)
  }

  const emit = <T extends keyof Events>(type: T, event?: Events[T]) => {
    emitter.emit(type, event as Events[T])
  }

  return { on, off, emit }
}
