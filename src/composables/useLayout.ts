import type { Ref, VNode } from 'vue'
import { ref, readonly, inject, provide, type InjectionKey, watch, computed } from 'vue'
import { useRoute } from 'vue-router'

interface ContentStyle {
  padding?: string
  background?: string
  [key: string]: any
}

interface LayoutContext {
  contentStyle: ReturnType<typeof ref<ContentStyle>>
  contentClass: ReturnType<typeof ref<string[]>>
  pageTitle: Ref<string>
  pageTitleVisible: Ref<boolean>
  pageTitleClass: Ref<string[]>
  pageTitleStyle: Ref<Record<string, string>>
  customTitleRender: Ref<(() => VNode) | null>
  updateContentStyle: (style: Partial<ContentStyle>) => void
  updateContentClass: (classList: string[]) => void
  resetContent: () => void
  setPageTitle: (title: string) => void
  showPageTitle: () => void
  hidePageTitle: () => void
  setCustomTitle: (render: (() => VNode) | null) => void
  showBackButton: Ref<boolean>
  onBack: (handler: () => void) => void
  clearBackHandler: () => void
  backHandler: Ref<(() => void) | null>
  titleClickable: Ref<boolean>
  onTitleClick: (handler: (e: MouseEvent) => void) => () => void
  clearTitleClickHandler: () => void
  titleClickHandler: Ref<((e: MouseEvent) => void) | null>
}

const LayoutSymbol: InjectionKey<LayoutContext> = Symbol('layout-context')

export function createLayout() {
  const route = useRoute()
  const contentStyle = ref<ContentStyle>({})
  const contentClass = ref<string[]>([])
  const pageTitle = ref('')
  const pageTitleVisible = ref(true)
  const pageTitleClass = ref<string[]>([])
  const pageTitleStyle = ref<Record<string, string>>({})
  const customTitleRender = ref<(() => VNode) | null>(null)
  const backHandler = ref<(() => void) | null>(null)
  const titleClickHandler = ref<((e: MouseEvent) => void) | null>(null)
  const showBackButton = computed(() => backHandler.value !== null)
  const titleClickable = computed(() => titleClickHandler.value !== null)

  const onBack = (handler: () => void) => {
    backHandler.value = handler
  }

  const clearBackHandler = () => {
    backHandler.value = null
  }

  const onTitleClick = (handler: (e: MouseEvent) => void) => {
    titleClickHandler.value = handler
    return () => {
      titleClickHandler.value = null
    }
  }

  const clearTitleClickHandler = () => {
    titleClickHandler.value = null
  }

  const clearCustomTitle = () => {
    customTitleRender.value = null
  }

  const resetLayout = () => {
    clearTitleClickHandler()
    clearBackHandler()
    clearCustomTitle()

    const title = route.meta.title as string
    if (title) {
      pageTitle.value = title
    }
  }

  watch(() => route.path, resetLayout, { immediate: true })

  const updateContentStyle = (style: Partial<ContentStyle>) => {
    contentStyle.value = { ...contentStyle.value, ...style }
  }

  const updateContentClass = (classList: string[]) => {
    contentClass.value = classList
  }

  const resetContent = () => {
    contentStyle.value = {}
    contentClass.value = []
  }

  const setPageTitle = (title: string) => {
    pageTitle.value = title
  }

  const showPageTitle = () => {
    pageTitleVisible.value = true
  }

  const hidePageTitle = () => {
    pageTitleVisible.value = false
  }

  const setCustomTitle = (render: (() => VNode) | null) => {
    customTitleRender.value = render
  }

  const context: LayoutContext = {
    contentStyle: readonly(contentStyle),
    contentClass,
    pageTitle: readonly(pageTitle),
    pageTitleVisible,
    pageTitleClass,
    pageTitleStyle,
    customTitleRender: readonly(customTitleRender),
    updateContentStyle,
    updateContentClass,
    resetContent,
    setPageTitle,
    showPageTitle,
    hidePageTitle,
    setCustomTitle,
    showBackButton: readonly(showBackButton),
    onBack,
    clearBackHandler,
    backHandler: readonly(backHandler),
    titleClickable: readonly(titleClickable),
    onTitleClick,
    clearTitleClickHandler,
    titleClickHandler: readonly(titleClickHandler)
  }

  provide(LayoutSymbol, context)

  return context
}

export function useLayout() {
  const context = inject(LayoutSymbol)

  if (!context) {
    throw new Error('useLayout must be used within layout provider')
  }

  return context
}
