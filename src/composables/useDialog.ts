import { ref, computed } from 'vue'
import { DialogUtils, type DialogMode, type ModeMappingValue, type ModelData } from '@/utils/dialog'
import type { ZDialogFormConfirmContext } from '@/ui/components/ZDialogForm/types'

export interface UseDialogOptions<T = ZDialogFormConfirmContext> {
  name: string
  modeMap?: Partial<Record<DialogMode, ModeMappingValue>>
  onConfirm?: (mode: DialogMode, ctx: T) => void | Promise<void>
  onCancel?: () => void
  onClose?: () => void
}

export function useDialog<T = ZDialogFormConfirmContext, M extends ModelData = ModelData>(
  options: UseDialogOptions<T>
) {
  const visible = ref(false)
  const mode = ref<DialogMode>('create')
  const model = ref<M | null>(null)
  const isSubmitting = ref(false)

  const title = computed(() =>
    DialogUtils.getTitle({
      name: options.name,
      mode: mode.value,
      modeMap: options.modeMap,
      model: model.value
    })
  )

  const open = (newMode: DialogMode, data?: M) => {
    mode.value = newMode
    model.value = data || null
    visible.value = true
    isSubmitting.value = false
  }

  const close = () => {
    visible.value = false
    model.value = null
    isSubmitting.value = false
  }

  const handleConfirm = async (ctx: T) => {
    if (isSubmitting.value) return

    try {
      isSubmitting.value = true
      if (options.onConfirm) {
        await options.onConfirm(mode.value, ctx)
      }
    } catch (error) {
      console.error('Dialog confirm error:', error)
    } finally {
      isSubmitting.value = false
    }
  }

  const handleCancel = () => {
    options.onCancel?.()
    close()
  }

  const handleClose = () => {
    options.onClose?.()
    close()
  }

  return {
    visible: computed({
      get: () => visible.value,
      set: (value) => visible.value = value
    }),
    mode,
    model,
    title,
    isSubmitting,
    open,
    close,
    handleConfirm,
    handleCancel,
    handleClose,
  }
}
