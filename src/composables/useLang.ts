import { useI18n } from 'vue-i18n'
import i18n, { loadLocaleMessages } from '@/i18n'
import { getStorage, setStorage } from '@/utils/storage'

const STORAGE_LANG_KEY = 'app_language'

// 分离初始化逻辑
export function initializeLanguage() {
  const savedLang = getStorage<'en' | 'zh'>(STORAGE_LANG_KEY)
  if (savedLang && ['en', 'zh'].includes(savedLang)) {
    loadLocaleMessages(savedLang).then(() => {
      i18n.global.locale.value = savedLang
      document.documentElement.setAttribute('lang', savedLang)
    })
  }
}

export function useLang() {
  const { locale } = useI18n()

  const switchLanguage = async (lang: 'en' | 'zh') => {
    try {
      if (!i18n.global.availableLocales.includes(lang)) {
        await loadLocaleMessages(lang)
      }
      locale.value = lang
      setStorage(STORAGE_LANG_KEY, lang)
      document.documentElement.setAttribute('lang', lang)
    } catch (error) {
      console.error('Failed to switch language:', error)
    }
  }

  return {
    locale,
    switchLanguage
  }
}
