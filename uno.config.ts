import { defineConfig, entriesToCss, presetWind3, toArray, transformerDirectives } from 'unocss'
import { themes } from './themes'
import { generateElementPlusOverrides } from './themes/element-plus-overrides'

export default defineConfig({
  preflights: [
    {
      getCSS: () => {
        const returnCss: any = []

        // 生成所有主题的 CSS
        themes.forEach(theme => {
          const { roots, light, dark } = theme

          // 浅色模式
          const lightCss = entriesToCss(Object.entries(light))
          const lightRoots = toArray([`body${roots} *,body${roots} ::before,body${roots} ::after`, `body${roots} ::backdrop`])
          returnCss.push(lightRoots.map(root => `${root}{${lightCss}}`).join(''))

          const darkCss = entriesToCss(Object.entries(dark))
          const darkRoots = toArray([`html.dark body${roots},html.dark body${roots} *,html.dark body${roots} ::before,html.dark body${roots} ::after`, `html.dark body${roots} ::backdrop`])
          returnCss.push(darkRoots.map(root => `${root}{${darkCss}}`).join(''))
        })

        // 添加 Element Plus 主题覆盖
        returnCss.push(generateElementPlusOverrides())

        return returnCss.join('')
      },
    },
  ],
  theme: {
    colors: {
      border: 'hsl(var(--border))',
      input: 'hsl(var(--input))',
      ring: 'hsl(var(--ring))',
      background: 'hsl(var(--background))',
      foreground: 'hsl(var(--foreground))',
      primary: {
        DEFAULT: 'hsl(var(--primary))',
        foreground: 'hsl(var(--primary-foreground))',
      },
      secondary: {
        DEFAULT: 'hsl(var(--secondary))',
        foreground: 'hsl(var(--secondary-foreground))',
      },
      destructive: {
        DEFAULT: 'hsl(var(--destructive))',
        foreground: 'hsl(var(--destructive-foreground))',
      },
      muted: {
        DEFAULT: 'hsl(var(--muted))',
        foreground: 'hsl(var(--muted-foreground))',
      },
      accent: {
        DEFAULT: 'hsl(var(--accent))',
        foreground: 'hsl(var(--accent-foreground))',
      },
      popover: {
        DEFAULT: 'hsl(var(--popover))',
        foreground: 'hsl(var(--popover-foreground))',
      },
      card: {
        DEFAULT: 'hsl(var(--card))',
        foreground: 'hsl(var(--card-foreground))',
      },
    },
    borderRadius: {
      xl: 'calc(var(--radius-size) + 4px)',
      lg: 'var(--radius-size)',
      md: 'calc(var(--radius-size) - 2px)',
      sm: 'calc(var(--radius-size) - 4px)',
    },
  },
  configDeps: [
    'themes/index.ts',
    'themes/element-plus-overrides.ts',
  ],
  presets: [
    presetWind3(),
  ],
  transformers: [
    transformerDirectives(),
  ],
})
