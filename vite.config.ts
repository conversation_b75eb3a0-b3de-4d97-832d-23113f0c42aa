import { fileURLToPath, URL } from 'node:url'
import path from 'path'
import { loadEnv } from 'vite'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import { envParse, parseLoadedEnv } from 'vite-plugin-env-parse'
import AppLoading from 'vite-plugin-app-loading'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import components from 'unplugin-vue-components/vite'
import pkg from './package.json'
import dayjs from 'dayjs'
import UnoCSS from 'unocss/vite'
import removeConsole from 'vite-plugin-remove-console';

// https://vite.dev/config/
export default defineConfig(({ mode, command }) => {
  const isBuild = command === 'build'
  const viteEnv = parseLoadedEnv(loadEnv(mode, process.cwd()))

  return {
    plugins: [
      vue(),
      vueJsx(),
      vueDevTools(),

      UnoCSS({
        hmrTopLevelAwait: false
      }),

      envParse({
        dtsPath: 'src/types/env.d.ts',
      }),

      AppLoading("loading.html"),

      components({
        globs: [
          'src/ui/components/*/index.vue',
        ],
        dts: './src/types/components.d.ts',
      }),

      createSvgIconsPlugin({
        iconDirs: [
          path.resolve(process.cwd(), 'src/assets/icons'),
          path.resolve(process.cwd(), 'src/map/assets/icons'),
        ],
        symbolId: 'icon-[dir]-[name]',
        svgoOptions: isBuild,
      }),

      removeConsole()
    ],

    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '#': path.resolve(__dirname, 'src/types'),
      },
    },

    define: {
      __SYSTEM_INFO__: JSON.stringify({
        pkg: {
          version: pkg.version,
          dependencies: pkg.dependencies,
          devDependencies: pkg.devDependencies,
        },
        lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      }),
    },

    server: {
      open: true,
      host: true,
      port: 9000,
      proxy: {
        '/proxy': {
          target: viteEnv.VITE_APP_API_BASEURL,
          changeOrigin: command === 'serve' && viteEnv.VITE_OPEN_PROXY === true,
          rewrite: path => path.replace(/\/proxy/, ''),
        },
      },
    },
    build: {
      outDir: mode === 'production' ? 'dist' : `dist-${mode}`,
      sourcemap: viteEnv.VITE_BUILD_SOURCEMAP === true,
    }
  }
})
