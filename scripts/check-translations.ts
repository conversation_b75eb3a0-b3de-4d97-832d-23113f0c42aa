import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const LOCALES_DIR = path.resolve(__dirname, '../src/i18n/locales')
const LOCALES = ['en', 'zh']
const MODULES = ['common', 'business']

async function main() {
  let hasError = false

  for (const module of MODULES) {
    const translations: Record<string, any> = {}

    // 加载所有语言的翻译
    for (const locale of LOCALES) {
      const filePath = path.join(LOCALES_DIR, locale, `${module}.json`)
      try {
        const content = await fs.readFile(filePath, 'utf-8')
        translations[locale] = JSON.parse(content)
      } catch (error) {
        console.error(`Error loading ${filePath}:`, error)
        hasError = true
        continue
      }
    }

    // 检查每种语言的完整性
    for (const locale of LOCALES) {
      if (locale === 'zh') continue // 跳过源语言

      const missingKeys = (await import('../src/i18n/validators')).validateTranslationCompleteness(
        translations[locale],
        translations['zh']
      )

      if (missingKeys.length > 0) {
        console.error(`Missing translations in ${locale}/${module}:`)
        missingKeys.forEach(key => console.error(`  - ${key}`))
        hasError = true
      }
    }
  }

  if (hasError) {
    process.exit(1)
  } else {
    console.log('All translations are complete!')
  }
}

main()
