import fs from 'fs/promises'
import path from 'path'
import { translate } from '@vitalets/google-translate-api'
import { fileURLToPath } from 'url'
import { dirname } from 'path'
import { HttpProxyAgent } from 'http-proxy-agent'

const agent = new HttpProxyAgent('http://127.0.0.1:12334');

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const LOCALES_DIR = path.resolve(__dirname, '../src/i18n/locales')
const SOURCE_LOCALE = 'zh'
const TARGET_LOCALES = ['en']

async function translateObject(obj: any, targetLang: string): Promise<any> {
  const result: any = {}

  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      const { text } = await translate(value, { from: SOURCE_LOCALE, to: targetLang, fetchOptions: { agent }, })
      result[key] = text
    } else if (typeof value === 'object' && value !== null) {
      result[key] = await translateObject(value, targetLang)
    } else {
      result[key] = value
    }
  }

  return result
}

async function main() {
  try {
    // 读取源语言文件
    const modules = ['common', 'business']

    for (const module of modules) {
      const sourceFile = path.join(LOCALES_DIR, SOURCE_LOCALE, `${module}.json`)
      const sourceContent = JSON.parse(
        await fs.readFile(sourceFile, 'utf-8')
      )

      // 翻译到目标语言
      for (const targetLang of TARGET_LOCALES) {
        const targetFile = path.join(LOCALES_DIR, targetLang, `${module}.json`)

        // 检查目标文件是否存在
        let targetContent: any = {}
        try {
          targetContent = JSON.parse(await fs.readFile(targetFile, 'utf-8'))
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (e) {
          // 文件不存在，使用空对象
        }

        // 只翻译缺失的键
        const translatedContent = await translateObject(sourceContent, targetLang)

        // 合并现有翻译和新翻译
        const mergedContent = {
          ...translatedContent,
          ...targetContent
        }

        // 保存翻译结果
        await fs.writeFile(
          targetFile,
          JSON.stringify(mergedContent, null, 2),
          'utf-8'
        )

        console.log(`Translated ${module} to ${targetLang}`)
      }
    }
  } catch (error) {
    console.error('Translation failed:', error)
    process.exit(1)
  }
}

main()
