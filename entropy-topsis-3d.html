<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>熵权TOPSIS 3D散点图分析</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .chart-container {
            height: 600px;
            margin: 20px 0;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }

        .info-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }

        .info-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }

        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .result-table th,
        .result-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .result-table th {
            background: #4facfe;
            color: white;
            font-weight: 500;
        }

        .result-table tr:hover {
            background: #f5f5f5;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>熵权TOPSIS多准则决策分析</h1>
            <p>基于3D散点图的可视化展示</p>
        </div>

        <div class="content">
            <div id="chart" class="chart-container"></div>

            <div class="info-panel">
                <div class="info-card">
                    <h3>📊 分析结果排名</h3>
                    <table class="result-table">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>方案</th>
                                <th>贴近度</th>
                            </tr>
                        </thead>
                        <tbody id="ranking-table">
                        </tbody>
                    </table>
                </div>

                <div class="info-card">
                    <h3>⚖️ 指标权重</h3>
                    <table class="result-table">
                        <thead>
                            <tr>
                                <th>指标</th>
                                <th>权重</th>
                                <th>类型</th>
                            </tr>
                        </thead>
                        <tbody id="weights-table">
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="legend" id="legend">
            </div>
        </div>
    </div>

    <!-- ECharts CDN -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts-gl@2.0.9/dist/echarts-gl.min.js"></script>

    <script>
        // 熵权TOPSIS算法实现
        function entropyTopsis(matrix, types) {
            const m = matrix.length;
            const n = matrix[0].length;

            // 1. 标准化矩阵
            const normMatrix = matrix.map(row => Array(n).fill(0));
            for (let j = 0; j < n; j++) {
                const sumSquare = Math.sqrt(matrix.reduce((sum, row) => sum + row[j] ** 2, 0));
                for (let i = 0; i < m; i++) {
                    normMatrix[i][j] = matrix[i][j] / sumSquare;
                }
            }

            // 2. 熵值法计算权重
            const pMatrix = normMatrix.map(row => Array(n).fill(0));
            const entropy = Array(n).fill(0);
            const weights = Array(n).fill(0);

            for (let j = 0; j < n; j++) {
                const sumNorm = normMatrix.reduce((sum, row) => sum + row[j], 0);
                for (let i = 0; i < m; i++) {
                    pMatrix[i][j] = sumNorm === 0 ? 0 : normMatrix[i][j] / sumNorm;
                }
                entropy[j] = pMatrix.reduce((sum, row) => {
                    const p = row[j];
                    return sum - (p === 0 ? 0 : p * Math.log(p));
                }, 0) / Math.log(m);
                weights[j] = (1 - entropy[j]) / (n - entropy.reduce((sum, e) => sum + e, 0));
            }

            // 3. 加权标准化矩阵
            const weightedMatrix = normMatrix.map(row => row.map((val, j) => val * weights[j]));

            // 4. 确定理想解和负理想解
            const idealPos = Array(n).fill(0);
            const idealNeg = Array(n).fill(0);
            for (let j = 0; j < n; j++) {
                const col = weightedMatrix.map(row => row[j]);
                idealPos[j] = types[j] === 1 ? Math.max(...col) : Math.min(...col);
                idealNeg[j] = types[j] === 1 ? Math.min(...col) : Math.max(...col);
            }

            // 5. 计算距离
            const sPos = weightedMatrix.map(row =>
                Math.sqrt(row.reduce((sum, val, j) => sum + (val - idealPos[j]) ** 2, 0))
            );
            const sNeg = weightedMatrix.map(row =>
                Math.sqrt(row.reduce((sum, val, j) => sum + (val - idealNeg[j]) ** 2, 0))
            );

            // 6. 计算贴近度
            const closeness = sPos.map((sp, i) => sNeg[i] / (sp + sNeg[i]));

            // 7. 排序并返回结果
            const ranked = closeness
                .map((c, i) => ({ scheme: `方案A${i + 1}`, closeness: c, index: i }))
                .sort((a, b) => b.closeness - a.closeness);

            return { weights, closeness: ranked, rawCloseness: closeness };
        }

        // 示例数据
        const decisionMatrix = [
            [80, 90, 10, 20], // 方案A1
            [70, 85, 15, 25], // 方案A2
            [90, 80, 12, 18], // 方案A3
        ];

        const criteriaTypes = [1, 1, -1, -1]; // C1, C2效益型，C3, C4成本型
        const criteriaNames = ['指标C1', '指标C2', '指标C3', '指标C4'];

        // 执行分析
        const result = entropyTopsis(decisionMatrix, criteriaTypes);
        console.log('分析结果:', result);

        // 准备3D散点图数据
        function prepareChartData(matrix, result) {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];

            return matrix.map((row, index) => {
                const closeness = result.rawCloseness[index];
                return {
                    name: `方案A${index + 1}`,
                    value: [row[0], row[1], closeness * 100], // Z轴用百分比显示
                    itemStyle: {
                        color: colors[index % colors.length]
                    }
                };
            });
        }

        const chartData = prepareChartData(decisionMatrix, result);

        // 初始化图表
        const chart = echarts.init(document.getElementById('chart'));

        // 配置3D散点图选项
        const option = {
            title: {
                text: '熵权TOPSIS 3D分析结果',
                left: 'center',
                top: 20,
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#333'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    const data = params.data;
                    return `
                        <div style="padding: 10px;">
                            <strong>${data.name}</strong><br/>
                            指标C1: ${data.value[0]}<br/>
                            指标C2: ${data.value[1]}<br/>
                            TOPSIS贴近度: ${(data.value[2]/100).toFixed(4)}<br/>
                            贴近度百分比: ${data.value[2].toFixed(2)}%
                        </div>
                    `;
                }
            },
            legend: {
                data: chartData.map(item => item.name),
                top: 50,
                textStyle: {
                    fontSize: 12
                }
            },
            xAxis3D: {
                name: '指标C1 (效益型)',
                nameTextStyle: {
                    fontSize: 14,
                    color: '#666'
                },
                axisLabel: {
                    fontSize: 12
                }
            },
            yAxis3D: {
                name: '指标C2 (效益型)',
                nameTextStyle: {
                    fontSize: 14,
                    color: '#666'
                },
                axisLabel: {
                    fontSize: 12
                }
            },
            zAxis3D: {
                name: 'TOPSIS贴近度 (%)',
                nameTextStyle: {
                    fontSize: 14,
                    color: '#666'
                },
                axisLabel: {
                    fontSize: 12,
                    formatter: '{value}%'
                }
            },
            grid3D: {
                boxWidth: 200,
                boxHeight: 200,
                boxDepth: 200,
                viewControl: {
                    projection: 'perspective',
                    autoRotate: true,
                    autoRotateSpeed: 5,
                    rotateSensitivity: 1,
                    zoomSensitivity: 1,
                    panSensitivity: 1,
                    alpha: 20,
                    beta: 40,
                    distance: 300
                },
                light: {
                    main: {
                        intensity: 1.2,
                        shadow: true
                    },
                    ambient: {
                        intensity: 0.3
                    }
                }
            },
            series: [{
                type: 'scatter3D',
                data: chartData,
                symbolSize: 20,
                emphasis: {
                    itemStyle: {
                        borderColor: '#fff',
                        borderWidth: 2
                    }
                },
                animationDuration: 2000,
                animationEasing: 'cubicOut'
            }]
        };

        // 设置图表选项
        chart.setOption(option);

        // 窗口大小改变时重新调整图表
        window.addEventListener('resize', function() {
            chart.resize();
        });

        // 填充结果表格
        function populateResultTables() {
            // 填充排名表格
            const rankingTableBody = document.getElementById('ranking-table');
            result.closeness.forEach((item, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${item.scheme}</td>
                    <td>${item.closeness.toFixed(4)}</td>
                `;
                rankingTableBody.appendChild(row);
            });

            // 填充权重表格
            const weightsTableBody = document.getElementById('weights-table');
            result.weights.forEach((weight, index) => {
                const row = document.createElement('tr');
                const type = criteriaTypes[index] === 1 ? '效益型' : '成本型';
                row.innerHTML = `
                    <td>${criteriaNames[index]}</td>
                    <td>${weight.toFixed(4)}</td>
                    <td>${type}</td>
                `;
                weightsTableBody.appendChild(row);
            });
        }

        // 创建图例
        function createLegend() {
            const legendContainer = document.getElementById('legend');
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];

            chartData.forEach((item, index) => {
                const legendItem = document.createElement('div');
                legendItem.className = 'legend-item';
                legendItem.innerHTML = `
                    <div class="legend-color" style="background-color: ${colors[index % colors.length]}"></div>
                    <span>${item.name}</span>
                `;
                legendContainer.appendChild(legendItem);
            });
        }

        // 初始化页面
        populateResultTables();
        createLegend();

        // 添加图表交互功能
        chart.on('click', function(params) {
            if (params.componentType === 'series') {
                const data = params.data;
                alert(`
方案详情：
${data.name}
指标C1: ${data.value[0]}
指标C2: ${data.value[1]}
TOPSIS贴近度: ${(data.value[2]/100).toFixed(4)}
                `.trim());
            }
        });
    </script>
</body>
</html>
